// Code generated by ent, DO NOT EDIT.

package wmsmaterial

import (
	"kratos-mono-demo/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldUpdatedAt, v))
}

// CreatedBy applies equality check predicate on the "created_by" field. It's identical to CreatedByEQ.
func CreatedBy(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldCreatedBy, v))
}

// UpdatedBy applies equality check predicate on the "updated_by" field. It's identical to UpdatedByEQ.
func UpdatedBy(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldUpdatedBy, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldStatus, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldRemark, v))
}

// FireStationID applies equality check predicate on the "fire_station_id" field. It's identical to FireStationIDEQ.
func FireStationID(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldFireStationID, v))
}

// MaterialType applies equality check predicate on the "material_type" field. It's identical to MaterialTypeEQ.
func MaterialType(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldMaterialType, v))
}

// EquipmentTypeID applies equality check predicate on the "equipment_type_id" field. It's identical to EquipmentTypeIDEQ.
func EquipmentTypeID(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldEquipmentTypeID, v))
}

// EquipmentID applies equality check predicate on the "equipment_id" field. It's identical to EquipmentIDEQ.
func EquipmentID(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldEquipmentID, v))
}

// Key applies equality check predicate on the "key" field. It's identical to KeyEQ.
func Key(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldKey, v))
}

// Code applies equality check predicate on the "code" field. It's identical to CodeEQ.
func Code(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldCode, v))
}

// CodeType applies equality check predicate on the "code_type" field. It's identical to CodeTypeEQ.
func CodeType(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldCodeType, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldName, v))
}

// ModelNo applies equality check predicate on the "model_no" field. It's identical to ModelNoEQ.
func ModelNo(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldModelNo, v))
}

// ProviderID applies equality check predicate on the "provider_id" field. It's identical to ProviderIDEQ.
func ProviderID(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldProviderID, v))
}

// Num applies equality check predicate on the "num" field. It's identical to NumEQ.
func Num(v uint32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldNum, v))
}

// Price applies equality check predicate on the "price" field. It's identical to PriceEQ.
func Price(v uint64) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldPrice, v))
}

// RepositoryID applies equality check predicate on the "repository_id" field. It's identical to RepositoryIDEQ.
func RepositoryID(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldRepositoryID, v))
}

// RepositoryAreaID applies equality check predicate on the "repository_area_id" field. It's identical to RepositoryAreaIDEQ.
func RepositoryAreaID(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldRepositoryAreaID, v))
}

// RepositoryPositionID applies equality check predicate on the "repository_position_id" field. It's identical to RepositoryPositionIDEQ.
func RepositoryPositionID(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldRepositoryPositionID, v))
}

// MeasureUnitID applies equality check predicate on the "measure_unit_id" field. It's identical to MeasureUnitIDEQ.
func MeasureUnitID(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldMeasureUnitID, v))
}

// ExpireTime applies equality check predicate on the "expire_time" field. It's identical to ExpireTimeEQ.
func ExpireTime(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldExpireTime, v))
}

// EquipmentStatus applies equality check predicate on the "equipment_status" field. It's identical to EquipmentStatusEQ.
func EquipmentStatus(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldEquipmentStatus, v))
}

// IsApproving applies equality check predicate on the "is_approving" field. It's identical to IsApprovingEQ.
func IsApproving(v bool) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldIsApproving, v))
}

// OwnerID applies equality check predicate on the "owner_id" field. It's identical to OwnerIDEQ.
func OwnerID(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldOwnerID, v))
}

// OriginalStatus applies equality check predicate on the "original_status" field. It's identical to OriginalStatusEQ.
func OriginalStatus(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldOriginalStatus, v))
}

// IsOneMaterialOneCode applies equality check predicate on the "is_one_material_one_code" field. It's identical to IsOneMaterialOneCodeEQ.
func IsOneMaterialOneCode(v bool) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldIsOneMaterialOneCode, v))
}

// DiscardMethod applies equality check predicate on the "discard_method" field. It's identical to DiscardMethodEQ.
func DiscardMethod(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldDiscardMethod, v))
}

// OriginalID applies equality check predicate on the "original_id" field. It's identical to OriginalIDEQ.
func OriginalID(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldOriginalID, v))
}

// OrderNo applies equality check predicate on the "order_no" field. It's identical to OrderNoEQ.
func OrderNo(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldOrderNo, v))
}

// FinanceSystemNo applies equality check predicate on the "finance_system_no" field. It's identical to FinanceSystemNoEQ.
func FinanceSystemNo(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldFinanceSystemNo, v))
}

// UseStartTime applies equality check predicate on the "use_start_time" field. It's identical to UseStartTimeEQ.
func UseStartTime(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldUseStartTime, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldUpdatedAt, v))
}

// CreatedByEQ applies the EQ predicate on the "created_by" field.
func CreatedByEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldCreatedBy, v))
}

// CreatedByNEQ applies the NEQ predicate on the "created_by" field.
func CreatedByNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldCreatedBy, v))
}

// CreatedByIn applies the In predicate on the "created_by" field.
func CreatedByIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldCreatedBy, vs...))
}

// CreatedByNotIn applies the NotIn predicate on the "created_by" field.
func CreatedByNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldCreatedBy, vs...))
}

// CreatedByGT applies the GT predicate on the "created_by" field.
func CreatedByGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldCreatedBy, v))
}

// CreatedByGTE applies the GTE predicate on the "created_by" field.
func CreatedByGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldCreatedBy, v))
}

// CreatedByLT applies the LT predicate on the "created_by" field.
func CreatedByLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldCreatedBy, v))
}

// CreatedByLTE applies the LTE predicate on the "created_by" field.
func CreatedByLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldCreatedBy, v))
}

// CreatedByContains applies the Contains predicate on the "created_by" field.
func CreatedByContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldCreatedBy, v))
}

// CreatedByHasPrefix applies the HasPrefix predicate on the "created_by" field.
func CreatedByHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldCreatedBy, v))
}

// CreatedByHasSuffix applies the HasSuffix predicate on the "created_by" field.
func CreatedByHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldCreatedBy, v))
}

// CreatedByIsNil applies the IsNil predicate on the "created_by" field.
func CreatedByIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldCreatedBy))
}

// CreatedByNotNil applies the NotNil predicate on the "created_by" field.
func CreatedByNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldCreatedBy))
}

// CreatedByEqualFold applies the EqualFold predicate on the "created_by" field.
func CreatedByEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldCreatedBy, v))
}

// CreatedByContainsFold applies the ContainsFold predicate on the "created_by" field.
func CreatedByContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldCreatedBy, v))
}

// UpdatedByEQ applies the EQ predicate on the "updated_by" field.
func UpdatedByEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldUpdatedBy, v))
}

// UpdatedByNEQ applies the NEQ predicate on the "updated_by" field.
func UpdatedByNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldUpdatedBy, v))
}

// UpdatedByIn applies the In predicate on the "updated_by" field.
func UpdatedByIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldUpdatedBy, vs...))
}

// UpdatedByNotIn applies the NotIn predicate on the "updated_by" field.
func UpdatedByNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldUpdatedBy, vs...))
}

// UpdatedByGT applies the GT predicate on the "updated_by" field.
func UpdatedByGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldUpdatedBy, v))
}

// UpdatedByGTE applies the GTE predicate on the "updated_by" field.
func UpdatedByGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldUpdatedBy, v))
}

// UpdatedByLT applies the LT predicate on the "updated_by" field.
func UpdatedByLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldUpdatedBy, v))
}

// UpdatedByLTE applies the LTE predicate on the "updated_by" field.
func UpdatedByLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldUpdatedBy, v))
}

// UpdatedByContains applies the Contains predicate on the "updated_by" field.
func UpdatedByContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldUpdatedBy, v))
}

// UpdatedByHasPrefix applies the HasPrefix predicate on the "updated_by" field.
func UpdatedByHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldUpdatedBy, v))
}

// UpdatedByHasSuffix applies the HasSuffix predicate on the "updated_by" field.
func UpdatedByHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldUpdatedBy, v))
}

// UpdatedByIsNil applies the IsNil predicate on the "updated_by" field.
func UpdatedByIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldUpdatedBy))
}

// UpdatedByNotNil applies the NotNil predicate on the "updated_by" field.
func UpdatedByNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldUpdatedBy))
}

// UpdatedByEqualFold applies the EqualFold predicate on the "updated_by" field.
func UpdatedByEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldUpdatedBy, v))
}

// UpdatedByContainsFold applies the ContainsFold predicate on the "updated_by" field.
func UpdatedByContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldUpdatedBy, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldStatus, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldRemark, v))
}

// FireStationIDEQ applies the EQ predicate on the "fire_station_id" field.
func FireStationIDEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldFireStationID, v))
}

// FireStationIDNEQ applies the NEQ predicate on the "fire_station_id" field.
func FireStationIDNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldFireStationID, v))
}

// FireStationIDIn applies the In predicate on the "fire_station_id" field.
func FireStationIDIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldFireStationID, vs...))
}

// FireStationIDNotIn applies the NotIn predicate on the "fire_station_id" field.
func FireStationIDNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldFireStationID, vs...))
}

// FireStationIDGT applies the GT predicate on the "fire_station_id" field.
func FireStationIDGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldFireStationID, v))
}

// FireStationIDGTE applies the GTE predicate on the "fire_station_id" field.
func FireStationIDGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldFireStationID, v))
}

// FireStationIDLT applies the LT predicate on the "fire_station_id" field.
func FireStationIDLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldFireStationID, v))
}

// FireStationIDLTE applies the LTE predicate on the "fire_station_id" field.
func FireStationIDLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldFireStationID, v))
}

// FireStationIDContains applies the Contains predicate on the "fire_station_id" field.
func FireStationIDContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldFireStationID, v))
}

// FireStationIDHasPrefix applies the HasPrefix predicate on the "fire_station_id" field.
func FireStationIDHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldFireStationID, v))
}

// FireStationIDHasSuffix applies the HasSuffix predicate on the "fire_station_id" field.
func FireStationIDHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldFireStationID, v))
}

// FireStationIDIsNil applies the IsNil predicate on the "fire_station_id" field.
func FireStationIDIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldFireStationID))
}

// FireStationIDNotNil applies the NotNil predicate on the "fire_station_id" field.
func FireStationIDNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldFireStationID))
}

// FireStationIDEqualFold applies the EqualFold predicate on the "fire_station_id" field.
func FireStationIDEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldFireStationID, v))
}

// FireStationIDContainsFold applies the ContainsFold predicate on the "fire_station_id" field.
func FireStationIDContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldFireStationID, v))
}

// MaterialTypeEQ applies the EQ predicate on the "material_type" field.
func MaterialTypeEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldMaterialType, v))
}

// MaterialTypeNEQ applies the NEQ predicate on the "material_type" field.
func MaterialTypeNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldMaterialType, v))
}

// MaterialTypeIn applies the In predicate on the "material_type" field.
func MaterialTypeIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldMaterialType, vs...))
}

// MaterialTypeNotIn applies the NotIn predicate on the "material_type" field.
func MaterialTypeNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldMaterialType, vs...))
}

// MaterialTypeGT applies the GT predicate on the "material_type" field.
func MaterialTypeGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldMaterialType, v))
}

// MaterialTypeGTE applies the GTE predicate on the "material_type" field.
func MaterialTypeGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldMaterialType, v))
}

// MaterialTypeLT applies the LT predicate on the "material_type" field.
func MaterialTypeLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldMaterialType, v))
}

// MaterialTypeLTE applies the LTE predicate on the "material_type" field.
func MaterialTypeLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldMaterialType, v))
}

// MaterialTypeContains applies the Contains predicate on the "material_type" field.
func MaterialTypeContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldMaterialType, v))
}

// MaterialTypeHasPrefix applies the HasPrefix predicate on the "material_type" field.
func MaterialTypeHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldMaterialType, v))
}

// MaterialTypeHasSuffix applies the HasSuffix predicate on the "material_type" field.
func MaterialTypeHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldMaterialType, v))
}

// MaterialTypeEqualFold applies the EqualFold predicate on the "material_type" field.
func MaterialTypeEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldMaterialType, v))
}

// MaterialTypeContainsFold applies the ContainsFold predicate on the "material_type" field.
func MaterialTypeContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldMaterialType, v))
}

// EquipmentTypeIDEQ applies the EQ predicate on the "equipment_type_id" field.
func EquipmentTypeIDEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDNEQ applies the NEQ predicate on the "equipment_type_id" field.
func EquipmentTypeIDNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDIn applies the In predicate on the "equipment_type_id" field.
func EquipmentTypeIDIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldEquipmentTypeID, vs...))
}

// EquipmentTypeIDNotIn applies the NotIn predicate on the "equipment_type_id" field.
func EquipmentTypeIDNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldEquipmentTypeID, vs...))
}

// EquipmentTypeIDGT applies the GT predicate on the "equipment_type_id" field.
func EquipmentTypeIDGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDGTE applies the GTE predicate on the "equipment_type_id" field.
func EquipmentTypeIDGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDLT applies the LT predicate on the "equipment_type_id" field.
func EquipmentTypeIDLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDLTE applies the LTE predicate on the "equipment_type_id" field.
func EquipmentTypeIDLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDContains applies the Contains predicate on the "equipment_type_id" field.
func EquipmentTypeIDContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDHasPrefix applies the HasPrefix predicate on the "equipment_type_id" field.
func EquipmentTypeIDHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDHasSuffix applies the HasSuffix predicate on the "equipment_type_id" field.
func EquipmentTypeIDHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDIsNil applies the IsNil predicate on the "equipment_type_id" field.
func EquipmentTypeIDIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldEquipmentTypeID))
}

// EquipmentTypeIDNotNil applies the NotNil predicate on the "equipment_type_id" field.
func EquipmentTypeIDNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldEquipmentTypeID))
}

// EquipmentTypeIDEqualFold applies the EqualFold predicate on the "equipment_type_id" field.
func EquipmentTypeIDEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDContainsFold applies the ContainsFold predicate on the "equipment_type_id" field.
func EquipmentTypeIDContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldEquipmentTypeID, v))
}

// EquipmentIDEQ applies the EQ predicate on the "equipment_id" field.
func EquipmentIDEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldEquipmentID, v))
}

// EquipmentIDNEQ applies the NEQ predicate on the "equipment_id" field.
func EquipmentIDNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldEquipmentID, v))
}

// EquipmentIDIn applies the In predicate on the "equipment_id" field.
func EquipmentIDIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldEquipmentID, vs...))
}

// EquipmentIDNotIn applies the NotIn predicate on the "equipment_id" field.
func EquipmentIDNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldEquipmentID, vs...))
}

// EquipmentIDGT applies the GT predicate on the "equipment_id" field.
func EquipmentIDGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldEquipmentID, v))
}

// EquipmentIDGTE applies the GTE predicate on the "equipment_id" field.
func EquipmentIDGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldEquipmentID, v))
}

// EquipmentIDLT applies the LT predicate on the "equipment_id" field.
func EquipmentIDLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldEquipmentID, v))
}

// EquipmentIDLTE applies the LTE predicate on the "equipment_id" field.
func EquipmentIDLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldEquipmentID, v))
}

// EquipmentIDContains applies the Contains predicate on the "equipment_id" field.
func EquipmentIDContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldEquipmentID, v))
}

// EquipmentIDHasPrefix applies the HasPrefix predicate on the "equipment_id" field.
func EquipmentIDHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldEquipmentID, v))
}

// EquipmentIDHasSuffix applies the HasSuffix predicate on the "equipment_id" field.
func EquipmentIDHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldEquipmentID, v))
}

// EquipmentIDIsNil applies the IsNil predicate on the "equipment_id" field.
func EquipmentIDIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldEquipmentID))
}

// EquipmentIDNotNil applies the NotNil predicate on the "equipment_id" field.
func EquipmentIDNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldEquipmentID))
}

// EquipmentIDEqualFold applies the EqualFold predicate on the "equipment_id" field.
func EquipmentIDEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldEquipmentID, v))
}

// EquipmentIDContainsFold applies the ContainsFold predicate on the "equipment_id" field.
func EquipmentIDContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldEquipmentID, v))
}

// KeyEQ applies the EQ predicate on the "key" field.
func KeyEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldKey, v))
}

// KeyNEQ applies the NEQ predicate on the "key" field.
func KeyNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldKey, v))
}

// KeyIn applies the In predicate on the "key" field.
func KeyIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldKey, vs...))
}

// KeyNotIn applies the NotIn predicate on the "key" field.
func KeyNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldKey, vs...))
}

// KeyGT applies the GT predicate on the "key" field.
func KeyGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldKey, v))
}

// KeyGTE applies the GTE predicate on the "key" field.
func KeyGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldKey, v))
}

// KeyLT applies the LT predicate on the "key" field.
func KeyLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldKey, v))
}

// KeyLTE applies the LTE predicate on the "key" field.
func KeyLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldKey, v))
}

// KeyContains applies the Contains predicate on the "key" field.
func KeyContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldKey, v))
}

// KeyHasPrefix applies the HasPrefix predicate on the "key" field.
func KeyHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldKey, v))
}

// KeyHasSuffix applies the HasSuffix predicate on the "key" field.
func KeyHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldKey, v))
}

// KeyIsNil applies the IsNil predicate on the "key" field.
func KeyIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldKey))
}

// KeyNotNil applies the NotNil predicate on the "key" field.
func KeyNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldKey))
}

// KeyEqualFold applies the EqualFold predicate on the "key" field.
func KeyEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldKey, v))
}

// KeyContainsFold applies the ContainsFold predicate on the "key" field.
func KeyContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldKey, v))
}

// CodeEQ applies the EQ predicate on the "code" field.
func CodeEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldCode, v))
}

// CodeNEQ applies the NEQ predicate on the "code" field.
func CodeNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldCode, v))
}

// CodeIn applies the In predicate on the "code" field.
func CodeIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldCode, vs...))
}

// CodeNotIn applies the NotIn predicate on the "code" field.
func CodeNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldCode, vs...))
}

// CodeGT applies the GT predicate on the "code" field.
func CodeGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldCode, v))
}

// CodeGTE applies the GTE predicate on the "code" field.
func CodeGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldCode, v))
}

// CodeLT applies the LT predicate on the "code" field.
func CodeLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldCode, v))
}

// CodeLTE applies the LTE predicate on the "code" field.
func CodeLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldCode, v))
}

// CodeContains applies the Contains predicate on the "code" field.
func CodeContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldCode, v))
}

// CodeHasPrefix applies the HasPrefix predicate on the "code" field.
func CodeHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldCode, v))
}

// CodeHasSuffix applies the HasSuffix predicate on the "code" field.
func CodeHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldCode, v))
}

// CodeIsNil applies the IsNil predicate on the "code" field.
func CodeIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldCode))
}

// CodeNotNil applies the NotNil predicate on the "code" field.
func CodeNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldCode))
}

// CodeEqualFold applies the EqualFold predicate on the "code" field.
func CodeEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldCode, v))
}

// CodeContainsFold applies the ContainsFold predicate on the "code" field.
func CodeContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldCode, v))
}

// CodeTypeEQ applies the EQ predicate on the "code_type" field.
func CodeTypeEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldCodeType, v))
}

// CodeTypeNEQ applies the NEQ predicate on the "code_type" field.
func CodeTypeNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldCodeType, v))
}

// CodeTypeIn applies the In predicate on the "code_type" field.
func CodeTypeIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldCodeType, vs...))
}

// CodeTypeNotIn applies the NotIn predicate on the "code_type" field.
func CodeTypeNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldCodeType, vs...))
}

// CodeTypeGT applies the GT predicate on the "code_type" field.
func CodeTypeGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldCodeType, v))
}

// CodeTypeGTE applies the GTE predicate on the "code_type" field.
func CodeTypeGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldCodeType, v))
}

// CodeTypeLT applies the LT predicate on the "code_type" field.
func CodeTypeLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldCodeType, v))
}

// CodeTypeLTE applies the LTE predicate on the "code_type" field.
func CodeTypeLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldCodeType, v))
}

// CodeTypeContains applies the Contains predicate on the "code_type" field.
func CodeTypeContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldCodeType, v))
}

// CodeTypeHasPrefix applies the HasPrefix predicate on the "code_type" field.
func CodeTypeHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldCodeType, v))
}

// CodeTypeHasSuffix applies the HasSuffix predicate on the "code_type" field.
func CodeTypeHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldCodeType, v))
}

// CodeTypeEqualFold applies the EqualFold predicate on the "code_type" field.
func CodeTypeEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldCodeType, v))
}

// CodeTypeContainsFold applies the ContainsFold predicate on the "code_type" field.
func CodeTypeContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldCodeType, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldName, v))
}

// ModelNoEQ applies the EQ predicate on the "model_no" field.
func ModelNoEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldModelNo, v))
}

// ModelNoNEQ applies the NEQ predicate on the "model_no" field.
func ModelNoNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldModelNo, v))
}

// ModelNoIn applies the In predicate on the "model_no" field.
func ModelNoIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldModelNo, vs...))
}

// ModelNoNotIn applies the NotIn predicate on the "model_no" field.
func ModelNoNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldModelNo, vs...))
}

// ModelNoGT applies the GT predicate on the "model_no" field.
func ModelNoGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldModelNo, v))
}

// ModelNoGTE applies the GTE predicate on the "model_no" field.
func ModelNoGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldModelNo, v))
}

// ModelNoLT applies the LT predicate on the "model_no" field.
func ModelNoLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldModelNo, v))
}

// ModelNoLTE applies the LTE predicate on the "model_no" field.
func ModelNoLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldModelNo, v))
}

// ModelNoContains applies the Contains predicate on the "model_no" field.
func ModelNoContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldModelNo, v))
}

// ModelNoHasPrefix applies the HasPrefix predicate on the "model_no" field.
func ModelNoHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldModelNo, v))
}

// ModelNoHasSuffix applies the HasSuffix predicate on the "model_no" field.
func ModelNoHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldModelNo, v))
}

// ModelNoEqualFold applies the EqualFold predicate on the "model_no" field.
func ModelNoEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldModelNo, v))
}

// ModelNoContainsFold applies the ContainsFold predicate on the "model_no" field.
func ModelNoContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldModelNo, v))
}

// ProviderIDEQ applies the EQ predicate on the "provider_id" field.
func ProviderIDEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldProviderID, v))
}

// ProviderIDNEQ applies the NEQ predicate on the "provider_id" field.
func ProviderIDNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldProviderID, v))
}

// ProviderIDIn applies the In predicate on the "provider_id" field.
func ProviderIDIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldProviderID, vs...))
}

// ProviderIDNotIn applies the NotIn predicate on the "provider_id" field.
func ProviderIDNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldProviderID, vs...))
}

// ProviderIDGT applies the GT predicate on the "provider_id" field.
func ProviderIDGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldProviderID, v))
}

// ProviderIDGTE applies the GTE predicate on the "provider_id" field.
func ProviderIDGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldProviderID, v))
}

// ProviderIDLT applies the LT predicate on the "provider_id" field.
func ProviderIDLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldProviderID, v))
}

// ProviderIDLTE applies the LTE predicate on the "provider_id" field.
func ProviderIDLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldProviderID, v))
}

// ProviderIDContains applies the Contains predicate on the "provider_id" field.
func ProviderIDContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldProviderID, v))
}

// ProviderIDHasPrefix applies the HasPrefix predicate on the "provider_id" field.
func ProviderIDHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldProviderID, v))
}

// ProviderIDHasSuffix applies the HasSuffix predicate on the "provider_id" field.
func ProviderIDHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldProviderID, v))
}

// ProviderIDEqualFold applies the EqualFold predicate on the "provider_id" field.
func ProviderIDEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldProviderID, v))
}

// ProviderIDContainsFold applies the ContainsFold predicate on the "provider_id" field.
func ProviderIDContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldProviderID, v))
}

// NumEQ applies the EQ predicate on the "num" field.
func NumEQ(v uint32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldNum, v))
}

// NumNEQ applies the NEQ predicate on the "num" field.
func NumNEQ(v uint32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldNum, v))
}

// NumIn applies the In predicate on the "num" field.
func NumIn(vs ...uint32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldNum, vs...))
}

// NumNotIn applies the NotIn predicate on the "num" field.
func NumNotIn(vs ...uint32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldNum, vs...))
}

// NumGT applies the GT predicate on the "num" field.
func NumGT(v uint32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldNum, v))
}

// NumGTE applies the GTE predicate on the "num" field.
func NumGTE(v uint32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldNum, v))
}

// NumLT applies the LT predicate on the "num" field.
func NumLT(v uint32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldNum, v))
}

// NumLTE applies the LTE predicate on the "num" field.
func NumLTE(v uint32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldNum, v))
}

// PriceEQ applies the EQ predicate on the "price" field.
func PriceEQ(v uint64) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldPrice, v))
}

// PriceNEQ applies the NEQ predicate on the "price" field.
func PriceNEQ(v uint64) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldPrice, v))
}

// PriceIn applies the In predicate on the "price" field.
func PriceIn(vs ...uint64) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldPrice, vs...))
}

// PriceNotIn applies the NotIn predicate on the "price" field.
func PriceNotIn(vs ...uint64) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldPrice, vs...))
}

// PriceGT applies the GT predicate on the "price" field.
func PriceGT(v uint64) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldPrice, v))
}

// PriceGTE applies the GTE predicate on the "price" field.
func PriceGTE(v uint64) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldPrice, v))
}

// PriceLT applies the LT predicate on the "price" field.
func PriceLT(v uint64) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldPrice, v))
}

// PriceLTE applies the LTE predicate on the "price" field.
func PriceLTE(v uint64) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldPrice, v))
}

// RepositoryIDEQ applies the EQ predicate on the "repository_id" field.
func RepositoryIDEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldRepositoryID, v))
}

// RepositoryIDNEQ applies the NEQ predicate on the "repository_id" field.
func RepositoryIDNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldRepositoryID, v))
}

// RepositoryIDIn applies the In predicate on the "repository_id" field.
func RepositoryIDIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldRepositoryID, vs...))
}

// RepositoryIDNotIn applies the NotIn predicate on the "repository_id" field.
func RepositoryIDNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldRepositoryID, vs...))
}

// RepositoryIDGT applies the GT predicate on the "repository_id" field.
func RepositoryIDGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldRepositoryID, v))
}

// RepositoryIDGTE applies the GTE predicate on the "repository_id" field.
func RepositoryIDGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldRepositoryID, v))
}

// RepositoryIDLT applies the LT predicate on the "repository_id" field.
func RepositoryIDLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldRepositoryID, v))
}

// RepositoryIDLTE applies the LTE predicate on the "repository_id" field.
func RepositoryIDLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldRepositoryID, v))
}

// RepositoryIDContains applies the Contains predicate on the "repository_id" field.
func RepositoryIDContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldRepositoryID, v))
}

// RepositoryIDHasPrefix applies the HasPrefix predicate on the "repository_id" field.
func RepositoryIDHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldRepositoryID, v))
}

// RepositoryIDHasSuffix applies the HasSuffix predicate on the "repository_id" field.
func RepositoryIDHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldRepositoryID, v))
}

// RepositoryIDIsNil applies the IsNil predicate on the "repository_id" field.
func RepositoryIDIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldRepositoryID))
}

// RepositoryIDNotNil applies the NotNil predicate on the "repository_id" field.
func RepositoryIDNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldRepositoryID))
}

// RepositoryIDEqualFold applies the EqualFold predicate on the "repository_id" field.
func RepositoryIDEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldRepositoryID, v))
}

// RepositoryIDContainsFold applies the ContainsFold predicate on the "repository_id" field.
func RepositoryIDContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldRepositoryID, v))
}

// RepositoryAreaIDEQ applies the EQ predicate on the "repository_area_id" field.
func RepositoryAreaIDEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDNEQ applies the NEQ predicate on the "repository_area_id" field.
func RepositoryAreaIDNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDIn applies the In predicate on the "repository_area_id" field.
func RepositoryAreaIDIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldRepositoryAreaID, vs...))
}

// RepositoryAreaIDNotIn applies the NotIn predicate on the "repository_area_id" field.
func RepositoryAreaIDNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldRepositoryAreaID, vs...))
}

// RepositoryAreaIDGT applies the GT predicate on the "repository_area_id" field.
func RepositoryAreaIDGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDGTE applies the GTE predicate on the "repository_area_id" field.
func RepositoryAreaIDGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDLT applies the LT predicate on the "repository_area_id" field.
func RepositoryAreaIDLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDLTE applies the LTE predicate on the "repository_area_id" field.
func RepositoryAreaIDLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDContains applies the Contains predicate on the "repository_area_id" field.
func RepositoryAreaIDContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDHasPrefix applies the HasPrefix predicate on the "repository_area_id" field.
func RepositoryAreaIDHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDHasSuffix applies the HasSuffix predicate on the "repository_area_id" field.
func RepositoryAreaIDHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDIsNil applies the IsNil predicate on the "repository_area_id" field.
func RepositoryAreaIDIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldRepositoryAreaID))
}

// RepositoryAreaIDNotNil applies the NotNil predicate on the "repository_area_id" field.
func RepositoryAreaIDNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldRepositoryAreaID))
}

// RepositoryAreaIDEqualFold applies the EqualFold predicate on the "repository_area_id" field.
func RepositoryAreaIDEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDContainsFold applies the ContainsFold predicate on the "repository_area_id" field.
func RepositoryAreaIDContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldRepositoryAreaID, v))
}

// RepositoryPositionIDEQ applies the EQ predicate on the "repository_position_id" field.
func RepositoryPositionIDEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDNEQ applies the NEQ predicate on the "repository_position_id" field.
func RepositoryPositionIDNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDIn applies the In predicate on the "repository_position_id" field.
func RepositoryPositionIDIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldRepositoryPositionID, vs...))
}

// RepositoryPositionIDNotIn applies the NotIn predicate on the "repository_position_id" field.
func RepositoryPositionIDNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldRepositoryPositionID, vs...))
}

// RepositoryPositionIDGT applies the GT predicate on the "repository_position_id" field.
func RepositoryPositionIDGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDGTE applies the GTE predicate on the "repository_position_id" field.
func RepositoryPositionIDGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDLT applies the LT predicate on the "repository_position_id" field.
func RepositoryPositionIDLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDLTE applies the LTE predicate on the "repository_position_id" field.
func RepositoryPositionIDLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDContains applies the Contains predicate on the "repository_position_id" field.
func RepositoryPositionIDContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDHasPrefix applies the HasPrefix predicate on the "repository_position_id" field.
func RepositoryPositionIDHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDHasSuffix applies the HasSuffix predicate on the "repository_position_id" field.
func RepositoryPositionIDHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDIsNil applies the IsNil predicate on the "repository_position_id" field.
func RepositoryPositionIDIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldRepositoryPositionID))
}

// RepositoryPositionIDNotNil applies the NotNil predicate on the "repository_position_id" field.
func RepositoryPositionIDNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldRepositoryPositionID))
}

// RepositoryPositionIDEqualFold applies the EqualFold predicate on the "repository_position_id" field.
func RepositoryPositionIDEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDContainsFold applies the ContainsFold predicate on the "repository_position_id" field.
func RepositoryPositionIDContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldRepositoryPositionID, v))
}

// MeasureUnitIDEQ applies the EQ predicate on the "measure_unit_id" field.
func MeasureUnitIDEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldMeasureUnitID, v))
}

// MeasureUnitIDNEQ applies the NEQ predicate on the "measure_unit_id" field.
func MeasureUnitIDNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldMeasureUnitID, v))
}

// MeasureUnitIDIn applies the In predicate on the "measure_unit_id" field.
func MeasureUnitIDIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldMeasureUnitID, vs...))
}

// MeasureUnitIDNotIn applies the NotIn predicate on the "measure_unit_id" field.
func MeasureUnitIDNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldMeasureUnitID, vs...))
}

// MeasureUnitIDGT applies the GT predicate on the "measure_unit_id" field.
func MeasureUnitIDGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldMeasureUnitID, v))
}

// MeasureUnitIDGTE applies the GTE predicate on the "measure_unit_id" field.
func MeasureUnitIDGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldMeasureUnitID, v))
}

// MeasureUnitIDLT applies the LT predicate on the "measure_unit_id" field.
func MeasureUnitIDLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldMeasureUnitID, v))
}

// MeasureUnitIDLTE applies the LTE predicate on the "measure_unit_id" field.
func MeasureUnitIDLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldMeasureUnitID, v))
}

// MeasureUnitIDContains applies the Contains predicate on the "measure_unit_id" field.
func MeasureUnitIDContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldMeasureUnitID, v))
}

// MeasureUnitIDHasPrefix applies the HasPrefix predicate on the "measure_unit_id" field.
func MeasureUnitIDHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldMeasureUnitID, v))
}

// MeasureUnitIDHasSuffix applies the HasSuffix predicate on the "measure_unit_id" field.
func MeasureUnitIDHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldMeasureUnitID, v))
}

// MeasureUnitIDIsNil applies the IsNil predicate on the "measure_unit_id" field.
func MeasureUnitIDIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldMeasureUnitID))
}

// MeasureUnitIDNotNil applies the NotNil predicate on the "measure_unit_id" field.
func MeasureUnitIDNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldMeasureUnitID))
}

// MeasureUnitIDEqualFold applies the EqualFold predicate on the "measure_unit_id" field.
func MeasureUnitIDEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldMeasureUnitID, v))
}

// MeasureUnitIDContainsFold applies the ContainsFold predicate on the "measure_unit_id" field.
func MeasureUnitIDContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldMeasureUnitID, v))
}

// ExpireTimeEQ applies the EQ predicate on the "expire_time" field.
func ExpireTimeEQ(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldExpireTime, v))
}

// ExpireTimeNEQ applies the NEQ predicate on the "expire_time" field.
func ExpireTimeNEQ(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldExpireTime, v))
}

// ExpireTimeIn applies the In predicate on the "expire_time" field.
func ExpireTimeIn(vs ...time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldExpireTime, vs...))
}

// ExpireTimeNotIn applies the NotIn predicate on the "expire_time" field.
func ExpireTimeNotIn(vs ...time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldExpireTime, vs...))
}

// ExpireTimeGT applies the GT predicate on the "expire_time" field.
func ExpireTimeGT(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldExpireTime, v))
}

// ExpireTimeGTE applies the GTE predicate on the "expire_time" field.
func ExpireTimeGTE(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldExpireTime, v))
}

// ExpireTimeLT applies the LT predicate on the "expire_time" field.
func ExpireTimeLT(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldExpireTime, v))
}

// ExpireTimeLTE applies the LTE predicate on the "expire_time" field.
func ExpireTimeLTE(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldExpireTime, v))
}

// ExpireTimeIsNil applies the IsNil predicate on the "expire_time" field.
func ExpireTimeIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldExpireTime))
}

// ExpireTimeNotNil applies the NotNil predicate on the "expire_time" field.
func ExpireTimeNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldExpireTime))
}

// EquipmentStatusEQ applies the EQ predicate on the "equipment_status" field.
func EquipmentStatusEQ(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldEquipmentStatus, v))
}

// EquipmentStatusNEQ applies the NEQ predicate on the "equipment_status" field.
func EquipmentStatusNEQ(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldEquipmentStatus, v))
}

// EquipmentStatusIn applies the In predicate on the "equipment_status" field.
func EquipmentStatusIn(vs ...int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldEquipmentStatus, vs...))
}

// EquipmentStatusNotIn applies the NotIn predicate on the "equipment_status" field.
func EquipmentStatusNotIn(vs ...int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldEquipmentStatus, vs...))
}

// EquipmentStatusGT applies the GT predicate on the "equipment_status" field.
func EquipmentStatusGT(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldEquipmentStatus, v))
}

// EquipmentStatusGTE applies the GTE predicate on the "equipment_status" field.
func EquipmentStatusGTE(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldEquipmentStatus, v))
}

// EquipmentStatusLT applies the LT predicate on the "equipment_status" field.
func EquipmentStatusLT(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldEquipmentStatus, v))
}

// EquipmentStatusLTE applies the LTE predicate on the "equipment_status" field.
func EquipmentStatusLTE(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldEquipmentStatus, v))
}

// EquipmentStatusIsNil applies the IsNil predicate on the "equipment_status" field.
func EquipmentStatusIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldEquipmentStatus))
}

// EquipmentStatusNotNil applies the NotNil predicate on the "equipment_status" field.
func EquipmentStatusNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldEquipmentStatus))
}

// IsApprovingEQ applies the EQ predicate on the "is_approving" field.
func IsApprovingEQ(v bool) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldIsApproving, v))
}

// IsApprovingNEQ applies the NEQ predicate on the "is_approving" field.
func IsApprovingNEQ(v bool) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldIsApproving, v))
}

// IsApprovingIsNil applies the IsNil predicate on the "is_approving" field.
func IsApprovingIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldIsApproving))
}

// IsApprovingNotNil applies the NotNil predicate on the "is_approving" field.
func IsApprovingNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldIsApproving))
}

// OwnerIDEQ applies the EQ predicate on the "owner_id" field.
func OwnerIDEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldOwnerID, v))
}

// OwnerIDNEQ applies the NEQ predicate on the "owner_id" field.
func OwnerIDNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldOwnerID, v))
}

// OwnerIDIn applies the In predicate on the "owner_id" field.
func OwnerIDIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldOwnerID, vs...))
}

// OwnerIDNotIn applies the NotIn predicate on the "owner_id" field.
func OwnerIDNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldOwnerID, vs...))
}

// OwnerIDGT applies the GT predicate on the "owner_id" field.
func OwnerIDGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldOwnerID, v))
}

// OwnerIDGTE applies the GTE predicate on the "owner_id" field.
func OwnerIDGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldOwnerID, v))
}

// OwnerIDLT applies the LT predicate on the "owner_id" field.
func OwnerIDLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldOwnerID, v))
}

// OwnerIDLTE applies the LTE predicate on the "owner_id" field.
func OwnerIDLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldOwnerID, v))
}

// OwnerIDContains applies the Contains predicate on the "owner_id" field.
func OwnerIDContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldOwnerID, v))
}

// OwnerIDHasPrefix applies the HasPrefix predicate on the "owner_id" field.
func OwnerIDHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldOwnerID, v))
}

// OwnerIDHasSuffix applies the HasSuffix predicate on the "owner_id" field.
func OwnerIDHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldOwnerID, v))
}

// OwnerIDIsNil applies the IsNil predicate on the "owner_id" field.
func OwnerIDIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldOwnerID))
}

// OwnerIDNotNil applies the NotNil predicate on the "owner_id" field.
func OwnerIDNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldOwnerID))
}

// OwnerIDEqualFold applies the EqualFold predicate on the "owner_id" field.
func OwnerIDEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldOwnerID, v))
}

// OwnerIDContainsFold applies the ContainsFold predicate on the "owner_id" field.
func OwnerIDContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldOwnerID, v))
}

// OriginalStatusEQ applies the EQ predicate on the "original_status" field.
func OriginalStatusEQ(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldOriginalStatus, v))
}

// OriginalStatusNEQ applies the NEQ predicate on the "original_status" field.
func OriginalStatusNEQ(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldOriginalStatus, v))
}

// OriginalStatusIn applies the In predicate on the "original_status" field.
func OriginalStatusIn(vs ...int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldOriginalStatus, vs...))
}

// OriginalStatusNotIn applies the NotIn predicate on the "original_status" field.
func OriginalStatusNotIn(vs ...int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldOriginalStatus, vs...))
}

// OriginalStatusGT applies the GT predicate on the "original_status" field.
func OriginalStatusGT(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldOriginalStatus, v))
}

// OriginalStatusGTE applies the GTE predicate on the "original_status" field.
func OriginalStatusGTE(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldOriginalStatus, v))
}

// OriginalStatusLT applies the LT predicate on the "original_status" field.
func OriginalStatusLT(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldOriginalStatus, v))
}

// OriginalStatusLTE applies the LTE predicate on the "original_status" field.
func OriginalStatusLTE(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldOriginalStatus, v))
}

// OriginalStatusIsNil applies the IsNil predicate on the "original_status" field.
func OriginalStatusIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldOriginalStatus))
}

// OriginalStatusNotNil applies the NotNil predicate on the "original_status" field.
func OriginalStatusNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldOriginalStatus))
}

// IsOneMaterialOneCodeEQ applies the EQ predicate on the "is_one_material_one_code" field.
func IsOneMaterialOneCodeEQ(v bool) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldIsOneMaterialOneCode, v))
}

// IsOneMaterialOneCodeNEQ applies the NEQ predicate on the "is_one_material_one_code" field.
func IsOneMaterialOneCodeNEQ(v bool) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldIsOneMaterialOneCode, v))
}

// DiscardMethodEQ applies the EQ predicate on the "discard_method" field.
func DiscardMethodEQ(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldDiscardMethod, v))
}

// DiscardMethodNEQ applies the NEQ predicate on the "discard_method" field.
func DiscardMethodNEQ(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldDiscardMethod, v))
}

// DiscardMethodIn applies the In predicate on the "discard_method" field.
func DiscardMethodIn(vs ...int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldDiscardMethod, vs...))
}

// DiscardMethodNotIn applies the NotIn predicate on the "discard_method" field.
func DiscardMethodNotIn(vs ...int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldDiscardMethod, vs...))
}

// DiscardMethodGT applies the GT predicate on the "discard_method" field.
func DiscardMethodGT(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldDiscardMethod, v))
}

// DiscardMethodGTE applies the GTE predicate on the "discard_method" field.
func DiscardMethodGTE(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldDiscardMethod, v))
}

// DiscardMethodLT applies the LT predicate on the "discard_method" field.
func DiscardMethodLT(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldDiscardMethod, v))
}

// DiscardMethodLTE applies the LTE predicate on the "discard_method" field.
func DiscardMethodLTE(v int32) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldDiscardMethod, v))
}

// FeatureIsNil applies the IsNil predicate on the "feature" field.
func FeatureIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldFeature))
}

// FeatureNotNil applies the NotNil predicate on the "feature" field.
func FeatureNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldFeature))
}

// OriginalIDEQ applies the EQ predicate on the "original_id" field.
func OriginalIDEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldOriginalID, v))
}

// OriginalIDNEQ applies the NEQ predicate on the "original_id" field.
func OriginalIDNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldOriginalID, v))
}

// OriginalIDIn applies the In predicate on the "original_id" field.
func OriginalIDIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldOriginalID, vs...))
}

// OriginalIDNotIn applies the NotIn predicate on the "original_id" field.
func OriginalIDNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldOriginalID, vs...))
}

// OriginalIDGT applies the GT predicate on the "original_id" field.
func OriginalIDGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldOriginalID, v))
}

// OriginalIDGTE applies the GTE predicate on the "original_id" field.
func OriginalIDGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldOriginalID, v))
}

// OriginalIDLT applies the LT predicate on the "original_id" field.
func OriginalIDLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldOriginalID, v))
}

// OriginalIDLTE applies the LTE predicate on the "original_id" field.
func OriginalIDLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldOriginalID, v))
}

// OriginalIDContains applies the Contains predicate on the "original_id" field.
func OriginalIDContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldOriginalID, v))
}

// OriginalIDHasPrefix applies the HasPrefix predicate on the "original_id" field.
func OriginalIDHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldOriginalID, v))
}

// OriginalIDHasSuffix applies the HasSuffix predicate on the "original_id" field.
func OriginalIDHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldOriginalID, v))
}

// OriginalIDIsNil applies the IsNil predicate on the "original_id" field.
func OriginalIDIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldOriginalID))
}

// OriginalIDNotNil applies the NotNil predicate on the "original_id" field.
func OriginalIDNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldOriginalID))
}

// OriginalIDEqualFold applies the EqualFold predicate on the "original_id" field.
func OriginalIDEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldOriginalID, v))
}

// OriginalIDContainsFold applies the ContainsFold predicate on the "original_id" field.
func OriginalIDContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldOriginalID, v))
}

// OrderNoEQ applies the EQ predicate on the "order_no" field.
func OrderNoEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldOrderNo, v))
}

// OrderNoNEQ applies the NEQ predicate on the "order_no" field.
func OrderNoNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldOrderNo, v))
}

// OrderNoIn applies the In predicate on the "order_no" field.
func OrderNoIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldOrderNo, vs...))
}

// OrderNoNotIn applies the NotIn predicate on the "order_no" field.
func OrderNoNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldOrderNo, vs...))
}

// OrderNoGT applies the GT predicate on the "order_no" field.
func OrderNoGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldOrderNo, v))
}

// OrderNoGTE applies the GTE predicate on the "order_no" field.
func OrderNoGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldOrderNo, v))
}

// OrderNoLT applies the LT predicate on the "order_no" field.
func OrderNoLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldOrderNo, v))
}

// OrderNoLTE applies the LTE predicate on the "order_no" field.
func OrderNoLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldOrderNo, v))
}

// OrderNoContains applies the Contains predicate on the "order_no" field.
func OrderNoContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldOrderNo, v))
}

// OrderNoHasPrefix applies the HasPrefix predicate on the "order_no" field.
func OrderNoHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldOrderNo, v))
}

// OrderNoHasSuffix applies the HasSuffix predicate on the "order_no" field.
func OrderNoHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldOrderNo, v))
}

// OrderNoIsNil applies the IsNil predicate on the "order_no" field.
func OrderNoIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldOrderNo))
}

// OrderNoNotNil applies the NotNil predicate on the "order_no" field.
func OrderNoNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldOrderNo))
}

// OrderNoEqualFold applies the EqualFold predicate on the "order_no" field.
func OrderNoEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldOrderNo, v))
}

// OrderNoContainsFold applies the ContainsFold predicate on the "order_no" field.
func OrderNoContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldOrderNo, v))
}

// FinanceSystemNoEQ applies the EQ predicate on the "finance_system_no" field.
func FinanceSystemNoEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldFinanceSystemNo, v))
}

// FinanceSystemNoNEQ applies the NEQ predicate on the "finance_system_no" field.
func FinanceSystemNoNEQ(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldFinanceSystemNo, v))
}

// FinanceSystemNoIn applies the In predicate on the "finance_system_no" field.
func FinanceSystemNoIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldFinanceSystemNo, vs...))
}

// FinanceSystemNoNotIn applies the NotIn predicate on the "finance_system_no" field.
func FinanceSystemNoNotIn(vs ...string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldFinanceSystemNo, vs...))
}

// FinanceSystemNoGT applies the GT predicate on the "finance_system_no" field.
func FinanceSystemNoGT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldFinanceSystemNo, v))
}

// FinanceSystemNoGTE applies the GTE predicate on the "finance_system_no" field.
func FinanceSystemNoGTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldFinanceSystemNo, v))
}

// FinanceSystemNoLT applies the LT predicate on the "finance_system_no" field.
func FinanceSystemNoLT(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldFinanceSystemNo, v))
}

// FinanceSystemNoLTE applies the LTE predicate on the "finance_system_no" field.
func FinanceSystemNoLTE(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldFinanceSystemNo, v))
}

// FinanceSystemNoContains applies the Contains predicate on the "finance_system_no" field.
func FinanceSystemNoContains(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContains(FieldFinanceSystemNo, v))
}

// FinanceSystemNoHasPrefix applies the HasPrefix predicate on the "finance_system_no" field.
func FinanceSystemNoHasPrefix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasPrefix(FieldFinanceSystemNo, v))
}

// FinanceSystemNoHasSuffix applies the HasSuffix predicate on the "finance_system_no" field.
func FinanceSystemNoHasSuffix(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldHasSuffix(FieldFinanceSystemNo, v))
}

// FinanceSystemNoIsNil applies the IsNil predicate on the "finance_system_no" field.
func FinanceSystemNoIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldFinanceSystemNo))
}

// FinanceSystemNoNotNil applies the NotNil predicate on the "finance_system_no" field.
func FinanceSystemNoNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldFinanceSystemNo))
}

// FinanceSystemNoEqualFold applies the EqualFold predicate on the "finance_system_no" field.
func FinanceSystemNoEqualFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEqualFold(FieldFinanceSystemNo, v))
}

// FinanceSystemNoContainsFold applies the ContainsFold predicate on the "finance_system_no" field.
func FinanceSystemNoContainsFold(v string) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldContainsFold(FieldFinanceSystemNo, v))
}

// UseStartTimeEQ applies the EQ predicate on the "use_start_time" field.
func UseStartTimeEQ(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldEQ(FieldUseStartTime, v))
}

// UseStartTimeNEQ applies the NEQ predicate on the "use_start_time" field.
func UseStartTimeNEQ(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNEQ(FieldUseStartTime, v))
}

// UseStartTimeIn applies the In predicate on the "use_start_time" field.
func UseStartTimeIn(vs ...time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIn(FieldUseStartTime, vs...))
}

// UseStartTimeNotIn applies the NotIn predicate on the "use_start_time" field.
func UseStartTimeNotIn(vs ...time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotIn(FieldUseStartTime, vs...))
}

// UseStartTimeGT applies the GT predicate on the "use_start_time" field.
func UseStartTimeGT(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGT(FieldUseStartTime, v))
}

// UseStartTimeGTE applies the GTE predicate on the "use_start_time" field.
func UseStartTimeGTE(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldGTE(FieldUseStartTime, v))
}

// UseStartTimeLT applies the LT predicate on the "use_start_time" field.
func UseStartTimeLT(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLT(FieldUseStartTime, v))
}

// UseStartTimeLTE applies the LTE predicate on the "use_start_time" field.
func UseStartTimeLTE(v time.Time) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldLTE(FieldUseStartTime, v))
}

// UseStartTimeIsNil applies the IsNil predicate on the "use_start_time" field.
func UseStartTimeIsNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldIsNull(FieldUseStartTime))
}

// UseStartTimeNotNil applies the NotNil predicate on the "use_start_time" field.
func UseStartTimeNotNil() predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.FieldNotNull(FieldUseStartTime))
}

// HasRepository applies the HasEdge predicate on the "repository" edge.
func HasRepository() predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, RepositoryTable, RepositoryColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasRepositoryWith applies the HasEdge predicate on the "repository" edge with a given conditions (other predicates).
func HasRepositoryWith(preds ...predicate.WmsRepository) predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := newRepositoryStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasCreatedUser applies the HasEdge predicate on the "createdUser" edge.
func HasCreatedUser() predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, CreatedUserTable, CreatedUserColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasCreatedUserWith applies the HasEdge predicate on the "createdUser" edge with a given conditions (other predicates).
func HasCreatedUserWith(preds ...predicate.SysUser) predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := newCreatedUserStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasRepositoryArea applies the HasEdge predicate on the "repository_area" edge.
func HasRepositoryArea() predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, RepositoryAreaTable, RepositoryAreaColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasRepositoryAreaWith applies the HasEdge predicate on the "repository_area" edge with a given conditions (other predicates).
func HasRepositoryAreaWith(preds ...predicate.WmsRepositoryArea) predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := newRepositoryAreaStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasRepositoryPosition applies the HasEdge predicate on the "repository_position" edge.
func HasRepositoryPosition() predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, RepositoryPositionTable, RepositoryPositionColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasRepositoryPositionWith applies the HasEdge predicate on the "repository_position" edge with a given conditions (other predicates).
func HasRepositoryPositionWith(preds ...predicate.WmsRepositoryPosition) predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := newRepositoryPositionStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasEquipment applies the HasEdge predicate on the "equipment" edge.
func HasEquipment() predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, EquipmentTable, EquipmentColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasEquipmentWith applies the HasEdge predicate on the "equipment" edge with a given conditions (other predicates).
func HasEquipmentWith(preds ...predicate.WmsEquipment) predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := newEquipmentStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasEquipmentType applies the HasEdge predicate on the "equipment_type" edge.
func HasEquipmentType() predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, EquipmentTypeTable, EquipmentTypeColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasEquipmentTypeWith applies the HasEdge predicate on the "equipment_type" edge with a given conditions (other predicates).
func HasEquipmentTypeWith(preds ...predicate.WmsEquipmentType) predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := newEquipmentTypeStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasFireStation applies the HasEdge predicate on the "fire_station" edge.
func HasFireStation() predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, FireStationTable, FireStationColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasFireStationWith applies the HasEdge predicate on the "fire_station" edge with a given conditions (other predicates).
func HasFireStationWith(preds ...predicate.WmsFireStation) predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := newFireStationStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasMeasureUnit applies the HasEdge predicate on the "measure_unit" edge.
func HasMeasureUnit() predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, MeasureUnitTable, MeasureUnitColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasMeasureUnitWith applies the HasEdge predicate on the "measure_unit" edge with a given conditions (other predicates).
func HasMeasureUnitWith(preds ...predicate.WmsMeasureUnit) predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := newMeasureUnitStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasApprovalDetail applies the HasEdge predicate on the "approval_detail" edge.
func HasApprovalDetail() predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, ApprovalDetailTable, ApprovalDetailColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasApprovalDetailWith applies the HasEdge predicate on the "approval_detail" edge with a given conditions (other predicates).
func HasApprovalDetailWith(preds ...predicate.WmsApprovalTaskDetail) predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := newApprovalDetailStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasAuditPlanDetails applies the HasEdge predicate on the "audit_plan_details" edge.
func HasAuditPlanDetails() predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, AuditPlanDetailsTable, AuditPlanDetailsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasAuditPlanDetailsWith applies the HasEdge predicate on the "audit_plan_details" edge with a given conditions (other predicates).
func HasAuditPlanDetailsWith(preds ...predicate.WmsAuditPlanDetail) predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := newAuditPlanDetailsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasOwner applies the HasEdge predicate on the "owner" edge.
func HasOwner() predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, OwnerTable, OwnerColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasOwnerWith applies the HasEdge predicate on the "owner" edge with a given conditions (other predicates).
func HasOwnerWith(preds ...predicate.SysUser) predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := newOwnerStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasWmsCar applies the HasEdge predicate on the "wms_car" edge.
func HasWmsCar() predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2O, false, WmsCarTable, WmsCarColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasWmsCarWith applies the HasEdge predicate on the "wms_car" edge with a given conditions (other predicates).
func HasWmsCarWith(preds ...predicate.WmsCar) predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := newWmsCarStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasCarOwner applies the HasEdge predicate on the "car_owner" edge.
func HasCarOwner() predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, CarOwnerTable, CarOwnerColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasCarOwnerWith applies the HasEdge predicate on the "car_owner" edge with a given conditions (other predicates).
func HasCarOwnerWith(preds ...predicate.WmsCar) predicate.WmsMaterial {
	return predicate.WmsMaterial(func(s *sql.Selector) {
		step := newCarOwnerStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.WmsMaterial) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.WmsMaterial) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.WmsMaterial) predicate.WmsMaterial {
	return predicate.WmsMaterial(sql.NotPredicates(p))
}
