// Code generated by ent, DO NOT EDIT.

package wmsmaterial

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the wmsmaterial type in the database.
	Label = "wms_material"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldCreatedBy holds the string denoting the created_by field in the database.
	FieldCreatedBy = "created_by"
	// FieldUpdatedBy holds the string denoting the updated_by field in the database.
	FieldUpdatedBy = "updated_by"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldRemark holds the string denoting the remark field in the database.
	FieldRemark = "remark"
	// FieldFireStationID holds the string denoting the fire_station_id field in the database.
	FieldFireStationID = "fire_station_id"
	// FieldMaterialType holds the string denoting the material_type field in the database.
	FieldMaterialType = "material_type"
	// FieldEquipmentTypeID holds the string denoting the equipment_type_id field in the database.
	FieldEquipmentTypeID = "equipment_type_id"
	// FieldEquipmentID holds the string denoting the equipment_id field in the database.
	FieldEquipmentID = "equipment_id"
	// FieldKey holds the string denoting the key field in the database.
	FieldKey = "key"
	// FieldCode holds the string denoting the code field in the database.
	FieldCode = "code"
	// FieldCodeType holds the string denoting the code_type field in the database.
	FieldCodeType = "code_type"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldModelNo holds the string denoting the model_no field in the database.
	FieldModelNo = "model_no"
	// FieldProviderID holds the string denoting the provider_id field in the database.
	FieldProviderID = "provider_id"
	// FieldNum holds the string denoting the num field in the database.
	FieldNum = "num"
	// FieldPrice holds the string denoting the price field in the database.
	FieldPrice = "price"
	// FieldRepositoryID holds the string denoting the repository_id field in the database.
	FieldRepositoryID = "repository_id"
	// FieldRepositoryAreaID holds the string denoting the repository_area_id field in the database.
	FieldRepositoryAreaID = "repository_area_id"
	// FieldRepositoryPositionID holds the string denoting the repository_position_id field in the database.
	FieldRepositoryPositionID = "repository_position_id"
	// FieldMeasureUnitID holds the string denoting the measure_unit_id field in the database.
	FieldMeasureUnitID = "measure_unit_id"
	// FieldExpireTime holds the string denoting the expire_time field in the database.
	FieldExpireTime = "expire_time"
	// FieldEquipmentStatus holds the string denoting the equipment_status field in the database.
	FieldEquipmentStatus = "equipment_status"
	// FieldIsApproving holds the string denoting the is_approving field in the database.
	FieldIsApproving = "is_approving"
	// FieldOwnerID holds the string denoting the owner_id field in the database.
	FieldOwnerID = "owner_id"
	// FieldOriginalStatus holds the string denoting the original_status field in the database.
	FieldOriginalStatus = "original_status"
	// FieldIsOneMaterialOneCode holds the string denoting the is_one_material_one_code field in the database.
	FieldIsOneMaterialOneCode = "is_one_material_one_code"
	// FieldDiscardMethod holds the string denoting the discard_method field in the database.
	FieldDiscardMethod = "discard_method"
	// FieldFeature holds the string denoting the feature field in the database.
	FieldFeature = "feature"
	// FieldOriginalID holds the string denoting the original_id field in the database.
	FieldOriginalID = "original_id"
	// FieldOrderNo holds the string denoting the order_no field in the database.
	FieldOrderNo = "order_no"
	// FieldFinanceSystemNo holds the string denoting the finance_system_no field in the database.
	FieldFinanceSystemNo = "finance_system_no"
	// FieldUseStartTime holds the string denoting the use_start_time field in the database.
	FieldUseStartTime = "use_start_time"
	// EdgeRepository holds the string denoting the repository edge name in mutations.
	EdgeRepository = "repository"
	// EdgeCreatedUser holds the string denoting the createduser edge name in mutations.
	EdgeCreatedUser = "createdUser"
	// EdgeRepositoryArea holds the string denoting the repository_area edge name in mutations.
	EdgeRepositoryArea = "repository_area"
	// EdgeRepositoryPosition holds the string denoting the repository_position edge name in mutations.
	EdgeRepositoryPosition = "repository_position"
	// EdgeEquipment holds the string denoting the equipment edge name in mutations.
	EdgeEquipment = "equipment"
	// EdgeEquipmentType holds the string denoting the equipment_type edge name in mutations.
	EdgeEquipmentType = "equipment_type"
	// EdgeFireStation holds the string denoting the fire_station edge name in mutations.
	EdgeFireStation = "fire_station"
	// EdgeMeasureUnit holds the string denoting the measure_unit edge name in mutations.
	EdgeMeasureUnit = "measure_unit"
	// EdgeApprovalDetail holds the string denoting the approval_detail edge name in mutations.
	EdgeApprovalDetail = "approval_detail"
	// EdgeAuditPlanDetails holds the string denoting the audit_plan_details edge name in mutations.
	EdgeAuditPlanDetails = "audit_plan_details"
	// EdgeOwner holds the string denoting the owner edge name in mutations.
	EdgeOwner = "owner"
	// EdgeWmsCar holds the string denoting the wms_car edge name in mutations.
	EdgeWmsCar = "wms_car"
	// EdgeCarOwner holds the string denoting the car_owner edge name in mutations.
	EdgeCarOwner = "car_owner"
	// Table holds the table name of the wmsmaterial in the database.
	Table = "wms_materials"
	// RepositoryTable is the table that holds the repository relation/edge.
	RepositoryTable = "wms_materials"
	// RepositoryInverseTable is the table name for the WmsRepository entity.
	// It exists in this package in order to avoid circular dependency with the "wmsrepository" package.
	RepositoryInverseTable = "wms_repositories"
	// RepositoryColumn is the table column denoting the repository relation/edge.
	RepositoryColumn = "repository_id"
	// CreatedUserTable is the table that holds the createdUser relation/edge.
	CreatedUserTable = "wms_materials"
	// CreatedUserInverseTable is the table name for the SysUser entity.
	// It exists in this package in order to avoid circular dependency with the "sysuser" package.
	CreatedUserInverseTable = "sys_users"
	// CreatedUserColumn is the table column denoting the createdUser relation/edge.
	CreatedUserColumn = "created_by"
	// RepositoryAreaTable is the table that holds the repository_area relation/edge.
	RepositoryAreaTable = "wms_materials"
	// RepositoryAreaInverseTable is the table name for the WmsRepositoryArea entity.
	// It exists in this package in order to avoid circular dependency with the "wmsrepositoryarea" package.
	RepositoryAreaInverseTable = "wms_repository_areas"
	// RepositoryAreaColumn is the table column denoting the repository_area relation/edge.
	RepositoryAreaColumn = "repository_area_id"
	// RepositoryPositionTable is the table that holds the repository_position relation/edge.
	RepositoryPositionTable = "wms_materials"
	// RepositoryPositionInverseTable is the table name for the WmsRepositoryPosition entity.
	// It exists in this package in order to avoid circular dependency with the "wmsrepositoryposition" package.
	RepositoryPositionInverseTable = "wms_repository_positions"
	// RepositoryPositionColumn is the table column denoting the repository_position relation/edge.
	RepositoryPositionColumn = "repository_position_id"
	// EquipmentTable is the table that holds the equipment relation/edge.
	EquipmentTable = "wms_materials"
	// EquipmentInverseTable is the table name for the WmsEquipment entity.
	// It exists in this package in order to avoid circular dependency with the "wmsequipment" package.
	EquipmentInverseTable = "wms_equipments"
	// EquipmentColumn is the table column denoting the equipment relation/edge.
	EquipmentColumn = "equipment_id"
	// EquipmentTypeTable is the table that holds the equipment_type relation/edge.
	EquipmentTypeTable = "wms_materials"
	// EquipmentTypeInverseTable is the table name for the WmsEquipmentType entity.
	// It exists in this package in order to avoid circular dependency with the "wmsequipmenttype" package.
	EquipmentTypeInverseTable = "wms_equipment_types"
	// EquipmentTypeColumn is the table column denoting the equipment_type relation/edge.
	EquipmentTypeColumn = "equipment_type_id"
	// FireStationTable is the table that holds the fire_station relation/edge.
	FireStationTable = "wms_materials"
	// FireStationInverseTable is the table name for the WmsFireStation entity.
	// It exists in this package in order to avoid circular dependency with the "wmsfirestation" package.
	FireStationInverseTable = "wms_fire_stations"
	// FireStationColumn is the table column denoting the fire_station relation/edge.
	FireStationColumn = "fire_station_id"
	// MeasureUnitTable is the table that holds the measure_unit relation/edge.
	MeasureUnitTable = "wms_materials"
	// MeasureUnitInverseTable is the table name for the WmsMeasureUnit entity.
	// It exists in this package in order to avoid circular dependency with the "wmsmeasureunit" package.
	MeasureUnitInverseTable = "wms_measure_units"
	// MeasureUnitColumn is the table column denoting the measure_unit relation/edge.
	MeasureUnitColumn = "measure_unit_id"
	// ApprovalDetailTable is the table that holds the approval_detail relation/edge.
	ApprovalDetailTable = "wms_approval_task_details"
	// ApprovalDetailInverseTable is the table name for the WmsApprovalTaskDetail entity.
	// It exists in this package in order to avoid circular dependency with the "wmsapprovaltaskdetail" package.
	ApprovalDetailInverseTable = "wms_approval_task_details"
	// ApprovalDetailColumn is the table column denoting the approval_detail relation/edge.
	ApprovalDetailColumn = "material_id"
	// AuditPlanDetailsTable is the table that holds the audit_plan_details relation/edge.
	AuditPlanDetailsTable = "wms_audit_plan_details"
	// AuditPlanDetailsInverseTable is the table name for the WmsAuditPlanDetail entity.
	// It exists in this package in order to avoid circular dependency with the "wmsauditplandetail" package.
	AuditPlanDetailsInverseTable = "wms_audit_plan_details"
	// AuditPlanDetailsColumn is the table column denoting the audit_plan_details relation/edge.
	AuditPlanDetailsColumn = "material_id"
	// OwnerTable is the table that holds the owner relation/edge.
	OwnerTable = "wms_materials"
	// OwnerInverseTable is the table name for the SysUser entity.
	// It exists in this package in order to avoid circular dependency with the "sysuser" package.
	OwnerInverseTable = "sys_users"
	// OwnerColumn is the table column denoting the owner relation/edge.
	OwnerColumn = "owner_id"
	// WmsCarTable is the table that holds the wms_car relation/edge.
	WmsCarTable = "wms_cars"
	// WmsCarInverseTable is the table name for the WmsCar entity.
	// It exists in this package in order to avoid circular dependency with the "wmscar" package.
	WmsCarInverseTable = "wms_cars"
	// WmsCarColumn is the table column denoting the wms_car relation/edge.
	WmsCarColumn = "wms_material_wms_car"
	// CarOwnerTable is the table that holds the car_owner relation/edge.
	CarOwnerTable = "wms_materials"
	// CarOwnerInverseTable is the table name for the WmsCar entity.
	// It exists in this package in order to avoid circular dependency with the "wmscar" package.
	CarOwnerInverseTable = "wms_cars"
	// CarOwnerColumn is the table column denoting the car_owner relation/edge.
	CarOwnerColumn = "wms_car_contained_materials"
)

// Columns holds all SQL columns for wmsmaterial fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldCreatedBy,
	FieldUpdatedBy,
	FieldStatus,
	FieldRemark,
	FieldFireStationID,
	FieldMaterialType,
	FieldEquipmentTypeID,
	FieldEquipmentID,
	FieldKey,
	FieldCode,
	FieldCodeType,
	FieldName,
	FieldModelNo,
	FieldProviderID,
	FieldNum,
	FieldPrice,
	FieldRepositoryID,
	FieldRepositoryAreaID,
	FieldRepositoryPositionID,
	FieldMeasureUnitID,
	FieldExpireTime,
	FieldEquipmentStatus,
	FieldIsApproving,
	FieldOwnerID,
	FieldOriginalStatus,
	FieldIsOneMaterialOneCode,
	FieldDiscardMethod,
	FieldFeature,
	FieldOriginalID,
	FieldOrderNo,
	FieldFinanceSystemNo,
	FieldUseStartTime,
}

// ForeignKeys holds the SQL foreign-keys that are owned by the "wms_materials"
// table and are not defined as standalone fields in the schema.
var ForeignKeys = []string{
	"wms_car_contained_materials",
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	for i := range ForeignKeys {
		if column == ForeignKeys[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus int32
	// DefaultNum holds the default value on creation for the "num" field.
	DefaultNum uint32
	// DefaultPrice holds the default value on creation for the "price" field.
	DefaultPrice uint64
	// DefaultEquipmentStatus holds the default value on creation for the "equipment_status" field.
	DefaultEquipmentStatus int32
	// DefaultIsApproving holds the default value on creation for the "is_approving" field.
	DefaultIsApproving bool
	// DefaultIsOneMaterialOneCode holds the default value on creation for the "is_one_material_one_code" field.
	DefaultIsOneMaterialOneCode bool
	// DefaultDiscardMethod holds the default value on creation for the "discard_method" field.
	DefaultDiscardMethod int32
	// DefaultFeature holds the default value on creation for the "feature" field.
	DefaultFeature map[string]interface{}
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() string
)

// OrderOption defines the ordering options for the WmsMaterial queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByCreatedBy orders the results by the created_by field.
func ByCreatedBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedBy, opts...).ToFunc()
}

// ByUpdatedBy orders the results by the updated_by field.
func ByUpdatedBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedBy, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByRemark orders the results by the remark field.
func ByRemark(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRemark, opts...).ToFunc()
}

// ByFireStationID orders the results by the fire_station_id field.
func ByFireStationID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFireStationID, opts...).ToFunc()
}

// ByMaterialType orders the results by the material_type field.
func ByMaterialType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMaterialType, opts...).ToFunc()
}

// ByEquipmentTypeID orders the results by the equipment_type_id field.
func ByEquipmentTypeID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEquipmentTypeID, opts...).ToFunc()
}

// ByEquipmentID orders the results by the equipment_id field.
func ByEquipmentID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEquipmentID, opts...).ToFunc()
}

// ByKey orders the results by the key field.
func ByKey(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldKey, opts...).ToFunc()
}

// ByCode orders the results by the code field.
func ByCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCode, opts...).ToFunc()
}

// ByCodeType orders the results by the code_type field.
func ByCodeType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCodeType, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByModelNo orders the results by the model_no field.
func ByModelNo(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldModelNo, opts...).ToFunc()
}

// ByProviderID orders the results by the provider_id field.
func ByProviderID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProviderID, opts...).ToFunc()
}

// ByNum orders the results by the num field.
func ByNum(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldNum, opts...).ToFunc()
}

// ByPrice orders the results by the price field.
func ByPrice(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPrice, opts...).ToFunc()
}

// ByRepositoryID orders the results by the repository_id field.
func ByRepositoryID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRepositoryID, opts...).ToFunc()
}

// ByRepositoryAreaID orders the results by the repository_area_id field.
func ByRepositoryAreaID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRepositoryAreaID, opts...).ToFunc()
}

// ByRepositoryPositionID orders the results by the repository_position_id field.
func ByRepositoryPositionID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRepositoryPositionID, opts...).ToFunc()
}

// ByMeasureUnitID orders the results by the measure_unit_id field.
func ByMeasureUnitID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMeasureUnitID, opts...).ToFunc()
}

// ByExpireTime orders the results by the expire_time field.
func ByExpireTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldExpireTime, opts...).ToFunc()
}

// ByEquipmentStatus orders the results by the equipment_status field.
func ByEquipmentStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEquipmentStatus, opts...).ToFunc()
}

// ByIsApproving orders the results by the is_approving field.
func ByIsApproving(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsApproving, opts...).ToFunc()
}

// ByOwnerID orders the results by the owner_id field.
func ByOwnerID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOwnerID, opts...).ToFunc()
}

// ByOriginalStatus orders the results by the original_status field.
func ByOriginalStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOriginalStatus, opts...).ToFunc()
}

// ByIsOneMaterialOneCode orders the results by the is_one_material_one_code field.
func ByIsOneMaterialOneCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsOneMaterialOneCode, opts...).ToFunc()
}

// ByDiscardMethod orders the results by the discard_method field.
func ByDiscardMethod(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDiscardMethod, opts...).ToFunc()
}

// ByOriginalID orders the results by the original_id field.
func ByOriginalID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOriginalID, opts...).ToFunc()
}

// ByOrderNo orders the results by the order_no field.
func ByOrderNo(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOrderNo, opts...).ToFunc()
}

// ByFinanceSystemNo orders the results by the finance_system_no field.
func ByFinanceSystemNo(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFinanceSystemNo, opts...).ToFunc()
}

// ByUseStartTime orders the results by the use_start_time field.
func ByUseStartTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUseStartTime, opts...).ToFunc()
}

// ByRepositoryField orders the results by repository field.
func ByRepositoryField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newRepositoryStep(), sql.OrderByField(field, opts...))
	}
}

// ByCreatedUserField orders the results by createdUser field.
func ByCreatedUserField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newCreatedUserStep(), sql.OrderByField(field, opts...))
	}
}

// ByRepositoryAreaField orders the results by repository_area field.
func ByRepositoryAreaField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newRepositoryAreaStep(), sql.OrderByField(field, opts...))
	}
}

// ByRepositoryPositionField orders the results by repository_position field.
func ByRepositoryPositionField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newRepositoryPositionStep(), sql.OrderByField(field, opts...))
	}
}

// ByEquipmentField orders the results by equipment field.
func ByEquipmentField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newEquipmentStep(), sql.OrderByField(field, opts...))
	}
}

// ByEquipmentTypeField orders the results by equipment_type field.
func ByEquipmentTypeField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newEquipmentTypeStep(), sql.OrderByField(field, opts...))
	}
}

// ByFireStationField orders the results by fire_station field.
func ByFireStationField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newFireStationStep(), sql.OrderByField(field, opts...))
	}
}

// ByMeasureUnitField orders the results by measure_unit field.
func ByMeasureUnitField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newMeasureUnitStep(), sql.OrderByField(field, opts...))
	}
}

// ByApprovalDetailCount orders the results by approval_detail count.
func ByApprovalDetailCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newApprovalDetailStep(), opts...)
	}
}

// ByApprovalDetail orders the results by approval_detail terms.
func ByApprovalDetail(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newApprovalDetailStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByAuditPlanDetailsCount orders the results by audit_plan_details count.
func ByAuditPlanDetailsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newAuditPlanDetailsStep(), opts...)
	}
}

// ByAuditPlanDetails orders the results by audit_plan_details terms.
func ByAuditPlanDetails(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newAuditPlanDetailsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByOwnerField orders the results by owner field.
func ByOwnerField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newOwnerStep(), sql.OrderByField(field, opts...))
	}
}

// ByWmsCarField orders the results by wms_car field.
func ByWmsCarField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newWmsCarStep(), sql.OrderByField(field, opts...))
	}
}

// ByCarOwnerField orders the results by car_owner field.
func ByCarOwnerField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newCarOwnerStep(), sql.OrderByField(field, opts...))
	}
}
func newRepositoryStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(RepositoryInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, RepositoryTable, RepositoryColumn),
	)
}
func newCreatedUserStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(CreatedUserInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, CreatedUserTable, CreatedUserColumn),
	)
}
func newRepositoryAreaStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(RepositoryAreaInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, RepositoryAreaTable, RepositoryAreaColumn),
	)
}
func newRepositoryPositionStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(RepositoryPositionInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, RepositoryPositionTable, RepositoryPositionColumn),
	)
}
func newEquipmentStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(EquipmentInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, EquipmentTable, EquipmentColumn),
	)
}
func newEquipmentTypeStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(EquipmentTypeInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, EquipmentTypeTable, EquipmentTypeColumn),
	)
}
func newFireStationStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(FireStationInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, FireStationTable, FireStationColumn),
	)
}
func newMeasureUnitStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(MeasureUnitInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, MeasureUnitTable, MeasureUnitColumn),
	)
}
func newApprovalDetailStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(ApprovalDetailInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, ApprovalDetailTable, ApprovalDetailColumn),
	)
}
func newAuditPlanDetailsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(AuditPlanDetailsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, AuditPlanDetailsTable, AuditPlanDetailsColumn),
	)
}
func newOwnerStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(OwnerInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, OwnerTable, OwnerColumn),
	)
}
func newWmsCarStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(WmsCarInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2O, false, WmsCarTable, WmsCarColumn),
	)
}
func newCarOwnerStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(CarOwnerInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, CarOwnerTable, CarOwnerColumn),
	)
}
