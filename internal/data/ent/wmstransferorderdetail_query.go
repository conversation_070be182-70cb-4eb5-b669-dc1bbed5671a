// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"kratos-mono-demo/internal/data/ent/predicate"
	"kratos-mono-demo/internal/data/ent/wmstransferorderdetail"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WmsTransferOrderDetailQuery is the builder for querying WmsTransferOrderDetail entities.
type WmsTransferOrderDetailQuery struct {
	config
	ctx        *QueryContext
	order      []wmstransferorderdetail.OrderOption
	inters     []Interceptor
	predicates []predicate.WmsTransferOrderDetail
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the WmsTransferOrderDetailQuery builder.
func (wtodq *WmsTransferOrderDetailQuery) Where(ps ...predicate.WmsTransferOrderDetail) *WmsTransferOrderDetailQuery {
	wtodq.predicates = append(wtodq.predicates, ps...)
	return wtodq
}

// Limit the number of records to be returned by this query.
func (wtodq *WmsTransferOrderDetailQuery) Limit(limit int) *WmsTransferOrderDetailQuery {
	wtodq.ctx.Limit = &limit
	return wtodq
}

// Offset to start from.
func (wtodq *WmsTransferOrderDetailQuery) Offset(offset int) *WmsTransferOrderDetailQuery {
	wtodq.ctx.Offset = &offset
	return wtodq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (wtodq *WmsTransferOrderDetailQuery) Unique(unique bool) *WmsTransferOrderDetailQuery {
	wtodq.ctx.Unique = &unique
	return wtodq
}

// Order specifies how the records should be ordered.
func (wtodq *WmsTransferOrderDetailQuery) Order(o ...wmstransferorderdetail.OrderOption) *WmsTransferOrderDetailQuery {
	wtodq.order = append(wtodq.order, o...)
	return wtodq
}

// First returns the first WmsTransferOrderDetail entity from the query.
// Returns a *NotFoundError when no WmsTransferOrderDetail was found.
func (wtodq *WmsTransferOrderDetailQuery) First(ctx context.Context) (*WmsTransferOrderDetail, error) {
	nodes, err := wtodq.Limit(1).All(setContextOp(ctx, wtodq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{wmstransferorderdetail.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (wtodq *WmsTransferOrderDetailQuery) FirstX(ctx context.Context) *WmsTransferOrderDetail {
	node, err := wtodq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first WmsTransferOrderDetail ID from the query.
// Returns a *NotFoundError when no WmsTransferOrderDetail ID was found.
func (wtodq *WmsTransferOrderDetailQuery) FirstID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = wtodq.Limit(1).IDs(setContextOp(ctx, wtodq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{wmstransferorderdetail.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (wtodq *WmsTransferOrderDetailQuery) FirstIDX(ctx context.Context) string {
	id, err := wtodq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single WmsTransferOrderDetail entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one WmsTransferOrderDetail entity is found.
// Returns a *NotFoundError when no WmsTransferOrderDetail entities are found.
func (wtodq *WmsTransferOrderDetailQuery) Only(ctx context.Context) (*WmsTransferOrderDetail, error) {
	nodes, err := wtodq.Limit(2).All(setContextOp(ctx, wtodq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{wmstransferorderdetail.Label}
	default:
		return nil, &NotSingularError{wmstransferorderdetail.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (wtodq *WmsTransferOrderDetailQuery) OnlyX(ctx context.Context) *WmsTransferOrderDetail {
	node, err := wtodq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only WmsTransferOrderDetail ID in the query.
// Returns a *NotSingularError when more than one WmsTransferOrderDetail ID is found.
// Returns a *NotFoundError when no entities are found.
func (wtodq *WmsTransferOrderDetailQuery) OnlyID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = wtodq.Limit(2).IDs(setContextOp(ctx, wtodq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{wmstransferorderdetail.Label}
	default:
		err = &NotSingularError{wmstransferorderdetail.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (wtodq *WmsTransferOrderDetailQuery) OnlyIDX(ctx context.Context) string {
	id, err := wtodq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of WmsTransferOrderDetails.
func (wtodq *WmsTransferOrderDetailQuery) All(ctx context.Context) ([]*WmsTransferOrderDetail, error) {
	ctx = setContextOp(ctx, wtodq.ctx, ent.OpQueryAll)
	if err := wtodq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*WmsTransferOrderDetail, *WmsTransferOrderDetailQuery]()
	return withInterceptors[[]*WmsTransferOrderDetail](ctx, wtodq, qr, wtodq.inters)
}

// AllX is like All, but panics if an error occurs.
func (wtodq *WmsTransferOrderDetailQuery) AllX(ctx context.Context) []*WmsTransferOrderDetail {
	nodes, err := wtodq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of WmsTransferOrderDetail IDs.
func (wtodq *WmsTransferOrderDetailQuery) IDs(ctx context.Context) (ids []string, err error) {
	if wtodq.ctx.Unique == nil && wtodq.path != nil {
		wtodq.Unique(true)
	}
	ctx = setContextOp(ctx, wtodq.ctx, ent.OpQueryIDs)
	if err = wtodq.Select(wmstransferorderdetail.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (wtodq *WmsTransferOrderDetailQuery) IDsX(ctx context.Context) []string {
	ids, err := wtodq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (wtodq *WmsTransferOrderDetailQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, wtodq.ctx, ent.OpQueryCount)
	if err := wtodq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, wtodq, querierCount[*WmsTransferOrderDetailQuery](), wtodq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (wtodq *WmsTransferOrderDetailQuery) CountX(ctx context.Context) int {
	count, err := wtodq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (wtodq *WmsTransferOrderDetailQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, wtodq.ctx, ent.OpQueryExist)
	switch _, err := wtodq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (wtodq *WmsTransferOrderDetailQuery) ExistX(ctx context.Context) bool {
	exist, err := wtodq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the WmsTransferOrderDetailQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (wtodq *WmsTransferOrderDetailQuery) Clone() *WmsTransferOrderDetailQuery {
	if wtodq == nil {
		return nil
	}
	return &WmsTransferOrderDetailQuery{
		config:     wtodq.config,
		ctx:        wtodq.ctx.Clone(),
		order:      append([]wmstransferorderdetail.OrderOption{}, wtodq.order...),
		inters:     append([]Interceptor{}, wtodq.inters...),
		predicates: append([]predicate.WmsTransferOrderDetail{}, wtodq.predicates...),
		// clone intermediate query.
		sql:       wtodq.sql.Clone(),
		path:      wtodq.path,
		modifiers: append([]func(*sql.Selector){}, wtodq.modifiers...),
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.WmsTransferOrderDetail.Query().
//		GroupBy(wmstransferorderdetail.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (wtodq *WmsTransferOrderDetailQuery) GroupBy(field string, fields ...string) *WmsTransferOrderDetailGroupBy {
	wtodq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &WmsTransferOrderDetailGroupBy{build: wtodq}
	grbuild.flds = &wtodq.ctx.Fields
	grbuild.label = wmstransferorderdetail.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.WmsTransferOrderDetail.Query().
//		Select(wmstransferorderdetail.FieldCreatedAt).
//		Scan(ctx, &v)
func (wtodq *WmsTransferOrderDetailQuery) Select(fields ...string) *WmsTransferOrderDetailSelect {
	wtodq.ctx.Fields = append(wtodq.ctx.Fields, fields...)
	sbuild := &WmsTransferOrderDetailSelect{WmsTransferOrderDetailQuery: wtodq}
	sbuild.label = wmstransferorderdetail.Label
	sbuild.flds, sbuild.scan = &wtodq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a WmsTransferOrderDetailSelect configured with the given aggregations.
func (wtodq *WmsTransferOrderDetailQuery) Aggregate(fns ...AggregateFunc) *WmsTransferOrderDetailSelect {
	return wtodq.Select().Aggregate(fns...)
}

func (wtodq *WmsTransferOrderDetailQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range wtodq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, wtodq); err != nil {
				return err
			}
		}
	}
	for _, f := range wtodq.ctx.Fields {
		if !wmstransferorderdetail.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if wtodq.path != nil {
		prev, err := wtodq.path(ctx)
		if err != nil {
			return err
		}
		wtodq.sql = prev
	}
	return nil
}

func (wtodq *WmsTransferOrderDetailQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*WmsTransferOrderDetail, error) {
	var (
		nodes = []*WmsTransferOrderDetail{}
		_spec = wtodq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*WmsTransferOrderDetail).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &WmsTransferOrderDetail{config: wtodq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(wtodq.modifiers) > 0 {
		_spec.Modifiers = wtodq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, wtodq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (wtodq *WmsTransferOrderDetailQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := wtodq.querySpec()
	if len(wtodq.modifiers) > 0 {
		_spec.Modifiers = wtodq.modifiers
	}
	_spec.Node.Columns = wtodq.ctx.Fields
	if len(wtodq.ctx.Fields) > 0 {
		_spec.Unique = wtodq.ctx.Unique != nil && *wtodq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, wtodq.driver, _spec)
}

func (wtodq *WmsTransferOrderDetailQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(wmstransferorderdetail.Table, wmstransferorderdetail.Columns, sqlgraph.NewFieldSpec(wmstransferorderdetail.FieldID, field.TypeString))
	_spec.From = wtodq.sql
	if unique := wtodq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if wtodq.path != nil {
		_spec.Unique = true
	}
	if fields := wtodq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, wmstransferorderdetail.FieldID)
		for i := range fields {
			if fields[i] != wmstransferorderdetail.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := wtodq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := wtodq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := wtodq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := wtodq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (wtodq *WmsTransferOrderDetailQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(wtodq.driver.Dialect())
	t1 := builder.Table(wmstransferorderdetail.Table)
	columns := wtodq.ctx.Fields
	if len(columns) == 0 {
		columns = wmstransferorderdetail.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if wtodq.sql != nil {
		selector = wtodq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if wtodq.ctx.Unique != nil && *wtodq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range wtodq.modifiers {
		m(selector)
	}
	for _, p := range wtodq.predicates {
		p(selector)
	}
	for _, p := range wtodq.order {
		p(selector)
	}
	if offset := wtodq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := wtodq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// Modify adds a query modifier for attaching custom logic to queries.
func (wtodq *WmsTransferOrderDetailQuery) Modify(modifiers ...func(s *sql.Selector)) *WmsTransferOrderDetailSelect {
	wtodq.modifiers = append(wtodq.modifiers, modifiers...)
	return wtodq.Select()
}

// WmsTransferOrderDetailGroupBy is the group-by builder for WmsTransferOrderDetail entities.
type WmsTransferOrderDetailGroupBy struct {
	selector
	build *WmsTransferOrderDetailQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (wtodgb *WmsTransferOrderDetailGroupBy) Aggregate(fns ...AggregateFunc) *WmsTransferOrderDetailGroupBy {
	wtodgb.fns = append(wtodgb.fns, fns...)
	return wtodgb
}

// Scan applies the selector query and scans the result into the given value.
func (wtodgb *WmsTransferOrderDetailGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, wtodgb.build.ctx, ent.OpQueryGroupBy)
	if err := wtodgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*WmsTransferOrderDetailQuery, *WmsTransferOrderDetailGroupBy](ctx, wtodgb.build, wtodgb, wtodgb.build.inters, v)
}

func (wtodgb *WmsTransferOrderDetailGroupBy) sqlScan(ctx context.Context, root *WmsTransferOrderDetailQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(wtodgb.fns))
	for _, fn := range wtodgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*wtodgb.flds)+len(wtodgb.fns))
		for _, f := range *wtodgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*wtodgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := wtodgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// WmsTransferOrderDetailSelect is the builder for selecting fields of WmsTransferOrderDetail entities.
type WmsTransferOrderDetailSelect struct {
	*WmsTransferOrderDetailQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (wtods *WmsTransferOrderDetailSelect) Aggregate(fns ...AggregateFunc) *WmsTransferOrderDetailSelect {
	wtods.fns = append(wtods.fns, fns...)
	return wtods
}

// Scan applies the selector query and scans the result into the given value.
func (wtods *WmsTransferOrderDetailSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, wtods.ctx, ent.OpQuerySelect)
	if err := wtods.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*WmsTransferOrderDetailQuery, *WmsTransferOrderDetailSelect](ctx, wtods.WmsTransferOrderDetailQuery, wtods, wtods.inters, v)
}

func (wtods *WmsTransferOrderDetailSelect) sqlScan(ctx context.Context, root *WmsTransferOrderDetailQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(wtods.fns))
	for _, fn := range wtods.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*wtods.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := wtods.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (wtods *WmsTransferOrderDetailSelect) Modify(modifiers ...func(s *sql.Selector)) *WmsTransferOrderDetailSelect {
	wtods.modifiers = append(wtods.modifiers, modifiers...)
	return wtods
}
