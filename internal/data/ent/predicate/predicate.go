// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// AppApproveActivity is the predicate function for appapproveactivity builders.
type AppApproveActivity func(*sql.Selector)

// AppApproveBasic is the predicate function for appapprovebasic builders.
type AppApproveBasic func(*sql.Selector)

// AppApproveGroup is the predicate function for appapprovegroup builders.
type AppApproveGroup func(*sql.Selector)

// AppApproveRecord is the predicate function for appapproverecord builders.
type AppApproveRecord func(*sql.Selector)

// AppApproveRecordLog is the predicate function for appapproverecordlog builders.
type AppApproveRecordLog func(*sql.Selector)

// AppApproveRecordQueue is the predicate function for appapproverecordqueue builders.
type AppApproveRecordQueue func(*sql.Selector)

// AppApproveWorkflow is the predicate function for appapproveworkflow builders.
type AppApproveWorkflow func(*sql.Selector)

// DemoBuilding is the predicate function for demobuilding builders.
type DemoBuilding func(*sql.Selector)

// SysApplication is the predicate function for sysapplication builders.
type SysApplication func(*sql.Selector)

// SysApplicationModule is the predicate function for sysapplicationmodule builders.
type SysApplicationModule func(*sql.Selector)

// SysApplicationModuleResource is the predicate function for sysapplicationmoduleresource builders.
type SysApplicationModuleResource func(*sql.Selector)

// SysArea is the predicate function for sysarea builders.
type SysArea func(*sql.Selector)

// SysCity is the predicate function for syscity builders.
type SysCity func(*sql.Selector)

// SysConfig is the predicate function for sysconfig builders.
type SysConfig func(*sql.Selector)

// SysDictionary is the predicate function for sysdictionary builders.
type SysDictionary func(*sql.Selector)

// SysDictionaryDetail is the predicate function for sysdictionarydetail builders.
type SysDictionaryDetail func(*sql.Selector)

// SysGame is the predicate function for sysgame builders.
type SysGame func(*sql.Selector)

// SysLabel is the predicate function for syslabel builders.
type SysLabel func(*sql.Selector)

// SysLog is the predicate function for syslog builders.
type SysLog func(*sql.Selector)

// SysMenu is the predicate function for sysmenu builders.
type SysMenu func(*sql.Selector)

// SysMessageTemplate is the predicate function for sysmessagetemplate builders.
type SysMessageTemplate func(*sql.Selector)

// SysModule is the predicate function for sysmodule builders.
type SysModule func(*sql.Selector)

// SysModuleResource is the predicate function for sysmoduleresource builders.
type SysModuleResource func(*sql.Selector)

// SysOrganization is the predicate function for sysorganization builders.
type SysOrganization func(*sql.Selector)

// SysPageCode is the predicate function for syspagecode builders.
type SysPageCode func(*sql.Selector)

// SysPageCodeHistory is the predicate function for syspagecodehistory builders.
type SysPageCodeHistory func(*sql.Selector)

// SysProject is the predicate function for sysproject builders.
type SysProject func(*sql.Selector)

// SysProvince is the predicate function for sysprovince builders.
type SysProvince func(*sql.Selector)

// SysResource is the predicate function for sysresource builders.
type SysResource func(*sql.Selector)

// SysRole is the predicate function for sysrole builders.
type SysRole func(*sql.Selector)

// SysRoleMenu is the predicate function for sysrolemenu builders.
type SysRoleMenu func(*sql.Selector)

// SysRoleResource is the predicate function for sysroleresource builders.
type SysRoleResource func(*sql.Selector)

// SysStreet is the predicate function for sysstreet builders.
type SysStreet func(*sql.Selector)

// SysTeam is the predicate function for systeam builders.
type SysTeam func(*sql.Selector)

// SysTeamApplication is the predicate function for systeamapplication builders.
type SysTeamApplication func(*sql.Selector)

// SysUpload is the predicate function for sysupload builders.
type SysUpload func(*sql.Selector)

// SysUser is the predicate function for sysuser builders.
type SysUser func(*sql.Selector)

// SysUserAuth is the predicate function for sysuserauth builders.
type SysUserAuth func(*sql.Selector)

// SysUserLog is the predicate function for sysuserlog builders.
type SysUserLog func(*sql.Selector)

// SysUserOrganization is the predicate function for sysuserorganization builders.
type SysUserOrganization func(*sql.Selector)

// SysUserRole is the predicate function for sysuserrole builders.
type SysUserRole func(*sql.Selector)

// WmsAccessDoorLog is the predicate function for wmsaccessdoorlog builders.
type WmsAccessDoorLog func(*sql.Selector)

// WmsApprovalTask is the predicate function for wmsapprovaltask builders.
type WmsApprovalTask func(*sql.Selector)

// WmsApprovalTaskDetail is the predicate function for wmsapprovaltaskdetail builders.
type WmsApprovalTaskDetail func(*sql.Selector)

// WmsApprovalTaskOperation is the predicate function for wmsapprovaltaskoperation builders.
type WmsApprovalTaskOperation func(*sql.Selector)

// WmsApprovalTaskRemark is the predicate function for wmsapprovaltaskremark builders.
type WmsApprovalTaskRemark func(*sql.Selector)

// WmsAuditPlan is the predicate function for wmsauditplan builders.
type WmsAuditPlan func(*sql.Selector)

// WmsAuditPlanDetail is the predicate function for wmsauditplandetail builders.
type WmsAuditPlanDetail func(*sql.Selector)

// WmsBorrowOrder is the predicate function for wmsborroworder builders.
type WmsBorrowOrder func(*sql.Selector)

// WmsBorrowOrderDetail is the predicate function for wmsborroworderdetail builders.
type WmsBorrowOrderDetail func(*sql.Selector)

// WmsCar is the predicate function for wmscar builders.
type WmsCar func(*sql.Selector)

// WmsClaimOrder is the predicate function for wmsclaimorder builders.
type WmsClaimOrder func(*sql.Selector)

// WmsClaimOrderDetail is the predicate function for wmsclaimorderdetail builders.
type WmsClaimOrderDetail func(*sql.Selector)

// WmsCompany is the predicate function for wmscompany builders.
type WmsCompany func(*sql.Selector)

// WmsCompanyAddress is the predicate function for wmscompanyaddress builders.
type WmsCompanyAddress func(*sql.Selector)

// WmsDiscardMeeting is the predicate function for wmsdiscardmeeting builders.
type WmsDiscardMeeting func(*sql.Selector)

// WmsDiscardMeetingDetail is the predicate function for wmsdiscardmeetingdetail builders.
type WmsDiscardMeetingDetail func(*sql.Selector)

// WmsDiscardOrder is the predicate function for wmsdiscardorder builders.
type WmsDiscardOrder func(*sql.Selector)

// WmsDiscardOrderDetail is the predicate function for wmsdiscardorderdetail builders.
type WmsDiscardOrderDetail func(*sql.Selector)

// WmsDiscardPlanOrder is the predicate function for wmsdiscardplanorder builders.
type WmsDiscardPlanOrder func(*sql.Selector)

// WmsDiscardPlanOrderDetail is the predicate function for wmsdiscardplanorderdetail builders.
type WmsDiscardPlanOrderDetail func(*sql.Selector)

// WmsDocument is the predicate function for wmsdocument builders.
type WmsDocument func(*sql.Selector)

// WmsEnterRepositoryOrder is the predicate function for wmsenterrepositoryorder builders.
type WmsEnterRepositoryOrder func(*sql.Selector)

// WmsEnterRepositoryOrderDetail is the predicate function for wmsenterrepositoryorderdetail builders.
type WmsEnterRepositoryOrderDetail func(*sql.Selector)

// WmsEquipment is the predicate function for wmsequipment builders.
type WmsEquipment func(*sql.Selector)

// WmsEquipmentDetail is the predicate function for wmsequipmentdetail builders.
type WmsEquipmentDetail func(*sql.Selector)

// WmsEquipmentType is the predicate function for wmsequipmenttype builders.
type WmsEquipmentType func(*sql.Selector)

// WmsEquipmentTypeProperty is the predicate function for wmsequipmenttypeproperty builders.
type WmsEquipmentTypeProperty func(*sql.Selector)

// WmsEquipmentTypePropertyGroup is the predicate function for wmsequipmenttypepropertygroup builders.
type WmsEquipmentTypePropertyGroup func(*sql.Selector)

// WmsEquipmentTypePropertyOption is the predicate function for wmsequipmenttypepropertyoption builders.
type WmsEquipmentTypePropertyOption func(*sql.Selector)

// WmsEquipmentUserGroup is the predicate function for wmsequipmentusergroup builders.
type WmsEquipmentUserGroup func(*sql.Selector)

// WmsFireStation is the predicate function for wmsfirestation builders.
type WmsFireStation func(*sql.Selector)

// WmsGPS is the predicate function for wmsgps builders.
type WmsGPS func(*sql.Selector)

// WmsGPSRecord is the predicate function for wmsgpsrecord builders.
type WmsGPSRecord func(*sql.Selector)

// WmsLearningCourse is the predicate function for wmslearningcourse builders.
type WmsLearningCourse func(*sql.Selector)

// WmsLearningCourseCourseware is the predicate function for wmslearningcoursecourseware builders.
type WmsLearningCourseCourseware func(*sql.Selector)

// WmsLearningCourseLog is the predicate function for wmslearningcourselog builders.
type WmsLearningCourseLog func(*sql.Selector)

// WmsLearningCourseRecord is the predicate function for wmslearningcourserecord builders.
type WmsLearningCourseRecord func(*sql.Selector)

// WmsLearningCourseware is the predicate function for wmslearningcourseware builders.
type WmsLearningCourseware func(*sql.Selector)

// WmsLearningCoursewareRecord is the predicate function for wmslearningcoursewarerecord builders.
type WmsLearningCoursewareRecord func(*sql.Selector)

// WmsLearningPlan is the predicate function for wmslearningplan builders.
type WmsLearningPlan func(*sql.Selector)

// WmsLearningPlanRecord is the predicate function for wmslearningplanrecord builders.
type WmsLearningPlanRecord func(*sql.Selector)

// WmsMaintainOrder is the predicate function for wmsmaintainorder builders.
type WmsMaintainOrder func(*sql.Selector)

// WmsMaintainOrderDetail is the predicate function for wmsmaintainorderdetail builders.
type WmsMaintainOrderDetail func(*sql.Selector)

// WmsMaintainPlan is the predicate function for wmsmaintainplan builders.
type WmsMaintainPlan func(*sql.Selector)

// WmsMaintainPlanDetail is the predicate function for wmsmaintainplandetail builders.
type WmsMaintainPlanDetail func(*sql.Selector)

// WmsMaterial is the predicate function for wmsmaterial builders.
type WmsMaterial func(*sql.Selector)

// WmsMaterialLog is the predicate function for wmsmateriallog builders.
type WmsMaterialLog func(*sql.Selector)

// WmsMeasureUnit is the predicate function for wmsmeasureunit builders.
type WmsMeasureUnit func(*sql.Selector)

// WmsOperateLog is the predicate function for wmsoperatelog builders.
type WmsOperateLog func(*sql.Selector)

// WmsOutRepositoryOrder is the predicate function for wmsoutrepositoryorder builders.
type WmsOutRepositoryOrder func(*sql.Selector)

// WmsOutRepositoryOrderDetail is the predicate function for wmsoutrepositoryorderdetail builders.
type WmsOutRepositoryOrderDetail func(*sql.Selector)

// WmsPurchaseOrder is the predicate function for wmspurchaseorder builders.
type WmsPurchaseOrder func(*sql.Selector)

// WmsPurchaseOrderDetail is the predicate function for wmspurchaseorderdetail builders.
type WmsPurchaseOrderDetail func(*sql.Selector)

// WmsRepairOrder is the predicate function for wmsrepairorder builders.
type WmsRepairOrder func(*sql.Selector)

// WmsRepairOrderDetail is the predicate function for wmsrepairorderdetail builders.
type WmsRepairOrderDetail func(*sql.Selector)

// WmsRepairSettlementOrder is the predicate function for wmsrepairsettlementorder builders.
type WmsRepairSettlementOrder func(*sql.Selector)

// WmsRepairSettlementOrderSettlementfeeDetail is the predicate function for wmsrepairsettlementordersettlementfeedetail builders.
type WmsRepairSettlementOrderSettlementfeeDetail func(*sql.Selector)

// WmsRepairSettlementOrderWorkfeeDetail is the predicate function for wmsrepairsettlementorderworkfeedetail builders.
type WmsRepairSettlementOrderWorkfeeDetail func(*sql.Selector)

// WmsRepository is the predicate function for wmsrepository builders.
type WmsRepository func(*sql.Selector)

// WmsRepositoryAdmin is the predicate function for wmsrepositoryadmin builders.
type WmsRepositoryAdmin func(*sql.Selector)

// WmsRepositoryArea is the predicate function for wmsrepositoryarea builders.
type WmsRepositoryArea func(*sql.Selector)

// WmsRepositoryPosition is the predicate function for wmsrepositoryposition builders.
type WmsRepositoryPosition func(*sql.Selector)

// WmsRepositoryScreen is the predicate function for wmsrepositoryscreen builders.
type WmsRepositoryScreen func(*sql.Selector)

// WmsRepositoryScreenRepositoryArea is the predicate function for wmsrepositoryscreenrepositoryarea builders.
type WmsRepositoryScreenRepositoryArea func(*sql.Selector)

// WmsReturnOrder is the predicate function for wmsreturnorder builders.
type WmsReturnOrder func(*sql.Selector)

// WmsReturnOrderDetail is the predicate function for wmsreturnorderdetail builders.
type WmsReturnOrderDetail func(*sql.Selector)

// WmsRfid is the predicate function for wmsrfid builders.
type WmsRfid func(*sql.Selector)

// WmsRfidReader is the predicate function for wmsrfidreader builders.
type WmsRfidReader func(*sql.Selector)

// WmsRfidReaderRecord is the predicate function for wmsrfidreaderrecord builders.
type WmsRfidReaderRecord func(*sql.Selector)

// WmsTransferOrder is the predicate function for wmstransferorder builders.
type WmsTransferOrder func(*sql.Selector)

// WmsTransferOrderDetail is the predicate function for wmstransferorderdetail builders.
type WmsTransferOrderDetail func(*sql.Selector)

// WmsVehicleRepairOrder is the predicate function for wmsvehiclerepairorder builders.
type WmsVehicleRepairOrder func(*sql.Selector)

// WmsVehicleRepairOrderMaterialfeeDetail is the predicate function for wmsvehiclerepairordermaterialfeedetail builders.
type WmsVehicleRepairOrderMaterialfeeDetail func(*sql.Selector)

// WmsVehicleRepairOrderSettlementfeeDetail is the predicate function for wmsvehiclerepairordersettlementfeedetail builders.
type WmsVehicleRepairOrderSettlementfeeDetail func(*sql.Selector)

// WmsVehicleRepairOrderWorkfeeDetail is the predicate function for wmsvehiclerepairorderworkfeedetail builders.
type WmsVehicleRepairOrderWorkfeeDetail func(*sql.Selector)

// WorkWxApprovalMessage is the predicate function for workwxapprovalmessage builders.
type WorkWxApprovalMessage func(*sql.Selector)

// WorkWxApprovalNode is the predicate function for workwxapprovalnode builders.
type WorkWxApprovalNode func(*sql.Selector)

// WorkWxNotifyNode is the predicate function for workwxnotifynode builders.
type WorkWxNotifyNode func(*sql.Selector)
