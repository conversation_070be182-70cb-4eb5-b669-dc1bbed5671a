// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"kratos-mono-demo/internal/data/ent/predicate"
	"kratos-mono-demo/internal/data/ent/wmstransferorderdetail"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WmsTransferOrderDetailDelete is the builder for deleting a WmsTransferOrderDetail entity.
type WmsTransferOrderDetailDelete struct {
	config
	hooks    []Hook
	mutation *WmsTransferOrderDetailMutation
}

// Where appends a list predicates to the WmsTransferOrderDetailDelete builder.
func (wtodd *WmsTransferOrderDetailDelete) Where(ps ...predicate.WmsTransferOrderDetail) *WmsTransferOrderDetailDelete {
	wtodd.mutation.Where(ps...)
	return wtodd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (wtodd *WmsTransferOrderDetailDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, wtodd.sqlExec, wtodd.mutation, wtodd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (wtodd *WmsTransferOrderDetailDelete) ExecX(ctx context.Context) int {
	n, err := wtodd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (wtodd *WmsTransferOrderDetailDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(wmstransferorderdetail.Table, sqlgraph.NewFieldSpec(wmstransferorderdetail.FieldID, field.TypeString))
	if ps := wtodd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, wtodd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	wtodd.mutation.done = true
	return affected, err
}

// WmsTransferOrderDetailDeleteOne is the builder for deleting a single WmsTransferOrderDetail entity.
type WmsTransferOrderDetailDeleteOne struct {
	wtodd *WmsTransferOrderDetailDelete
}

// Where appends a list predicates to the WmsTransferOrderDetailDelete builder.
func (wtoddo *WmsTransferOrderDetailDeleteOne) Where(ps ...predicate.WmsTransferOrderDetail) *WmsTransferOrderDetailDeleteOne {
	wtoddo.wtodd.mutation.Where(ps...)
	return wtoddo
}

// Exec executes the deletion query.
func (wtoddo *WmsTransferOrderDetailDeleteOne) Exec(ctx context.Context) error {
	n, err := wtoddo.wtodd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{wmstransferorderdetail.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (wtoddo *WmsTransferOrderDetailDeleteOne) ExecX(ctx context.Context) {
	if err := wtoddo.Exec(ctx); err != nil {
		panic(err)
	}
}
