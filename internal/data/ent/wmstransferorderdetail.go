// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"kratos-mono-demo/internal/data/ent/wmstransferorderdetail"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// 调拨单明细
type WmsTransferOrderDetail struct {
	config `json:"-"`
	// ID of the ent.
	ID string `json:"id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 创建人
	CreatedBy string `json:"created_by,omitempty"`
	// 更新人
	UpdatedBy *string `json:"updated_by,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// 调拨单
	OrderID string `json:"order_id,omitempty"`
	// 物料
	MaterialID string `json:"material_id,omitempty"`
	// 物料名称
	MaterialName string `json:"material_name,omitempty"`
	// 编码
	Code string `json:"code,omitempty"`
	// 装备
	EquipmentID string `json:"equipment_id,omitempty"`
	// 装备类型
	EquipmentTypeID string `json:"equipment_type_id,omitempty"`
	// 详细规格
	Feature map[string]interface{} `json:"feature,omitempty"`
	// 仓库
	RepositoryID *string `json:"repository_id,omitempty"`
	// 库区
	RepositoryAreaID *string `json:"repository_area_id,omitempty"`
	// 库位
	RepositoryPositionID *string `json:"repository_position_id,omitempty"`
	// 归属人
	OwnerID *string `json:"owner_id,omitempty"`
	// 规格型号
	ModelNo string `json:"ModelNo,omitempty"`
	// 计量单位
	MeasureUnitID string `json:"measure_unit_id,omitempty"`
	// 调拨数量
	Num uint32 `json:"num,omitempty"`
	// 调拨原因
	TransferReason *string `json:"transfer_reason,omitempty"`
	// 调拨时间
	TransferTime *time.Time `json:"transfer_time,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*WmsTransferOrderDetail) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case wmstransferorderdetail.FieldFeature:
			values[i] = new([]byte)
		case wmstransferorderdetail.FieldNum:
			values[i] = new(sql.NullInt64)
		case wmstransferorderdetail.FieldID, wmstransferorderdetail.FieldCreatedBy, wmstransferorderdetail.FieldUpdatedBy, wmstransferorderdetail.FieldRemark, wmstransferorderdetail.FieldOrderID, wmstransferorderdetail.FieldMaterialID, wmstransferorderdetail.FieldMaterialName, wmstransferorderdetail.FieldCode, wmstransferorderdetail.FieldEquipmentID, wmstransferorderdetail.FieldEquipmentTypeID, wmstransferorderdetail.FieldRepositoryID, wmstransferorderdetail.FieldRepositoryAreaID, wmstransferorderdetail.FieldRepositoryPositionID, wmstransferorderdetail.FieldOwnerID, wmstransferorderdetail.FieldModelNo, wmstransferorderdetail.FieldMeasureUnitID, wmstransferorderdetail.FieldTransferReason:
			values[i] = new(sql.NullString)
		case wmstransferorderdetail.FieldCreatedAt, wmstransferorderdetail.FieldUpdatedAt, wmstransferorderdetail.FieldTransferTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the WmsTransferOrderDetail fields.
func (wtod *WmsTransferOrderDetail) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case wmstransferorderdetail.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				wtod.ID = value.String
			}
		case wmstransferorderdetail.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				wtod.CreatedAt = value.Time
			}
		case wmstransferorderdetail.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				wtod.UpdatedAt = value.Time
			}
		case wmstransferorderdetail.FieldCreatedBy:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field created_by", values[i])
			} else if value.Valid {
				wtod.CreatedBy = value.String
			}
		case wmstransferorderdetail.FieldUpdatedBy:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field updated_by", values[i])
			} else if value.Valid {
				wtod.UpdatedBy = new(string)
				*wtod.UpdatedBy = value.String
			}
		case wmstransferorderdetail.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				wtod.Remark = new(string)
				*wtod.Remark = value.String
			}
		case wmstransferorderdetail.FieldOrderID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field order_id", values[i])
			} else if value.Valid {
				wtod.OrderID = value.String
			}
		case wmstransferorderdetail.FieldMaterialID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field material_id", values[i])
			} else if value.Valid {
				wtod.MaterialID = value.String
			}
		case wmstransferorderdetail.FieldMaterialName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field material_name", values[i])
			} else if value.Valid {
				wtod.MaterialName = value.String
			}
		case wmstransferorderdetail.FieldCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field code", values[i])
			} else if value.Valid {
				wtod.Code = value.String
			}
		case wmstransferorderdetail.FieldEquipmentID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field equipment_id", values[i])
			} else if value.Valid {
				wtod.EquipmentID = value.String
			}
		case wmstransferorderdetail.FieldEquipmentTypeID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field equipment_type_id", values[i])
			} else if value.Valid {
				wtod.EquipmentTypeID = value.String
			}
		case wmstransferorderdetail.FieldFeature:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field feature", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &wtod.Feature); err != nil {
					return fmt.Errorf("unmarshal field feature: %w", err)
				}
			}
		case wmstransferorderdetail.FieldRepositoryID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field repository_id", values[i])
			} else if value.Valid {
				wtod.RepositoryID = new(string)
				*wtod.RepositoryID = value.String
			}
		case wmstransferorderdetail.FieldRepositoryAreaID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field repository_area_id", values[i])
			} else if value.Valid {
				wtod.RepositoryAreaID = new(string)
				*wtod.RepositoryAreaID = value.String
			}
		case wmstransferorderdetail.FieldRepositoryPositionID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field repository_position_id", values[i])
			} else if value.Valid {
				wtod.RepositoryPositionID = new(string)
				*wtod.RepositoryPositionID = value.String
			}
		case wmstransferorderdetail.FieldOwnerID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field owner_id", values[i])
			} else if value.Valid {
				wtod.OwnerID = new(string)
				*wtod.OwnerID = value.String
			}
		case wmstransferorderdetail.FieldModelNo:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field ModelNo", values[i])
			} else if value.Valid {
				wtod.ModelNo = value.String
			}
		case wmstransferorderdetail.FieldMeasureUnitID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field measure_unit_id", values[i])
			} else if value.Valid {
				wtod.MeasureUnitID = value.String
			}
		case wmstransferorderdetail.FieldNum:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field num", values[i])
			} else if value.Valid {
				wtod.Num = uint32(value.Int64)
			}
		case wmstransferorderdetail.FieldTransferReason:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field transfer_reason", values[i])
			} else if value.Valid {
				wtod.TransferReason = new(string)
				*wtod.TransferReason = value.String
			}
		case wmstransferorderdetail.FieldTransferTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field transfer_time", values[i])
			} else if value.Valid {
				wtod.TransferTime = new(time.Time)
				*wtod.TransferTime = value.Time
			}
		default:
			wtod.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the WmsTransferOrderDetail.
// This includes values selected through modifiers, order, etc.
func (wtod *WmsTransferOrderDetail) Value(name string) (ent.Value, error) {
	return wtod.selectValues.Get(name)
}

// Update returns a builder for updating this WmsTransferOrderDetail.
// Note that you need to call WmsTransferOrderDetail.Unwrap() before calling this method if this WmsTransferOrderDetail
// was returned from a transaction, and the transaction was committed or rolled back.
func (wtod *WmsTransferOrderDetail) Update() *WmsTransferOrderDetailUpdateOne {
	return NewWmsTransferOrderDetailClient(wtod.config).UpdateOne(wtod)
}

// Unwrap unwraps the WmsTransferOrderDetail entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (wtod *WmsTransferOrderDetail) Unwrap() *WmsTransferOrderDetail {
	_tx, ok := wtod.config.driver.(*txDriver)
	if !ok {
		panic("ent: WmsTransferOrderDetail is not a transactional entity")
	}
	wtod.config.driver = _tx.drv
	return wtod
}

// String implements the fmt.Stringer.
func (wtod *WmsTransferOrderDetail) String() string {
	var builder strings.Builder
	builder.WriteString("WmsTransferOrderDetail(")
	builder.WriteString(fmt.Sprintf("id=%v, ", wtod.ID))
	builder.WriteString("created_at=")
	builder.WriteString(wtod.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(wtod.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("created_by=")
	builder.WriteString(wtod.CreatedBy)
	builder.WriteString(", ")
	if v := wtod.UpdatedBy; v != nil {
		builder.WriteString("updated_by=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := wtod.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("order_id=")
	builder.WriteString(wtod.OrderID)
	builder.WriteString(", ")
	builder.WriteString("material_id=")
	builder.WriteString(wtod.MaterialID)
	builder.WriteString(", ")
	builder.WriteString("material_name=")
	builder.WriteString(wtod.MaterialName)
	builder.WriteString(", ")
	builder.WriteString("code=")
	builder.WriteString(wtod.Code)
	builder.WriteString(", ")
	builder.WriteString("equipment_id=")
	builder.WriteString(wtod.EquipmentID)
	builder.WriteString(", ")
	builder.WriteString("equipment_type_id=")
	builder.WriteString(wtod.EquipmentTypeID)
	builder.WriteString(", ")
	builder.WriteString("feature=")
	builder.WriteString(fmt.Sprintf("%v", wtod.Feature))
	builder.WriteString(", ")
	if v := wtod.RepositoryID; v != nil {
		builder.WriteString("repository_id=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := wtod.RepositoryAreaID; v != nil {
		builder.WriteString("repository_area_id=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := wtod.RepositoryPositionID; v != nil {
		builder.WriteString("repository_position_id=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := wtod.OwnerID; v != nil {
		builder.WriteString("owner_id=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("ModelNo=")
	builder.WriteString(wtod.ModelNo)
	builder.WriteString(", ")
	builder.WriteString("measure_unit_id=")
	builder.WriteString(wtod.MeasureUnitID)
	builder.WriteString(", ")
	builder.WriteString("num=")
	builder.WriteString(fmt.Sprintf("%v", wtod.Num))
	builder.WriteString(", ")
	if v := wtod.TransferReason; v != nil {
		builder.WriteString("transfer_reason=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := wtod.TransferTime; v != nil {
		builder.WriteString("transfer_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteByte(')')
	return builder.String()
}

// WmsTransferOrderDetails is a parsable slice of WmsTransferOrderDetail.
type WmsTransferOrderDetails []*WmsTransferOrderDetail
