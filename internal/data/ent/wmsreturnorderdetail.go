// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"kratos-mono-demo/internal/data/ent/wmsreturnorderdetail"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// 退还单明细
type WmsReturnOrderDetail struct {
	config `json:"-"`
	// ID of the ent.
	ID string `json:"id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 创建人
	CreatedBy string `json:"created_by,omitempty"`
	// 更新人
	UpdatedBy *string `json:"updated_by,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// 退还单
	OrderID string `json:"order_id,omitempty"`
	// 物料
	MaterialID string `json:"material_id,omitempty"`
	// 物料名称
	MaterialName string `json:"material_name,omitempty"`
	// 编码
	Code string `json:"code,omitempty"`
	// 装备
	EquipmentID string `json:"equipment_id,omitempty"`
	// 装备类型
	EquipmentTypeID string `json:"equipment_type_id,omitempty"`
	// 详细规格
	Feature map[string]interface{} `json:"feature,omitempty"`
	// 归属人
	OwnerID *string `json:"owner_id,omitempty"`
	// 规格型号
	ModelNo string `json:"ModelNo,omitempty"`
	// 计量单位
	MeasureUnitID string `json:"measure_unit_id,omitempty"`
	// 退还数量
	Num uint32 `json:"num,omitempty"`
	// 仓库
	ToRepositoryID *string `json:"to_repository_id,omitempty"`
	// 库区
	ToRepositoryAreaID *string `json:"to_repository_area_id,omitempty"`
	// 库位
	ToRepositoryPositionID *string `json:"to_repository_position_id,omitempty"`
	// 退还原因
	Reason *string `json:"reason,omitempty"`
	// 退还时间
	ReturnTime   *time.Time `json:"return_time,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*WmsReturnOrderDetail) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case wmsreturnorderdetail.FieldFeature:
			values[i] = new([]byte)
		case wmsreturnorderdetail.FieldNum:
			values[i] = new(sql.NullInt64)
		case wmsreturnorderdetail.FieldID, wmsreturnorderdetail.FieldCreatedBy, wmsreturnorderdetail.FieldUpdatedBy, wmsreturnorderdetail.FieldRemark, wmsreturnorderdetail.FieldOrderID, wmsreturnorderdetail.FieldMaterialID, wmsreturnorderdetail.FieldMaterialName, wmsreturnorderdetail.FieldCode, wmsreturnorderdetail.FieldEquipmentID, wmsreturnorderdetail.FieldEquipmentTypeID, wmsreturnorderdetail.FieldOwnerID, wmsreturnorderdetail.FieldModelNo, wmsreturnorderdetail.FieldMeasureUnitID, wmsreturnorderdetail.FieldToRepositoryID, wmsreturnorderdetail.FieldToRepositoryAreaID, wmsreturnorderdetail.FieldToRepositoryPositionID, wmsreturnorderdetail.FieldReason:
			values[i] = new(sql.NullString)
		case wmsreturnorderdetail.FieldCreatedAt, wmsreturnorderdetail.FieldUpdatedAt, wmsreturnorderdetail.FieldReturnTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the WmsReturnOrderDetail fields.
func (wrod *WmsReturnOrderDetail) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case wmsreturnorderdetail.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				wrod.ID = value.String
			}
		case wmsreturnorderdetail.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				wrod.CreatedAt = value.Time
			}
		case wmsreturnorderdetail.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				wrod.UpdatedAt = value.Time
			}
		case wmsreturnorderdetail.FieldCreatedBy:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field created_by", values[i])
			} else if value.Valid {
				wrod.CreatedBy = value.String
			}
		case wmsreturnorderdetail.FieldUpdatedBy:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field updated_by", values[i])
			} else if value.Valid {
				wrod.UpdatedBy = new(string)
				*wrod.UpdatedBy = value.String
			}
		case wmsreturnorderdetail.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				wrod.Remark = new(string)
				*wrod.Remark = value.String
			}
		case wmsreturnorderdetail.FieldOrderID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field order_id", values[i])
			} else if value.Valid {
				wrod.OrderID = value.String
			}
		case wmsreturnorderdetail.FieldMaterialID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field material_id", values[i])
			} else if value.Valid {
				wrod.MaterialID = value.String
			}
		case wmsreturnorderdetail.FieldMaterialName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field material_name", values[i])
			} else if value.Valid {
				wrod.MaterialName = value.String
			}
		case wmsreturnorderdetail.FieldCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field code", values[i])
			} else if value.Valid {
				wrod.Code = value.String
			}
		case wmsreturnorderdetail.FieldEquipmentID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field equipment_id", values[i])
			} else if value.Valid {
				wrod.EquipmentID = value.String
			}
		case wmsreturnorderdetail.FieldEquipmentTypeID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field equipment_type_id", values[i])
			} else if value.Valid {
				wrod.EquipmentTypeID = value.String
			}
		case wmsreturnorderdetail.FieldFeature:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field feature", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &wrod.Feature); err != nil {
					return fmt.Errorf("unmarshal field feature: %w", err)
				}
			}
		case wmsreturnorderdetail.FieldOwnerID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field owner_id", values[i])
			} else if value.Valid {
				wrod.OwnerID = new(string)
				*wrod.OwnerID = value.String
			}
		case wmsreturnorderdetail.FieldModelNo:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field ModelNo", values[i])
			} else if value.Valid {
				wrod.ModelNo = value.String
			}
		case wmsreturnorderdetail.FieldMeasureUnitID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field measure_unit_id", values[i])
			} else if value.Valid {
				wrod.MeasureUnitID = value.String
			}
		case wmsreturnorderdetail.FieldNum:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field num", values[i])
			} else if value.Valid {
				wrod.Num = uint32(value.Int64)
			}
		case wmsreturnorderdetail.FieldToRepositoryID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field to_repository_id", values[i])
			} else if value.Valid {
				wrod.ToRepositoryID = new(string)
				*wrod.ToRepositoryID = value.String
			}
		case wmsreturnorderdetail.FieldToRepositoryAreaID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field to_repository_area_id", values[i])
			} else if value.Valid {
				wrod.ToRepositoryAreaID = new(string)
				*wrod.ToRepositoryAreaID = value.String
			}
		case wmsreturnorderdetail.FieldToRepositoryPositionID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field to_repository_position_id", values[i])
			} else if value.Valid {
				wrod.ToRepositoryPositionID = new(string)
				*wrod.ToRepositoryPositionID = value.String
			}
		case wmsreturnorderdetail.FieldReason:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field reason", values[i])
			} else if value.Valid {
				wrod.Reason = new(string)
				*wrod.Reason = value.String
			}
		case wmsreturnorderdetail.FieldReturnTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field return_time", values[i])
			} else if value.Valid {
				wrod.ReturnTime = new(time.Time)
				*wrod.ReturnTime = value.Time
			}
		default:
			wrod.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the WmsReturnOrderDetail.
// This includes values selected through modifiers, order, etc.
func (wrod *WmsReturnOrderDetail) Value(name string) (ent.Value, error) {
	return wrod.selectValues.Get(name)
}

// Update returns a builder for updating this WmsReturnOrderDetail.
// Note that you need to call WmsReturnOrderDetail.Unwrap() before calling this method if this WmsReturnOrderDetail
// was returned from a transaction, and the transaction was committed or rolled back.
func (wrod *WmsReturnOrderDetail) Update() *WmsReturnOrderDetailUpdateOne {
	return NewWmsReturnOrderDetailClient(wrod.config).UpdateOne(wrod)
}

// Unwrap unwraps the WmsReturnOrderDetail entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (wrod *WmsReturnOrderDetail) Unwrap() *WmsReturnOrderDetail {
	_tx, ok := wrod.config.driver.(*txDriver)
	if !ok {
		panic("ent: WmsReturnOrderDetail is not a transactional entity")
	}
	wrod.config.driver = _tx.drv
	return wrod
}

// String implements the fmt.Stringer.
func (wrod *WmsReturnOrderDetail) String() string {
	var builder strings.Builder
	builder.WriteString("WmsReturnOrderDetail(")
	builder.WriteString(fmt.Sprintf("id=%v, ", wrod.ID))
	builder.WriteString("created_at=")
	builder.WriteString(wrod.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(wrod.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("created_by=")
	builder.WriteString(wrod.CreatedBy)
	builder.WriteString(", ")
	if v := wrod.UpdatedBy; v != nil {
		builder.WriteString("updated_by=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := wrod.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("order_id=")
	builder.WriteString(wrod.OrderID)
	builder.WriteString(", ")
	builder.WriteString("material_id=")
	builder.WriteString(wrod.MaterialID)
	builder.WriteString(", ")
	builder.WriteString("material_name=")
	builder.WriteString(wrod.MaterialName)
	builder.WriteString(", ")
	builder.WriteString("code=")
	builder.WriteString(wrod.Code)
	builder.WriteString(", ")
	builder.WriteString("equipment_id=")
	builder.WriteString(wrod.EquipmentID)
	builder.WriteString(", ")
	builder.WriteString("equipment_type_id=")
	builder.WriteString(wrod.EquipmentTypeID)
	builder.WriteString(", ")
	builder.WriteString("feature=")
	builder.WriteString(fmt.Sprintf("%v", wrod.Feature))
	builder.WriteString(", ")
	if v := wrod.OwnerID; v != nil {
		builder.WriteString("owner_id=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("ModelNo=")
	builder.WriteString(wrod.ModelNo)
	builder.WriteString(", ")
	builder.WriteString("measure_unit_id=")
	builder.WriteString(wrod.MeasureUnitID)
	builder.WriteString(", ")
	builder.WriteString("num=")
	builder.WriteString(fmt.Sprintf("%v", wrod.Num))
	builder.WriteString(", ")
	if v := wrod.ToRepositoryID; v != nil {
		builder.WriteString("to_repository_id=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := wrod.ToRepositoryAreaID; v != nil {
		builder.WriteString("to_repository_area_id=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := wrod.ToRepositoryPositionID; v != nil {
		builder.WriteString("to_repository_position_id=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := wrod.Reason; v != nil {
		builder.WriteString("reason=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := wrod.ReturnTime; v != nil {
		builder.WriteString("return_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteByte(')')
	return builder.String()
}

// WmsReturnOrderDetails is a parsable slice of WmsReturnOrderDetail.
type WmsReturnOrderDetails []*WmsReturnOrderDetail
