// Code generated by ent, DO NOT EDIT.

package hook

import (
	"context"
	"fmt"
	"kratos-mono-demo/internal/data/ent"
)

// The AppApproveActivityFunc type is an adapter to allow the use of ordinary
// function as AppApproveActivity mutator.
type AppApproveActivityFunc func(context.Context, *ent.AppApproveActivityMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f AppApproveActivityFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.AppApproveActivityMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.AppApproveActivityMutation", m)
}

// The AppApproveBasicFunc type is an adapter to allow the use of ordinary
// function as AppApproveBasic mutator.
type AppApproveBasicFunc func(context.Context, *ent.AppApproveBasicMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f AppApproveBasicFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.AppApproveBasicMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.AppApproveBasicMutation", m)
}

// The AppApproveGroupFunc type is an adapter to allow the use of ordinary
// function as AppApproveGroup mutator.
type AppApproveGroupFunc func(context.Context, *ent.AppApproveGroupMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f AppApproveGroupFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.AppApproveGroupMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.AppApproveGroupMutation", m)
}

// The AppApproveRecordFunc type is an adapter to allow the use of ordinary
// function as AppApproveRecord mutator.
type AppApproveRecordFunc func(context.Context, *ent.AppApproveRecordMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f AppApproveRecordFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.AppApproveRecordMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.AppApproveRecordMutation", m)
}

// The AppApproveRecordLogFunc type is an adapter to allow the use of ordinary
// function as AppApproveRecordLog mutator.
type AppApproveRecordLogFunc func(context.Context, *ent.AppApproveRecordLogMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f AppApproveRecordLogFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.AppApproveRecordLogMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.AppApproveRecordLogMutation", m)
}

// The AppApproveRecordQueueFunc type is an adapter to allow the use of ordinary
// function as AppApproveRecordQueue mutator.
type AppApproveRecordQueueFunc func(context.Context, *ent.AppApproveRecordQueueMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f AppApproveRecordQueueFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.AppApproveRecordQueueMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.AppApproveRecordQueueMutation", m)
}

// The AppApproveWorkflowFunc type is an adapter to allow the use of ordinary
// function as AppApproveWorkflow mutator.
type AppApproveWorkflowFunc func(context.Context, *ent.AppApproveWorkflowMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f AppApproveWorkflowFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.AppApproveWorkflowMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.AppApproveWorkflowMutation", m)
}

// The DemoBuildingFunc type is an adapter to allow the use of ordinary
// function as DemoBuilding mutator.
type DemoBuildingFunc func(context.Context, *ent.DemoBuildingMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f DemoBuildingFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.DemoBuildingMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.DemoBuildingMutation", m)
}

// The SysApplicationFunc type is an adapter to allow the use of ordinary
// function as SysApplication mutator.
type SysApplicationFunc func(context.Context, *ent.SysApplicationMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysApplicationFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysApplicationMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysApplicationMutation", m)
}

// The SysApplicationModuleFunc type is an adapter to allow the use of ordinary
// function as SysApplicationModule mutator.
type SysApplicationModuleFunc func(context.Context, *ent.SysApplicationModuleMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysApplicationModuleFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysApplicationModuleMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysApplicationModuleMutation", m)
}

// The SysApplicationModuleResourceFunc type is an adapter to allow the use of ordinary
// function as SysApplicationModuleResource mutator.
type SysApplicationModuleResourceFunc func(context.Context, *ent.SysApplicationModuleResourceMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysApplicationModuleResourceFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysApplicationModuleResourceMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysApplicationModuleResourceMutation", m)
}

// The SysAreaFunc type is an adapter to allow the use of ordinary
// function as SysArea mutator.
type SysAreaFunc func(context.Context, *ent.SysAreaMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysAreaFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysAreaMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysAreaMutation", m)
}

// The SysCityFunc type is an adapter to allow the use of ordinary
// function as SysCity mutator.
type SysCityFunc func(context.Context, *ent.SysCityMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysCityFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysCityMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysCityMutation", m)
}

// The SysConfigFunc type is an adapter to allow the use of ordinary
// function as SysConfig mutator.
type SysConfigFunc func(context.Context, *ent.SysConfigMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysConfigFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysConfigMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysConfigMutation", m)
}

// The SysDictionaryFunc type is an adapter to allow the use of ordinary
// function as SysDictionary mutator.
type SysDictionaryFunc func(context.Context, *ent.SysDictionaryMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysDictionaryFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysDictionaryMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysDictionaryMutation", m)
}

// The SysDictionaryDetailFunc type is an adapter to allow the use of ordinary
// function as SysDictionaryDetail mutator.
type SysDictionaryDetailFunc func(context.Context, *ent.SysDictionaryDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysDictionaryDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysDictionaryDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysDictionaryDetailMutation", m)
}

// The SysGameFunc type is an adapter to allow the use of ordinary
// function as SysGame mutator.
type SysGameFunc func(context.Context, *ent.SysGameMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysGameFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysGameMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysGameMutation", m)
}

// The SysLabelFunc type is an adapter to allow the use of ordinary
// function as SysLabel mutator.
type SysLabelFunc func(context.Context, *ent.SysLabelMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysLabelFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysLabelMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysLabelMutation", m)
}

// The SysLogFunc type is an adapter to allow the use of ordinary
// function as SysLog mutator.
type SysLogFunc func(context.Context, *ent.SysLogMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysLogFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysLogMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysLogMutation", m)
}

// The SysMenuFunc type is an adapter to allow the use of ordinary
// function as SysMenu mutator.
type SysMenuFunc func(context.Context, *ent.SysMenuMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysMenuFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysMenuMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysMenuMutation", m)
}

// The SysMessageTemplateFunc type is an adapter to allow the use of ordinary
// function as SysMessageTemplate mutator.
type SysMessageTemplateFunc func(context.Context, *ent.SysMessageTemplateMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysMessageTemplateFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysMessageTemplateMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysMessageTemplateMutation", m)
}

// The SysModuleFunc type is an adapter to allow the use of ordinary
// function as SysModule mutator.
type SysModuleFunc func(context.Context, *ent.SysModuleMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysModuleFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysModuleMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysModuleMutation", m)
}

// The SysModuleResourceFunc type is an adapter to allow the use of ordinary
// function as SysModuleResource mutator.
type SysModuleResourceFunc func(context.Context, *ent.SysModuleResourceMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysModuleResourceFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysModuleResourceMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysModuleResourceMutation", m)
}

// The SysOrganizationFunc type is an adapter to allow the use of ordinary
// function as SysOrganization mutator.
type SysOrganizationFunc func(context.Context, *ent.SysOrganizationMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysOrganizationFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysOrganizationMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysOrganizationMutation", m)
}

// The SysPageCodeFunc type is an adapter to allow the use of ordinary
// function as SysPageCode mutator.
type SysPageCodeFunc func(context.Context, *ent.SysPageCodeMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysPageCodeFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysPageCodeMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysPageCodeMutation", m)
}

// The SysPageCodeHistoryFunc type is an adapter to allow the use of ordinary
// function as SysPageCodeHistory mutator.
type SysPageCodeHistoryFunc func(context.Context, *ent.SysPageCodeHistoryMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysPageCodeHistoryFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysPageCodeHistoryMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysPageCodeHistoryMutation", m)
}

// The SysProjectFunc type is an adapter to allow the use of ordinary
// function as SysProject mutator.
type SysProjectFunc func(context.Context, *ent.SysProjectMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysProjectFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysProjectMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysProjectMutation", m)
}

// The SysProvinceFunc type is an adapter to allow the use of ordinary
// function as SysProvince mutator.
type SysProvinceFunc func(context.Context, *ent.SysProvinceMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysProvinceFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysProvinceMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysProvinceMutation", m)
}

// The SysResourceFunc type is an adapter to allow the use of ordinary
// function as SysResource mutator.
type SysResourceFunc func(context.Context, *ent.SysResourceMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysResourceFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysResourceMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysResourceMutation", m)
}

// The SysRoleFunc type is an adapter to allow the use of ordinary
// function as SysRole mutator.
type SysRoleFunc func(context.Context, *ent.SysRoleMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysRoleFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysRoleMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysRoleMutation", m)
}

// The SysRoleMenuFunc type is an adapter to allow the use of ordinary
// function as SysRoleMenu mutator.
type SysRoleMenuFunc func(context.Context, *ent.SysRoleMenuMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysRoleMenuFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysRoleMenuMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysRoleMenuMutation", m)
}

// The SysRoleResourceFunc type is an adapter to allow the use of ordinary
// function as SysRoleResource mutator.
type SysRoleResourceFunc func(context.Context, *ent.SysRoleResourceMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysRoleResourceFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysRoleResourceMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysRoleResourceMutation", m)
}

// The SysStreetFunc type is an adapter to allow the use of ordinary
// function as SysStreet mutator.
type SysStreetFunc func(context.Context, *ent.SysStreetMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysStreetFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysStreetMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysStreetMutation", m)
}

// The SysTeamFunc type is an adapter to allow the use of ordinary
// function as SysTeam mutator.
type SysTeamFunc func(context.Context, *ent.SysTeamMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysTeamFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysTeamMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysTeamMutation", m)
}

// The SysTeamApplicationFunc type is an adapter to allow the use of ordinary
// function as SysTeamApplication mutator.
type SysTeamApplicationFunc func(context.Context, *ent.SysTeamApplicationMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysTeamApplicationFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysTeamApplicationMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysTeamApplicationMutation", m)
}

// The SysUploadFunc type is an adapter to allow the use of ordinary
// function as SysUpload mutator.
type SysUploadFunc func(context.Context, *ent.SysUploadMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysUploadFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysUploadMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysUploadMutation", m)
}

// The SysUserFunc type is an adapter to allow the use of ordinary
// function as SysUser mutator.
type SysUserFunc func(context.Context, *ent.SysUserMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysUserFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysUserMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysUserMutation", m)
}

// The SysUserAuthFunc type is an adapter to allow the use of ordinary
// function as SysUserAuth mutator.
type SysUserAuthFunc func(context.Context, *ent.SysUserAuthMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysUserAuthFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysUserAuthMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysUserAuthMutation", m)
}

// The SysUserLogFunc type is an adapter to allow the use of ordinary
// function as SysUserLog mutator.
type SysUserLogFunc func(context.Context, *ent.SysUserLogMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysUserLogFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysUserLogMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysUserLogMutation", m)
}

// The SysUserOrganizationFunc type is an adapter to allow the use of ordinary
// function as SysUserOrganization mutator.
type SysUserOrganizationFunc func(context.Context, *ent.SysUserOrganizationMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysUserOrganizationFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysUserOrganizationMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysUserOrganizationMutation", m)
}

// The SysUserRoleFunc type is an adapter to allow the use of ordinary
// function as SysUserRole mutator.
type SysUserRoleFunc func(context.Context, *ent.SysUserRoleMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SysUserRoleFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SysUserRoleMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SysUserRoleMutation", m)
}

// The WmsAccessDoorLogFunc type is an adapter to allow the use of ordinary
// function as WmsAccessDoorLog mutator.
type WmsAccessDoorLogFunc func(context.Context, *ent.WmsAccessDoorLogMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsAccessDoorLogFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsAccessDoorLogMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsAccessDoorLogMutation", m)
}

// The WmsApprovalTaskFunc type is an adapter to allow the use of ordinary
// function as WmsApprovalTask mutator.
type WmsApprovalTaskFunc func(context.Context, *ent.WmsApprovalTaskMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsApprovalTaskFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsApprovalTaskMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsApprovalTaskMutation", m)
}

// The WmsApprovalTaskDetailFunc type is an adapter to allow the use of ordinary
// function as WmsApprovalTaskDetail mutator.
type WmsApprovalTaskDetailFunc func(context.Context, *ent.WmsApprovalTaskDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsApprovalTaskDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsApprovalTaskDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsApprovalTaskDetailMutation", m)
}

// The WmsApprovalTaskOperationFunc type is an adapter to allow the use of ordinary
// function as WmsApprovalTaskOperation mutator.
type WmsApprovalTaskOperationFunc func(context.Context, *ent.WmsApprovalTaskOperationMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsApprovalTaskOperationFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsApprovalTaskOperationMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsApprovalTaskOperationMutation", m)
}

// The WmsApprovalTaskRemarkFunc type is an adapter to allow the use of ordinary
// function as WmsApprovalTaskRemark mutator.
type WmsApprovalTaskRemarkFunc func(context.Context, *ent.WmsApprovalTaskRemarkMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsApprovalTaskRemarkFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsApprovalTaskRemarkMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsApprovalTaskRemarkMutation", m)
}

// The WmsAuditPlanFunc type is an adapter to allow the use of ordinary
// function as WmsAuditPlan mutator.
type WmsAuditPlanFunc func(context.Context, *ent.WmsAuditPlanMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsAuditPlanFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsAuditPlanMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsAuditPlanMutation", m)
}

// The WmsAuditPlanDetailFunc type is an adapter to allow the use of ordinary
// function as WmsAuditPlanDetail mutator.
type WmsAuditPlanDetailFunc func(context.Context, *ent.WmsAuditPlanDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsAuditPlanDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsAuditPlanDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsAuditPlanDetailMutation", m)
}

// The WmsBorrowOrderFunc type is an adapter to allow the use of ordinary
// function as WmsBorrowOrder mutator.
type WmsBorrowOrderFunc func(context.Context, *ent.WmsBorrowOrderMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsBorrowOrderFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsBorrowOrderMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsBorrowOrderMutation", m)
}

// The WmsBorrowOrderDetailFunc type is an adapter to allow the use of ordinary
// function as WmsBorrowOrderDetail mutator.
type WmsBorrowOrderDetailFunc func(context.Context, *ent.WmsBorrowOrderDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsBorrowOrderDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsBorrowOrderDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsBorrowOrderDetailMutation", m)
}

// The WmsCarFunc type is an adapter to allow the use of ordinary
// function as WmsCar mutator.
type WmsCarFunc func(context.Context, *ent.WmsCarMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsCarFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsCarMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsCarMutation", m)
}

// The WmsClaimOrderFunc type is an adapter to allow the use of ordinary
// function as WmsClaimOrder mutator.
type WmsClaimOrderFunc func(context.Context, *ent.WmsClaimOrderMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsClaimOrderFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsClaimOrderMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsClaimOrderMutation", m)
}

// The WmsClaimOrderDetailFunc type is an adapter to allow the use of ordinary
// function as WmsClaimOrderDetail mutator.
type WmsClaimOrderDetailFunc func(context.Context, *ent.WmsClaimOrderDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsClaimOrderDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsClaimOrderDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsClaimOrderDetailMutation", m)
}

// The WmsCompanyFunc type is an adapter to allow the use of ordinary
// function as WmsCompany mutator.
type WmsCompanyFunc func(context.Context, *ent.WmsCompanyMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsCompanyFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsCompanyMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsCompanyMutation", m)
}

// The WmsCompanyAddressFunc type is an adapter to allow the use of ordinary
// function as WmsCompanyAddress mutator.
type WmsCompanyAddressFunc func(context.Context, *ent.WmsCompanyAddressMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsCompanyAddressFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsCompanyAddressMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsCompanyAddressMutation", m)
}

// The WmsDiscardMeetingFunc type is an adapter to allow the use of ordinary
// function as WmsDiscardMeeting mutator.
type WmsDiscardMeetingFunc func(context.Context, *ent.WmsDiscardMeetingMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsDiscardMeetingFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsDiscardMeetingMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsDiscardMeetingMutation", m)
}

// The WmsDiscardMeetingDetailFunc type is an adapter to allow the use of ordinary
// function as WmsDiscardMeetingDetail mutator.
type WmsDiscardMeetingDetailFunc func(context.Context, *ent.WmsDiscardMeetingDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsDiscardMeetingDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsDiscardMeetingDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsDiscardMeetingDetailMutation", m)
}

// The WmsDiscardOrderFunc type is an adapter to allow the use of ordinary
// function as WmsDiscardOrder mutator.
type WmsDiscardOrderFunc func(context.Context, *ent.WmsDiscardOrderMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsDiscardOrderFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsDiscardOrderMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsDiscardOrderMutation", m)
}

// The WmsDiscardOrderDetailFunc type is an adapter to allow the use of ordinary
// function as WmsDiscardOrderDetail mutator.
type WmsDiscardOrderDetailFunc func(context.Context, *ent.WmsDiscardOrderDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsDiscardOrderDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsDiscardOrderDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsDiscardOrderDetailMutation", m)
}

// The WmsDiscardPlanOrderFunc type is an adapter to allow the use of ordinary
// function as WmsDiscardPlanOrder mutator.
type WmsDiscardPlanOrderFunc func(context.Context, *ent.WmsDiscardPlanOrderMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsDiscardPlanOrderFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsDiscardPlanOrderMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsDiscardPlanOrderMutation", m)
}

// The WmsDiscardPlanOrderDetailFunc type is an adapter to allow the use of ordinary
// function as WmsDiscardPlanOrderDetail mutator.
type WmsDiscardPlanOrderDetailFunc func(context.Context, *ent.WmsDiscardPlanOrderDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsDiscardPlanOrderDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsDiscardPlanOrderDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsDiscardPlanOrderDetailMutation", m)
}

// The WmsDocumentFunc type is an adapter to allow the use of ordinary
// function as WmsDocument mutator.
type WmsDocumentFunc func(context.Context, *ent.WmsDocumentMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsDocumentFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsDocumentMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsDocumentMutation", m)
}

// The WmsEnterRepositoryOrderFunc type is an adapter to allow the use of ordinary
// function as WmsEnterRepositoryOrder mutator.
type WmsEnterRepositoryOrderFunc func(context.Context, *ent.WmsEnterRepositoryOrderMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsEnterRepositoryOrderFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsEnterRepositoryOrderMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsEnterRepositoryOrderMutation", m)
}

// The WmsEnterRepositoryOrderDetailFunc type is an adapter to allow the use of ordinary
// function as WmsEnterRepositoryOrderDetail mutator.
type WmsEnterRepositoryOrderDetailFunc func(context.Context, *ent.WmsEnterRepositoryOrderDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsEnterRepositoryOrderDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsEnterRepositoryOrderDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsEnterRepositoryOrderDetailMutation", m)
}

// The WmsEquipmentFunc type is an adapter to allow the use of ordinary
// function as WmsEquipment mutator.
type WmsEquipmentFunc func(context.Context, *ent.WmsEquipmentMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsEquipmentFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsEquipmentMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsEquipmentMutation", m)
}

// The WmsEquipmentDetailFunc type is an adapter to allow the use of ordinary
// function as WmsEquipmentDetail mutator.
type WmsEquipmentDetailFunc func(context.Context, *ent.WmsEquipmentDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsEquipmentDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsEquipmentDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsEquipmentDetailMutation", m)
}

// The WmsEquipmentTypeFunc type is an adapter to allow the use of ordinary
// function as WmsEquipmentType mutator.
type WmsEquipmentTypeFunc func(context.Context, *ent.WmsEquipmentTypeMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsEquipmentTypeFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsEquipmentTypeMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsEquipmentTypeMutation", m)
}

// The WmsEquipmentTypePropertyFunc type is an adapter to allow the use of ordinary
// function as WmsEquipmentTypeProperty mutator.
type WmsEquipmentTypePropertyFunc func(context.Context, *ent.WmsEquipmentTypePropertyMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsEquipmentTypePropertyFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsEquipmentTypePropertyMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsEquipmentTypePropertyMutation", m)
}

// The WmsEquipmentTypePropertyGroupFunc type is an adapter to allow the use of ordinary
// function as WmsEquipmentTypePropertyGroup mutator.
type WmsEquipmentTypePropertyGroupFunc func(context.Context, *ent.WmsEquipmentTypePropertyGroupMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsEquipmentTypePropertyGroupFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsEquipmentTypePropertyGroupMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsEquipmentTypePropertyGroupMutation", m)
}

// The WmsEquipmentTypePropertyOptionFunc type is an adapter to allow the use of ordinary
// function as WmsEquipmentTypePropertyOption mutator.
type WmsEquipmentTypePropertyOptionFunc func(context.Context, *ent.WmsEquipmentTypePropertyOptionMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsEquipmentTypePropertyOptionFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsEquipmentTypePropertyOptionMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsEquipmentTypePropertyOptionMutation", m)
}

// The WmsEquipmentUserGroupFunc type is an adapter to allow the use of ordinary
// function as WmsEquipmentUserGroup mutator.
type WmsEquipmentUserGroupFunc func(context.Context, *ent.WmsEquipmentUserGroupMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsEquipmentUserGroupFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsEquipmentUserGroupMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsEquipmentUserGroupMutation", m)
}

// The WmsFireStationFunc type is an adapter to allow the use of ordinary
// function as WmsFireStation mutator.
type WmsFireStationFunc func(context.Context, *ent.WmsFireStationMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsFireStationFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsFireStationMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsFireStationMutation", m)
}

// The WmsGPSFunc type is an adapter to allow the use of ordinary
// function as WmsGPS mutator.
type WmsGPSFunc func(context.Context, *ent.WmsGPSMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsGPSFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsGPSMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsGPSMutation", m)
}

// The WmsGPSRecordFunc type is an adapter to allow the use of ordinary
// function as WmsGPSRecord mutator.
type WmsGPSRecordFunc func(context.Context, *ent.WmsGPSRecordMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsGPSRecordFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsGPSRecordMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsGPSRecordMutation", m)
}

// The WmsLearningCourseFunc type is an adapter to allow the use of ordinary
// function as WmsLearningCourse mutator.
type WmsLearningCourseFunc func(context.Context, *ent.WmsLearningCourseMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsLearningCourseFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsLearningCourseMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsLearningCourseMutation", m)
}

// The WmsLearningCourseCoursewareFunc type is an adapter to allow the use of ordinary
// function as WmsLearningCourseCourseware mutator.
type WmsLearningCourseCoursewareFunc func(context.Context, *ent.WmsLearningCourseCoursewareMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsLearningCourseCoursewareFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsLearningCourseCoursewareMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsLearningCourseCoursewareMutation", m)
}

// The WmsLearningCourseLogFunc type is an adapter to allow the use of ordinary
// function as WmsLearningCourseLog mutator.
type WmsLearningCourseLogFunc func(context.Context, *ent.WmsLearningCourseLogMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsLearningCourseLogFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsLearningCourseLogMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsLearningCourseLogMutation", m)
}

// The WmsLearningCourseRecordFunc type is an adapter to allow the use of ordinary
// function as WmsLearningCourseRecord mutator.
type WmsLearningCourseRecordFunc func(context.Context, *ent.WmsLearningCourseRecordMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsLearningCourseRecordFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsLearningCourseRecordMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsLearningCourseRecordMutation", m)
}

// The WmsLearningCoursewareFunc type is an adapter to allow the use of ordinary
// function as WmsLearningCourseware mutator.
type WmsLearningCoursewareFunc func(context.Context, *ent.WmsLearningCoursewareMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsLearningCoursewareFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsLearningCoursewareMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsLearningCoursewareMutation", m)
}

// The WmsLearningCoursewareRecordFunc type is an adapter to allow the use of ordinary
// function as WmsLearningCoursewareRecord mutator.
type WmsLearningCoursewareRecordFunc func(context.Context, *ent.WmsLearningCoursewareRecordMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsLearningCoursewareRecordFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsLearningCoursewareRecordMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsLearningCoursewareRecordMutation", m)
}

// The WmsLearningPlanFunc type is an adapter to allow the use of ordinary
// function as WmsLearningPlan mutator.
type WmsLearningPlanFunc func(context.Context, *ent.WmsLearningPlanMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsLearningPlanFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsLearningPlanMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsLearningPlanMutation", m)
}

// The WmsLearningPlanRecordFunc type is an adapter to allow the use of ordinary
// function as WmsLearningPlanRecord mutator.
type WmsLearningPlanRecordFunc func(context.Context, *ent.WmsLearningPlanRecordMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsLearningPlanRecordFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsLearningPlanRecordMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsLearningPlanRecordMutation", m)
}

// The WmsMaintainOrderFunc type is an adapter to allow the use of ordinary
// function as WmsMaintainOrder mutator.
type WmsMaintainOrderFunc func(context.Context, *ent.WmsMaintainOrderMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsMaintainOrderFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsMaintainOrderMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsMaintainOrderMutation", m)
}

// The WmsMaintainOrderDetailFunc type is an adapter to allow the use of ordinary
// function as WmsMaintainOrderDetail mutator.
type WmsMaintainOrderDetailFunc func(context.Context, *ent.WmsMaintainOrderDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsMaintainOrderDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsMaintainOrderDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsMaintainOrderDetailMutation", m)
}

// The WmsMaintainPlanFunc type is an adapter to allow the use of ordinary
// function as WmsMaintainPlan mutator.
type WmsMaintainPlanFunc func(context.Context, *ent.WmsMaintainPlanMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsMaintainPlanFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsMaintainPlanMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsMaintainPlanMutation", m)
}

// The WmsMaintainPlanDetailFunc type is an adapter to allow the use of ordinary
// function as WmsMaintainPlanDetail mutator.
type WmsMaintainPlanDetailFunc func(context.Context, *ent.WmsMaintainPlanDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsMaintainPlanDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsMaintainPlanDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsMaintainPlanDetailMutation", m)
}

// The WmsMaterialFunc type is an adapter to allow the use of ordinary
// function as WmsMaterial mutator.
type WmsMaterialFunc func(context.Context, *ent.WmsMaterialMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsMaterialFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsMaterialMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsMaterialMutation", m)
}

// The WmsMaterialLogFunc type is an adapter to allow the use of ordinary
// function as WmsMaterialLog mutator.
type WmsMaterialLogFunc func(context.Context, *ent.WmsMaterialLogMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsMaterialLogFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsMaterialLogMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsMaterialLogMutation", m)
}

// The WmsMeasureUnitFunc type is an adapter to allow the use of ordinary
// function as WmsMeasureUnit mutator.
type WmsMeasureUnitFunc func(context.Context, *ent.WmsMeasureUnitMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsMeasureUnitFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsMeasureUnitMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsMeasureUnitMutation", m)
}

// The WmsOperateLogFunc type is an adapter to allow the use of ordinary
// function as WmsOperateLog mutator.
type WmsOperateLogFunc func(context.Context, *ent.WmsOperateLogMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsOperateLogFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsOperateLogMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsOperateLogMutation", m)
}

// The WmsOutRepositoryOrderFunc type is an adapter to allow the use of ordinary
// function as WmsOutRepositoryOrder mutator.
type WmsOutRepositoryOrderFunc func(context.Context, *ent.WmsOutRepositoryOrderMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsOutRepositoryOrderFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsOutRepositoryOrderMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsOutRepositoryOrderMutation", m)
}

// The WmsOutRepositoryOrderDetailFunc type is an adapter to allow the use of ordinary
// function as WmsOutRepositoryOrderDetail mutator.
type WmsOutRepositoryOrderDetailFunc func(context.Context, *ent.WmsOutRepositoryOrderDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsOutRepositoryOrderDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsOutRepositoryOrderDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsOutRepositoryOrderDetailMutation", m)
}

// The WmsPurchaseOrderFunc type is an adapter to allow the use of ordinary
// function as WmsPurchaseOrder mutator.
type WmsPurchaseOrderFunc func(context.Context, *ent.WmsPurchaseOrderMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsPurchaseOrderFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsPurchaseOrderMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsPurchaseOrderMutation", m)
}

// The WmsPurchaseOrderDetailFunc type is an adapter to allow the use of ordinary
// function as WmsPurchaseOrderDetail mutator.
type WmsPurchaseOrderDetailFunc func(context.Context, *ent.WmsPurchaseOrderDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsPurchaseOrderDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsPurchaseOrderDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsPurchaseOrderDetailMutation", m)
}

// The WmsRepairOrderFunc type is an adapter to allow the use of ordinary
// function as WmsRepairOrder mutator.
type WmsRepairOrderFunc func(context.Context, *ent.WmsRepairOrderMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsRepairOrderFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsRepairOrderMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsRepairOrderMutation", m)
}

// The WmsRepairOrderDetailFunc type is an adapter to allow the use of ordinary
// function as WmsRepairOrderDetail mutator.
type WmsRepairOrderDetailFunc func(context.Context, *ent.WmsRepairOrderDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsRepairOrderDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsRepairOrderDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsRepairOrderDetailMutation", m)
}

// The WmsRepairSettlementOrderFunc type is an adapter to allow the use of ordinary
// function as WmsRepairSettlementOrder mutator.
type WmsRepairSettlementOrderFunc func(context.Context, *ent.WmsRepairSettlementOrderMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsRepairSettlementOrderFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsRepairSettlementOrderMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsRepairSettlementOrderMutation", m)
}

// The WmsRepairSettlementOrderSettlementfeeDetailFunc type is an adapter to allow the use of ordinary
// function as WmsRepairSettlementOrderSettlementfeeDetail mutator.
type WmsRepairSettlementOrderSettlementfeeDetailFunc func(context.Context, *ent.WmsRepairSettlementOrderSettlementfeeDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsRepairSettlementOrderSettlementfeeDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsRepairSettlementOrderSettlementfeeDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsRepairSettlementOrderSettlementfeeDetailMutation", m)
}

// The WmsRepairSettlementOrderWorkfeeDetailFunc type is an adapter to allow the use of ordinary
// function as WmsRepairSettlementOrderWorkfeeDetail mutator.
type WmsRepairSettlementOrderWorkfeeDetailFunc func(context.Context, *ent.WmsRepairSettlementOrderWorkfeeDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsRepairSettlementOrderWorkfeeDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsRepairSettlementOrderWorkfeeDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsRepairSettlementOrderWorkfeeDetailMutation", m)
}

// The WmsRepositoryFunc type is an adapter to allow the use of ordinary
// function as WmsRepository mutator.
type WmsRepositoryFunc func(context.Context, *ent.WmsRepositoryMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsRepositoryFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsRepositoryMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsRepositoryMutation", m)
}

// The WmsRepositoryAdminFunc type is an adapter to allow the use of ordinary
// function as WmsRepositoryAdmin mutator.
type WmsRepositoryAdminFunc func(context.Context, *ent.WmsRepositoryAdminMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsRepositoryAdminFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsRepositoryAdminMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsRepositoryAdminMutation", m)
}

// The WmsRepositoryAreaFunc type is an adapter to allow the use of ordinary
// function as WmsRepositoryArea mutator.
type WmsRepositoryAreaFunc func(context.Context, *ent.WmsRepositoryAreaMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsRepositoryAreaFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsRepositoryAreaMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsRepositoryAreaMutation", m)
}

// The WmsRepositoryPositionFunc type is an adapter to allow the use of ordinary
// function as WmsRepositoryPosition mutator.
type WmsRepositoryPositionFunc func(context.Context, *ent.WmsRepositoryPositionMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsRepositoryPositionFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsRepositoryPositionMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsRepositoryPositionMutation", m)
}

// The WmsRepositoryScreenFunc type is an adapter to allow the use of ordinary
// function as WmsRepositoryScreen mutator.
type WmsRepositoryScreenFunc func(context.Context, *ent.WmsRepositoryScreenMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsRepositoryScreenFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsRepositoryScreenMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsRepositoryScreenMutation", m)
}

// The WmsRepositoryScreenRepositoryAreaFunc type is an adapter to allow the use of ordinary
// function as WmsRepositoryScreenRepositoryArea mutator.
type WmsRepositoryScreenRepositoryAreaFunc func(context.Context, *ent.WmsRepositoryScreenRepositoryAreaMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsRepositoryScreenRepositoryAreaFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsRepositoryScreenRepositoryAreaMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsRepositoryScreenRepositoryAreaMutation", m)
}

// The WmsReturnOrderFunc type is an adapter to allow the use of ordinary
// function as WmsReturnOrder mutator.
type WmsReturnOrderFunc func(context.Context, *ent.WmsReturnOrderMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsReturnOrderFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsReturnOrderMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsReturnOrderMutation", m)
}

// The WmsReturnOrderDetailFunc type is an adapter to allow the use of ordinary
// function as WmsReturnOrderDetail mutator.
type WmsReturnOrderDetailFunc func(context.Context, *ent.WmsReturnOrderDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsReturnOrderDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsReturnOrderDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsReturnOrderDetailMutation", m)
}

// The WmsRfidFunc type is an adapter to allow the use of ordinary
// function as WmsRfid mutator.
type WmsRfidFunc func(context.Context, *ent.WmsRfidMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsRfidFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsRfidMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsRfidMutation", m)
}

// The WmsRfidReaderFunc type is an adapter to allow the use of ordinary
// function as WmsRfidReader mutator.
type WmsRfidReaderFunc func(context.Context, *ent.WmsRfidReaderMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsRfidReaderFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsRfidReaderMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsRfidReaderMutation", m)
}

// The WmsRfidReaderRecordFunc type is an adapter to allow the use of ordinary
// function as WmsRfidReaderRecord mutator.
type WmsRfidReaderRecordFunc func(context.Context, *ent.WmsRfidReaderRecordMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsRfidReaderRecordFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsRfidReaderRecordMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsRfidReaderRecordMutation", m)
}

// The WmsTransferOrderFunc type is an adapter to allow the use of ordinary
// function as WmsTransferOrder mutator.
type WmsTransferOrderFunc func(context.Context, *ent.WmsTransferOrderMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsTransferOrderFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsTransferOrderMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsTransferOrderMutation", m)
}

// The WmsTransferOrderDetailFunc type is an adapter to allow the use of ordinary
// function as WmsTransferOrderDetail mutator.
type WmsTransferOrderDetailFunc func(context.Context, *ent.WmsTransferOrderDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsTransferOrderDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsTransferOrderDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsTransferOrderDetailMutation", m)
}

// The WmsVehicleRepairOrderFunc type is an adapter to allow the use of ordinary
// function as WmsVehicleRepairOrder mutator.
type WmsVehicleRepairOrderFunc func(context.Context, *ent.WmsVehicleRepairOrderMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsVehicleRepairOrderFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsVehicleRepairOrderMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsVehicleRepairOrderMutation", m)
}

// The WmsVehicleRepairOrderMaterialfeeDetailFunc type is an adapter to allow the use of ordinary
// function as WmsVehicleRepairOrderMaterialfeeDetail mutator.
type WmsVehicleRepairOrderMaterialfeeDetailFunc func(context.Context, *ent.WmsVehicleRepairOrderMaterialfeeDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsVehicleRepairOrderMaterialfeeDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsVehicleRepairOrderMaterialfeeDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsVehicleRepairOrderMaterialfeeDetailMutation", m)
}

// The WmsVehicleRepairOrderSettlementfeeDetailFunc type is an adapter to allow the use of ordinary
// function as WmsVehicleRepairOrderSettlementfeeDetail mutator.
type WmsVehicleRepairOrderSettlementfeeDetailFunc func(context.Context, *ent.WmsVehicleRepairOrderSettlementfeeDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsVehicleRepairOrderSettlementfeeDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsVehicleRepairOrderSettlementfeeDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsVehicleRepairOrderSettlementfeeDetailMutation", m)
}

// The WmsVehicleRepairOrderWorkfeeDetailFunc type is an adapter to allow the use of ordinary
// function as WmsVehicleRepairOrderWorkfeeDetail mutator.
type WmsVehicleRepairOrderWorkfeeDetailFunc func(context.Context, *ent.WmsVehicleRepairOrderWorkfeeDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WmsVehicleRepairOrderWorkfeeDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WmsVehicleRepairOrderWorkfeeDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WmsVehicleRepairOrderWorkfeeDetailMutation", m)
}

// The WorkWxApprovalMessageFunc type is an adapter to allow the use of ordinary
// function as WorkWxApprovalMessage mutator.
type WorkWxApprovalMessageFunc func(context.Context, *ent.WorkWxApprovalMessageMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WorkWxApprovalMessageFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WorkWxApprovalMessageMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WorkWxApprovalMessageMutation", m)
}

// The WorkWxApprovalNodeFunc type is an adapter to allow the use of ordinary
// function as WorkWxApprovalNode mutator.
type WorkWxApprovalNodeFunc func(context.Context, *ent.WorkWxApprovalNodeMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WorkWxApprovalNodeFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WorkWxApprovalNodeMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WorkWxApprovalNodeMutation", m)
}

// The WorkWxNotifyNodeFunc type is an adapter to allow the use of ordinary
// function as WorkWxNotifyNode mutator.
type WorkWxNotifyNodeFunc func(context.Context, *ent.WorkWxNotifyNodeMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WorkWxNotifyNodeFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WorkWxNotifyNodeMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WorkWxNotifyNodeMutation", m)
}

// Condition is a hook condition function.
type Condition func(context.Context, ent.Mutation) bool

// And groups conditions with the AND operator.
func And(first, second Condition, rest ...Condition) Condition {
	return func(ctx context.Context, m ent.Mutation) bool {
		if !first(ctx, m) || !second(ctx, m) {
			return false
		}
		for _, cond := range rest {
			if !cond(ctx, m) {
				return false
			}
		}
		return true
	}
}

// Or groups conditions with the OR operator.
func Or(first, second Condition, rest ...Condition) Condition {
	return func(ctx context.Context, m ent.Mutation) bool {
		if first(ctx, m) || second(ctx, m) {
			return true
		}
		for _, cond := range rest {
			if cond(ctx, m) {
				return true
			}
		}
		return false
	}
}

// Not negates a given condition.
func Not(cond Condition) Condition {
	return func(ctx context.Context, m ent.Mutation) bool {
		return !cond(ctx, m)
	}
}

// HasOp is a condition testing mutation operation.
func HasOp(op ent.Op) Condition {
	return func(_ context.Context, m ent.Mutation) bool {
		return m.Op().Is(op)
	}
}

// HasAddedFields is a condition validating `.AddedField` on fields.
func HasAddedFields(field string, fields ...string) Condition {
	return func(_ context.Context, m ent.Mutation) bool {
		if _, exists := m.AddedField(field); !exists {
			return false
		}
		for _, field := range fields {
			if _, exists := m.AddedField(field); !exists {
				return false
			}
		}
		return true
	}
}

// HasClearedFields is a condition validating `.FieldCleared` on fields.
func HasClearedFields(field string, fields ...string) Condition {
	return func(_ context.Context, m ent.Mutation) bool {
		if exists := m.FieldCleared(field); !exists {
			return false
		}
		for _, field := range fields {
			if exists := m.FieldCleared(field); !exists {
				return false
			}
		}
		return true
	}
}

// HasFields is a condition validating `.Field` on fields.
func HasFields(field string, fields ...string) Condition {
	return func(_ context.Context, m ent.Mutation) bool {
		if _, exists := m.Field(field); !exists {
			return false
		}
		for _, field := range fields {
			if _, exists := m.Field(field); !exists {
				return false
			}
		}
		return true
	}
}

// If executes the given hook under condition.
//
//	hook.If(ComputeAverage, And(HasFields(...), HasAddedFields(...)))
func If(hk ent.Hook, cond Condition) ent.Hook {
	return func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if cond(ctx, m) {
				return hk(next).Mutate(ctx, m)
			}
			return next.Mutate(ctx, m)
		})
	}
}

// On executes the given hook only for the given operation.
//
//	hook.On(Log, ent.Delete|ent.Create)
func On(hk ent.Hook, op ent.Op) ent.Hook {
	return If(hk, HasOp(op))
}

// Unless skips the given hook only for the given operation.
//
//	hook.Unless(Log, ent.Update|ent.UpdateOne)
func Unless(hk ent.Hook, op ent.Op) ent.Hook {
	return If(hk, Not(HasOp(op)))
}

// FixedError is a hook returning a fixed error.
func FixedError(err error) ent.Hook {
	return func(ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(context.Context, ent.Mutation) (ent.Value, error) {
			return nil, err
		})
	}
}

// Reject returns a hook that rejects all operations that match op.
//
//	func (T) Hooks() []ent.Hook {
//		return []ent.Hook{
//			Reject(ent.Delete|ent.Update),
//		}
//	}
func Reject(op ent.Op) ent.Hook {
	hk := FixedError(fmt.Errorf("%s operation is not allowed", op))
	return On(hk, op)
}

// Chain acts as a list of hooks and is effectively immutable.
// Once created, it will always hold the same set of hooks in the same order.
type Chain struct {
	hooks []ent.Hook
}

// NewChain creates a new chain of hooks.
func NewChain(hooks ...ent.Hook) Chain {
	return Chain{append([]ent.Hook(nil), hooks...)}
}

// Hook chains the list of hooks and returns the final hook.
func (c Chain) Hook() ent.Hook {
	return func(mutator ent.Mutator) ent.Mutator {
		for i := len(c.hooks) - 1; i >= 0; i-- {
			mutator = c.hooks[i](mutator)
		}
		return mutator
	}
}

// Append extends a chain, adding the specified hook
// as the last ones in the mutation flow.
func (c Chain) Append(hooks ...ent.Hook) Chain {
	newHooks := make([]ent.Hook, 0, len(c.hooks)+len(hooks))
	newHooks = append(newHooks, c.hooks...)
	newHooks = append(newHooks, hooks...)
	return Chain{newHooks}
}

// Extend extends a chain, adding the specified chain
// as the last ones in the mutation flow.
func (c Chain) Extend(chain Chain) Chain {
	return c.Append(chain.hooks...)
}
