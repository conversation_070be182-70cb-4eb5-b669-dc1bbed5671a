// Code generated by ent, DO NOT EDIT.

package wmsdiscardorderdetail

import (
	"kratos-mono-demo/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContainsFold(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldUpdatedAt, v))
}

// CreatedBy applies equality check predicate on the "created_by" field. It's identical to CreatedByEQ.
func CreatedBy(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldCreatedBy, v))
}

// UpdatedBy applies equality check predicate on the "updated_by" field. It's identical to UpdatedByEQ.
func UpdatedBy(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldUpdatedBy, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldRemark, v))
}

// OrderID applies equality check predicate on the "order_id" field. It's identical to OrderIDEQ.
func OrderID(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldOrderID, v))
}

// MaterialID applies equality check predicate on the "material_id" field. It's identical to MaterialIDEQ.
func MaterialID(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldMaterialID, v))
}

// MaterialName applies equality check predicate on the "material_name" field. It's identical to MaterialNameEQ.
func MaterialName(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldMaterialName, v))
}

// Code applies equality check predicate on the "code" field. It's identical to CodeEQ.
func Code(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldCode, v))
}

// EquipmentID applies equality check predicate on the "equipment_id" field. It's identical to EquipmentIDEQ.
func EquipmentID(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldEquipmentID, v))
}

// EquipmentTypeID applies equality check predicate on the "equipment_type_id" field. It's identical to EquipmentTypeIDEQ.
func EquipmentTypeID(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldEquipmentTypeID, v))
}

// OwnerID applies equality check predicate on the "owner_id" field. It's identical to OwnerIDEQ.
func OwnerID(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldOwnerID, v))
}

// ModelNo applies equality check predicate on the "ModelNo" field. It's identical to ModelNoEQ.
func ModelNo(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldModelNo, v))
}

// MeasureUnitID applies equality check predicate on the "measure_unit_id" field. It's identical to MeasureUnitIDEQ.
func MeasureUnitID(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldMeasureUnitID, v))
}

// Num applies equality check predicate on the "num" field. It's identical to NumEQ.
func Num(v uint32) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldNum, v))
}

// RepositoryID applies equality check predicate on the "repository_id" field. It's identical to RepositoryIDEQ.
func RepositoryID(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldRepositoryID, v))
}

// RepositoryAreaID applies equality check predicate on the "repository_area_id" field. It's identical to RepositoryAreaIDEQ.
func RepositoryAreaID(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldRepositoryAreaID, v))
}

// RepositoryPositionID applies equality check predicate on the "repository_position_id" field. It's identical to RepositoryPositionIDEQ.
func RepositoryPositionID(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldRepositoryPositionID, v))
}

// Reason applies equality check predicate on the "reason" field. It's identical to ReasonEQ.
func Reason(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldReason, v))
}

// DiscardTime applies equality check predicate on the "discard_time" field. It's identical to DiscardTimeEQ.
func DiscardTime(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldDiscardTime, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldUpdatedAt, v))
}

// CreatedByEQ applies the EQ predicate on the "created_by" field.
func CreatedByEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldCreatedBy, v))
}

// CreatedByNEQ applies the NEQ predicate on the "created_by" field.
func CreatedByNEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldCreatedBy, v))
}

// CreatedByIn applies the In predicate on the "created_by" field.
func CreatedByIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldCreatedBy, vs...))
}

// CreatedByNotIn applies the NotIn predicate on the "created_by" field.
func CreatedByNotIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldCreatedBy, vs...))
}

// CreatedByGT applies the GT predicate on the "created_by" field.
func CreatedByGT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldCreatedBy, v))
}

// CreatedByGTE applies the GTE predicate on the "created_by" field.
func CreatedByGTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldCreatedBy, v))
}

// CreatedByLT applies the LT predicate on the "created_by" field.
func CreatedByLT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldCreatedBy, v))
}

// CreatedByLTE applies the LTE predicate on the "created_by" field.
func CreatedByLTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldCreatedBy, v))
}

// CreatedByContains applies the Contains predicate on the "created_by" field.
func CreatedByContains(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContains(FieldCreatedBy, v))
}

// CreatedByHasPrefix applies the HasPrefix predicate on the "created_by" field.
func CreatedByHasPrefix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasPrefix(FieldCreatedBy, v))
}

// CreatedByHasSuffix applies the HasSuffix predicate on the "created_by" field.
func CreatedByHasSuffix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasSuffix(FieldCreatedBy, v))
}

// CreatedByIsNil applies the IsNil predicate on the "created_by" field.
func CreatedByIsNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIsNull(FieldCreatedBy))
}

// CreatedByNotNil applies the NotNil predicate on the "created_by" field.
func CreatedByNotNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotNull(FieldCreatedBy))
}

// CreatedByEqualFold applies the EqualFold predicate on the "created_by" field.
func CreatedByEqualFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEqualFold(FieldCreatedBy, v))
}

// CreatedByContainsFold applies the ContainsFold predicate on the "created_by" field.
func CreatedByContainsFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContainsFold(FieldCreatedBy, v))
}

// UpdatedByEQ applies the EQ predicate on the "updated_by" field.
func UpdatedByEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldUpdatedBy, v))
}

// UpdatedByNEQ applies the NEQ predicate on the "updated_by" field.
func UpdatedByNEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldUpdatedBy, v))
}

// UpdatedByIn applies the In predicate on the "updated_by" field.
func UpdatedByIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldUpdatedBy, vs...))
}

// UpdatedByNotIn applies the NotIn predicate on the "updated_by" field.
func UpdatedByNotIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldUpdatedBy, vs...))
}

// UpdatedByGT applies the GT predicate on the "updated_by" field.
func UpdatedByGT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldUpdatedBy, v))
}

// UpdatedByGTE applies the GTE predicate on the "updated_by" field.
func UpdatedByGTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldUpdatedBy, v))
}

// UpdatedByLT applies the LT predicate on the "updated_by" field.
func UpdatedByLT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldUpdatedBy, v))
}

// UpdatedByLTE applies the LTE predicate on the "updated_by" field.
func UpdatedByLTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldUpdatedBy, v))
}

// UpdatedByContains applies the Contains predicate on the "updated_by" field.
func UpdatedByContains(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContains(FieldUpdatedBy, v))
}

// UpdatedByHasPrefix applies the HasPrefix predicate on the "updated_by" field.
func UpdatedByHasPrefix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasPrefix(FieldUpdatedBy, v))
}

// UpdatedByHasSuffix applies the HasSuffix predicate on the "updated_by" field.
func UpdatedByHasSuffix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasSuffix(FieldUpdatedBy, v))
}

// UpdatedByIsNil applies the IsNil predicate on the "updated_by" field.
func UpdatedByIsNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIsNull(FieldUpdatedBy))
}

// UpdatedByNotNil applies the NotNil predicate on the "updated_by" field.
func UpdatedByNotNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotNull(FieldUpdatedBy))
}

// UpdatedByEqualFold applies the EqualFold predicate on the "updated_by" field.
func UpdatedByEqualFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEqualFold(FieldUpdatedBy, v))
}

// UpdatedByContainsFold applies the ContainsFold predicate on the "updated_by" field.
func UpdatedByContainsFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContainsFold(FieldUpdatedBy, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContainsFold(FieldRemark, v))
}

// OrderIDEQ applies the EQ predicate on the "order_id" field.
func OrderIDEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldOrderID, v))
}

// OrderIDNEQ applies the NEQ predicate on the "order_id" field.
func OrderIDNEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldOrderID, v))
}

// OrderIDIn applies the In predicate on the "order_id" field.
func OrderIDIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldOrderID, vs...))
}

// OrderIDNotIn applies the NotIn predicate on the "order_id" field.
func OrderIDNotIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldOrderID, vs...))
}

// OrderIDGT applies the GT predicate on the "order_id" field.
func OrderIDGT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldOrderID, v))
}

// OrderIDGTE applies the GTE predicate on the "order_id" field.
func OrderIDGTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldOrderID, v))
}

// OrderIDLT applies the LT predicate on the "order_id" field.
func OrderIDLT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldOrderID, v))
}

// OrderIDLTE applies the LTE predicate on the "order_id" field.
func OrderIDLTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldOrderID, v))
}

// OrderIDContains applies the Contains predicate on the "order_id" field.
func OrderIDContains(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContains(FieldOrderID, v))
}

// OrderIDHasPrefix applies the HasPrefix predicate on the "order_id" field.
func OrderIDHasPrefix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasPrefix(FieldOrderID, v))
}

// OrderIDHasSuffix applies the HasSuffix predicate on the "order_id" field.
func OrderIDHasSuffix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasSuffix(FieldOrderID, v))
}

// OrderIDIsNil applies the IsNil predicate on the "order_id" field.
func OrderIDIsNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIsNull(FieldOrderID))
}

// OrderIDNotNil applies the NotNil predicate on the "order_id" field.
func OrderIDNotNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotNull(FieldOrderID))
}

// OrderIDEqualFold applies the EqualFold predicate on the "order_id" field.
func OrderIDEqualFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEqualFold(FieldOrderID, v))
}

// OrderIDContainsFold applies the ContainsFold predicate on the "order_id" field.
func OrderIDContainsFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContainsFold(FieldOrderID, v))
}

// MaterialIDEQ applies the EQ predicate on the "material_id" field.
func MaterialIDEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldMaterialID, v))
}

// MaterialIDNEQ applies the NEQ predicate on the "material_id" field.
func MaterialIDNEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldMaterialID, v))
}

// MaterialIDIn applies the In predicate on the "material_id" field.
func MaterialIDIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldMaterialID, vs...))
}

// MaterialIDNotIn applies the NotIn predicate on the "material_id" field.
func MaterialIDNotIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldMaterialID, vs...))
}

// MaterialIDGT applies the GT predicate on the "material_id" field.
func MaterialIDGT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldMaterialID, v))
}

// MaterialIDGTE applies the GTE predicate on the "material_id" field.
func MaterialIDGTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldMaterialID, v))
}

// MaterialIDLT applies the LT predicate on the "material_id" field.
func MaterialIDLT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldMaterialID, v))
}

// MaterialIDLTE applies the LTE predicate on the "material_id" field.
func MaterialIDLTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldMaterialID, v))
}

// MaterialIDContains applies the Contains predicate on the "material_id" field.
func MaterialIDContains(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContains(FieldMaterialID, v))
}

// MaterialIDHasPrefix applies the HasPrefix predicate on the "material_id" field.
func MaterialIDHasPrefix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasPrefix(FieldMaterialID, v))
}

// MaterialIDHasSuffix applies the HasSuffix predicate on the "material_id" field.
func MaterialIDHasSuffix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasSuffix(FieldMaterialID, v))
}

// MaterialIDIsNil applies the IsNil predicate on the "material_id" field.
func MaterialIDIsNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIsNull(FieldMaterialID))
}

// MaterialIDNotNil applies the NotNil predicate on the "material_id" field.
func MaterialIDNotNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotNull(FieldMaterialID))
}

// MaterialIDEqualFold applies the EqualFold predicate on the "material_id" field.
func MaterialIDEqualFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEqualFold(FieldMaterialID, v))
}

// MaterialIDContainsFold applies the ContainsFold predicate on the "material_id" field.
func MaterialIDContainsFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContainsFold(FieldMaterialID, v))
}

// MaterialNameEQ applies the EQ predicate on the "material_name" field.
func MaterialNameEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldMaterialName, v))
}

// MaterialNameNEQ applies the NEQ predicate on the "material_name" field.
func MaterialNameNEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldMaterialName, v))
}

// MaterialNameIn applies the In predicate on the "material_name" field.
func MaterialNameIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldMaterialName, vs...))
}

// MaterialNameNotIn applies the NotIn predicate on the "material_name" field.
func MaterialNameNotIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldMaterialName, vs...))
}

// MaterialNameGT applies the GT predicate on the "material_name" field.
func MaterialNameGT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldMaterialName, v))
}

// MaterialNameGTE applies the GTE predicate on the "material_name" field.
func MaterialNameGTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldMaterialName, v))
}

// MaterialNameLT applies the LT predicate on the "material_name" field.
func MaterialNameLT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldMaterialName, v))
}

// MaterialNameLTE applies the LTE predicate on the "material_name" field.
func MaterialNameLTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldMaterialName, v))
}

// MaterialNameContains applies the Contains predicate on the "material_name" field.
func MaterialNameContains(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContains(FieldMaterialName, v))
}

// MaterialNameHasPrefix applies the HasPrefix predicate on the "material_name" field.
func MaterialNameHasPrefix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasPrefix(FieldMaterialName, v))
}

// MaterialNameHasSuffix applies the HasSuffix predicate on the "material_name" field.
func MaterialNameHasSuffix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasSuffix(FieldMaterialName, v))
}

// MaterialNameIsNil applies the IsNil predicate on the "material_name" field.
func MaterialNameIsNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIsNull(FieldMaterialName))
}

// MaterialNameNotNil applies the NotNil predicate on the "material_name" field.
func MaterialNameNotNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotNull(FieldMaterialName))
}

// MaterialNameEqualFold applies the EqualFold predicate on the "material_name" field.
func MaterialNameEqualFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEqualFold(FieldMaterialName, v))
}

// MaterialNameContainsFold applies the ContainsFold predicate on the "material_name" field.
func MaterialNameContainsFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContainsFold(FieldMaterialName, v))
}

// CodeEQ applies the EQ predicate on the "code" field.
func CodeEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldCode, v))
}

// CodeNEQ applies the NEQ predicate on the "code" field.
func CodeNEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldCode, v))
}

// CodeIn applies the In predicate on the "code" field.
func CodeIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldCode, vs...))
}

// CodeNotIn applies the NotIn predicate on the "code" field.
func CodeNotIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldCode, vs...))
}

// CodeGT applies the GT predicate on the "code" field.
func CodeGT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldCode, v))
}

// CodeGTE applies the GTE predicate on the "code" field.
func CodeGTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldCode, v))
}

// CodeLT applies the LT predicate on the "code" field.
func CodeLT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldCode, v))
}

// CodeLTE applies the LTE predicate on the "code" field.
func CodeLTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldCode, v))
}

// CodeContains applies the Contains predicate on the "code" field.
func CodeContains(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContains(FieldCode, v))
}

// CodeHasPrefix applies the HasPrefix predicate on the "code" field.
func CodeHasPrefix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasPrefix(FieldCode, v))
}

// CodeHasSuffix applies the HasSuffix predicate on the "code" field.
func CodeHasSuffix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasSuffix(FieldCode, v))
}

// CodeEqualFold applies the EqualFold predicate on the "code" field.
func CodeEqualFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEqualFold(FieldCode, v))
}

// CodeContainsFold applies the ContainsFold predicate on the "code" field.
func CodeContainsFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContainsFold(FieldCode, v))
}

// EquipmentIDEQ applies the EQ predicate on the "equipment_id" field.
func EquipmentIDEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldEquipmentID, v))
}

// EquipmentIDNEQ applies the NEQ predicate on the "equipment_id" field.
func EquipmentIDNEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldEquipmentID, v))
}

// EquipmentIDIn applies the In predicate on the "equipment_id" field.
func EquipmentIDIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldEquipmentID, vs...))
}

// EquipmentIDNotIn applies the NotIn predicate on the "equipment_id" field.
func EquipmentIDNotIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldEquipmentID, vs...))
}

// EquipmentIDGT applies the GT predicate on the "equipment_id" field.
func EquipmentIDGT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldEquipmentID, v))
}

// EquipmentIDGTE applies the GTE predicate on the "equipment_id" field.
func EquipmentIDGTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldEquipmentID, v))
}

// EquipmentIDLT applies the LT predicate on the "equipment_id" field.
func EquipmentIDLT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldEquipmentID, v))
}

// EquipmentIDLTE applies the LTE predicate on the "equipment_id" field.
func EquipmentIDLTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldEquipmentID, v))
}

// EquipmentIDContains applies the Contains predicate on the "equipment_id" field.
func EquipmentIDContains(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContains(FieldEquipmentID, v))
}

// EquipmentIDHasPrefix applies the HasPrefix predicate on the "equipment_id" field.
func EquipmentIDHasPrefix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasPrefix(FieldEquipmentID, v))
}

// EquipmentIDHasSuffix applies the HasSuffix predicate on the "equipment_id" field.
func EquipmentIDHasSuffix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasSuffix(FieldEquipmentID, v))
}

// EquipmentIDIsNil applies the IsNil predicate on the "equipment_id" field.
func EquipmentIDIsNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIsNull(FieldEquipmentID))
}

// EquipmentIDNotNil applies the NotNil predicate on the "equipment_id" field.
func EquipmentIDNotNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotNull(FieldEquipmentID))
}

// EquipmentIDEqualFold applies the EqualFold predicate on the "equipment_id" field.
func EquipmentIDEqualFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEqualFold(FieldEquipmentID, v))
}

// EquipmentIDContainsFold applies the ContainsFold predicate on the "equipment_id" field.
func EquipmentIDContainsFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContainsFold(FieldEquipmentID, v))
}

// EquipmentTypeIDEQ applies the EQ predicate on the "equipment_type_id" field.
func EquipmentTypeIDEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDNEQ applies the NEQ predicate on the "equipment_type_id" field.
func EquipmentTypeIDNEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDIn applies the In predicate on the "equipment_type_id" field.
func EquipmentTypeIDIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldEquipmentTypeID, vs...))
}

// EquipmentTypeIDNotIn applies the NotIn predicate on the "equipment_type_id" field.
func EquipmentTypeIDNotIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldEquipmentTypeID, vs...))
}

// EquipmentTypeIDGT applies the GT predicate on the "equipment_type_id" field.
func EquipmentTypeIDGT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDGTE applies the GTE predicate on the "equipment_type_id" field.
func EquipmentTypeIDGTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDLT applies the LT predicate on the "equipment_type_id" field.
func EquipmentTypeIDLT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDLTE applies the LTE predicate on the "equipment_type_id" field.
func EquipmentTypeIDLTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDContains applies the Contains predicate on the "equipment_type_id" field.
func EquipmentTypeIDContains(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContains(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDHasPrefix applies the HasPrefix predicate on the "equipment_type_id" field.
func EquipmentTypeIDHasPrefix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasPrefix(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDHasSuffix applies the HasSuffix predicate on the "equipment_type_id" field.
func EquipmentTypeIDHasSuffix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasSuffix(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDIsNil applies the IsNil predicate on the "equipment_type_id" field.
func EquipmentTypeIDIsNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIsNull(FieldEquipmentTypeID))
}

// EquipmentTypeIDNotNil applies the NotNil predicate on the "equipment_type_id" field.
func EquipmentTypeIDNotNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotNull(FieldEquipmentTypeID))
}

// EquipmentTypeIDEqualFold applies the EqualFold predicate on the "equipment_type_id" field.
func EquipmentTypeIDEqualFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEqualFold(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDContainsFold applies the ContainsFold predicate on the "equipment_type_id" field.
func EquipmentTypeIDContainsFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContainsFold(FieldEquipmentTypeID, v))
}

// FeatureIsNil applies the IsNil predicate on the "feature" field.
func FeatureIsNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIsNull(FieldFeature))
}

// FeatureNotNil applies the NotNil predicate on the "feature" field.
func FeatureNotNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotNull(FieldFeature))
}

// OwnerIDEQ applies the EQ predicate on the "owner_id" field.
func OwnerIDEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldOwnerID, v))
}

// OwnerIDNEQ applies the NEQ predicate on the "owner_id" field.
func OwnerIDNEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldOwnerID, v))
}

// OwnerIDIn applies the In predicate on the "owner_id" field.
func OwnerIDIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldOwnerID, vs...))
}

// OwnerIDNotIn applies the NotIn predicate on the "owner_id" field.
func OwnerIDNotIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldOwnerID, vs...))
}

// OwnerIDGT applies the GT predicate on the "owner_id" field.
func OwnerIDGT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldOwnerID, v))
}

// OwnerIDGTE applies the GTE predicate on the "owner_id" field.
func OwnerIDGTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldOwnerID, v))
}

// OwnerIDLT applies the LT predicate on the "owner_id" field.
func OwnerIDLT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldOwnerID, v))
}

// OwnerIDLTE applies the LTE predicate on the "owner_id" field.
func OwnerIDLTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldOwnerID, v))
}

// OwnerIDContains applies the Contains predicate on the "owner_id" field.
func OwnerIDContains(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContains(FieldOwnerID, v))
}

// OwnerIDHasPrefix applies the HasPrefix predicate on the "owner_id" field.
func OwnerIDHasPrefix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasPrefix(FieldOwnerID, v))
}

// OwnerIDHasSuffix applies the HasSuffix predicate on the "owner_id" field.
func OwnerIDHasSuffix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasSuffix(FieldOwnerID, v))
}

// OwnerIDIsNil applies the IsNil predicate on the "owner_id" field.
func OwnerIDIsNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIsNull(FieldOwnerID))
}

// OwnerIDNotNil applies the NotNil predicate on the "owner_id" field.
func OwnerIDNotNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotNull(FieldOwnerID))
}

// OwnerIDEqualFold applies the EqualFold predicate on the "owner_id" field.
func OwnerIDEqualFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEqualFold(FieldOwnerID, v))
}

// OwnerIDContainsFold applies the ContainsFold predicate on the "owner_id" field.
func OwnerIDContainsFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContainsFold(FieldOwnerID, v))
}

// ModelNoEQ applies the EQ predicate on the "ModelNo" field.
func ModelNoEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldModelNo, v))
}

// ModelNoNEQ applies the NEQ predicate on the "ModelNo" field.
func ModelNoNEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldModelNo, v))
}

// ModelNoIn applies the In predicate on the "ModelNo" field.
func ModelNoIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldModelNo, vs...))
}

// ModelNoNotIn applies the NotIn predicate on the "ModelNo" field.
func ModelNoNotIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldModelNo, vs...))
}

// ModelNoGT applies the GT predicate on the "ModelNo" field.
func ModelNoGT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldModelNo, v))
}

// ModelNoGTE applies the GTE predicate on the "ModelNo" field.
func ModelNoGTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldModelNo, v))
}

// ModelNoLT applies the LT predicate on the "ModelNo" field.
func ModelNoLT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldModelNo, v))
}

// ModelNoLTE applies the LTE predicate on the "ModelNo" field.
func ModelNoLTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldModelNo, v))
}

// ModelNoContains applies the Contains predicate on the "ModelNo" field.
func ModelNoContains(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContains(FieldModelNo, v))
}

// ModelNoHasPrefix applies the HasPrefix predicate on the "ModelNo" field.
func ModelNoHasPrefix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasPrefix(FieldModelNo, v))
}

// ModelNoHasSuffix applies the HasSuffix predicate on the "ModelNo" field.
func ModelNoHasSuffix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasSuffix(FieldModelNo, v))
}

// ModelNoEqualFold applies the EqualFold predicate on the "ModelNo" field.
func ModelNoEqualFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEqualFold(FieldModelNo, v))
}

// ModelNoContainsFold applies the ContainsFold predicate on the "ModelNo" field.
func ModelNoContainsFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContainsFold(FieldModelNo, v))
}

// MeasureUnitIDEQ applies the EQ predicate on the "measure_unit_id" field.
func MeasureUnitIDEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldMeasureUnitID, v))
}

// MeasureUnitIDNEQ applies the NEQ predicate on the "measure_unit_id" field.
func MeasureUnitIDNEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldMeasureUnitID, v))
}

// MeasureUnitIDIn applies the In predicate on the "measure_unit_id" field.
func MeasureUnitIDIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldMeasureUnitID, vs...))
}

// MeasureUnitIDNotIn applies the NotIn predicate on the "measure_unit_id" field.
func MeasureUnitIDNotIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldMeasureUnitID, vs...))
}

// MeasureUnitIDGT applies the GT predicate on the "measure_unit_id" field.
func MeasureUnitIDGT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldMeasureUnitID, v))
}

// MeasureUnitIDGTE applies the GTE predicate on the "measure_unit_id" field.
func MeasureUnitIDGTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldMeasureUnitID, v))
}

// MeasureUnitIDLT applies the LT predicate on the "measure_unit_id" field.
func MeasureUnitIDLT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldMeasureUnitID, v))
}

// MeasureUnitIDLTE applies the LTE predicate on the "measure_unit_id" field.
func MeasureUnitIDLTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldMeasureUnitID, v))
}

// MeasureUnitIDContains applies the Contains predicate on the "measure_unit_id" field.
func MeasureUnitIDContains(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContains(FieldMeasureUnitID, v))
}

// MeasureUnitIDHasPrefix applies the HasPrefix predicate on the "measure_unit_id" field.
func MeasureUnitIDHasPrefix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasPrefix(FieldMeasureUnitID, v))
}

// MeasureUnitIDHasSuffix applies the HasSuffix predicate on the "measure_unit_id" field.
func MeasureUnitIDHasSuffix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasSuffix(FieldMeasureUnitID, v))
}

// MeasureUnitIDIsNil applies the IsNil predicate on the "measure_unit_id" field.
func MeasureUnitIDIsNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIsNull(FieldMeasureUnitID))
}

// MeasureUnitIDNotNil applies the NotNil predicate on the "measure_unit_id" field.
func MeasureUnitIDNotNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotNull(FieldMeasureUnitID))
}

// MeasureUnitIDEqualFold applies the EqualFold predicate on the "measure_unit_id" field.
func MeasureUnitIDEqualFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEqualFold(FieldMeasureUnitID, v))
}

// MeasureUnitIDContainsFold applies the ContainsFold predicate on the "measure_unit_id" field.
func MeasureUnitIDContainsFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContainsFold(FieldMeasureUnitID, v))
}

// NumEQ applies the EQ predicate on the "num" field.
func NumEQ(v uint32) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldNum, v))
}

// NumNEQ applies the NEQ predicate on the "num" field.
func NumNEQ(v uint32) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldNum, v))
}

// NumIn applies the In predicate on the "num" field.
func NumIn(vs ...uint32) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldNum, vs...))
}

// NumNotIn applies the NotIn predicate on the "num" field.
func NumNotIn(vs ...uint32) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldNum, vs...))
}

// NumGT applies the GT predicate on the "num" field.
func NumGT(v uint32) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldNum, v))
}

// NumGTE applies the GTE predicate on the "num" field.
func NumGTE(v uint32) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldNum, v))
}

// NumLT applies the LT predicate on the "num" field.
func NumLT(v uint32) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldNum, v))
}

// NumLTE applies the LTE predicate on the "num" field.
func NumLTE(v uint32) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldNum, v))
}

// RepositoryIDEQ applies the EQ predicate on the "repository_id" field.
func RepositoryIDEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldRepositoryID, v))
}

// RepositoryIDNEQ applies the NEQ predicate on the "repository_id" field.
func RepositoryIDNEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldRepositoryID, v))
}

// RepositoryIDIn applies the In predicate on the "repository_id" field.
func RepositoryIDIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldRepositoryID, vs...))
}

// RepositoryIDNotIn applies the NotIn predicate on the "repository_id" field.
func RepositoryIDNotIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldRepositoryID, vs...))
}

// RepositoryIDGT applies the GT predicate on the "repository_id" field.
func RepositoryIDGT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldRepositoryID, v))
}

// RepositoryIDGTE applies the GTE predicate on the "repository_id" field.
func RepositoryIDGTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldRepositoryID, v))
}

// RepositoryIDLT applies the LT predicate on the "repository_id" field.
func RepositoryIDLT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldRepositoryID, v))
}

// RepositoryIDLTE applies the LTE predicate on the "repository_id" field.
func RepositoryIDLTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldRepositoryID, v))
}

// RepositoryIDContains applies the Contains predicate on the "repository_id" field.
func RepositoryIDContains(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContains(FieldRepositoryID, v))
}

// RepositoryIDHasPrefix applies the HasPrefix predicate on the "repository_id" field.
func RepositoryIDHasPrefix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasPrefix(FieldRepositoryID, v))
}

// RepositoryIDHasSuffix applies the HasSuffix predicate on the "repository_id" field.
func RepositoryIDHasSuffix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasSuffix(FieldRepositoryID, v))
}

// RepositoryIDIsNil applies the IsNil predicate on the "repository_id" field.
func RepositoryIDIsNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIsNull(FieldRepositoryID))
}

// RepositoryIDNotNil applies the NotNil predicate on the "repository_id" field.
func RepositoryIDNotNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotNull(FieldRepositoryID))
}

// RepositoryIDEqualFold applies the EqualFold predicate on the "repository_id" field.
func RepositoryIDEqualFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEqualFold(FieldRepositoryID, v))
}

// RepositoryIDContainsFold applies the ContainsFold predicate on the "repository_id" field.
func RepositoryIDContainsFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContainsFold(FieldRepositoryID, v))
}

// RepositoryAreaIDEQ applies the EQ predicate on the "repository_area_id" field.
func RepositoryAreaIDEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDNEQ applies the NEQ predicate on the "repository_area_id" field.
func RepositoryAreaIDNEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDIn applies the In predicate on the "repository_area_id" field.
func RepositoryAreaIDIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldRepositoryAreaID, vs...))
}

// RepositoryAreaIDNotIn applies the NotIn predicate on the "repository_area_id" field.
func RepositoryAreaIDNotIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldRepositoryAreaID, vs...))
}

// RepositoryAreaIDGT applies the GT predicate on the "repository_area_id" field.
func RepositoryAreaIDGT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDGTE applies the GTE predicate on the "repository_area_id" field.
func RepositoryAreaIDGTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDLT applies the LT predicate on the "repository_area_id" field.
func RepositoryAreaIDLT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDLTE applies the LTE predicate on the "repository_area_id" field.
func RepositoryAreaIDLTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDContains applies the Contains predicate on the "repository_area_id" field.
func RepositoryAreaIDContains(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContains(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDHasPrefix applies the HasPrefix predicate on the "repository_area_id" field.
func RepositoryAreaIDHasPrefix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasPrefix(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDHasSuffix applies the HasSuffix predicate on the "repository_area_id" field.
func RepositoryAreaIDHasSuffix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasSuffix(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDIsNil applies the IsNil predicate on the "repository_area_id" field.
func RepositoryAreaIDIsNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIsNull(FieldRepositoryAreaID))
}

// RepositoryAreaIDNotNil applies the NotNil predicate on the "repository_area_id" field.
func RepositoryAreaIDNotNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotNull(FieldRepositoryAreaID))
}

// RepositoryAreaIDEqualFold applies the EqualFold predicate on the "repository_area_id" field.
func RepositoryAreaIDEqualFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEqualFold(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDContainsFold applies the ContainsFold predicate on the "repository_area_id" field.
func RepositoryAreaIDContainsFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContainsFold(FieldRepositoryAreaID, v))
}

// RepositoryPositionIDEQ applies the EQ predicate on the "repository_position_id" field.
func RepositoryPositionIDEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDNEQ applies the NEQ predicate on the "repository_position_id" field.
func RepositoryPositionIDNEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDIn applies the In predicate on the "repository_position_id" field.
func RepositoryPositionIDIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldRepositoryPositionID, vs...))
}

// RepositoryPositionIDNotIn applies the NotIn predicate on the "repository_position_id" field.
func RepositoryPositionIDNotIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldRepositoryPositionID, vs...))
}

// RepositoryPositionIDGT applies the GT predicate on the "repository_position_id" field.
func RepositoryPositionIDGT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDGTE applies the GTE predicate on the "repository_position_id" field.
func RepositoryPositionIDGTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDLT applies the LT predicate on the "repository_position_id" field.
func RepositoryPositionIDLT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDLTE applies the LTE predicate on the "repository_position_id" field.
func RepositoryPositionIDLTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDContains applies the Contains predicate on the "repository_position_id" field.
func RepositoryPositionIDContains(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContains(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDHasPrefix applies the HasPrefix predicate on the "repository_position_id" field.
func RepositoryPositionIDHasPrefix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasPrefix(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDHasSuffix applies the HasSuffix predicate on the "repository_position_id" field.
func RepositoryPositionIDHasSuffix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasSuffix(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDIsNil applies the IsNil predicate on the "repository_position_id" field.
func RepositoryPositionIDIsNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIsNull(FieldRepositoryPositionID))
}

// RepositoryPositionIDNotNil applies the NotNil predicate on the "repository_position_id" field.
func RepositoryPositionIDNotNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotNull(FieldRepositoryPositionID))
}

// RepositoryPositionIDEqualFold applies the EqualFold predicate on the "repository_position_id" field.
func RepositoryPositionIDEqualFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEqualFold(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDContainsFold applies the ContainsFold predicate on the "repository_position_id" field.
func RepositoryPositionIDContainsFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContainsFold(FieldRepositoryPositionID, v))
}

// ReasonEQ applies the EQ predicate on the "reason" field.
func ReasonEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldReason, v))
}

// ReasonNEQ applies the NEQ predicate on the "reason" field.
func ReasonNEQ(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldReason, v))
}

// ReasonIn applies the In predicate on the "reason" field.
func ReasonIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldReason, vs...))
}

// ReasonNotIn applies the NotIn predicate on the "reason" field.
func ReasonNotIn(vs ...string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldReason, vs...))
}

// ReasonGT applies the GT predicate on the "reason" field.
func ReasonGT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldReason, v))
}

// ReasonGTE applies the GTE predicate on the "reason" field.
func ReasonGTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldReason, v))
}

// ReasonLT applies the LT predicate on the "reason" field.
func ReasonLT(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldReason, v))
}

// ReasonLTE applies the LTE predicate on the "reason" field.
func ReasonLTE(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldReason, v))
}

// ReasonContains applies the Contains predicate on the "reason" field.
func ReasonContains(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContains(FieldReason, v))
}

// ReasonHasPrefix applies the HasPrefix predicate on the "reason" field.
func ReasonHasPrefix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasPrefix(FieldReason, v))
}

// ReasonHasSuffix applies the HasSuffix predicate on the "reason" field.
func ReasonHasSuffix(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldHasSuffix(FieldReason, v))
}

// ReasonIsNil applies the IsNil predicate on the "reason" field.
func ReasonIsNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIsNull(FieldReason))
}

// ReasonNotNil applies the NotNil predicate on the "reason" field.
func ReasonNotNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotNull(FieldReason))
}

// ReasonEqualFold applies the EqualFold predicate on the "reason" field.
func ReasonEqualFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEqualFold(FieldReason, v))
}

// ReasonContainsFold applies the ContainsFold predicate on the "reason" field.
func ReasonContainsFold(v string) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldContainsFold(FieldReason, v))
}

// DiscardTimeEQ applies the EQ predicate on the "discard_time" field.
func DiscardTimeEQ(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldEQ(FieldDiscardTime, v))
}

// DiscardTimeNEQ applies the NEQ predicate on the "discard_time" field.
func DiscardTimeNEQ(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNEQ(FieldDiscardTime, v))
}

// DiscardTimeIn applies the In predicate on the "discard_time" field.
func DiscardTimeIn(vs ...time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIn(FieldDiscardTime, vs...))
}

// DiscardTimeNotIn applies the NotIn predicate on the "discard_time" field.
func DiscardTimeNotIn(vs ...time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotIn(FieldDiscardTime, vs...))
}

// DiscardTimeGT applies the GT predicate on the "discard_time" field.
func DiscardTimeGT(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGT(FieldDiscardTime, v))
}

// DiscardTimeGTE applies the GTE predicate on the "discard_time" field.
func DiscardTimeGTE(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldGTE(FieldDiscardTime, v))
}

// DiscardTimeLT applies the LT predicate on the "discard_time" field.
func DiscardTimeLT(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLT(FieldDiscardTime, v))
}

// DiscardTimeLTE applies the LTE predicate on the "discard_time" field.
func DiscardTimeLTE(v time.Time) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldLTE(FieldDiscardTime, v))
}

// DiscardTimeIsNil applies the IsNil predicate on the "discard_time" field.
func DiscardTimeIsNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldIsNull(FieldDiscardTime))
}

// DiscardTimeNotNil applies the NotNil predicate on the "discard_time" field.
func DiscardTimeNotNil() predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.FieldNotNull(FieldDiscardTime))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.WmsDiscardOrderDetail) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.WmsDiscardOrderDetail) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.WmsDiscardOrderDetail) predicate.WmsDiscardOrderDetail {
	return predicate.WmsDiscardOrderDetail(sql.NotPredicates(p))
}
