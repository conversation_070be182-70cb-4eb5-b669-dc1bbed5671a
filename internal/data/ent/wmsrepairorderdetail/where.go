// Code generated by ent, DO NOT EDIT.

package wmsrepairorderdetail

import (
	"kratos-mono-demo/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContainsFold(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldUpdatedAt, v))
}

// CreatedBy applies equality check predicate on the "created_by" field. It's identical to CreatedByEQ.
func CreatedBy(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldCreatedBy, v))
}

// UpdatedBy applies equality check predicate on the "updated_by" field. It's identical to UpdatedByEQ.
func UpdatedBy(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldUpdatedBy, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldRemark, v))
}

// OrderID applies equality check predicate on the "order_id" field. It's identical to OrderIDEQ.
func OrderID(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldOrderID, v))
}

// MaterialID applies equality check predicate on the "material_id" field. It's identical to MaterialIDEQ.
func MaterialID(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldMaterialID, v))
}

// MaterialName applies equality check predicate on the "material_name" field. It's identical to MaterialNameEQ.
func MaterialName(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldMaterialName, v))
}

// Code applies equality check predicate on the "code" field. It's identical to CodeEQ.
func Code(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldCode, v))
}

// EquipmentID applies equality check predicate on the "equipment_id" field. It's identical to EquipmentIDEQ.
func EquipmentID(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldEquipmentID, v))
}

// EquipmentTypeID applies equality check predicate on the "equipment_type_id" field. It's identical to EquipmentTypeIDEQ.
func EquipmentTypeID(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldEquipmentTypeID, v))
}

// RepositoryID applies equality check predicate on the "repository_id" field. It's identical to RepositoryIDEQ.
func RepositoryID(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldRepositoryID, v))
}

// RepositoryAreaID applies equality check predicate on the "repository_area_id" field. It's identical to RepositoryAreaIDEQ.
func RepositoryAreaID(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldRepositoryAreaID, v))
}

// RepositoryPositionID applies equality check predicate on the "repository_position_id" field. It's identical to RepositoryPositionIDEQ.
func RepositoryPositionID(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldRepositoryPositionID, v))
}

// OwnerID applies equality check predicate on the "owner_id" field. It's identical to OwnerIDEQ.
func OwnerID(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldOwnerID, v))
}

// ModelNo applies equality check predicate on the "ModelNo" field. It's identical to ModelNoEQ.
func ModelNo(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldModelNo, v))
}

// MeasureUnitID applies equality check predicate on the "measure_unit_id" field. It's identical to MeasureUnitIDEQ.
func MeasureUnitID(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldMeasureUnitID, v))
}

// Num applies equality check predicate on the "num" field. It's identical to NumEQ.
func Num(v uint32) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldNum, v))
}

// RepairType applies equality check predicate on the "repair_type" field. It's identical to RepairTypeEQ.
func RepairType(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldRepairType, v))
}

// RepairReason applies equality check predicate on the "repair_reason" field. It's identical to RepairReasonEQ.
func RepairReason(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldRepairReason, v))
}

// ImageUrls applies equality check predicate on the "image_urls" field. It's identical to ImageUrlsEQ.
func ImageUrls(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldImageUrls, v))
}

// RepairTime applies equality check predicate on the "repair_time" field. It's identical to RepairTimeEQ.
func RepairTime(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldRepairTime, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldUpdatedAt, v))
}

// CreatedByEQ applies the EQ predicate on the "created_by" field.
func CreatedByEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldCreatedBy, v))
}

// CreatedByNEQ applies the NEQ predicate on the "created_by" field.
func CreatedByNEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldCreatedBy, v))
}

// CreatedByIn applies the In predicate on the "created_by" field.
func CreatedByIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldCreatedBy, vs...))
}

// CreatedByNotIn applies the NotIn predicate on the "created_by" field.
func CreatedByNotIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldCreatedBy, vs...))
}

// CreatedByGT applies the GT predicate on the "created_by" field.
func CreatedByGT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldCreatedBy, v))
}

// CreatedByGTE applies the GTE predicate on the "created_by" field.
func CreatedByGTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldCreatedBy, v))
}

// CreatedByLT applies the LT predicate on the "created_by" field.
func CreatedByLT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldCreatedBy, v))
}

// CreatedByLTE applies the LTE predicate on the "created_by" field.
func CreatedByLTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldCreatedBy, v))
}

// CreatedByContains applies the Contains predicate on the "created_by" field.
func CreatedByContains(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContains(FieldCreatedBy, v))
}

// CreatedByHasPrefix applies the HasPrefix predicate on the "created_by" field.
func CreatedByHasPrefix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasPrefix(FieldCreatedBy, v))
}

// CreatedByHasSuffix applies the HasSuffix predicate on the "created_by" field.
func CreatedByHasSuffix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasSuffix(FieldCreatedBy, v))
}

// CreatedByIsNil applies the IsNil predicate on the "created_by" field.
func CreatedByIsNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIsNull(FieldCreatedBy))
}

// CreatedByNotNil applies the NotNil predicate on the "created_by" field.
func CreatedByNotNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotNull(FieldCreatedBy))
}

// CreatedByEqualFold applies the EqualFold predicate on the "created_by" field.
func CreatedByEqualFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEqualFold(FieldCreatedBy, v))
}

// CreatedByContainsFold applies the ContainsFold predicate on the "created_by" field.
func CreatedByContainsFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContainsFold(FieldCreatedBy, v))
}

// UpdatedByEQ applies the EQ predicate on the "updated_by" field.
func UpdatedByEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldUpdatedBy, v))
}

// UpdatedByNEQ applies the NEQ predicate on the "updated_by" field.
func UpdatedByNEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldUpdatedBy, v))
}

// UpdatedByIn applies the In predicate on the "updated_by" field.
func UpdatedByIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldUpdatedBy, vs...))
}

// UpdatedByNotIn applies the NotIn predicate on the "updated_by" field.
func UpdatedByNotIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldUpdatedBy, vs...))
}

// UpdatedByGT applies the GT predicate on the "updated_by" field.
func UpdatedByGT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldUpdatedBy, v))
}

// UpdatedByGTE applies the GTE predicate on the "updated_by" field.
func UpdatedByGTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldUpdatedBy, v))
}

// UpdatedByLT applies the LT predicate on the "updated_by" field.
func UpdatedByLT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldUpdatedBy, v))
}

// UpdatedByLTE applies the LTE predicate on the "updated_by" field.
func UpdatedByLTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldUpdatedBy, v))
}

// UpdatedByContains applies the Contains predicate on the "updated_by" field.
func UpdatedByContains(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContains(FieldUpdatedBy, v))
}

// UpdatedByHasPrefix applies the HasPrefix predicate on the "updated_by" field.
func UpdatedByHasPrefix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasPrefix(FieldUpdatedBy, v))
}

// UpdatedByHasSuffix applies the HasSuffix predicate on the "updated_by" field.
func UpdatedByHasSuffix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasSuffix(FieldUpdatedBy, v))
}

// UpdatedByIsNil applies the IsNil predicate on the "updated_by" field.
func UpdatedByIsNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIsNull(FieldUpdatedBy))
}

// UpdatedByNotNil applies the NotNil predicate on the "updated_by" field.
func UpdatedByNotNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotNull(FieldUpdatedBy))
}

// UpdatedByEqualFold applies the EqualFold predicate on the "updated_by" field.
func UpdatedByEqualFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEqualFold(FieldUpdatedBy, v))
}

// UpdatedByContainsFold applies the ContainsFold predicate on the "updated_by" field.
func UpdatedByContainsFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContainsFold(FieldUpdatedBy, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContainsFold(FieldRemark, v))
}

// OrderIDEQ applies the EQ predicate on the "order_id" field.
func OrderIDEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldOrderID, v))
}

// OrderIDNEQ applies the NEQ predicate on the "order_id" field.
func OrderIDNEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldOrderID, v))
}

// OrderIDIn applies the In predicate on the "order_id" field.
func OrderIDIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldOrderID, vs...))
}

// OrderIDNotIn applies the NotIn predicate on the "order_id" field.
func OrderIDNotIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldOrderID, vs...))
}

// OrderIDGT applies the GT predicate on the "order_id" field.
func OrderIDGT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldOrderID, v))
}

// OrderIDGTE applies the GTE predicate on the "order_id" field.
func OrderIDGTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldOrderID, v))
}

// OrderIDLT applies the LT predicate on the "order_id" field.
func OrderIDLT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldOrderID, v))
}

// OrderIDLTE applies the LTE predicate on the "order_id" field.
func OrderIDLTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldOrderID, v))
}

// OrderIDContains applies the Contains predicate on the "order_id" field.
func OrderIDContains(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContains(FieldOrderID, v))
}

// OrderIDHasPrefix applies the HasPrefix predicate on the "order_id" field.
func OrderIDHasPrefix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasPrefix(FieldOrderID, v))
}

// OrderIDHasSuffix applies the HasSuffix predicate on the "order_id" field.
func OrderIDHasSuffix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasSuffix(FieldOrderID, v))
}

// OrderIDIsNil applies the IsNil predicate on the "order_id" field.
func OrderIDIsNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIsNull(FieldOrderID))
}

// OrderIDNotNil applies the NotNil predicate on the "order_id" field.
func OrderIDNotNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotNull(FieldOrderID))
}

// OrderIDEqualFold applies the EqualFold predicate on the "order_id" field.
func OrderIDEqualFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEqualFold(FieldOrderID, v))
}

// OrderIDContainsFold applies the ContainsFold predicate on the "order_id" field.
func OrderIDContainsFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContainsFold(FieldOrderID, v))
}

// MaterialIDEQ applies the EQ predicate on the "material_id" field.
func MaterialIDEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldMaterialID, v))
}

// MaterialIDNEQ applies the NEQ predicate on the "material_id" field.
func MaterialIDNEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldMaterialID, v))
}

// MaterialIDIn applies the In predicate on the "material_id" field.
func MaterialIDIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldMaterialID, vs...))
}

// MaterialIDNotIn applies the NotIn predicate on the "material_id" field.
func MaterialIDNotIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldMaterialID, vs...))
}

// MaterialIDGT applies the GT predicate on the "material_id" field.
func MaterialIDGT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldMaterialID, v))
}

// MaterialIDGTE applies the GTE predicate on the "material_id" field.
func MaterialIDGTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldMaterialID, v))
}

// MaterialIDLT applies the LT predicate on the "material_id" field.
func MaterialIDLT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldMaterialID, v))
}

// MaterialIDLTE applies the LTE predicate on the "material_id" field.
func MaterialIDLTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldMaterialID, v))
}

// MaterialIDContains applies the Contains predicate on the "material_id" field.
func MaterialIDContains(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContains(FieldMaterialID, v))
}

// MaterialIDHasPrefix applies the HasPrefix predicate on the "material_id" field.
func MaterialIDHasPrefix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasPrefix(FieldMaterialID, v))
}

// MaterialIDHasSuffix applies the HasSuffix predicate on the "material_id" field.
func MaterialIDHasSuffix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasSuffix(FieldMaterialID, v))
}

// MaterialIDIsNil applies the IsNil predicate on the "material_id" field.
func MaterialIDIsNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIsNull(FieldMaterialID))
}

// MaterialIDNotNil applies the NotNil predicate on the "material_id" field.
func MaterialIDNotNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotNull(FieldMaterialID))
}

// MaterialIDEqualFold applies the EqualFold predicate on the "material_id" field.
func MaterialIDEqualFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEqualFold(FieldMaterialID, v))
}

// MaterialIDContainsFold applies the ContainsFold predicate on the "material_id" field.
func MaterialIDContainsFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContainsFold(FieldMaterialID, v))
}

// MaterialNameEQ applies the EQ predicate on the "material_name" field.
func MaterialNameEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldMaterialName, v))
}

// MaterialNameNEQ applies the NEQ predicate on the "material_name" field.
func MaterialNameNEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldMaterialName, v))
}

// MaterialNameIn applies the In predicate on the "material_name" field.
func MaterialNameIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldMaterialName, vs...))
}

// MaterialNameNotIn applies the NotIn predicate on the "material_name" field.
func MaterialNameNotIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldMaterialName, vs...))
}

// MaterialNameGT applies the GT predicate on the "material_name" field.
func MaterialNameGT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldMaterialName, v))
}

// MaterialNameGTE applies the GTE predicate on the "material_name" field.
func MaterialNameGTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldMaterialName, v))
}

// MaterialNameLT applies the LT predicate on the "material_name" field.
func MaterialNameLT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldMaterialName, v))
}

// MaterialNameLTE applies the LTE predicate on the "material_name" field.
func MaterialNameLTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldMaterialName, v))
}

// MaterialNameContains applies the Contains predicate on the "material_name" field.
func MaterialNameContains(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContains(FieldMaterialName, v))
}

// MaterialNameHasPrefix applies the HasPrefix predicate on the "material_name" field.
func MaterialNameHasPrefix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasPrefix(FieldMaterialName, v))
}

// MaterialNameHasSuffix applies the HasSuffix predicate on the "material_name" field.
func MaterialNameHasSuffix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasSuffix(FieldMaterialName, v))
}

// MaterialNameIsNil applies the IsNil predicate on the "material_name" field.
func MaterialNameIsNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIsNull(FieldMaterialName))
}

// MaterialNameNotNil applies the NotNil predicate on the "material_name" field.
func MaterialNameNotNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotNull(FieldMaterialName))
}

// MaterialNameEqualFold applies the EqualFold predicate on the "material_name" field.
func MaterialNameEqualFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEqualFold(FieldMaterialName, v))
}

// MaterialNameContainsFold applies the ContainsFold predicate on the "material_name" field.
func MaterialNameContainsFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContainsFold(FieldMaterialName, v))
}

// CodeEQ applies the EQ predicate on the "code" field.
func CodeEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldCode, v))
}

// CodeNEQ applies the NEQ predicate on the "code" field.
func CodeNEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldCode, v))
}

// CodeIn applies the In predicate on the "code" field.
func CodeIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldCode, vs...))
}

// CodeNotIn applies the NotIn predicate on the "code" field.
func CodeNotIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldCode, vs...))
}

// CodeGT applies the GT predicate on the "code" field.
func CodeGT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldCode, v))
}

// CodeGTE applies the GTE predicate on the "code" field.
func CodeGTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldCode, v))
}

// CodeLT applies the LT predicate on the "code" field.
func CodeLT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldCode, v))
}

// CodeLTE applies the LTE predicate on the "code" field.
func CodeLTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldCode, v))
}

// CodeContains applies the Contains predicate on the "code" field.
func CodeContains(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContains(FieldCode, v))
}

// CodeHasPrefix applies the HasPrefix predicate on the "code" field.
func CodeHasPrefix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasPrefix(FieldCode, v))
}

// CodeHasSuffix applies the HasSuffix predicate on the "code" field.
func CodeHasSuffix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasSuffix(FieldCode, v))
}

// CodeEqualFold applies the EqualFold predicate on the "code" field.
func CodeEqualFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEqualFold(FieldCode, v))
}

// CodeContainsFold applies the ContainsFold predicate on the "code" field.
func CodeContainsFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContainsFold(FieldCode, v))
}

// EquipmentIDEQ applies the EQ predicate on the "equipment_id" field.
func EquipmentIDEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldEquipmentID, v))
}

// EquipmentIDNEQ applies the NEQ predicate on the "equipment_id" field.
func EquipmentIDNEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldEquipmentID, v))
}

// EquipmentIDIn applies the In predicate on the "equipment_id" field.
func EquipmentIDIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldEquipmentID, vs...))
}

// EquipmentIDNotIn applies the NotIn predicate on the "equipment_id" field.
func EquipmentIDNotIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldEquipmentID, vs...))
}

// EquipmentIDGT applies the GT predicate on the "equipment_id" field.
func EquipmentIDGT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldEquipmentID, v))
}

// EquipmentIDGTE applies the GTE predicate on the "equipment_id" field.
func EquipmentIDGTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldEquipmentID, v))
}

// EquipmentIDLT applies the LT predicate on the "equipment_id" field.
func EquipmentIDLT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldEquipmentID, v))
}

// EquipmentIDLTE applies the LTE predicate on the "equipment_id" field.
func EquipmentIDLTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldEquipmentID, v))
}

// EquipmentIDContains applies the Contains predicate on the "equipment_id" field.
func EquipmentIDContains(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContains(FieldEquipmentID, v))
}

// EquipmentIDHasPrefix applies the HasPrefix predicate on the "equipment_id" field.
func EquipmentIDHasPrefix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasPrefix(FieldEquipmentID, v))
}

// EquipmentIDHasSuffix applies the HasSuffix predicate on the "equipment_id" field.
func EquipmentIDHasSuffix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasSuffix(FieldEquipmentID, v))
}

// EquipmentIDIsNil applies the IsNil predicate on the "equipment_id" field.
func EquipmentIDIsNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIsNull(FieldEquipmentID))
}

// EquipmentIDNotNil applies the NotNil predicate on the "equipment_id" field.
func EquipmentIDNotNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotNull(FieldEquipmentID))
}

// EquipmentIDEqualFold applies the EqualFold predicate on the "equipment_id" field.
func EquipmentIDEqualFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEqualFold(FieldEquipmentID, v))
}

// EquipmentIDContainsFold applies the ContainsFold predicate on the "equipment_id" field.
func EquipmentIDContainsFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContainsFold(FieldEquipmentID, v))
}

// EquipmentTypeIDEQ applies the EQ predicate on the "equipment_type_id" field.
func EquipmentTypeIDEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDNEQ applies the NEQ predicate on the "equipment_type_id" field.
func EquipmentTypeIDNEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDIn applies the In predicate on the "equipment_type_id" field.
func EquipmentTypeIDIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldEquipmentTypeID, vs...))
}

// EquipmentTypeIDNotIn applies the NotIn predicate on the "equipment_type_id" field.
func EquipmentTypeIDNotIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldEquipmentTypeID, vs...))
}

// EquipmentTypeIDGT applies the GT predicate on the "equipment_type_id" field.
func EquipmentTypeIDGT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDGTE applies the GTE predicate on the "equipment_type_id" field.
func EquipmentTypeIDGTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDLT applies the LT predicate on the "equipment_type_id" field.
func EquipmentTypeIDLT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDLTE applies the LTE predicate on the "equipment_type_id" field.
func EquipmentTypeIDLTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDContains applies the Contains predicate on the "equipment_type_id" field.
func EquipmentTypeIDContains(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContains(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDHasPrefix applies the HasPrefix predicate on the "equipment_type_id" field.
func EquipmentTypeIDHasPrefix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasPrefix(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDHasSuffix applies the HasSuffix predicate on the "equipment_type_id" field.
func EquipmentTypeIDHasSuffix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasSuffix(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDIsNil applies the IsNil predicate on the "equipment_type_id" field.
func EquipmentTypeIDIsNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIsNull(FieldEquipmentTypeID))
}

// EquipmentTypeIDNotNil applies the NotNil predicate on the "equipment_type_id" field.
func EquipmentTypeIDNotNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotNull(FieldEquipmentTypeID))
}

// EquipmentTypeIDEqualFold applies the EqualFold predicate on the "equipment_type_id" field.
func EquipmentTypeIDEqualFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEqualFold(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDContainsFold applies the ContainsFold predicate on the "equipment_type_id" field.
func EquipmentTypeIDContainsFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContainsFold(FieldEquipmentTypeID, v))
}

// FeatureIsNil applies the IsNil predicate on the "feature" field.
func FeatureIsNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIsNull(FieldFeature))
}

// FeatureNotNil applies the NotNil predicate on the "feature" field.
func FeatureNotNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotNull(FieldFeature))
}

// RepositoryIDEQ applies the EQ predicate on the "repository_id" field.
func RepositoryIDEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldRepositoryID, v))
}

// RepositoryIDNEQ applies the NEQ predicate on the "repository_id" field.
func RepositoryIDNEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldRepositoryID, v))
}

// RepositoryIDIn applies the In predicate on the "repository_id" field.
func RepositoryIDIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldRepositoryID, vs...))
}

// RepositoryIDNotIn applies the NotIn predicate on the "repository_id" field.
func RepositoryIDNotIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldRepositoryID, vs...))
}

// RepositoryIDGT applies the GT predicate on the "repository_id" field.
func RepositoryIDGT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldRepositoryID, v))
}

// RepositoryIDGTE applies the GTE predicate on the "repository_id" field.
func RepositoryIDGTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldRepositoryID, v))
}

// RepositoryIDLT applies the LT predicate on the "repository_id" field.
func RepositoryIDLT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldRepositoryID, v))
}

// RepositoryIDLTE applies the LTE predicate on the "repository_id" field.
func RepositoryIDLTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldRepositoryID, v))
}

// RepositoryIDContains applies the Contains predicate on the "repository_id" field.
func RepositoryIDContains(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContains(FieldRepositoryID, v))
}

// RepositoryIDHasPrefix applies the HasPrefix predicate on the "repository_id" field.
func RepositoryIDHasPrefix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasPrefix(FieldRepositoryID, v))
}

// RepositoryIDHasSuffix applies the HasSuffix predicate on the "repository_id" field.
func RepositoryIDHasSuffix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasSuffix(FieldRepositoryID, v))
}

// RepositoryIDIsNil applies the IsNil predicate on the "repository_id" field.
func RepositoryIDIsNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIsNull(FieldRepositoryID))
}

// RepositoryIDNotNil applies the NotNil predicate on the "repository_id" field.
func RepositoryIDNotNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotNull(FieldRepositoryID))
}

// RepositoryIDEqualFold applies the EqualFold predicate on the "repository_id" field.
func RepositoryIDEqualFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEqualFold(FieldRepositoryID, v))
}

// RepositoryIDContainsFold applies the ContainsFold predicate on the "repository_id" field.
func RepositoryIDContainsFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContainsFold(FieldRepositoryID, v))
}

// RepositoryAreaIDEQ applies the EQ predicate on the "repository_area_id" field.
func RepositoryAreaIDEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDNEQ applies the NEQ predicate on the "repository_area_id" field.
func RepositoryAreaIDNEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDIn applies the In predicate on the "repository_area_id" field.
func RepositoryAreaIDIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldRepositoryAreaID, vs...))
}

// RepositoryAreaIDNotIn applies the NotIn predicate on the "repository_area_id" field.
func RepositoryAreaIDNotIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldRepositoryAreaID, vs...))
}

// RepositoryAreaIDGT applies the GT predicate on the "repository_area_id" field.
func RepositoryAreaIDGT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDGTE applies the GTE predicate on the "repository_area_id" field.
func RepositoryAreaIDGTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDLT applies the LT predicate on the "repository_area_id" field.
func RepositoryAreaIDLT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDLTE applies the LTE predicate on the "repository_area_id" field.
func RepositoryAreaIDLTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDContains applies the Contains predicate on the "repository_area_id" field.
func RepositoryAreaIDContains(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContains(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDHasPrefix applies the HasPrefix predicate on the "repository_area_id" field.
func RepositoryAreaIDHasPrefix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasPrefix(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDHasSuffix applies the HasSuffix predicate on the "repository_area_id" field.
func RepositoryAreaIDHasSuffix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasSuffix(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDIsNil applies the IsNil predicate on the "repository_area_id" field.
func RepositoryAreaIDIsNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIsNull(FieldRepositoryAreaID))
}

// RepositoryAreaIDNotNil applies the NotNil predicate on the "repository_area_id" field.
func RepositoryAreaIDNotNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotNull(FieldRepositoryAreaID))
}

// RepositoryAreaIDEqualFold applies the EqualFold predicate on the "repository_area_id" field.
func RepositoryAreaIDEqualFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEqualFold(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDContainsFold applies the ContainsFold predicate on the "repository_area_id" field.
func RepositoryAreaIDContainsFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContainsFold(FieldRepositoryAreaID, v))
}

// RepositoryPositionIDEQ applies the EQ predicate on the "repository_position_id" field.
func RepositoryPositionIDEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDNEQ applies the NEQ predicate on the "repository_position_id" field.
func RepositoryPositionIDNEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDIn applies the In predicate on the "repository_position_id" field.
func RepositoryPositionIDIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldRepositoryPositionID, vs...))
}

// RepositoryPositionIDNotIn applies the NotIn predicate on the "repository_position_id" field.
func RepositoryPositionIDNotIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldRepositoryPositionID, vs...))
}

// RepositoryPositionIDGT applies the GT predicate on the "repository_position_id" field.
func RepositoryPositionIDGT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDGTE applies the GTE predicate on the "repository_position_id" field.
func RepositoryPositionIDGTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDLT applies the LT predicate on the "repository_position_id" field.
func RepositoryPositionIDLT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDLTE applies the LTE predicate on the "repository_position_id" field.
func RepositoryPositionIDLTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDContains applies the Contains predicate on the "repository_position_id" field.
func RepositoryPositionIDContains(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContains(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDHasPrefix applies the HasPrefix predicate on the "repository_position_id" field.
func RepositoryPositionIDHasPrefix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasPrefix(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDHasSuffix applies the HasSuffix predicate on the "repository_position_id" field.
func RepositoryPositionIDHasSuffix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasSuffix(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDIsNil applies the IsNil predicate on the "repository_position_id" field.
func RepositoryPositionIDIsNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIsNull(FieldRepositoryPositionID))
}

// RepositoryPositionIDNotNil applies the NotNil predicate on the "repository_position_id" field.
func RepositoryPositionIDNotNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotNull(FieldRepositoryPositionID))
}

// RepositoryPositionIDEqualFold applies the EqualFold predicate on the "repository_position_id" field.
func RepositoryPositionIDEqualFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEqualFold(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDContainsFold applies the ContainsFold predicate on the "repository_position_id" field.
func RepositoryPositionIDContainsFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContainsFold(FieldRepositoryPositionID, v))
}

// OwnerIDEQ applies the EQ predicate on the "owner_id" field.
func OwnerIDEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldOwnerID, v))
}

// OwnerIDNEQ applies the NEQ predicate on the "owner_id" field.
func OwnerIDNEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldOwnerID, v))
}

// OwnerIDIn applies the In predicate on the "owner_id" field.
func OwnerIDIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldOwnerID, vs...))
}

// OwnerIDNotIn applies the NotIn predicate on the "owner_id" field.
func OwnerIDNotIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldOwnerID, vs...))
}

// OwnerIDGT applies the GT predicate on the "owner_id" field.
func OwnerIDGT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldOwnerID, v))
}

// OwnerIDGTE applies the GTE predicate on the "owner_id" field.
func OwnerIDGTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldOwnerID, v))
}

// OwnerIDLT applies the LT predicate on the "owner_id" field.
func OwnerIDLT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldOwnerID, v))
}

// OwnerIDLTE applies the LTE predicate on the "owner_id" field.
func OwnerIDLTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldOwnerID, v))
}

// OwnerIDContains applies the Contains predicate on the "owner_id" field.
func OwnerIDContains(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContains(FieldOwnerID, v))
}

// OwnerIDHasPrefix applies the HasPrefix predicate on the "owner_id" field.
func OwnerIDHasPrefix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasPrefix(FieldOwnerID, v))
}

// OwnerIDHasSuffix applies the HasSuffix predicate on the "owner_id" field.
func OwnerIDHasSuffix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasSuffix(FieldOwnerID, v))
}

// OwnerIDIsNil applies the IsNil predicate on the "owner_id" field.
func OwnerIDIsNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIsNull(FieldOwnerID))
}

// OwnerIDNotNil applies the NotNil predicate on the "owner_id" field.
func OwnerIDNotNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotNull(FieldOwnerID))
}

// OwnerIDEqualFold applies the EqualFold predicate on the "owner_id" field.
func OwnerIDEqualFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEqualFold(FieldOwnerID, v))
}

// OwnerIDContainsFold applies the ContainsFold predicate on the "owner_id" field.
func OwnerIDContainsFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContainsFold(FieldOwnerID, v))
}

// ModelNoEQ applies the EQ predicate on the "ModelNo" field.
func ModelNoEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldModelNo, v))
}

// ModelNoNEQ applies the NEQ predicate on the "ModelNo" field.
func ModelNoNEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldModelNo, v))
}

// ModelNoIn applies the In predicate on the "ModelNo" field.
func ModelNoIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldModelNo, vs...))
}

// ModelNoNotIn applies the NotIn predicate on the "ModelNo" field.
func ModelNoNotIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldModelNo, vs...))
}

// ModelNoGT applies the GT predicate on the "ModelNo" field.
func ModelNoGT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldModelNo, v))
}

// ModelNoGTE applies the GTE predicate on the "ModelNo" field.
func ModelNoGTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldModelNo, v))
}

// ModelNoLT applies the LT predicate on the "ModelNo" field.
func ModelNoLT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldModelNo, v))
}

// ModelNoLTE applies the LTE predicate on the "ModelNo" field.
func ModelNoLTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldModelNo, v))
}

// ModelNoContains applies the Contains predicate on the "ModelNo" field.
func ModelNoContains(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContains(FieldModelNo, v))
}

// ModelNoHasPrefix applies the HasPrefix predicate on the "ModelNo" field.
func ModelNoHasPrefix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasPrefix(FieldModelNo, v))
}

// ModelNoHasSuffix applies the HasSuffix predicate on the "ModelNo" field.
func ModelNoHasSuffix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasSuffix(FieldModelNo, v))
}

// ModelNoEqualFold applies the EqualFold predicate on the "ModelNo" field.
func ModelNoEqualFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEqualFold(FieldModelNo, v))
}

// ModelNoContainsFold applies the ContainsFold predicate on the "ModelNo" field.
func ModelNoContainsFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContainsFold(FieldModelNo, v))
}

// MeasureUnitIDEQ applies the EQ predicate on the "measure_unit_id" field.
func MeasureUnitIDEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldMeasureUnitID, v))
}

// MeasureUnitIDNEQ applies the NEQ predicate on the "measure_unit_id" field.
func MeasureUnitIDNEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldMeasureUnitID, v))
}

// MeasureUnitIDIn applies the In predicate on the "measure_unit_id" field.
func MeasureUnitIDIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldMeasureUnitID, vs...))
}

// MeasureUnitIDNotIn applies the NotIn predicate on the "measure_unit_id" field.
func MeasureUnitIDNotIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldMeasureUnitID, vs...))
}

// MeasureUnitIDGT applies the GT predicate on the "measure_unit_id" field.
func MeasureUnitIDGT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldMeasureUnitID, v))
}

// MeasureUnitIDGTE applies the GTE predicate on the "measure_unit_id" field.
func MeasureUnitIDGTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldMeasureUnitID, v))
}

// MeasureUnitIDLT applies the LT predicate on the "measure_unit_id" field.
func MeasureUnitIDLT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldMeasureUnitID, v))
}

// MeasureUnitIDLTE applies the LTE predicate on the "measure_unit_id" field.
func MeasureUnitIDLTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldMeasureUnitID, v))
}

// MeasureUnitIDContains applies the Contains predicate on the "measure_unit_id" field.
func MeasureUnitIDContains(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContains(FieldMeasureUnitID, v))
}

// MeasureUnitIDHasPrefix applies the HasPrefix predicate on the "measure_unit_id" field.
func MeasureUnitIDHasPrefix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasPrefix(FieldMeasureUnitID, v))
}

// MeasureUnitIDHasSuffix applies the HasSuffix predicate on the "measure_unit_id" field.
func MeasureUnitIDHasSuffix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasSuffix(FieldMeasureUnitID, v))
}

// MeasureUnitIDIsNil applies the IsNil predicate on the "measure_unit_id" field.
func MeasureUnitIDIsNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIsNull(FieldMeasureUnitID))
}

// MeasureUnitIDNotNil applies the NotNil predicate on the "measure_unit_id" field.
func MeasureUnitIDNotNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotNull(FieldMeasureUnitID))
}

// MeasureUnitIDEqualFold applies the EqualFold predicate on the "measure_unit_id" field.
func MeasureUnitIDEqualFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEqualFold(FieldMeasureUnitID, v))
}

// MeasureUnitIDContainsFold applies the ContainsFold predicate on the "measure_unit_id" field.
func MeasureUnitIDContainsFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContainsFold(FieldMeasureUnitID, v))
}

// NumEQ applies the EQ predicate on the "num" field.
func NumEQ(v uint32) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldNum, v))
}

// NumNEQ applies the NEQ predicate on the "num" field.
func NumNEQ(v uint32) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldNum, v))
}

// NumIn applies the In predicate on the "num" field.
func NumIn(vs ...uint32) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldNum, vs...))
}

// NumNotIn applies the NotIn predicate on the "num" field.
func NumNotIn(vs ...uint32) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldNum, vs...))
}

// NumGT applies the GT predicate on the "num" field.
func NumGT(v uint32) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldNum, v))
}

// NumGTE applies the GTE predicate on the "num" field.
func NumGTE(v uint32) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldNum, v))
}

// NumLT applies the LT predicate on the "num" field.
func NumLT(v uint32) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldNum, v))
}

// NumLTE applies the LTE predicate on the "num" field.
func NumLTE(v uint32) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldNum, v))
}

// RepairTypeEQ applies the EQ predicate on the "repair_type" field.
func RepairTypeEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldRepairType, v))
}

// RepairTypeNEQ applies the NEQ predicate on the "repair_type" field.
func RepairTypeNEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldRepairType, v))
}

// RepairTypeIn applies the In predicate on the "repair_type" field.
func RepairTypeIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldRepairType, vs...))
}

// RepairTypeNotIn applies the NotIn predicate on the "repair_type" field.
func RepairTypeNotIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldRepairType, vs...))
}

// RepairTypeGT applies the GT predicate on the "repair_type" field.
func RepairTypeGT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldRepairType, v))
}

// RepairTypeGTE applies the GTE predicate on the "repair_type" field.
func RepairTypeGTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldRepairType, v))
}

// RepairTypeLT applies the LT predicate on the "repair_type" field.
func RepairTypeLT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldRepairType, v))
}

// RepairTypeLTE applies the LTE predicate on the "repair_type" field.
func RepairTypeLTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldRepairType, v))
}

// RepairTypeContains applies the Contains predicate on the "repair_type" field.
func RepairTypeContains(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContains(FieldRepairType, v))
}

// RepairTypeHasPrefix applies the HasPrefix predicate on the "repair_type" field.
func RepairTypeHasPrefix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasPrefix(FieldRepairType, v))
}

// RepairTypeHasSuffix applies the HasSuffix predicate on the "repair_type" field.
func RepairTypeHasSuffix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasSuffix(FieldRepairType, v))
}

// RepairTypeIsNil applies the IsNil predicate on the "repair_type" field.
func RepairTypeIsNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIsNull(FieldRepairType))
}

// RepairTypeNotNil applies the NotNil predicate on the "repair_type" field.
func RepairTypeNotNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotNull(FieldRepairType))
}

// RepairTypeEqualFold applies the EqualFold predicate on the "repair_type" field.
func RepairTypeEqualFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEqualFold(FieldRepairType, v))
}

// RepairTypeContainsFold applies the ContainsFold predicate on the "repair_type" field.
func RepairTypeContainsFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContainsFold(FieldRepairType, v))
}

// RepairReasonEQ applies the EQ predicate on the "repair_reason" field.
func RepairReasonEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldRepairReason, v))
}

// RepairReasonNEQ applies the NEQ predicate on the "repair_reason" field.
func RepairReasonNEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldRepairReason, v))
}

// RepairReasonIn applies the In predicate on the "repair_reason" field.
func RepairReasonIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldRepairReason, vs...))
}

// RepairReasonNotIn applies the NotIn predicate on the "repair_reason" field.
func RepairReasonNotIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldRepairReason, vs...))
}

// RepairReasonGT applies the GT predicate on the "repair_reason" field.
func RepairReasonGT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldRepairReason, v))
}

// RepairReasonGTE applies the GTE predicate on the "repair_reason" field.
func RepairReasonGTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldRepairReason, v))
}

// RepairReasonLT applies the LT predicate on the "repair_reason" field.
func RepairReasonLT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldRepairReason, v))
}

// RepairReasonLTE applies the LTE predicate on the "repair_reason" field.
func RepairReasonLTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldRepairReason, v))
}

// RepairReasonContains applies the Contains predicate on the "repair_reason" field.
func RepairReasonContains(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContains(FieldRepairReason, v))
}

// RepairReasonHasPrefix applies the HasPrefix predicate on the "repair_reason" field.
func RepairReasonHasPrefix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasPrefix(FieldRepairReason, v))
}

// RepairReasonHasSuffix applies the HasSuffix predicate on the "repair_reason" field.
func RepairReasonHasSuffix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasSuffix(FieldRepairReason, v))
}

// RepairReasonIsNil applies the IsNil predicate on the "repair_reason" field.
func RepairReasonIsNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIsNull(FieldRepairReason))
}

// RepairReasonNotNil applies the NotNil predicate on the "repair_reason" field.
func RepairReasonNotNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotNull(FieldRepairReason))
}

// RepairReasonEqualFold applies the EqualFold predicate on the "repair_reason" field.
func RepairReasonEqualFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEqualFold(FieldRepairReason, v))
}

// RepairReasonContainsFold applies the ContainsFold predicate on the "repair_reason" field.
func RepairReasonContainsFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContainsFold(FieldRepairReason, v))
}

// ImageUrlsEQ applies the EQ predicate on the "image_urls" field.
func ImageUrlsEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldImageUrls, v))
}

// ImageUrlsNEQ applies the NEQ predicate on the "image_urls" field.
func ImageUrlsNEQ(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldImageUrls, v))
}

// ImageUrlsIn applies the In predicate on the "image_urls" field.
func ImageUrlsIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldImageUrls, vs...))
}

// ImageUrlsNotIn applies the NotIn predicate on the "image_urls" field.
func ImageUrlsNotIn(vs ...string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldImageUrls, vs...))
}

// ImageUrlsGT applies the GT predicate on the "image_urls" field.
func ImageUrlsGT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldImageUrls, v))
}

// ImageUrlsGTE applies the GTE predicate on the "image_urls" field.
func ImageUrlsGTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldImageUrls, v))
}

// ImageUrlsLT applies the LT predicate on the "image_urls" field.
func ImageUrlsLT(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldImageUrls, v))
}

// ImageUrlsLTE applies the LTE predicate on the "image_urls" field.
func ImageUrlsLTE(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldImageUrls, v))
}

// ImageUrlsContains applies the Contains predicate on the "image_urls" field.
func ImageUrlsContains(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContains(FieldImageUrls, v))
}

// ImageUrlsHasPrefix applies the HasPrefix predicate on the "image_urls" field.
func ImageUrlsHasPrefix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasPrefix(FieldImageUrls, v))
}

// ImageUrlsHasSuffix applies the HasSuffix predicate on the "image_urls" field.
func ImageUrlsHasSuffix(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldHasSuffix(FieldImageUrls, v))
}

// ImageUrlsIsNil applies the IsNil predicate on the "image_urls" field.
func ImageUrlsIsNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIsNull(FieldImageUrls))
}

// ImageUrlsNotNil applies the NotNil predicate on the "image_urls" field.
func ImageUrlsNotNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotNull(FieldImageUrls))
}

// ImageUrlsEqualFold applies the EqualFold predicate on the "image_urls" field.
func ImageUrlsEqualFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEqualFold(FieldImageUrls, v))
}

// ImageUrlsContainsFold applies the ContainsFold predicate on the "image_urls" field.
func ImageUrlsContainsFold(v string) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldContainsFold(FieldImageUrls, v))
}

// RepairTimeEQ applies the EQ predicate on the "repair_time" field.
func RepairTimeEQ(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldEQ(FieldRepairTime, v))
}

// RepairTimeNEQ applies the NEQ predicate on the "repair_time" field.
func RepairTimeNEQ(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNEQ(FieldRepairTime, v))
}

// RepairTimeIn applies the In predicate on the "repair_time" field.
func RepairTimeIn(vs ...time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIn(FieldRepairTime, vs...))
}

// RepairTimeNotIn applies the NotIn predicate on the "repair_time" field.
func RepairTimeNotIn(vs ...time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotIn(FieldRepairTime, vs...))
}

// RepairTimeGT applies the GT predicate on the "repair_time" field.
func RepairTimeGT(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGT(FieldRepairTime, v))
}

// RepairTimeGTE applies the GTE predicate on the "repair_time" field.
func RepairTimeGTE(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldGTE(FieldRepairTime, v))
}

// RepairTimeLT applies the LT predicate on the "repair_time" field.
func RepairTimeLT(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLT(FieldRepairTime, v))
}

// RepairTimeLTE applies the LTE predicate on the "repair_time" field.
func RepairTimeLTE(v time.Time) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldLTE(FieldRepairTime, v))
}

// RepairTimeIsNil applies the IsNil predicate on the "repair_time" field.
func RepairTimeIsNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldIsNull(FieldRepairTime))
}

// RepairTimeNotNil applies the NotNil predicate on the "repair_time" field.
func RepairTimeNotNil() predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.FieldNotNull(FieldRepairTime))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.WmsRepairOrderDetail) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.WmsRepairOrderDetail) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.WmsRepairOrderDetail) predicate.WmsRepairOrderDetail {
	return predicate.WmsRepairOrderDetail(sql.NotPredicates(p))
}
