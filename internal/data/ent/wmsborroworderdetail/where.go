// Code generated by ent, DO NOT EDIT.

package wmsborroworderdetail

import (
	"kratos-mono-demo/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContainsFold(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldUpdatedAt, v))
}

// CreatedBy applies equality check predicate on the "created_by" field. It's identical to CreatedByEQ.
func CreatedBy(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldCreatedBy, v))
}

// UpdatedBy applies equality check predicate on the "updated_by" field. It's identical to UpdatedByEQ.
func UpdatedBy(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldUpdatedBy, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldRemark, v))
}

// OrderID applies equality check predicate on the "order_id" field. It's identical to OrderIDEQ.
func OrderID(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldOrderID, v))
}

// EquipmentTypeID applies equality check predicate on the "equipment_type_id" field. It's identical to EquipmentTypeIDEQ.
func EquipmentTypeID(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldEquipmentTypeID, v))
}

// EquipmentID applies equality check predicate on the "equipment_id" field. It's identical to EquipmentIDEQ.
func EquipmentID(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldEquipmentID, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldName, v))
}

// ModelNo applies equality check predicate on the "model_no" field. It's identical to ModelNoEQ.
func ModelNo(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldModelNo, v))
}

// MeasureUnitID applies equality check predicate on the "measure_unit_id" field. It's identical to MeasureUnitIDEQ.
func MeasureUnitID(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldMeasureUnitID, v))
}

// Num applies equality check predicate on the "num" field. It's identical to NumEQ.
func Num(v uint32) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldNum, v))
}

// RepositoryID applies equality check predicate on the "repository_id" field. It's identical to RepositoryIDEQ.
func RepositoryID(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldRepositoryID, v))
}

// ReturnTime applies equality check predicate on the "return_time" field. It's identical to ReturnTimeEQ.
func ReturnTime(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldReturnTime, v))
}

// IsReturn applies equality check predicate on the "is_return" field. It's identical to IsReturnEQ.
func IsReturn(v bool) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldIsReturn, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLTE(FieldUpdatedAt, v))
}

// CreatedByEQ applies the EQ predicate on the "created_by" field.
func CreatedByEQ(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldCreatedBy, v))
}

// CreatedByNEQ applies the NEQ predicate on the "created_by" field.
func CreatedByNEQ(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNEQ(FieldCreatedBy, v))
}

// CreatedByIn applies the In predicate on the "created_by" field.
func CreatedByIn(vs ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIn(FieldCreatedBy, vs...))
}

// CreatedByNotIn applies the NotIn predicate on the "created_by" field.
func CreatedByNotIn(vs ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotIn(FieldCreatedBy, vs...))
}

// CreatedByGT applies the GT predicate on the "created_by" field.
func CreatedByGT(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGT(FieldCreatedBy, v))
}

// CreatedByGTE applies the GTE predicate on the "created_by" field.
func CreatedByGTE(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGTE(FieldCreatedBy, v))
}

// CreatedByLT applies the LT predicate on the "created_by" field.
func CreatedByLT(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLT(FieldCreatedBy, v))
}

// CreatedByLTE applies the LTE predicate on the "created_by" field.
func CreatedByLTE(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLTE(FieldCreatedBy, v))
}

// CreatedByContains applies the Contains predicate on the "created_by" field.
func CreatedByContains(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContains(FieldCreatedBy, v))
}

// CreatedByHasPrefix applies the HasPrefix predicate on the "created_by" field.
func CreatedByHasPrefix(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldHasPrefix(FieldCreatedBy, v))
}

// CreatedByHasSuffix applies the HasSuffix predicate on the "created_by" field.
func CreatedByHasSuffix(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldHasSuffix(FieldCreatedBy, v))
}

// CreatedByIsNil applies the IsNil predicate on the "created_by" field.
func CreatedByIsNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIsNull(FieldCreatedBy))
}

// CreatedByNotNil applies the NotNil predicate on the "created_by" field.
func CreatedByNotNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotNull(FieldCreatedBy))
}

// CreatedByEqualFold applies the EqualFold predicate on the "created_by" field.
func CreatedByEqualFold(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEqualFold(FieldCreatedBy, v))
}

// CreatedByContainsFold applies the ContainsFold predicate on the "created_by" field.
func CreatedByContainsFold(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContainsFold(FieldCreatedBy, v))
}

// UpdatedByEQ applies the EQ predicate on the "updated_by" field.
func UpdatedByEQ(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldUpdatedBy, v))
}

// UpdatedByNEQ applies the NEQ predicate on the "updated_by" field.
func UpdatedByNEQ(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNEQ(FieldUpdatedBy, v))
}

// UpdatedByIn applies the In predicate on the "updated_by" field.
func UpdatedByIn(vs ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIn(FieldUpdatedBy, vs...))
}

// UpdatedByNotIn applies the NotIn predicate on the "updated_by" field.
func UpdatedByNotIn(vs ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotIn(FieldUpdatedBy, vs...))
}

// UpdatedByGT applies the GT predicate on the "updated_by" field.
func UpdatedByGT(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGT(FieldUpdatedBy, v))
}

// UpdatedByGTE applies the GTE predicate on the "updated_by" field.
func UpdatedByGTE(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGTE(FieldUpdatedBy, v))
}

// UpdatedByLT applies the LT predicate on the "updated_by" field.
func UpdatedByLT(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLT(FieldUpdatedBy, v))
}

// UpdatedByLTE applies the LTE predicate on the "updated_by" field.
func UpdatedByLTE(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLTE(FieldUpdatedBy, v))
}

// UpdatedByContains applies the Contains predicate on the "updated_by" field.
func UpdatedByContains(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContains(FieldUpdatedBy, v))
}

// UpdatedByHasPrefix applies the HasPrefix predicate on the "updated_by" field.
func UpdatedByHasPrefix(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldHasPrefix(FieldUpdatedBy, v))
}

// UpdatedByHasSuffix applies the HasSuffix predicate on the "updated_by" field.
func UpdatedByHasSuffix(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldHasSuffix(FieldUpdatedBy, v))
}

// UpdatedByIsNil applies the IsNil predicate on the "updated_by" field.
func UpdatedByIsNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIsNull(FieldUpdatedBy))
}

// UpdatedByNotNil applies the NotNil predicate on the "updated_by" field.
func UpdatedByNotNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotNull(FieldUpdatedBy))
}

// UpdatedByEqualFold applies the EqualFold predicate on the "updated_by" field.
func UpdatedByEqualFold(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEqualFold(FieldUpdatedBy, v))
}

// UpdatedByContainsFold applies the ContainsFold predicate on the "updated_by" field.
func UpdatedByContainsFold(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContainsFold(FieldUpdatedBy, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContainsFold(FieldRemark, v))
}

// OrderIDEQ applies the EQ predicate on the "order_id" field.
func OrderIDEQ(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldOrderID, v))
}

// OrderIDNEQ applies the NEQ predicate on the "order_id" field.
func OrderIDNEQ(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNEQ(FieldOrderID, v))
}

// OrderIDIn applies the In predicate on the "order_id" field.
func OrderIDIn(vs ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIn(FieldOrderID, vs...))
}

// OrderIDNotIn applies the NotIn predicate on the "order_id" field.
func OrderIDNotIn(vs ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotIn(FieldOrderID, vs...))
}

// OrderIDGT applies the GT predicate on the "order_id" field.
func OrderIDGT(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGT(FieldOrderID, v))
}

// OrderIDGTE applies the GTE predicate on the "order_id" field.
func OrderIDGTE(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGTE(FieldOrderID, v))
}

// OrderIDLT applies the LT predicate on the "order_id" field.
func OrderIDLT(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLT(FieldOrderID, v))
}

// OrderIDLTE applies the LTE predicate on the "order_id" field.
func OrderIDLTE(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLTE(FieldOrderID, v))
}

// OrderIDContains applies the Contains predicate on the "order_id" field.
func OrderIDContains(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContains(FieldOrderID, v))
}

// OrderIDHasPrefix applies the HasPrefix predicate on the "order_id" field.
func OrderIDHasPrefix(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldHasPrefix(FieldOrderID, v))
}

// OrderIDHasSuffix applies the HasSuffix predicate on the "order_id" field.
func OrderIDHasSuffix(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldHasSuffix(FieldOrderID, v))
}

// OrderIDIsNil applies the IsNil predicate on the "order_id" field.
func OrderIDIsNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIsNull(FieldOrderID))
}

// OrderIDNotNil applies the NotNil predicate on the "order_id" field.
func OrderIDNotNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotNull(FieldOrderID))
}

// OrderIDEqualFold applies the EqualFold predicate on the "order_id" field.
func OrderIDEqualFold(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEqualFold(FieldOrderID, v))
}

// OrderIDContainsFold applies the ContainsFold predicate on the "order_id" field.
func OrderIDContainsFold(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContainsFold(FieldOrderID, v))
}

// EquipmentTypeIDEQ applies the EQ predicate on the "equipment_type_id" field.
func EquipmentTypeIDEQ(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDNEQ applies the NEQ predicate on the "equipment_type_id" field.
func EquipmentTypeIDNEQ(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNEQ(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDIn applies the In predicate on the "equipment_type_id" field.
func EquipmentTypeIDIn(vs ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIn(FieldEquipmentTypeID, vs...))
}

// EquipmentTypeIDNotIn applies the NotIn predicate on the "equipment_type_id" field.
func EquipmentTypeIDNotIn(vs ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotIn(FieldEquipmentTypeID, vs...))
}

// EquipmentTypeIDGT applies the GT predicate on the "equipment_type_id" field.
func EquipmentTypeIDGT(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGT(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDGTE applies the GTE predicate on the "equipment_type_id" field.
func EquipmentTypeIDGTE(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGTE(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDLT applies the LT predicate on the "equipment_type_id" field.
func EquipmentTypeIDLT(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLT(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDLTE applies the LTE predicate on the "equipment_type_id" field.
func EquipmentTypeIDLTE(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLTE(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDContains applies the Contains predicate on the "equipment_type_id" field.
func EquipmentTypeIDContains(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContains(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDHasPrefix applies the HasPrefix predicate on the "equipment_type_id" field.
func EquipmentTypeIDHasPrefix(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldHasPrefix(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDHasSuffix applies the HasSuffix predicate on the "equipment_type_id" field.
func EquipmentTypeIDHasSuffix(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldHasSuffix(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDIsNil applies the IsNil predicate on the "equipment_type_id" field.
func EquipmentTypeIDIsNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIsNull(FieldEquipmentTypeID))
}

// EquipmentTypeIDNotNil applies the NotNil predicate on the "equipment_type_id" field.
func EquipmentTypeIDNotNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotNull(FieldEquipmentTypeID))
}

// EquipmentTypeIDEqualFold applies the EqualFold predicate on the "equipment_type_id" field.
func EquipmentTypeIDEqualFold(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEqualFold(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDContainsFold applies the ContainsFold predicate on the "equipment_type_id" field.
func EquipmentTypeIDContainsFold(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContainsFold(FieldEquipmentTypeID, v))
}

// EquipmentIDEQ applies the EQ predicate on the "equipment_id" field.
func EquipmentIDEQ(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldEquipmentID, v))
}

// EquipmentIDNEQ applies the NEQ predicate on the "equipment_id" field.
func EquipmentIDNEQ(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNEQ(FieldEquipmentID, v))
}

// EquipmentIDIn applies the In predicate on the "equipment_id" field.
func EquipmentIDIn(vs ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIn(FieldEquipmentID, vs...))
}

// EquipmentIDNotIn applies the NotIn predicate on the "equipment_id" field.
func EquipmentIDNotIn(vs ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotIn(FieldEquipmentID, vs...))
}

// EquipmentIDGT applies the GT predicate on the "equipment_id" field.
func EquipmentIDGT(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGT(FieldEquipmentID, v))
}

// EquipmentIDGTE applies the GTE predicate on the "equipment_id" field.
func EquipmentIDGTE(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGTE(FieldEquipmentID, v))
}

// EquipmentIDLT applies the LT predicate on the "equipment_id" field.
func EquipmentIDLT(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLT(FieldEquipmentID, v))
}

// EquipmentIDLTE applies the LTE predicate on the "equipment_id" field.
func EquipmentIDLTE(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLTE(FieldEquipmentID, v))
}

// EquipmentIDContains applies the Contains predicate on the "equipment_id" field.
func EquipmentIDContains(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContains(FieldEquipmentID, v))
}

// EquipmentIDHasPrefix applies the HasPrefix predicate on the "equipment_id" field.
func EquipmentIDHasPrefix(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldHasPrefix(FieldEquipmentID, v))
}

// EquipmentIDHasSuffix applies the HasSuffix predicate on the "equipment_id" field.
func EquipmentIDHasSuffix(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldHasSuffix(FieldEquipmentID, v))
}

// EquipmentIDIsNil applies the IsNil predicate on the "equipment_id" field.
func EquipmentIDIsNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIsNull(FieldEquipmentID))
}

// EquipmentIDNotNil applies the NotNil predicate on the "equipment_id" field.
func EquipmentIDNotNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotNull(FieldEquipmentID))
}

// EquipmentIDEqualFold applies the EqualFold predicate on the "equipment_id" field.
func EquipmentIDEqualFold(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEqualFold(FieldEquipmentID, v))
}

// EquipmentIDContainsFold applies the ContainsFold predicate on the "equipment_id" field.
func EquipmentIDContainsFold(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContainsFold(FieldEquipmentID, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldHasSuffix(FieldName, v))
}

// NameIsNil applies the IsNil predicate on the "name" field.
func NameIsNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIsNull(FieldName))
}

// NameNotNil applies the NotNil predicate on the "name" field.
func NameNotNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotNull(FieldName))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContainsFold(FieldName, v))
}

// ModelNoEQ applies the EQ predicate on the "model_no" field.
func ModelNoEQ(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldModelNo, v))
}

// ModelNoNEQ applies the NEQ predicate on the "model_no" field.
func ModelNoNEQ(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNEQ(FieldModelNo, v))
}

// ModelNoIn applies the In predicate on the "model_no" field.
func ModelNoIn(vs ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIn(FieldModelNo, vs...))
}

// ModelNoNotIn applies the NotIn predicate on the "model_no" field.
func ModelNoNotIn(vs ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotIn(FieldModelNo, vs...))
}

// ModelNoGT applies the GT predicate on the "model_no" field.
func ModelNoGT(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGT(FieldModelNo, v))
}

// ModelNoGTE applies the GTE predicate on the "model_no" field.
func ModelNoGTE(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGTE(FieldModelNo, v))
}

// ModelNoLT applies the LT predicate on the "model_no" field.
func ModelNoLT(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLT(FieldModelNo, v))
}

// ModelNoLTE applies the LTE predicate on the "model_no" field.
func ModelNoLTE(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLTE(FieldModelNo, v))
}

// ModelNoContains applies the Contains predicate on the "model_no" field.
func ModelNoContains(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContains(FieldModelNo, v))
}

// ModelNoHasPrefix applies the HasPrefix predicate on the "model_no" field.
func ModelNoHasPrefix(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldHasPrefix(FieldModelNo, v))
}

// ModelNoHasSuffix applies the HasSuffix predicate on the "model_no" field.
func ModelNoHasSuffix(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldHasSuffix(FieldModelNo, v))
}

// ModelNoEqualFold applies the EqualFold predicate on the "model_no" field.
func ModelNoEqualFold(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEqualFold(FieldModelNo, v))
}

// ModelNoContainsFold applies the ContainsFold predicate on the "model_no" field.
func ModelNoContainsFold(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContainsFold(FieldModelNo, v))
}

// MeasureUnitIDEQ applies the EQ predicate on the "measure_unit_id" field.
func MeasureUnitIDEQ(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldMeasureUnitID, v))
}

// MeasureUnitIDNEQ applies the NEQ predicate on the "measure_unit_id" field.
func MeasureUnitIDNEQ(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNEQ(FieldMeasureUnitID, v))
}

// MeasureUnitIDIn applies the In predicate on the "measure_unit_id" field.
func MeasureUnitIDIn(vs ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIn(FieldMeasureUnitID, vs...))
}

// MeasureUnitIDNotIn applies the NotIn predicate on the "measure_unit_id" field.
func MeasureUnitIDNotIn(vs ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotIn(FieldMeasureUnitID, vs...))
}

// MeasureUnitIDGT applies the GT predicate on the "measure_unit_id" field.
func MeasureUnitIDGT(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGT(FieldMeasureUnitID, v))
}

// MeasureUnitIDGTE applies the GTE predicate on the "measure_unit_id" field.
func MeasureUnitIDGTE(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGTE(FieldMeasureUnitID, v))
}

// MeasureUnitIDLT applies the LT predicate on the "measure_unit_id" field.
func MeasureUnitIDLT(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLT(FieldMeasureUnitID, v))
}

// MeasureUnitIDLTE applies the LTE predicate on the "measure_unit_id" field.
func MeasureUnitIDLTE(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLTE(FieldMeasureUnitID, v))
}

// MeasureUnitIDContains applies the Contains predicate on the "measure_unit_id" field.
func MeasureUnitIDContains(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContains(FieldMeasureUnitID, v))
}

// MeasureUnitIDHasPrefix applies the HasPrefix predicate on the "measure_unit_id" field.
func MeasureUnitIDHasPrefix(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldHasPrefix(FieldMeasureUnitID, v))
}

// MeasureUnitIDHasSuffix applies the HasSuffix predicate on the "measure_unit_id" field.
func MeasureUnitIDHasSuffix(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldHasSuffix(FieldMeasureUnitID, v))
}

// MeasureUnitIDIsNil applies the IsNil predicate on the "measure_unit_id" field.
func MeasureUnitIDIsNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIsNull(FieldMeasureUnitID))
}

// MeasureUnitIDNotNil applies the NotNil predicate on the "measure_unit_id" field.
func MeasureUnitIDNotNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotNull(FieldMeasureUnitID))
}

// MeasureUnitIDEqualFold applies the EqualFold predicate on the "measure_unit_id" field.
func MeasureUnitIDEqualFold(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEqualFold(FieldMeasureUnitID, v))
}

// MeasureUnitIDContainsFold applies the ContainsFold predicate on the "measure_unit_id" field.
func MeasureUnitIDContainsFold(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContainsFold(FieldMeasureUnitID, v))
}

// NumEQ applies the EQ predicate on the "num" field.
func NumEQ(v uint32) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldNum, v))
}

// NumNEQ applies the NEQ predicate on the "num" field.
func NumNEQ(v uint32) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNEQ(FieldNum, v))
}

// NumIn applies the In predicate on the "num" field.
func NumIn(vs ...uint32) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIn(FieldNum, vs...))
}

// NumNotIn applies the NotIn predicate on the "num" field.
func NumNotIn(vs ...uint32) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotIn(FieldNum, vs...))
}

// NumGT applies the GT predicate on the "num" field.
func NumGT(v uint32) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGT(FieldNum, v))
}

// NumGTE applies the GTE predicate on the "num" field.
func NumGTE(v uint32) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGTE(FieldNum, v))
}

// NumLT applies the LT predicate on the "num" field.
func NumLT(v uint32) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLT(FieldNum, v))
}

// NumLTE applies the LTE predicate on the "num" field.
func NumLTE(v uint32) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLTE(FieldNum, v))
}

// RepositoryIDEQ applies the EQ predicate on the "repository_id" field.
func RepositoryIDEQ(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldRepositoryID, v))
}

// RepositoryIDNEQ applies the NEQ predicate on the "repository_id" field.
func RepositoryIDNEQ(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNEQ(FieldRepositoryID, v))
}

// RepositoryIDIn applies the In predicate on the "repository_id" field.
func RepositoryIDIn(vs ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIn(FieldRepositoryID, vs...))
}

// RepositoryIDNotIn applies the NotIn predicate on the "repository_id" field.
func RepositoryIDNotIn(vs ...string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotIn(FieldRepositoryID, vs...))
}

// RepositoryIDGT applies the GT predicate on the "repository_id" field.
func RepositoryIDGT(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGT(FieldRepositoryID, v))
}

// RepositoryIDGTE applies the GTE predicate on the "repository_id" field.
func RepositoryIDGTE(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGTE(FieldRepositoryID, v))
}

// RepositoryIDLT applies the LT predicate on the "repository_id" field.
func RepositoryIDLT(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLT(FieldRepositoryID, v))
}

// RepositoryIDLTE applies the LTE predicate on the "repository_id" field.
func RepositoryIDLTE(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLTE(FieldRepositoryID, v))
}

// RepositoryIDContains applies the Contains predicate on the "repository_id" field.
func RepositoryIDContains(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContains(FieldRepositoryID, v))
}

// RepositoryIDHasPrefix applies the HasPrefix predicate on the "repository_id" field.
func RepositoryIDHasPrefix(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldHasPrefix(FieldRepositoryID, v))
}

// RepositoryIDHasSuffix applies the HasSuffix predicate on the "repository_id" field.
func RepositoryIDHasSuffix(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldHasSuffix(FieldRepositoryID, v))
}

// RepositoryIDIsNil applies the IsNil predicate on the "repository_id" field.
func RepositoryIDIsNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIsNull(FieldRepositoryID))
}

// RepositoryIDNotNil applies the NotNil predicate on the "repository_id" field.
func RepositoryIDNotNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotNull(FieldRepositoryID))
}

// RepositoryIDEqualFold applies the EqualFold predicate on the "repository_id" field.
func RepositoryIDEqualFold(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEqualFold(FieldRepositoryID, v))
}

// RepositoryIDContainsFold applies the ContainsFold predicate on the "repository_id" field.
func RepositoryIDContainsFold(v string) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldContainsFold(FieldRepositoryID, v))
}

// ReturnTimeEQ applies the EQ predicate on the "return_time" field.
func ReturnTimeEQ(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldReturnTime, v))
}

// ReturnTimeNEQ applies the NEQ predicate on the "return_time" field.
func ReturnTimeNEQ(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNEQ(FieldReturnTime, v))
}

// ReturnTimeIn applies the In predicate on the "return_time" field.
func ReturnTimeIn(vs ...time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIn(FieldReturnTime, vs...))
}

// ReturnTimeNotIn applies the NotIn predicate on the "return_time" field.
func ReturnTimeNotIn(vs ...time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotIn(FieldReturnTime, vs...))
}

// ReturnTimeGT applies the GT predicate on the "return_time" field.
func ReturnTimeGT(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGT(FieldReturnTime, v))
}

// ReturnTimeGTE applies the GTE predicate on the "return_time" field.
func ReturnTimeGTE(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldGTE(FieldReturnTime, v))
}

// ReturnTimeLT applies the LT predicate on the "return_time" field.
func ReturnTimeLT(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLT(FieldReturnTime, v))
}

// ReturnTimeLTE applies the LTE predicate on the "return_time" field.
func ReturnTimeLTE(v time.Time) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldLTE(FieldReturnTime, v))
}

// ReturnTimeIsNil applies the IsNil predicate on the "return_time" field.
func ReturnTimeIsNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIsNull(FieldReturnTime))
}

// ReturnTimeNotNil applies the NotNil predicate on the "return_time" field.
func ReturnTimeNotNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotNull(FieldReturnTime))
}

// IsReturnEQ applies the EQ predicate on the "is_return" field.
func IsReturnEQ(v bool) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldEQ(FieldIsReturn, v))
}

// IsReturnNEQ applies the NEQ predicate on the "is_return" field.
func IsReturnNEQ(v bool) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNEQ(FieldIsReturn, v))
}

// FeatureIsNil applies the IsNil predicate on the "feature" field.
func FeatureIsNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldIsNull(FieldFeature))
}

// FeatureNotNil applies the NotNil predicate on the "feature" field.
func FeatureNotNil() predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.FieldNotNull(FieldFeature))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.WmsBorrowOrderDetail) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.WmsBorrowOrderDetail) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.WmsBorrowOrderDetail) predicate.WmsBorrowOrderDetail {
	return predicate.WmsBorrowOrderDetail(sql.NotPredicates(p))
}
