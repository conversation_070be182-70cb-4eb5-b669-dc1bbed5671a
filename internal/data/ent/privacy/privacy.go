// Code generated by ent, DO NOT EDIT.

package privacy

import (
	"context"

	"kratos-mono-demo/internal/data/ent"

	"entgo.io/ent/entql"
	"entgo.io/ent/privacy"
)

var (
	// Allow may be returned by rules to indicate that the policy
	// evaluation should terminate with allow decision.
	Allow = privacy.Allow

	// Deny may be returned by rules to indicate that the policy
	// evaluation should terminate with deny decision.
	Deny = privacy.Deny

	// Skip may be returned by rules to indicate that the policy
	// evaluation should continue to the next rule.
	Skip = privacy.Skip
)

// Allowf returns a formatted wrapped Allow decision.
func Allowf(format string, a ...any) error {
	return privacy.Allowf(format, a...)
}

// Denyf returns a formatted wrapped Deny decision.
func Denyf(format string, a ...any) error {
	return privacy.Denyf(format, a...)
}

// <PERSON><PERSON> returns a formatted wrapped Skip decision.
func Skipf(format string, a ...any) error {
	return privacy.Skipf(format, a...)
}

// DecisionContext creates a new context from the given parent context with
// a policy decision attach to it.
func DecisionContext(parent context.Context, decision error) context.Context {
	return privacy.DecisionContext(parent, decision)
}

// DecisionFromContext retrieves the policy decision from the context.
func DecisionFromContext(ctx context.Context) (error, bool) {
	return privacy.DecisionFromContext(ctx)
}

type (
	// Policy groups query and mutation policies.
	Policy = privacy.Policy

	// QueryRule defines the interface deciding whether a
	// query is allowed and optionally modify it.
	QueryRule = privacy.QueryRule
	// QueryPolicy combines multiple query rules into a single policy.
	QueryPolicy = privacy.QueryPolicy

	// MutationRule defines the interface which decides whether a
	// mutation is allowed and optionally modifies it.
	MutationRule = privacy.MutationRule
	// MutationPolicy combines multiple mutation rules into a single policy.
	MutationPolicy = privacy.MutationPolicy
	// MutationRuleFunc type is an adapter which allows the use of
	// ordinary functions as mutation rules.
	MutationRuleFunc = privacy.MutationRuleFunc

	// QueryMutationRule is an interface which groups query and mutation rules.
	QueryMutationRule = privacy.QueryMutationRule
)

// QueryRuleFunc type is an adapter to allow the use of
// ordinary functions as query rules.
type QueryRuleFunc func(context.Context, ent.Query) error

// Eval returns f(ctx, q).
func (f QueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	return f(ctx, q)
}

// AlwaysAllowRule returns a rule that returns an allow decision.
func AlwaysAllowRule() QueryMutationRule {
	return privacy.AlwaysAllowRule()
}

// AlwaysDenyRule returns a rule that returns a deny decision.
func AlwaysDenyRule() QueryMutationRule {
	return privacy.AlwaysDenyRule()
}

// ContextQueryMutationRule creates a query/mutation rule from a context eval func.
func ContextQueryMutationRule(eval func(context.Context) error) QueryMutationRule {
	return privacy.ContextQueryMutationRule(eval)
}

// OnMutationOperation evaluates the given rule only on a given mutation operation.
func OnMutationOperation(rule MutationRule, op ent.Op) MutationRule {
	return privacy.OnMutationOperation(rule, op)
}

// DenyMutationOperationRule returns a rule denying specified mutation operation.
func DenyMutationOperationRule(op ent.Op) MutationRule {
	rule := MutationRuleFunc(func(_ context.Context, m ent.Mutation) error {
		return Denyf("ent/privacy: operation %s is not allowed", m.Op())
	})
	return OnMutationOperation(rule, op)
}

// The AppApproveActivityQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type AppApproveActivityQueryRuleFunc func(context.Context, *ent.AppApproveActivityQuery) error

// EvalQuery return f(ctx, q).
func (f AppApproveActivityQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AppApproveActivityQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.AppApproveActivityQuery", q)
}

// The AppApproveActivityMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type AppApproveActivityMutationRuleFunc func(context.Context, *ent.AppApproveActivityMutation) error

// EvalMutation calls f(ctx, m).
func (f AppApproveActivityMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.AppApproveActivityMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.AppApproveActivityMutation", m)
}

// The AppApproveBasicQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type AppApproveBasicQueryRuleFunc func(context.Context, *ent.AppApproveBasicQuery) error

// EvalQuery return f(ctx, q).
func (f AppApproveBasicQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AppApproveBasicQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.AppApproveBasicQuery", q)
}

// The AppApproveBasicMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type AppApproveBasicMutationRuleFunc func(context.Context, *ent.AppApproveBasicMutation) error

// EvalMutation calls f(ctx, m).
func (f AppApproveBasicMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.AppApproveBasicMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.AppApproveBasicMutation", m)
}

// The AppApproveGroupQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type AppApproveGroupQueryRuleFunc func(context.Context, *ent.AppApproveGroupQuery) error

// EvalQuery return f(ctx, q).
func (f AppApproveGroupQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AppApproveGroupQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.AppApproveGroupQuery", q)
}

// The AppApproveGroupMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type AppApproveGroupMutationRuleFunc func(context.Context, *ent.AppApproveGroupMutation) error

// EvalMutation calls f(ctx, m).
func (f AppApproveGroupMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.AppApproveGroupMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.AppApproveGroupMutation", m)
}

// The AppApproveRecordQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type AppApproveRecordQueryRuleFunc func(context.Context, *ent.AppApproveRecordQuery) error

// EvalQuery return f(ctx, q).
func (f AppApproveRecordQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AppApproveRecordQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.AppApproveRecordQuery", q)
}

// The AppApproveRecordMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type AppApproveRecordMutationRuleFunc func(context.Context, *ent.AppApproveRecordMutation) error

// EvalMutation calls f(ctx, m).
func (f AppApproveRecordMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.AppApproveRecordMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.AppApproveRecordMutation", m)
}

// The AppApproveRecordLogQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type AppApproveRecordLogQueryRuleFunc func(context.Context, *ent.AppApproveRecordLogQuery) error

// EvalQuery return f(ctx, q).
func (f AppApproveRecordLogQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AppApproveRecordLogQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.AppApproveRecordLogQuery", q)
}

// The AppApproveRecordLogMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type AppApproveRecordLogMutationRuleFunc func(context.Context, *ent.AppApproveRecordLogMutation) error

// EvalMutation calls f(ctx, m).
func (f AppApproveRecordLogMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.AppApproveRecordLogMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.AppApproveRecordLogMutation", m)
}

// The AppApproveRecordQueueQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type AppApproveRecordQueueQueryRuleFunc func(context.Context, *ent.AppApproveRecordQueueQuery) error

// EvalQuery return f(ctx, q).
func (f AppApproveRecordQueueQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AppApproveRecordQueueQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.AppApproveRecordQueueQuery", q)
}

// The AppApproveRecordQueueMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type AppApproveRecordQueueMutationRuleFunc func(context.Context, *ent.AppApproveRecordQueueMutation) error

// EvalMutation calls f(ctx, m).
func (f AppApproveRecordQueueMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.AppApproveRecordQueueMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.AppApproveRecordQueueMutation", m)
}

// The AppApproveWorkflowQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type AppApproveWorkflowQueryRuleFunc func(context.Context, *ent.AppApproveWorkflowQuery) error

// EvalQuery return f(ctx, q).
func (f AppApproveWorkflowQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AppApproveWorkflowQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.AppApproveWorkflowQuery", q)
}

// The AppApproveWorkflowMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type AppApproveWorkflowMutationRuleFunc func(context.Context, *ent.AppApproveWorkflowMutation) error

// EvalMutation calls f(ctx, m).
func (f AppApproveWorkflowMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.AppApproveWorkflowMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.AppApproveWorkflowMutation", m)
}

// The DemoBuildingQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type DemoBuildingQueryRuleFunc func(context.Context, *ent.DemoBuildingQuery) error

// EvalQuery return f(ctx, q).
func (f DemoBuildingQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DemoBuildingQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.DemoBuildingQuery", q)
}

// The DemoBuildingMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type DemoBuildingMutationRuleFunc func(context.Context, *ent.DemoBuildingMutation) error

// EvalMutation calls f(ctx, m).
func (f DemoBuildingMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.DemoBuildingMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.DemoBuildingMutation", m)
}

// The SysApplicationQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysApplicationQueryRuleFunc func(context.Context, *ent.SysApplicationQuery) error

// EvalQuery return f(ctx, q).
func (f SysApplicationQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysApplicationQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysApplicationQuery", q)
}

// The SysApplicationMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysApplicationMutationRuleFunc func(context.Context, *ent.SysApplicationMutation) error

// EvalMutation calls f(ctx, m).
func (f SysApplicationMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysApplicationMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysApplicationMutation", m)
}

// The SysApplicationModuleQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysApplicationModuleQueryRuleFunc func(context.Context, *ent.SysApplicationModuleQuery) error

// EvalQuery return f(ctx, q).
func (f SysApplicationModuleQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysApplicationModuleQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysApplicationModuleQuery", q)
}

// The SysApplicationModuleMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysApplicationModuleMutationRuleFunc func(context.Context, *ent.SysApplicationModuleMutation) error

// EvalMutation calls f(ctx, m).
func (f SysApplicationModuleMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysApplicationModuleMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysApplicationModuleMutation", m)
}

// The SysApplicationModuleResourceQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysApplicationModuleResourceQueryRuleFunc func(context.Context, *ent.SysApplicationModuleResourceQuery) error

// EvalQuery return f(ctx, q).
func (f SysApplicationModuleResourceQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysApplicationModuleResourceQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysApplicationModuleResourceQuery", q)
}

// The SysApplicationModuleResourceMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysApplicationModuleResourceMutationRuleFunc func(context.Context, *ent.SysApplicationModuleResourceMutation) error

// EvalMutation calls f(ctx, m).
func (f SysApplicationModuleResourceMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysApplicationModuleResourceMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysApplicationModuleResourceMutation", m)
}

// The SysAreaQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysAreaQueryRuleFunc func(context.Context, *ent.SysAreaQuery) error

// EvalQuery return f(ctx, q).
func (f SysAreaQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysAreaQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysAreaQuery", q)
}

// The SysAreaMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysAreaMutationRuleFunc func(context.Context, *ent.SysAreaMutation) error

// EvalMutation calls f(ctx, m).
func (f SysAreaMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysAreaMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysAreaMutation", m)
}

// The SysCityQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysCityQueryRuleFunc func(context.Context, *ent.SysCityQuery) error

// EvalQuery return f(ctx, q).
func (f SysCityQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysCityQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysCityQuery", q)
}

// The SysCityMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysCityMutationRuleFunc func(context.Context, *ent.SysCityMutation) error

// EvalMutation calls f(ctx, m).
func (f SysCityMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysCityMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysCityMutation", m)
}

// The SysConfigQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysConfigQueryRuleFunc func(context.Context, *ent.SysConfigQuery) error

// EvalQuery return f(ctx, q).
func (f SysConfigQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysConfigQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysConfigQuery", q)
}

// The SysConfigMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysConfigMutationRuleFunc func(context.Context, *ent.SysConfigMutation) error

// EvalMutation calls f(ctx, m).
func (f SysConfigMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysConfigMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysConfigMutation", m)
}

// The SysDictionaryQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysDictionaryQueryRuleFunc func(context.Context, *ent.SysDictionaryQuery) error

// EvalQuery return f(ctx, q).
func (f SysDictionaryQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysDictionaryQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysDictionaryQuery", q)
}

// The SysDictionaryMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysDictionaryMutationRuleFunc func(context.Context, *ent.SysDictionaryMutation) error

// EvalMutation calls f(ctx, m).
func (f SysDictionaryMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysDictionaryMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysDictionaryMutation", m)
}

// The SysDictionaryDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysDictionaryDetailQueryRuleFunc func(context.Context, *ent.SysDictionaryDetailQuery) error

// EvalQuery return f(ctx, q).
func (f SysDictionaryDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysDictionaryDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysDictionaryDetailQuery", q)
}

// The SysDictionaryDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysDictionaryDetailMutationRuleFunc func(context.Context, *ent.SysDictionaryDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f SysDictionaryDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysDictionaryDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysDictionaryDetailMutation", m)
}

// The SysGameQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysGameQueryRuleFunc func(context.Context, *ent.SysGameQuery) error

// EvalQuery return f(ctx, q).
func (f SysGameQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysGameQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysGameQuery", q)
}

// The SysGameMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysGameMutationRuleFunc func(context.Context, *ent.SysGameMutation) error

// EvalMutation calls f(ctx, m).
func (f SysGameMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysGameMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysGameMutation", m)
}

// The SysLabelQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysLabelQueryRuleFunc func(context.Context, *ent.SysLabelQuery) error

// EvalQuery return f(ctx, q).
func (f SysLabelQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysLabelQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysLabelQuery", q)
}

// The SysLabelMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysLabelMutationRuleFunc func(context.Context, *ent.SysLabelMutation) error

// EvalMutation calls f(ctx, m).
func (f SysLabelMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysLabelMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysLabelMutation", m)
}

// The SysLogQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysLogQueryRuleFunc func(context.Context, *ent.SysLogQuery) error

// EvalQuery return f(ctx, q).
func (f SysLogQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysLogQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysLogQuery", q)
}

// The SysLogMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysLogMutationRuleFunc func(context.Context, *ent.SysLogMutation) error

// EvalMutation calls f(ctx, m).
func (f SysLogMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysLogMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysLogMutation", m)
}

// The SysMenuQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysMenuQueryRuleFunc func(context.Context, *ent.SysMenuQuery) error

// EvalQuery return f(ctx, q).
func (f SysMenuQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysMenuQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysMenuQuery", q)
}

// The SysMenuMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysMenuMutationRuleFunc func(context.Context, *ent.SysMenuMutation) error

// EvalMutation calls f(ctx, m).
func (f SysMenuMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysMenuMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysMenuMutation", m)
}

// The SysMessageTemplateQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysMessageTemplateQueryRuleFunc func(context.Context, *ent.SysMessageTemplateQuery) error

// EvalQuery return f(ctx, q).
func (f SysMessageTemplateQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysMessageTemplateQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysMessageTemplateQuery", q)
}

// The SysMessageTemplateMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysMessageTemplateMutationRuleFunc func(context.Context, *ent.SysMessageTemplateMutation) error

// EvalMutation calls f(ctx, m).
func (f SysMessageTemplateMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysMessageTemplateMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysMessageTemplateMutation", m)
}

// The SysModuleQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysModuleQueryRuleFunc func(context.Context, *ent.SysModuleQuery) error

// EvalQuery return f(ctx, q).
func (f SysModuleQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysModuleQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysModuleQuery", q)
}

// The SysModuleMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysModuleMutationRuleFunc func(context.Context, *ent.SysModuleMutation) error

// EvalMutation calls f(ctx, m).
func (f SysModuleMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysModuleMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysModuleMutation", m)
}

// The SysModuleResourceQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysModuleResourceQueryRuleFunc func(context.Context, *ent.SysModuleResourceQuery) error

// EvalQuery return f(ctx, q).
func (f SysModuleResourceQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysModuleResourceQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysModuleResourceQuery", q)
}

// The SysModuleResourceMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysModuleResourceMutationRuleFunc func(context.Context, *ent.SysModuleResourceMutation) error

// EvalMutation calls f(ctx, m).
func (f SysModuleResourceMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysModuleResourceMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysModuleResourceMutation", m)
}

// The SysOrganizationQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysOrganizationQueryRuleFunc func(context.Context, *ent.SysOrganizationQuery) error

// EvalQuery return f(ctx, q).
func (f SysOrganizationQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysOrganizationQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysOrganizationQuery", q)
}

// The SysOrganizationMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysOrganizationMutationRuleFunc func(context.Context, *ent.SysOrganizationMutation) error

// EvalMutation calls f(ctx, m).
func (f SysOrganizationMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysOrganizationMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysOrganizationMutation", m)
}

// The SysPageCodeQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysPageCodeQueryRuleFunc func(context.Context, *ent.SysPageCodeQuery) error

// EvalQuery return f(ctx, q).
func (f SysPageCodeQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysPageCodeQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysPageCodeQuery", q)
}

// The SysPageCodeMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysPageCodeMutationRuleFunc func(context.Context, *ent.SysPageCodeMutation) error

// EvalMutation calls f(ctx, m).
func (f SysPageCodeMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysPageCodeMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysPageCodeMutation", m)
}

// The SysPageCodeHistoryQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysPageCodeHistoryQueryRuleFunc func(context.Context, *ent.SysPageCodeHistoryQuery) error

// EvalQuery return f(ctx, q).
func (f SysPageCodeHistoryQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysPageCodeHistoryQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysPageCodeHistoryQuery", q)
}

// The SysPageCodeHistoryMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysPageCodeHistoryMutationRuleFunc func(context.Context, *ent.SysPageCodeHistoryMutation) error

// EvalMutation calls f(ctx, m).
func (f SysPageCodeHistoryMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysPageCodeHistoryMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysPageCodeHistoryMutation", m)
}

// The SysProjectQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysProjectQueryRuleFunc func(context.Context, *ent.SysProjectQuery) error

// EvalQuery return f(ctx, q).
func (f SysProjectQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysProjectQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysProjectQuery", q)
}

// The SysProjectMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysProjectMutationRuleFunc func(context.Context, *ent.SysProjectMutation) error

// EvalMutation calls f(ctx, m).
func (f SysProjectMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysProjectMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysProjectMutation", m)
}

// The SysProvinceQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysProvinceQueryRuleFunc func(context.Context, *ent.SysProvinceQuery) error

// EvalQuery return f(ctx, q).
func (f SysProvinceQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysProvinceQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysProvinceQuery", q)
}

// The SysProvinceMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysProvinceMutationRuleFunc func(context.Context, *ent.SysProvinceMutation) error

// EvalMutation calls f(ctx, m).
func (f SysProvinceMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysProvinceMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysProvinceMutation", m)
}

// The SysResourceQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysResourceQueryRuleFunc func(context.Context, *ent.SysResourceQuery) error

// EvalQuery return f(ctx, q).
func (f SysResourceQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysResourceQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysResourceQuery", q)
}

// The SysResourceMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysResourceMutationRuleFunc func(context.Context, *ent.SysResourceMutation) error

// EvalMutation calls f(ctx, m).
func (f SysResourceMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysResourceMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysResourceMutation", m)
}

// The SysRoleQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysRoleQueryRuleFunc func(context.Context, *ent.SysRoleQuery) error

// EvalQuery return f(ctx, q).
func (f SysRoleQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysRoleQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysRoleQuery", q)
}

// The SysRoleMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysRoleMutationRuleFunc func(context.Context, *ent.SysRoleMutation) error

// EvalMutation calls f(ctx, m).
func (f SysRoleMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysRoleMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysRoleMutation", m)
}

// The SysRoleMenuQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysRoleMenuQueryRuleFunc func(context.Context, *ent.SysRoleMenuQuery) error

// EvalQuery return f(ctx, q).
func (f SysRoleMenuQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysRoleMenuQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysRoleMenuQuery", q)
}

// The SysRoleMenuMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysRoleMenuMutationRuleFunc func(context.Context, *ent.SysRoleMenuMutation) error

// EvalMutation calls f(ctx, m).
func (f SysRoleMenuMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysRoleMenuMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysRoleMenuMutation", m)
}

// The SysRoleResourceQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysRoleResourceQueryRuleFunc func(context.Context, *ent.SysRoleResourceQuery) error

// EvalQuery return f(ctx, q).
func (f SysRoleResourceQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysRoleResourceQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysRoleResourceQuery", q)
}

// The SysRoleResourceMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysRoleResourceMutationRuleFunc func(context.Context, *ent.SysRoleResourceMutation) error

// EvalMutation calls f(ctx, m).
func (f SysRoleResourceMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysRoleResourceMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysRoleResourceMutation", m)
}

// The SysStreetQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysStreetQueryRuleFunc func(context.Context, *ent.SysStreetQuery) error

// EvalQuery return f(ctx, q).
func (f SysStreetQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysStreetQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysStreetQuery", q)
}

// The SysStreetMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysStreetMutationRuleFunc func(context.Context, *ent.SysStreetMutation) error

// EvalMutation calls f(ctx, m).
func (f SysStreetMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysStreetMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysStreetMutation", m)
}

// The SysTeamQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysTeamQueryRuleFunc func(context.Context, *ent.SysTeamQuery) error

// EvalQuery return f(ctx, q).
func (f SysTeamQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysTeamQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysTeamQuery", q)
}

// The SysTeamMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysTeamMutationRuleFunc func(context.Context, *ent.SysTeamMutation) error

// EvalMutation calls f(ctx, m).
func (f SysTeamMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysTeamMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysTeamMutation", m)
}

// The SysTeamApplicationQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysTeamApplicationQueryRuleFunc func(context.Context, *ent.SysTeamApplicationQuery) error

// EvalQuery return f(ctx, q).
func (f SysTeamApplicationQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysTeamApplicationQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysTeamApplicationQuery", q)
}

// The SysTeamApplicationMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysTeamApplicationMutationRuleFunc func(context.Context, *ent.SysTeamApplicationMutation) error

// EvalMutation calls f(ctx, m).
func (f SysTeamApplicationMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysTeamApplicationMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysTeamApplicationMutation", m)
}

// The SysUploadQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysUploadQueryRuleFunc func(context.Context, *ent.SysUploadQuery) error

// EvalQuery return f(ctx, q).
func (f SysUploadQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysUploadQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysUploadQuery", q)
}

// The SysUploadMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysUploadMutationRuleFunc func(context.Context, *ent.SysUploadMutation) error

// EvalMutation calls f(ctx, m).
func (f SysUploadMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysUploadMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysUploadMutation", m)
}

// The SysUserQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysUserQueryRuleFunc func(context.Context, *ent.SysUserQuery) error

// EvalQuery return f(ctx, q).
func (f SysUserQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysUserQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysUserQuery", q)
}

// The SysUserMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysUserMutationRuleFunc func(context.Context, *ent.SysUserMutation) error

// EvalMutation calls f(ctx, m).
func (f SysUserMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysUserMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysUserMutation", m)
}

// The SysUserAuthQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysUserAuthQueryRuleFunc func(context.Context, *ent.SysUserAuthQuery) error

// EvalQuery return f(ctx, q).
func (f SysUserAuthQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysUserAuthQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysUserAuthQuery", q)
}

// The SysUserAuthMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysUserAuthMutationRuleFunc func(context.Context, *ent.SysUserAuthMutation) error

// EvalMutation calls f(ctx, m).
func (f SysUserAuthMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysUserAuthMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysUserAuthMutation", m)
}

// The SysUserLogQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysUserLogQueryRuleFunc func(context.Context, *ent.SysUserLogQuery) error

// EvalQuery return f(ctx, q).
func (f SysUserLogQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysUserLogQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysUserLogQuery", q)
}

// The SysUserLogMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysUserLogMutationRuleFunc func(context.Context, *ent.SysUserLogMutation) error

// EvalMutation calls f(ctx, m).
func (f SysUserLogMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysUserLogMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysUserLogMutation", m)
}

// The SysUserOrganizationQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysUserOrganizationQueryRuleFunc func(context.Context, *ent.SysUserOrganizationQuery) error

// EvalQuery return f(ctx, q).
func (f SysUserOrganizationQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysUserOrganizationQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysUserOrganizationQuery", q)
}

// The SysUserOrganizationMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysUserOrganizationMutationRuleFunc func(context.Context, *ent.SysUserOrganizationMutation) error

// EvalMutation calls f(ctx, m).
func (f SysUserOrganizationMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysUserOrganizationMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysUserOrganizationMutation", m)
}

// The SysUserRoleQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SysUserRoleQueryRuleFunc func(context.Context, *ent.SysUserRoleQuery) error

// EvalQuery return f(ctx, q).
func (f SysUserRoleQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysUserRoleQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SysUserRoleQuery", q)
}

// The SysUserRoleMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SysUserRoleMutationRuleFunc func(context.Context, *ent.SysUserRoleMutation) error

// EvalMutation calls f(ctx, m).
func (f SysUserRoleMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SysUserRoleMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SysUserRoleMutation", m)
}

// The WmsAccessDoorLogQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsAccessDoorLogQueryRuleFunc func(context.Context, *ent.WmsAccessDoorLogQuery) error

// EvalQuery return f(ctx, q).
func (f WmsAccessDoorLogQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsAccessDoorLogQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsAccessDoorLogQuery", q)
}

// The WmsAccessDoorLogMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsAccessDoorLogMutationRuleFunc func(context.Context, *ent.WmsAccessDoorLogMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsAccessDoorLogMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsAccessDoorLogMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsAccessDoorLogMutation", m)
}

// The WmsApprovalTaskQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsApprovalTaskQueryRuleFunc func(context.Context, *ent.WmsApprovalTaskQuery) error

// EvalQuery return f(ctx, q).
func (f WmsApprovalTaskQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsApprovalTaskQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsApprovalTaskQuery", q)
}

// The WmsApprovalTaskMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsApprovalTaskMutationRuleFunc func(context.Context, *ent.WmsApprovalTaskMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsApprovalTaskMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsApprovalTaskMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsApprovalTaskMutation", m)
}

// The WmsApprovalTaskDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsApprovalTaskDetailQueryRuleFunc func(context.Context, *ent.WmsApprovalTaskDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsApprovalTaskDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsApprovalTaskDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsApprovalTaskDetailQuery", q)
}

// The WmsApprovalTaskDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsApprovalTaskDetailMutationRuleFunc func(context.Context, *ent.WmsApprovalTaskDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsApprovalTaskDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsApprovalTaskDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsApprovalTaskDetailMutation", m)
}

// The WmsApprovalTaskOperationQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsApprovalTaskOperationQueryRuleFunc func(context.Context, *ent.WmsApprovalTaskOperationQuery) error

// EvalQuery return f(ctx, q).
func (f WmsApprovalTaskOperationQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsApprovalTaskOperationQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsApprovalTaskOperationQuery", q)
}

// The WmsApprovalTaskOperationMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsApprovalTaskOperationMutationRuleFunc func(context.Context, *ent.WmsApprovalTaskOperationMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsApprovalTaskOperationMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsApprovalTaskOperationMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsApprovalTaskOperationMutation", m)
}

// The WmsApprovalTaskRemarkQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsApprovalTaskRemarkQueryRuleFunc func(context.Context, *ent.WmsApprovalTaskRemarkQuery) error

// EvalQuery return f(ctx, q).
func (f WmsApprovalTaskRemarkQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsApprovalTaskRemarkQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsApprovalTaskRemarkQuery", q)
}

// The WmsApprovalTaskRemarkMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsApprovalTaskRemarkMutationRuleFunc func(context.Context, *ent.WmsApprovalTaskRemarkMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsApprovalTaskRemarkMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsApprovalTaskRemarkMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsApprovalTaskRemarkMutation", m)
}

// The WmsAuditPlanQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsAuditPlanQueryRuleFunc func(context.Context, *ent.WmsAuditPlanQuery) error

// EvalQuery return f(ctx, q).
func (f WmsAuditPlanQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsAuditPlanQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsAuditPlanQuery", q)
}

// The WmsAuditPlanMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsAuditPlanMutationRuleFunc func(context.Context, *ent.WmsAuditPlanMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsAuditPlanMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsAuditPlanMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsAuditPlanMutation", m)
}

// The WmsAuditPlanDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsAuditPlanDetailQueryRuleFunc func(context.Context, *ent.WmsAuditPlanDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsAuditPlanDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsAuditPlanDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsAuditPlanDetailQuery", q)
}

// The WmsAuditPlanDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsAuditPlanDetailMutationRuleFunc func(context.Context, *ent.WmsAuditPlanDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsAuditPlanDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsAuditPlanDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsAuditPlanDetailMutation", m)
}

// The WmsBorrowOrderQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsBorrowOrderQueryRuleFunc func(context.Context, *ent.WmsBorrowOrderQuery) error

// EvalQuery return f(ctx, q).
func (f WmsBorrowOrderQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsBorrowOrderQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsBorrowOrderQuery", q)
}

// The WmsBorrowOrderMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsBorrowOrderMutationRuleFunc func(context.Context, *ent.WmsBorrowOrderMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsBorrowOrderMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsBorrowOrderMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsBorrowOrderMutation", m)
}

// The WmsBorrowOrderDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsBorrowOrderDetailQueryRuleFunc func(context.Context, *ent.WmsBorrowOrderDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsBorrowOrderDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsBorrowOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsBorrowOrderDetailQuery", q)
}

// The WmsBorrowOrderDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsBorrowOrderDetailMutationRuleFunc func(context.Context, *ent.WmsBorrowOrderDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsBorrowOrderDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsBorrowOrderDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsBorrowOrderDetailMutation", m)
}

// The WmsCarQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsCarQueryRuleFunc func(context.Context, *ent.WmsCarQuery) error

// EvalQuery return f(ctx, q).
func (f WmsCarQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsCarQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsCarQuery", q)
}

// The WmsCarMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsCarMutationRuleFunc func(context.Context, *ent.WmsCarMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsCarMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsCarMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsCarMutation", m)
}

// The WmsClaimOrderQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsClaimOrderQueryRuleFunc func(context.Context, *ent.WmsClaimOrderQuery) error

// EvalQuery return f(ctx, q).
func (f WmsClaimOrderQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsClaimOrderQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsClaimOrderQuery", q)
}

// The WmsClaimOrderMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsClaimOrderMutationRuleFunc func(context.Context, *ent.WmsClaimOrderMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsClaimOrderMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsClaimOrderMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsClaimOrderMutation", m)
}

// The WmsClaimOrderDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsClaimOrderDetailQueryRuleFunc func(context.Context, *ent.WmsClaimOrderDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsClaimOrderDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsClaimOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsClaimOrderDetailQuery", q)
}

// The WmsClaimOrderDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsClaimOrderDetailMutationRuleFunc func(context.Context, *ent.WmsClaimOrderDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsClaimOrderDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsClaimOrderDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsClaimOrderDetailMutation", m)
}

// The WmsCompanyQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsCompanyQueryRuleFunc func(context.Context, *ent.WmsCompanyQuery) error

// EvalQuery return f(ctx, q).
func (f WmsCompanyQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsCompanyQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsCompanyQuery", q)
}

// The WmsCompanyMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsCompanyMutationRuleFunc func(context.Context, *ent.WmsCompanyMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsCompanyMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsCompanyMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsCompanyMutation", m)
}

// The WmsCompanyAddressQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsCompanyAddressQueryRuleFunc func(context.Context, *ent.WmsCompanyAddressQuery) error

// EvalQuery return f(ctx, q).
func (f WmsCompanyAddressQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsCompanyAddressQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsCompanyAddressQuery", q)
}

// The WmsCompanyAddressMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsCompanyAddressMutationRuleFunc func(context.Context, *ent.WmsCompanyAddressMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsCompanyAddressMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsCompanyAddressMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsCompanyAddressMutation", m)
}

// The WmsDiscardMeetingQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsDiscardMeetingQueryRuleFunc func(context.Context, *ent.WmsDiscardMeetingQuery) error

// EvalQuery return f(ctx, q).
func (f WmsDiscardMeetingQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsDiscardMeetingQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsDiscardMeetingQuery", q)
}

// The WmsDiscardMeetingMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsDiscardMeetingMutationRuleFunc func(context.Context, *ent.WmsDiscardMeetingMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsDiscardMeetingMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsDiscardMeetingMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsDiscardMeetingMutation", m)
}

// The WmsDiscardMeetingDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsDiscardMeetingDetailQueryRuleFunc func(context.Context, *ent.WmsDiscardMeetingDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsDiscardMeetingDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsDiscardMeetingDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsDiscardMeetingDetailQuery", q)
}

// The WmsDiscardMeetingDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsDiscardMeetingDetailMutationRuleFunc func(context.Context, *ent.WmsDiscardMeetingDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsDiscardMeetingDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsDiscardMeetingDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsDiscardMeetingDetailMutation", m)
}

// The WmsDiscardOrderQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsDiscardOrderQueryRuleFunc func(context.Context, *ent.WmsDiscardOrderQuery) error

// EvalQuery return f(ctx, q).
func (f WmsDiscardOrderQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsDiscardOrderQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsDiscardOrderQuery", q)
}

// The WmsDiscardOrderMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsDiscardOrderMutationRuleFunc func(context.Context, *ent.WmsDiscardOrderMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsDiscardOrderMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsDiscardOrderMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsDiscardOrderMutation", m)
}

// The WmsDiscardOrderDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsDiscardOrderDetailQueryRuleFunc func(context.Context, *ent.WmsDiscardOrderDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsDiscardOrderDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsDiscardOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsDiscardOrderDetailQuery", q)
}

// The WmsDiscardOrderDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsDiscardOrderDetailMutationRuleFunc func(context.Context, *ent.WmsDiscardOrderDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsDiscardOrderDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsDiscardOrderDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsDiscardOrderDetailMutation", m)
}

// The WmsDiscardPlanOrderQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsDiscardPlanOrderQueryRuleFunc func(context.Context, *ent.WmsDiscardPlanOrderQuery) error

// EvalQuery return f(ctx, q).
func (f WmsDiscardPlanOrderQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsDiscardPlanOrderQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsDiscardPlanOrderQuery", q)
}

// The WmsDiscardPlanOrderMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsDiscardPlanOrderMutationRuleFunc func(context.Context, *ent.WmsDiscardPlanOrderMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsDiscardPlanOrderMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsDiscardPlanOrderMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsDiscardPlanOrderMutation", m)
}

// The WmsDiscardPlanOrderDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsDiscardPlanOrderDetailQueryRuleFunc func(context.Context, *ent.WmsDiscardPlanOrderDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsDiscardPlanOrderDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsDiscardPlanOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsDiscardPlanOrderDetailQuery", q)
}

// The WmsDiscardPlanOrderDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsDiscardPlanOrderDetailMutationRuleFunc func(context.Context, *ent.WmsDiscardPlanOrderDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsDiscardPlanOrderDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsDiscardPlanOrderDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsDiscardPlanOrderDetailMutation", m)
}

// The WmsDocumentQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsDocumentQueryRuleFunc func(context.Context, *ent.WmsDocumentQuery) error

// EvalQuery return f(ctx, q).
func (f WmsDocumentQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsDocumentQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsDocumentQuery", q)
}

// The WmsDocumentMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsDocumentMutationRuleFunc func(context.Context, *ent.WmsDocumentMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsDocumentMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsDocumentMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsDocumentMutation", m)
}

// The WmsEnterRepositoryOrderQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsEnterRepositoryOrderQueryRuleFunc func(context.Context, *ent.WmsEnterRepositoryOrderQuery) error

// EvalQuery return f(ctx, q).
func (f WmsEnterRepositoryOrderQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsEnterRepositoryOrderQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsEnterRepositoryOrderQuery", q)
}

// The WmsEnterRepositoryOrderMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsEnterRepositoryOrderMutationRuleFunc func(context.Context, *ent.WmsEnterRepositoryOrderMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsEnterRepositoryOrderMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsEnterRepositoryOrderMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsEnterRepositoryOrderMutation", m)
}

// The WmsEnterRepositoryOrderDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsEnterRepositoryOrderDetailQueryRuleFunc func(context.Context, *ent.WmsEnterRepositoryOrderDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsEnterRepositoryOrderDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsEnterRepositoryOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsEnterRepositoryOrderDetailQuery", q)
}

// The WmsEnterRepositoryOrderDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsEnterRepositoryOrderDetailMutationRuleFunc func(context.Context, *ent.WmsEnterRepositoryOrderDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsEnterRepositoryOrderDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsEnterRepositoryOrderDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsEnterRepositoryOrderDetailMutation", m)
}

// The WmsEquipmentQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsEquipmentQueryRuleFunc func(context.Context, *ent.WmsEquipmentQuery) error

// EvalQuery return f(ctx, q).
func (f WmsEquipmentQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsEquipmentQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsEquipmentQuery", q)
}

// The WmsEquipmentMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsEquipmentMutationRuleFunc func(context.Context, *ent.WmsEquipmentMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsEquipmentMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsEquipmentMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsEquipmentMutation", m)
}

// The WmsEquipmentDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsEquipmentDetailQueryRuleFunc func(context.Context, *ent.WmsEquipmentDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsEquipmentDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsEquipmentDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsEquipmentDetailQuery", q)
}

// The WmsEquipmentDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsEquipmentDetailMutationRuleFunc func(context.Context, *ent.WmsEquipmentDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsEquipmentDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsEquipmentDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsEquipmentDetailMutation", m)
}

// The WmsEquipmentTypeQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsEquipmentTypeQueryRuleFunc func(context.Context, *ent.WmsEquipmentTypeQuery) error

// EvalQuery return f(ctx, q).
func (f WmsEquipmentTypeQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsEquipmentTypeQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsEquipmentTypeQuery", q)
}

// The WmsEquipmentTypeMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsEquipmentTypeMutationRuleFunc func(context.Context, *ent.WmsEquipmentTypeMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsEquipmentTypeMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsEquipmentTypeMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsEquipmentTypeMutation", m)
}

// The WmsEquipmentTypePropertyQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsEquipmentTypePropertyQueryRuleFunc func(context.Context, *ent.WmsEquipmentTypePropertyQuery) error

// EvalQuery return f(ctx, q).
func (f WmsEquipmentTypePropertyQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsEquipmentTypePropertyQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsEquipmentTypePropertyQuery", q)
}

// The WmsEquipmentTypePropertyMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsEquipmentTypePropertyMutationRuleFunc func(context.Context, *ent.WmsEquipmentTypePropertyMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsEquipmentTypePropertyMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsEquipmentTypePropertyMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsEquipmentTypePropertyMutation", m)
}

// The WmsEquipmentTypePropertyGroupQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsEquipmentTypePropertyGroupQueryRuleFunc func(context.Context, *ent.WmsEquipmentTypePropertyGroupQuery) error

// EvalQuery return f(ctx, q).
func (f WmsEquipmentTypePropertyGroupQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsEquipmentTypePropertyGroupQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsEquipmentTypePropertyGroupQuery", q)
}

// The WmsEquipmentTypePropertyGroupMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsEquipmentTypePropertyGroupMutationRuleFunc func(context.Context, *ent.WmsEquipmentTypePropertyGroupMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsEquipmentTypePropertyGroupMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsEquipmentTypePropertyGroupMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsEquipmentTypePropertyGroupMutation", m)
}

// The WmsEquipmentTypePropertyOptionQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsEquipmentTypePropertyOptionQueryRuleFunc func(context.Context, *ent.WmsEquipmentTypePropertyOptionQuery) error

// EvalQuery return f(ctx, q).
func (f WmsEquipmentTypePropertyOptionQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsEquipmentTypePropertyOptionQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsEquipmentTypePropertyOptionQuery", q)
}

// The WmsEquipmentTypePropertyOptionMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsEquipmentTypePropertyOptionMutationRuleFunc func(context.Context, *ent.WmsEquipmentTypePropertyOptionMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsEquipmentTypePropertyOptionMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsEquipmentTypePropertyOptionMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsEquipmentTypePropertyOptionMutation", m)
}

// The WmsEquipmentUserGroupQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsEquipmentUserGroupQueryRuleFunc func(context.Context, *ent.WmsEquipmentUserGroupQuery) error

// EvalQuery return f(ctx, q).
func (f WmsEquipmentUserGroupQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsEquipmentUserGroupQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsEquipmentUserGroupQuery", q)
}

// The WmsEquipmentUserGroupMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsEquipmentUserGroupMutationRuleFunc func(context.Context, *ent.WmsEquipmentUserGroupMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsEquipmentUserGroupMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsEquipmentUserGroupMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsEquipmentUserGroupMutation", m)
}

// The WmsFireStationQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsFireStationQueryRuleFunc func(context.Context, *ent.WmsFireStationQuery) error

// EvalQuery return f(ctx, q).
func (f WmsFireStationQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsFireStationQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsFireStationQuery", q)
}

// The WmsFireStationMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsFireStationMutationRuleFunc func(context.Context, *ent.WmsFireStationMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsFireStationMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsFireStationMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsFireStationMutation", m)
}

// The WmsGPSQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsGPSQueryRuleFunc func(context.Context, *ent.WmsGPSQuery) error

// EvalQuery return f(ctx, q).
func (f WmsGPSQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsGPSQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsGPSQuery", q)
}

// The WmsGPSMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsGPSMutationRuleFunc func(context.Context, *ent.WmsGPSMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsGPSMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsGPSMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsGPSMutation", m)
}

// The WmsGPSRecordQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsGPSRecordQueryRuleFunc func(context.Context, *ent.WmsGPSRecordQuery) error

// EvalQuery return f(ctx, q).
func (f WmsGPSRecordQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsGPSRecordQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsGPSRecordQuery", q)
}

// The WmsGPSRecordMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsGPSRecordMutationRuleFunc func(context.Context, *ent.WmsGPSRecordMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsGPSRecordMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsGPSRecordMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsGPSRecordMutation", m)
}

// The WmsLearningCourseQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsLearningCourseQueryRuleFunc func(context.Context, *ent.WmsLearningCourseQuery) error

// EvalQuery return f(ctx, q).
func (f WmsLearningCourseQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsLearningCourseQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsLearningCourseQuery", q)
}

// The WmsLearningCourseMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsLearningCourseMutationRuleFunc func(context.Context, *ent.WmsLearningCourseMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsLearningCourseMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsLearningCourseMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsLearningCourseMutation", m)
}

// The WmsLearningCourseCoursewareQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsLearningCourseCoursewareQueryRuleFunc func(context.Context, *ent.WmsLearningCourseCoursewareQuery) error

// EvalQuery return f(ctx, q).
func (f WmsLearningCourseCoursewareQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsLearningCourseCoursewareQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsLearningCourseCoursewareQuery", q)
}

// The WmsLearningCourseCoursewareMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsLearningCourseCoursewareMutationRuleFunc func(context.Context, *ent.WmsLearningCourseCoursewareMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsLearningCourseCoursewareMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsLearningCourseCoursewareMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsLearningCourseCoursewareMutation", m)
}

// The WmsLearningCourseLogQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsLearningCourseLogQueryRuleFunc func(context.Context, *ent.WmsLearningCourseLogQuery) error

// EvalQuery return f(ctx, q).
func (f WmsLearningCourseLogQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsLearningCourseLogQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsLearningCourseLogQuery", q)
}

// The WmsLearningCourseLogMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsLearningCourseLogMutationRuleFunc func(context.Context, *ent.WmsLearningCourseLogMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsLearningCourseLogMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsLearningCourseLogMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsLearningCourseLogMutation", m)
}

// The WmsLearningCourseRecordQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsLearningCourseRecordQueryRuleFunc func(context.Context, *ent.WmsLearningCourseRecordQuery) error

// EvalQuery return f(ctx, q).
func (f WmsLearningCourseRecordQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsLearningCourseRecordQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsLearningCourseRecordQuery", q)
}

// The WmsLearningCourseRecordMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsLearningCourseRecordMutationRuleFunc func(context.Context, *ent.WmsLearningCourseRecordMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsLearningCourseRecordMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsLearningCourseRecordMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsLearningCourseRecordMutation", m)
}

// The WmsLearningCoursewareQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsLearningCoursewareQueryRuleFunc func(context.Context, *ent.WmsLearningCoursewareQuery) error

// EvalQuery return f(ctx, q).
func (f WmsLearningCoursewareQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsLearningCoursewareQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsLearningCoursewareQuery", q)
}

// The WmsLearningCoursewareMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsLearningCoursewareMutationRuleFunc func(context.Context, *ent.WmsLearningCoursewareMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsLearningCoursewareMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsLearningCoursewareMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsLearningCoursewareMutation", m)
}

// The WmsLearningCoursewareRecordQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsLearningCoursewareRecordQueryRuleFunc func(context.Context, *ent.WmsLearningCoursewareRecordQuery) error

// EvalQuery return f(ctx, q).
func (f WmsLearningCoursewareRecordQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsLearningCoursewareRecordQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsLearningCoursewareRecordQuery", q)
}

// The WmsLearningCoursewareRecordMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsLearningCoursewareRecordMutationRuleFunc func(context.Context, *ent.WmsLearningCoursewareRecordMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsLearningCoursewareRecordMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsLearningCoursewareRecordMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsLearningCoursewareRecordMutation", m)
}

// The WmsLearningPlanQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsLearningPlanQueryRuleFunc func(context.Context, *ent.WmsLearningPlanQuery) error

// EvalQuery return f(ctx, q).
func (f WmsLearningPlanQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsLearningPlanQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsLearningPlanQuery", q)
}

// The WmsLearningPlanMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsLearningPlanMutationRuleFunc func(context.Context, *ent.WmsLearningPlanMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsLearningPlanMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsLearningPlanMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsLearningPlanMutation", m)
}

// The WmsLearningPlanRecordQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsLearningPlanRecordQueryRuleFunc func(context.Context, *ent.WmsLearningPlanRecordQuery) error

// EvalQuery return f(ctx, q).
func (f WmsLearningPlanRecordQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsLearningPlanRecordQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsLearningPlanRecordQuery", q)
}

// The WmsLearningPlanRecordMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsLearningPlanRecordMutationRuleFunc func(context.Context, *ent.WmsLearningPlanRecordMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsLearningPlanRecordMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsLearningPlanRecordMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsLearningPlanRecordMutation", m)
}

// The WmsMaintainOrderQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsMaintainOrderQueryRuleFunc func(context.Context, *ent.WmsMaintainOrderQuery) error

// EvalQuery return f(ctx, q).
func (f WmsMaintainOrderQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsMaintainOrderQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsMaintainOrderQuery", q)
}

// The WmsMaintainOrderMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsMaintainOrderMutationRuleFunc func(context.Context, *ent.WmsMaintainOrderMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsMaintainOrderMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsMaintainOrderMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsMaintainOrderMutation", m)
}

// The WmsMaintainOrderDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsMaintainOrderDetailQueryRuleFunc func(context.Context, *ent.WmsMaintainOrderDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsMaintainOrderDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsMaintainOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsMaintainOrderDetailQuery", q)
}

// The WmsMaintainOrderDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsMaintainOrderDetailMutationRuleFunc func(context.Context, *ent.WmsMaintainOrderDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsMaintainOrderDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsMaintainOrderDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsMaintainOrderDetailMutation", m)
}

// The WmsMaintainPlanQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsMaintainPlanQueryRuleFunc func(context.Context, *ent.WmsMaintainPlanQuery) error

// EvalQuery return f(ctx, q).
func (f WmsMaintainPlanQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsMaintainPlanQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsMaintainPlanQuery", q)
}

// The WmsMaintainPlanMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsMaintainPlanMutationRuleFunc func(context.Context, *ent.WmsMaintainPlanMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsMaintainPlanMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsMaintainPlanMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsMaintainPlanMutation", m)
}

// The WmsMaintainPlanDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsMaintainPlanDetailQueryRuleFunc func(context.Context, *ent.WmsMaintainPlanDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsMaintainPlanDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsMaintainPlanDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsMaintainPlanDetailQuery", q)
}

// The WmsMaintainPlanDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsMaintainPlanDetailMutationRuleFunc func(context.Context, *ent.WmsMaintainPlanDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsMaintainPlanDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsMaintainPlanDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsMaintainPlanDetailMutation", m)
}

// The WmsMaterialQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsMaterialQueryRuleFunc func(context.Context, *ent.WmsMaterialQuery) error

// EvalQuery return f(ctx, q).
func (f WmsMaterialQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsMaterialQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsMaterialQuery", q)
}

// The WmsMaterialMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsMaterialMutationRuleFunc func(context.Context, *ent.WmsMaterialMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsMaterialMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsMaterialMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsMaterialMutation", m)
}

// The WmsMaterialLogQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsMaterialLogQueryRuleFunc func(context.Context, *ent.WmsMaterialLogQuery) error

// EvalQuery return f(ctx, q).
func (f WmsMaterialLogQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsMaterialLogQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsMaterialLogQuery", q)
}

// The WmsMaterialLogMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsMaterialLogMutationRuleFunc func(context.Context, *ent.WmsMaterialLogMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsMaterialLogMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsMaterialLogMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsMaterialLogMutation", m)
}

// The WmsMeasureUnitQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsMeasureUnitQueryRuleFunc func(context.Context, *ent.WmsMeasureUnitQuery) error

// EvalQuery return f(ctx, q).
func (f WmsMeasureUnitQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsMeasureUnitQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsMeasureUnitQuery", q)
}

// The WmsMeasureUnitMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsMeasureUnitMutationRuleFunc func(context.Context, *ent.WmsMeasureUnitMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsMeasureUnitMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsMeasureUnitMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsMeasureUnitMutation", m)
}

// The WmsOperateLogQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsOperateLogQueryRuleFunc func(context.Context, *ent.WmsOperateLogQuery) error

// EvalQuery return f(ctx, q).
func (f WmsOperateLogQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsOperateLogQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsOperateLogQuery", q)
}

// The WmsOperateLogMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsOperateLogMutationRuleFunc func(context.Context, *ent.WmsOperateLogMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsOperateLogMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsOperateLogMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsOperateLogMutation", m)
}

// The WmsOutRepositoryOrderQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsOutRepositoryOrderQueryRuleFunc func(context.Context, *ent.WmsOutRepositoryOrderQuery) error

// EvalQuery return f(ctx, q).
func (f WmsOutRepositoryOrderQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsOutRepositoryOrderQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsOutRepositoryOrderQuery", q)
}

// The WmsOutRepositoryOrderMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsOutRepositoryOrderMutationRuleFunc func(context.Context, *ent.WmsOutRepositoryOrderMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsOutRepositoryOrderMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsOutRepositoryOrderMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsOutRepositoryOrderMutation", m)
}

// The WmsOutRepositoryOrderDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsOutRepositoryOrderDetailQueryRuleFunc func(context.Context, *ent.WmsOutRepositoryOrderDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsOutRepositoryOrderDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsOutRepositoryOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsOutRepositoryOrderDetailQuery", q)
}

// The WmsOutRepositoryOrderDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsOutRepositoryOrderDetailMutationRuleFunc func(context.Context, *ent.WmsOutRepositoryOrderDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsOutRepositoryOrderDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsOutRepositoryOrderDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsOutRepositoryOrderDetailMutation", m)
}

// The WmsPurchaseOrderQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsPurchaseOrderQueryRuleFunc func(context.Context, *ent.WmsPurchaseOrderQuery) error

// EvalQuery return f(ctx, q).
func (f WmsPurchaseOrderQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsPurchaseOrderQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsPurchaseOrderQuery", q)
}

// The WmsPurchaseOrderMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsPurchaseOrderMutationRuleFunc func(context.Context, *ent.WmsPurchaseOrderMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsPurchaseOrderMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsPurchaseOrderMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsPurchaseOrderMutation", m)
}

// The WmsPurchaseOrderDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsPurchaseOrderDetailQueryRuleFunc func(context.Context, *ent.WmsPurchaseOrderDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsPurchaseOrderDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsPurchaseOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsPurchaseOrderDetailQuery", q)
}

// The WmsPurchaseOrderDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsPurchaseOrderDetailMutationRuleFunc func(context.Context, *ent.WmsPurchaseOrderDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsPurchaseOrderDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsPurchaseOrderDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsPurchaseOrderDetailMutation", m)
}

// The WmsRepairOrderQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsRepairOrderQueryRuleFunc func(context.Context, *ent.WmsRepairOrderQuery) error

// EvalQuery return f(ctx, q).
func (f WmsRepairOrderQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepairOrderQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsRepairOrderQuery", q)
}

// The WmsRepairOrderMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsRepairOrderMutationRuleFunc func(context.Context, *ent.WmsRepairOrderMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsRepairOrderMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsRepairOrderMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsRepairOrderMutation", m)
}

// The WmsRepairOrderDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsRepairOrderDetailQueryRuleFunc func(context.Context, *ent.WmsRepairOrderDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsRepairOrderDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepairOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsRepairOrderDetailQuery", q)
}

// The WmsRepairOrderDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsRepairOrderDetailMutationRuleFunc func(context.Context, *ent.WmsRepairOrderDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsRepairOrderDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsRepairOrderDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsRepairOrderDetailMutation", m)
}

// The WmsRepairSettlementOrderQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsRepairSettlementOrderQueryRuleFunc func(context.Context, *ent.WmsRepairSettlementOrderQuery) error

// EvalQuery return f(ctx, q).
func (f WmsRepairSettlementOrderQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepairSettlementOrderQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsRepairSettlementOrderQuery", q)
}

// The WmsRepairSettlementOrderMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsRepairSettlementOrderMutationRuleFunc func(context.Context, *ent.WmsRepairSettlementOrderMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsRepairSettlementOrderMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsRepairSettlementOrderMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsRepairSettlementOrderMutation", m)
}

// The WmsRepairSettlementOrderSettlementfeeDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsRepairSettlementOrderSettlementfeeDetailQueryRuleFunc func(context.Context, *ent.WmsRepairSettlementOrderSettlementfeeDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsRepairSettlementOrderSettlementfeeDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepairSettlementOrderSettlementfeeDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsRepairSettlementOrderSettlementfeeDetailQuery", q)
}

// The WmsRepairSettlementOrderSettlementfeeDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsRepairSettlementOrderSettlementfeeDetailMutationRuleFunc func(context.Context, *ent.WmsRepairSettlementOrderSettlementfeeDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsRepairSettlementOrderSettlementfeeDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsRepairSettlementOrderSettlementfeeDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsRepairSettlementOrderSettlementfeeDetailMutation", m)
}

// The WmsRepairSettlementOrderWorkfeeDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsRepairSettlementOrderWorkfeeDetailQueryRuleFunc func(context.Context, *ent.WmsRepairSettlementOrderWorkfeeDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsRepairSettlementOrderWorkfeeDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepairSettlementOrderWorkfeeDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsRepairSettlementOrderWorkfeeDetailQuery", q)
}

// The WmsRepairSettlementOrderWorkfeeDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsRepairSettlementOrderWorkfeeDetailMutationRuleFunc func(context.Context, *ent.WmsRepairSettlementOrderWorkfeeDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsRepairSettlementOrderWorkfeeDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsRepairSettlementOrderWorkfeeDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsRepairSettlementOrderWorkfeeDetailMutation", m)
}

// The WmsRepositoryQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsRepositoryQueryRuleFunc func(context.Context, *ent.WmsRepositoryQuery) error

// EvalQuery return f(ctx, q).
func (f WmsRepositoryQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepositoryQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsRepositoryQuery", q)
}

// The WmsRepositoryMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsRepositoryMutationRuleFunc func(context.Context, *ent.WmsRepositoryMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsRepositoryMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsRepositoryMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsRepositoryMutation", m)
}

// The WmsRepositoryAdminQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsRepositoryAdminQueryRuleFunc func(context.Context, *ent.WmsRepositoryAdminQuery) error

// EvalQuery return f(ctx, q).
func (f WmsRepositoryAdminQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepositoryAdminQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsRepositoryAdminQuery", q)
}

// The WmsRepositoryAdminMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsRepositoryAdminMutationRuleFunc func(context.Context, *ent.WmsRepositoryAdminMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsRepositoryAdminMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsRepositoryAdminMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsRepositoryAdminMutation", m)
}

// The WmsRepositoryAreaQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsRepositoryAreaQueryRuleFunc func(context.Context, *ent.WmsRepositoryAreaQuery) error

// EvalQuery return f(ctx, q).
func (f WmsRepositoryAreaQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepositoryAreaQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsRepositoryAreaQuery", q)
}

// The WmsRepositoryAreaMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsRepositoryAreaMutationRuleFunc func(context.Context, *ent.WmsRepositoryAreaMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsRepositoryAreaMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsRepositoryAreaMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsRepositoryAreaMutation", m)
}

// The WmsRepositoryPositionQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsRepositoryPositionQueryRuleFunc func(context.Context, *ent.WmsRepositoryPositionQuery) error

// EvalQuery return f(ctx, q).
func (f WmsRepositoryPositionQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepositoryPositionQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsRepositoryPositionQuery", q)
}

// The WmsRepositoryPositionMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsRepositoryPositionMutationRuleFunc func(context.Context, *ent.WmsRepositoryPositionMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsRepositoryPositionMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsRepositoryPositionMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsRepositoryPositionMutation", m)
}

// The WmsRepositoryScreenQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsRepositoryScreenQueryRuleFunc func(context.Context, *ent.WmsRepositoryScreenQuery) error

// EvalQuery return f(ctx, q).
func (f WmsRepositoryScreenQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepositoryScreenQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsRepositoryScreenQuery", q)
}

// The WmsRepositoryScreenMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsRepositoryScreenMutationRuleFunc func(context.Context, *ent.WmsRepositoryScreenMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsRepositoryScreenMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsRepositoryScreenMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsRepositoryScreenMutation", m)
}

// The WmsRepositoryScreenRepositoryAreaQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsRepositoryScreenRepositoryAreaQueryRuleFunc func(context.Context, *ent.WmsRepositoryScreenRepositoryAreaQuery) error

// EvalQuery return f(ctx, q).
func (f WmsRepositoryScreenRepositoryAreaQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepositoryScreenRepositoryAreaQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsRepositoryScreenRepositoryAreaQuery", q)
}

// The WmsRepositoryScreenRepositoryAreaMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsRepositoryScreenRepositoryAreaMutationRuleFunc func(context.Context, *ent.WmsRepositoryScreenRepositoryAreaMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsRepositoryScreenRepositoryAreaMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsRepositoryScreenRepositoryAreaMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsRepositoryScreenRepositoryAreaMutation", m)
}

// The WmsReturnOrderQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsReturnOrderQueryRuleFunc func(context.Context, *ent.WmsReturnOrderQuery) error

// EvalQuery return f(ctx, q).
func (f WmsReturnOrderQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsReturnOrderQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsReturnOrderQuery", q)
}

// The WmsReturnOrderMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsReturnOrderMutationRuleFunc func(context.Context, *ent.WmsReturnOrderMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsReturnOrderMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsReturnOrderMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsReturnOrderMutation", m)
}

// The WmsReturnOrderDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsReturnOrderDetailQueryRuleFunc func(context.Context, *ent.WmsReturnOrderDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsReturnOrderDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsReturnOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsReturnOrderDetailQuery", q)
}

// The WmsReturnOrderDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsReturnOrderDetailMutationRuleFunc func(context.Context, *ent.WmsReturnOrderDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsReturnOrderDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsReturnOrderDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsReturnOrderDetailMutation", m)
}

// The WmsRfidQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsRfidQueryRuleFunc func(context.Context, *ent.WmsRfidQuery) error

// EvalQuery return f(ctx, q).
func (f WmsRfidQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRfidQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsRfidQuery", q)
}

// The WmsRfidMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsRfidMutationRuleFunc func(context.Context, *ent.WmsRfidMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsRfidMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsRfidMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsRfidMutation", m)
}

// The WmsRfidReaderQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsRfidReaderQueryRuleFunc func(context.Context, *ent.WmsRfidReaderQuery) error

// EvalQuery return f(ctx, q).
func (f WmsRfidReaderQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRfidReaderQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsRfidReaderQuery", q)
}

// The WmsRfidReaderMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsRfidReaderMutationRuleFunc func(context.Context, *ent.WmsRfidReaderMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsRfidReaderMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsRfidReaderMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsRfidReaderMutation", m)
}

// The WmsRfidReaderRecordQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsRfidReaderRecordQueryRuleFunc func(context.Context, *ent.WmsRfidReaderRecordQuery) error

// EvalQuery return f(ctx, q).
func (f WmsRfidReaderRecordQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRfidReaderRecordQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsRfidReaderRecordQuery", q)
}

// The WmsRfidReaderRecordMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsRfidReaderRecordMutationRuleFunc func(context.Context, *ent.WmsRfidReaderRecordMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsRfidReaderRecordMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsRfidReaderRecordMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsRfidReaderRecordMutation", m)
}

// The WmsTransferOrderQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsTransferOrderQueryRuleFunc func(context.Context, *ent.WmsTransferOrderQuery) error

// EvalQuery return f(ctx, q).
func (f WmsTransferOrderQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsTransferOrderQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsTransferOrderQuery", q)
}

// The WmsTransferOrderMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsTransferOrderMutationRuleFunc func(context.Context, *ent.WmsTransferOrderMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsTransferOrderMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsTransferOrderMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsTransferOrderMutation", m)
}

// The WmsTransferOrderDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsTransferOrderDetailQueryRuleFunc func(context.Context, *ent.WmsTransferOrderDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsTransferOrderDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsTransferOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsTransferOrderDetailQuery", q)
}

// The WmsTransferOrderDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsTransferOrderDetailMutationRuleFunc func(context.Context, *ent.WmsTransferOrderDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsTransferOrderDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsTransferOrderDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsTransferOrderDetailMutation", m)
}

// The WmsVehicleRepairOrderQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsVehicleRepairOrderQueryRuleFunc func(context.Context, *ent.WmsVehicleRepairOrderQuery) error

// EvalQuery return f(ctx, q).
func (f WmsVehicleRepairOrderQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsVehicleRepairOrderQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsVehicleRepairOrderQuery", q)
}

// The WmsVehicleRepairOrderMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsVehicleRepairOrderMutationRuleFunc func(context.Context, *ent.WmsVehicleRepairOrderMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsVehicleRepairOrderMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsVehicleRepairOrderMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsVehicleRepairOrderMutation", m)
}

// The WmsVehicleRepairOrderMaterialfeeDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsVehicleRepairOrderMaterialfeeDetailQueryRuleFunc func(context.Context, *ent.WmsVehicleRepairOrderMaterialfeeDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsVehicleRepairOrderMaterialfeeDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsVehicleRepairOrderMaterialfeeDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsVehicleRepairOrderMaterialfeeDetailQuery", q)
}

// The WmsVehicleRepairOrderMaterialfeeDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsVehicleRepairOrderMaterialfeeDetailMutationRuleFunc func(context.Context, *ent.WmsVehicleRepairOrderMaterialfeeDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsVehicleRepairOrderMaterialfeeDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsVehicleRepairOrderMaterialfeeDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsVehicleRepairOrderMaterialfeeDetailMutation", m)
}

// The WmsVehicleRepairOrderSettlementfeeDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsVehicleRepairOrderSettlementfeeDetailQueryRuleFunc func(context.Context, *ent.WmsVehicleRepairOrderSettlementfeeDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsVehicleRepairOrderSettlementfeeDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsVehicleRepairOrderSettlementfeeDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsVehicleRepairOrderSettlementfeeDetailQuery", q)
}

// The WmsVehicleRepairOrderSettlementfeeDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsVehicleRepairOrderSettlementfeeDetailMutationRuleFunc func(context.Context, *ent.WmsVehicleRepairOrderSettlementfeeDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsVehicleRepairOrderSettlementfeeDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsVehicleRepairOrderSettlementfeeDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsVehicleRepairOrderSettlementfeeDetailMutation", m)
}

// The WmsVehicleRepairOrderWorkfeeDetailQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WmsVehicleRepairOrderWorkfeeDetailQueryRuleFunc func(context.Context, *ent.WmsVehicleRepairOrderWorkfeeDetailQuery) error

// EvalQuery return f(ctx, q).
func (f WmsVehicleRepairOrderWorkfeeDetailQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsVehicleRepairOrderWorkfeeDetailQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WmsVehicleRepairOrderWorkfeeDetailQuery", q)
}

// The WmsVehicleRepairOrderWorkfeeDetailMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WmsVehicleRepairOrderWorkfeeDetailMutationRuleFunc func(context.Context, *ent.WmsVehicleRepairOrderWorkfeeDetailMutation) error

// EvalMutation calls f(ctx, m).
func (f WmsVehicleRepairOrderWorkfeeDetailMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WmsVehicleRepairOrderWorkfeeDetailMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WmsVehicleRepairOrderWorkfeeDetailMutation", m)
}

// The WorkWxApprovalMessageQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WorkWxApprovalMessageQueryRuleFunc func(context.Context, *ent.WorkWxApprovalMessageQuery) error

// EvalQuery return f(ctx, q).
func (f WorkWxApprovalMessageQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WorkWxApprovalMessageQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WorkWxApprovalMessageQuery", q)
}

// The WorkWxApprovalMessageMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WorkWxApprovalMessageMutationRuleFunc func(context.Context, *ent.WorkWxApprovalMessageMutation) error

// EvalMutation calls f(ctx, m).
func (f WorkWxApprovalMessageMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WorkWxApprovalMessageMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WorkWxApprovalMessageMutation", m)
}

// The WorkWxApprovalNodeQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WorkWxApprovalNodeQueryRuleFunc func(context.Context, *ent.WorkWxApprovalNodeQuery) error

// EvalQuery return f(ctx, q).
func (f WorkWxApprovalNodeQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WorkWxApprovalNodeQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WorkWxApprovalNodeQuery", q)
}

// The WorkWxApprovalNodeMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WorkWxApprovalNodeMutationRuleFunc func(context.Context, *ent.WorkWxApprovalNodeMutation) error

// EvalMutation calls f(ctx, m).
func (f WorkWxApprovalNodeMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WorkWxApprovalNodeMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WorkWxApprovalNodeMutation", m)
}

// The WorkWxNotifyNodeQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WorkWxNotifyNodeQueryRuleFunc func(context.Context, *ent.WorkWxNotifyNodeQuery) error

// EvalQuery return f(ctx, q).
func (f WorkWxNotifyNodeQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WorkWxNotifyNodeQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WorkWxNotifyNodeQuery", q)
}

// The WorkWxNotifyNodeMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WorkWxNotifyNodeMutationRuleFunc func(context.Context, *ent.WorkWxNotifyNodeMutation) error

// EvalMutation calls f(ctx, m).
func (f WorkWxNotifyNodeMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WorkWxNotifyNodeMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WorkWxNotifyNodeMutation", m)
}

type (
	// Filter is the interface that wraps the Where function
	// for filtering nodes in queries and mutations.
	Filter interface {
		// Where applies a filter on the executed query/mutation.
		Where(entql.P)
	}

	// The FilterFunc type is an adapter that allows the use of ordinary
	// functions as filters for query and mutation types.
	FilterFunc func(context.Context, Filter) error
)

// EvalQuery calls f(ctx, q) if the query implements the Filter interface, otherwise it is denied.
func (f FilterFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	fr, err := queryFilter(q)
	if err != nil {
		return err
	}
	return f(ctx, fr)
}

// EvalMutation calls f(ctx, q) if the mutation implements the Filter interface, otherwise it is denied.
func (f FilterFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	fr, err := mutationFilter(m)
	if err != nil {
		return err
	}
	return f(ctx, fr)
}

var _ QueryMutationRule = FilterFunc(nil)

func queryFilter(q ent.Query) (Filter, error) {
	switch q := q.(type) {
	case *ent.AppApproveActivityQuery:
		return q.Filter(), nil
	case *ent.AppApproveBasicQuery:
		return q.Filter(), nil
	case *ent.AppApproveGroupQuery:
		return q.Filter(), nil
	case *ent.AppApproveRecordQuery:
		return q.Filter(), nil
	case *ent.AppApproveRecordLogQuery:
		return q.Filter(), nil
	case *ent.AppApproveRecordQueueQuery:
		return q.Filter(), nil
	case *ent.AppApproveWorkflowQuery:
		return q.Filter(), nil
	case *ent.DemoBuildingQuery:
		return q.Filter(), nil
	case *ent.SysApplicationQuery:
		return q.Filter(), nil
	case *ent.SysApplicationModuleQuery:
		return q.Filter(), nil
	case *ent.SysApplicationModuleResourceQuery:
		return q.Filter(), nil
	case *ent.SysAreaQuery:
		return q.Filter(), nil
	case *ent.SysCityQuery:
		return q.Filter(), nil
	case *ent.SysConfigQuery:
		return q.Filter(), nil
	case *ent.SysDictionaryQuery:
		return q.Filter(), nil
	case *ent.SysDictionaryDetailQuery:
		return q.Filter(), nil
	case *ent.SysGameQuery:
		return q.Filter(), nil
	case *ent.SysLabelQuery:
		return q.Filter(), nil
	case *ent.SysLogQuery:
		return q.Filter(), nil
	case *ent.SysMenuQuery:
		return q.Filter(), nil
	case *ent.SysMessageTemplateQuery:
		return q.Filter(), nil
	case *ent.SysModuleQuery:
		return q.Filter(), nil
	case *ent.SysModuleResourceQuery:
		return q.Filter(), nil
	case *ent.SysOrganizationQuery:
		return q.Filter(), nil
	case *ent.SysPageCodeQuery:
		return q.Filter(), nil
	case *ent.SysPageCodeHistoryQuery:
		return q.Filter(), nil
	case *ent.SysProjectQuery:
		return q.Filter(), nil
	case *ent.SysProvinceQuery:
		return q.Filter(), nil
	case *ent.SysResourceQuery:
		return q.Filter(), nil
	case *ent.SysRoleQuery:
		return q.Filter(), nil
	case *ent.SysRoleMenuQuery:
		return q.Filter(), nil
	case *ent.SysRoleResourceQuery:
		return q.Filter(), nil
	case *ent.SysStreetQuery:
		return q.Filter(), nil
	case *ent.SysTeamQuery:
		return q.Filter(), nil
	case *ent.SysTeamApplicationQuery:
		return q.Filter(), nil
	case *ent.SysUploadQuery:
		return q.Filter(), nil
	case *ent.SysUserQuery:
		return q.Filter(), nil
	case *ent.SysUserAuthQuery:
		return q.Filter(), nil
	case *ent.SysUserLogQuery:
		return q.Filter(), nil
	case *ent.SysUserOrganizationQuery:
		return q.Filter(), nil
	case *ent.SysUserRoleQuery:
		return q.Filter(), nil
	case *ent.WmsAccessDoorLogQuery:
		return q.Filter(), nil
	case *ent.WmsApprovalTaskQuery:
		return q.Filter(), nil
	case *ent.WmsApprovalTaskDetailQuery:
		return q.Filter(), nil
	case *ent.WmsApprovalTaskOperationQuery:
		return q.Filter(), nil
	case *ent.WmsApprovalTaskRemarkQuery:
		return q.Filter(), nil
	case *ent.WmsAuditPlanQuery:
		return q.Filter(), nil
	case *ent.WmsAuditPlanDetailQuery:
		return q.Filter(), nil
	case *ent.WmsBorrowOrderQuery:
		return q.Filter(), nil
	case *ent.WmsBorrowOrderDetailQuery:
		return q.Filter(), nil
	case *ent.WmsCarQuery:
		return q.Filter(), nil
	case *ent.WmsClaimOrderQuery:
		return q.Filter(), nil
	case *ent.WmsClaimOrderDetailQuery:
		return q.Filter(), nil
	case *ent.WmsCompanyQuery:
		return q.Filter(), nil
	case *ent.WmsCompanyAddressQuery:
		return q.Filter(), nil
	case *ent.WmsDiscardMeetingQuery:
		return q.Filter(), nil
	case *ent.WmsDiscardMeetingDetailQuery:
		return q.Filter(), nil
	case *ent.WmsDiscardOrderQuery:
		return q.Filter(), nil
	case *ent.WmsDiscardOrderDetailQuery:
		return q.Filter(), nil
	case *ent.WmsDiscardPlanOrderQuery:
		return q.Filter(), nil
	case *ent.WmsDiscardPlanOrderDetailQuery:
		return q.Filter(), nil
	case *ent.WmsDocumentQuery:
		return q.Filter(), nil
	case *ent.WmsEnterRepositoryOrderQuery:
		return q.Filter(), nil
	case *ent.WmsEnterRepositoryOrderDetailQuery:
		return q.Filter(), nil
	case *ent.WmsEquipmentQuery:
		return q.Filter(), nil
	case *ent.WmsEquipmentDetailQuery:
		return q.Filter(), nil
	case *ent.WmsEquipmentTypeQuery:
		return q.Filter(), nil
	case *ent.WmsEquipmentTypePropertyQuery:
		return q.Filter(), nil
	case *ent.WmsEquipmentTypePropertyGroupQuery:
		return q.Filter(), nil
	case *ent.WmsEquipmentTypePropertyOptionQuery:
		return q.Filter(), nil
	case *ent.WmsEquipmentUserGroupQuery:
		return q.Filter(), nil
	case *ent.WmsFireStationQuery:
		return q.Filter(), nil
	case *ent.WmsGPSQuery:
		return q.Filter(), nil
	case *ent.WmsGPSRecordQuery:
		return q.Filter(), nil
	case *ent.WmsLearningCourseQuery:
		return q.Filter(), nil
	case *ent.WmsLearningCourseCoursewareQuery:
		return q.Filter(), nil
	case *ent.WmsLearningCourseLogQuery:
		return q.Filter(), nil
	case *ent.WmsLearningCourseRecordQuery:
		return q.Filter(), nil
	case *ent.WmsLearningCoursewareQuery:
		return q.Filter(), nil
	case *ent.WmsLearningCoursewareRecordQuery:
		return q.Filter(), nil
	case *ent.WmsLearningPlanQuery:
		return q.Filter(), nil
	case *ent.WmsLearningPlanRecordQuery:
		return q.Filter(), nil
	case *ent.WmsMaintainOrderQuery:
		return q.Filter(), nil
	case *ent.WmsMaintainOrderDetailQuery:
		return q.Filter(), nil
	case *ent.WmsMaintainPlanQuery:
		return q.Filter(), nil
	case *ent.WmsMaintainPlanDetailQuery:
		return q.Filter(), nil
	case *ent.WmsMaterialQuery:
		return q.Filter(), nil
	case *ent.WmsMaterialLogQuery:
		return q.Filter(), nil
	case *ent.WmsMeasureUnitQuery:
		return q.Filter(), nil
	case *ent.WmsOperateLogQuery:
		return q.Filter(), nil
	case *ent.WmsOutRepositoryOrderQuery:
		return q.Filter(), nil
	case *ent.WmsOutRepositoryOrderDetailQuery:
		return q.Filter(), nil
	case *ent.WmsPurchaseOrderQuery:
		return q.Filter(), nil
	case *ent.WmsPurchaseOrderDetailQuery:
		return q.Filter(), nil
	case *ent.WmsRepairOrderQuery:
		return q.Filter(), nil
	case *ent.WmsRepairOrderDetailQuery:
		return q.Filter(), nil
	case *ent.WmsRepairSettlementOrderQuery:
		return q.Filter(), nil
	case *ent.WmsRepairSettlementOrderSettlementfeeDetailQuery:
		return q.Filter(), nil
	case *ent.WmsRepairSettlementOrderWorkfeeDetailQuery:
		return q.Filter(), nil
	case *ent.WmsRepositoryQuery:
		return q.Filter(), nil
	case *ent.WmsRepositoryAdminQuery:
		return q.Filter(), nil
	case *ent.WmsRepositoryAreaQuery:
		return q.Filter(), nil
	case *ent.WmsRepositoryPositionQuery:
		return q.Filter(), nil
	case *ent.WmsRepositoryScreenQuery:
		return q.Filter(), nil
	case *ent.WmsRepositoryScreenRepositoryAreaQuery:
		return q.Filter(), nil
	case *ent.WmsReturnOrderQuery:
		return q.Filter(), nil
	case *ent.WmsReturnOrderDetailQuery:
		return q.Filter(), nil
	case *ent.WmsRfidQuery:
		return q.Filter(), nil
	case *ent.WmsRfidReaderQuery:
		return q.Filter(), nil
	case *ent.WmsRfidReaderRecordQuery:
		return q.Filter(), nil
	case *ent.WmsTransferOrderQuery:
		return q.Filter(), nil
	case *ent.WmsTransferOrderDetailQuery:
		return q.Filter(), nil
	case *ent.WmsVehicleRepairOrderQuery:
		return q.Filter(), nil
	case *ent.WmsVehicleRepairOrderMaterialfeeDetailQuery:
		return q.Filter(), nil
	case *ent.WmsVehicleRepairOrderSettlementfeeDetailQuery:
		return q.Filter(), nil
	case *ent.WmsVehicleRepairOrderWorkfeeDetailQuery:
		return q.Filter(), nil
	case *ent.WorkWxApprovalMessageQuery:
		return q.Filter(), nil
	case *ent.WorkWxApprovalNodeQuery:
		return q.Filter(), nil
	case *ent.WorkWxNotifyNodeQuery:
		return q.Filter(), nil
	default:
		return nil, Denyf("ent/privacy: unexpected query type %T for query filter", q)
	}
}

func mutationFilter(m ent.Mutation) (Filter, error) {
	switch m := m.(type) {
	case *ent.AppApproveActivityMutation:
		return m.Filter(), nil
	case *ent.AppApproveBasicMutation:
		return m.Filter(), nil
	case *ent.AppApproveGroupMutation:
		return m.Filter(), nil
	case *ent.AppApproveRecordMutation:
		return m.Filter(), nil
	case *ent.AppApproveRecordLogMutation:
		return m.Filter(), nil
	case *ent.AppApproveRecordQueueMutation:
		return m.Filter(), nil
	case *ent.AppApproveWorkflowMutation:
		return m.Filter(), nil
	case *ent.DemoBuildingMutation:
		return m.Filter(), nil
	case *ent.SysApplicationMutation:
		return m.Filter(), nil
	case *ent.SysApplicationModuleMutation:
		return m.Filter(), nil
	case *ent.SysApplicationModuleResourceMutation:
		return m.Filter(), nil
	case *ent.SysAreaMutation:
		return m.Filter(), nil
	case *ent.SysCityMutation:
		return m.Filter(), nil
	case *ent.SysConfigMutation:
		return m.Filter(), nil
	case *ent.SysDictionaryMutation:
		return m.Filter(), nil
	case *ent.SysDictionaryDetailMutation:
		return m.Filter(), nil
	case *ent.SysGameMutation:
		return m.Filter(), nil
	case *ent.SysLabelMutation:
		return m.Filter(), nil
	case *ent.SysLogMutation:
		return m.Filter(), nil
	case *ent.SysMenuMutation:
		return m.Filter(), nil
	case *ent.SysMessageTemplateMutation:
		return m.Filter(), nil
	case *ent.SysModuleMutation:
		return m.Filter(), nil
	case *ent.SysModuleResourceMutation:
		return m.Filter(), nil
	case *ent.SysOrganizationMutation:
		return m.Filter(), nil
	case *ent.SysPageCodeMutation:
		return m.Filter(), nil
	case *ent.SysPageCodeHistoryMutation:
		return m.Filter(), nil
	case *ent.SysProjectMutation:
		return m.Filter(), nil
	case *ent.SysProvinceMutation:
		return m.Filter(), nil
	case *ent.SysResourceMutation:
		return m.Filter(), nil
	case *ent.SysRoleMutation:
		return m.Filter(), nil
	case *ent.SysRoleMenuMutation:
		return m.Filter(), nil
	case *ent.SysRoleResourceMutation:
		return m.Filter(), nil
	case *ent.SysStreetMutation:
		return m.Filter(), nil
	case *ent.SysTeamMutation:
		return m.Filter(), nil
	case *ent.SysTeamApplicationMutation:
		return m.Filter(), nil
	case *ent.SysUploadMutation:
		return m.Filter(), nil
	case *ent.SysUserMutation:
		return m.Filter(), nil
	case *ent.SysUserAuthMutation:
		return m.Filter(), nil
	case *ent.SysUserLogMutation:
		return m.Filter(), nil
	case *ent.SysUserOrganizationMutation:
		return m.Filter(), nil
	case *ent.SysUserRoleMutation:
		return m.Filter(), nil
	case *ent.WmsAccessDoorLogMutation:
		return m.Filter(), nil
	case *ent.WmsApprovalTaskMutation:
		return m.Filter(), nil
	case *ent.WmsApprovalTaskDetailMutation:
		return m.Filter(), nil
	case *ent.WmsApprovalTaskOperationMutation:
		return m.Filter(), nil
	case *ent.WmsApprovalTaskRemarkMutation:
		return m.Filter(), nil
	case *ent.WmsAuditPlanMutation:
		return m.Filter(), nil
	case *ent.WmsAuditPlanDetailMutation:
		return m.Filter(), nil
	case *ent.WmsBorrowOrderMutation:
		return m.Filter(), nil
	case *ent.WmsBorrowOrderDetailMutation:
		return m.Filter(), nil
	case *ent.WmsCarMutation:
		return m.Filter(), nil
	case *ent.WmsClaimOrderMutation:
		return m.Filter(), nil
	case *ent.WmsClaimOrderDetailMutation:
		return m.Filter(), nil
	case *ent.WmsCompanyMutation:
		return m.Filter(), nil
	case *ent.WmsCompanyAddressMutation:
		return m.Filter(), nil
	case *ent.WmsDiscardMeetingMutation:
		return m.Filter(), nil
	case *ent.WmsDiscardMeetingDetailMutation:
		return m.Filter(), nil
	case *ent.WmsDiscardOrderMutation:
		return m.Filter(), nil
	case *ent.WmsDiscardOrderDetailMutation:
		return m.Filter(), nil
	case *ent.WmsDiscardPlanOrderMutation:
		return m.Filter(), nil
	case *ent.WmsDiscardPlanOrderDetailMutation:
		return m.Filter(), nil
	case *ent.WmsDocumentMutation:
		return m.Filter(), nil
	case *ent.WmsEnterRepositoryOrderMutation:
		return m.Filter(), nil
	case *ent.WmsEnterRepositoryOrderDetailMutation:
		return m.Filter(), nil
	case *ent.WmsEquipmentMutation:
		return m.Filter(), nil
	case *ent.WmsEquipmentDetailMutation:
		return m.Filter(), nil
	case *ent.WmsEquipmentTypeMutation:
		return m.Filter(), nil
	case *ent.WmsEquipmentTypePropertyMutation:
		return m.Filter(), nil
	case *ent.WmsEquipmentTypePropertyGroupMutation:
		return m.Filter(), nil
	case *ent.WmsEquipmentTypePropertyOptionMutation:
		return m.Filter(), nil
	case *ent.WmsEquipmentUserGroupMutation:
		return m.Filter(), nil
	case *ent.WmsFireStationMutation:
		return m.Filter(), nil
	case *ent.WmsGPSMutation:
		return m.Filter(), nil
	case *ent.WmsGPSRecordMutation:
		return m.Filter(), nil
	case *ent.WmsLearningCourseMutation:
		return m.Filter(), nil
	case *ent.WmsLearningCourseCoursewareMutation:
		return m.Filter(), nil
	case *ent.WmsLearningCourseLogMutation:
		return m.Filter(), nil
	case *ent.WmsLearningCourseRecordMutation:
		return m.Filter(), nil
	case *ent.WmsLearningCoursewareMutation:
		return m.Filter(), nil
	case *ent.WmsLearningCoursewareRecordMutation:
		return m.Filter(), nil
	case *ent.WmsLearningPlanMutation:
		return m.Filter(), nil
	case *ent.WmsLearningPlanRecordMutation:
		return m.Filter(), nil
	case *ent.WmsMaintainOrderMutation:
		return m.Filter(), nil
	case *ent.WmsMaintainOrderDetailMutation:
		return m.Filter(), nil
	case *ent.WmsMaintainPlanMutation:
		return m.Filter(), nil
	case *ent.WmsMaintainPlanDetailMutation:
		return m.Filter(), nil
	case *ent.WmsMaterialMutation:
		return m.Filter(), nil
	case *ent.WmsMaterialLogMutation:
		return m.Filter(), nil
	case *ent.WmsMeasureUnitMutation:
		return m.Filter(), nil
	case *ent.WmsOperateLogMutation:
		return m.Filter(), nil
	case *ent.WmsOutRepositoryOrderMutation:
		return m.Filter(), nil
	case *ent.WmsOutRepositoryOrderDetailMutation:
		return m.Filter(), nil
	case *ent.WmsPurchaseOrderMutation:
		return m.Filter(), nil
	case *ent.WmsPurchaseOrderDetailMutation:
		return m.Filter(), nil
	case *ent.WmsRepairOrderMutation:
		return m.Filter(), nil
	case *ent.WmsRepairOrderDetailMutation:
		return m.Filter(), nil
	case *ent.WmsRepairSettlementOrderMutation:
		return m.Filter(), nil
	case *ent.WmsRepairSettlementOrderSettlementfeeDetailMutation:
		return m.Filter(), nil
	case *ent.WmsRepairSettlementOrderWorkfeeDetailMutation:
		return m.Filter(), nil
	case *ent.WmsRepositoryMutation:
		return m.Filter(), nil
	case *ent.WmsRepositoryAdminMutation:
		return m.Filter(), nil
	case *ent.WmsRepositoryAreaMutation:
		return m.Filter(), nil
	case *ent.WmsRepositoryPositionMutation:
		return m.Filter(), nil
	case *ent.WmsRepositoryScreenMutation:
		return m.Filter(), nil
	case *ent.WmsRepositoryScreenRepositoryAreaMutation:
		return m.Filter(), nil
	case *ent.WmsReturnOrderMutation:
		return m.Filter(), nil
	case *ent.WmsReturnOrderDetailMutation:
		return m.Filter(), nil
	case *ent.WmsRfidMutation:
		return m.Filter(), nil
	case *ent.WmsRfidReaderMutation:
		return m.Filter(), nil
	case *ent.WmsRfidReaderRecordMutation:
		return m.Filter(), nil
	case *ent.WmsTransferOrderMutation:
		return m.Filter(), nil
	case *ent.WmsTransferOrderDetailMutation:
		return m.Filter(), nil
	case *ent.WmsVehicleRepairOrderMutation:
		return m.Filter(), nil
	case *ent.WmsVehicleRepairOrderMaterialfeeDetailMutation:
		return m.Filter(), nil
	case *ent.WmsVehicleRepairOrderSettlementfeeDetailMutation:
		return m.Filter(), nil
	case *ent.WmsVehicleRepairOrderWorkfeeDetailMutation:
		return m.Filter(), nil
	case *ent.WorkWxApprovalMessageMutation:
		return m.Filter(), nil
	case *ent.WorkWxApprovalNodeMutation:
		return m.Filter(), nil
	case *ent.WorkWxNotifyNodeMutation:
		return m.Filter(), nil
	default:
		return nil, Denyf("ent/privacy: unexpected mutation type %T for mutation filter", m)
	}
}
