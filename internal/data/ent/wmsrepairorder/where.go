// Code generated by ent, DO NOT EDIT.

package wmsrepairorder

import (
	"kratos-mono-demo/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldContainsFold(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEQ(FieldUpdatedAt, v))
}

// CreatedBy applies equality check predicate on the "created_by" field. It's identical to CreatedByEQ.
func CreatedBy(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEQ(FieldCreatedBy, v))
}

// UpdatedBy applies equality check predicate on the "updated_by" field. It's identical to UpdatedByEQ.
func UpdatedBy(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEQ(FieldUpdatedBy, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEQ(FieldRemark, v))
}

// OrderNo applies equality check predicate on the "order_no" field. It's identical to OrderNoEQ.
func OrderNo(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEQ(FieldOrderNo, v))
}

// EquipmentNum applies equality check predicate on the "equipment_num" field. It's identical to EquipmentNumEQ.
func EquipmentNum(v uint32) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEQ(FieldEquipmentNum, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEQ(FieldStatus, v))
}

// RepairTime applies equality check predicate on the "repair_time" field. It's identical to RepairTimeEQ.
func RepairTime(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEQ(FieldRepairTime, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldLTE(FieldUpdatedAt, v))
}

// CreatedByEQ applies the EQ predicate on the "created_by" field.
func CreatedByEQ(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEQ(FieldCreatedBy, v))
}

// CreatedByNEQ applies the NEQ predicate on the "created_by" field.
func CreatedByNEQ(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNEQ(FieldCreatedBy, v))
}

// CreatedByIn applies the In predicate on the "created_by" field.
func CreatedByIn(vs ...string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldIn(FieldCreatedBy, vs...))
}

// CreatedByNotIn applies the NotIn predicate on the "created_by" field.
func CreatedByNotIn(vs ...string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNotIn(FieldCreatedBy, vs...))
}

// CreatedByGT applies the GT predicate on the "created_by" field.
func CreatedByGT(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldGT(FieldCreatedBy, v))
}

// CreatedByGTE applies the GTE predicate on the "created_by" field.
func CreatedByGTE(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldGTE(FieldCreatedBy, v))
}

// CreatedByLT applies the LT predicate on the "created_by" field.
func CreatedByLT(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldLT(FieldCreatedBy, v))
}

// CreatedByLTE applies the LTE predicate on the "created_by" field.
func CreatedByLTE(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldLTE(FieldCreatedBy, v))
}

// CreatedByContains applies the Contains predicate on the "created_by" field.
func CreatedByContains(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldContains(FieldCreatedBy, v))
}

// CreatedByHasPrefix applies the HasPrefix predicate on the "created_by" field.
func CreatedByHasPrefix(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldHasPrefix(FieldCreatedBy, v))
}

// CreatedByHasSuffix applies the HasSuffix predicate on the "created_by" field.
func CreatedByHasSuffix(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldHasSuffix(FieldCreatedBy, v))
}

// CreatedByIsNil applies the IsNil predicate on the "created_by" field.
func CreatedByIsNil() predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldIsNull(FieldCreatedBy))
}

// CreatedByNotNil applies the NotNil predicate on the "created_by" field.
func CreatedByNotNil() predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNotNull(FieldCreatedBy))
}

// CreatedByEqualFold applies the EqualFold predicate on the "created_by" field.
func CreatedByEqualFold(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEqualFold(FieldCreatedBy, v))
}

// CreatedByContainsFold applies the ContainsFold predicate on the "created_by" field.
func CreatedByContainsFold(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldContainsFold(FieldCreatedBy, v))
}

// UpdatedByEQ applies the EQ predicate on the "updated_by" field.
func UpdatedByEQ(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEQ(FieldUpdatedBy, v))
}

// UpdatedByNEQ applies the NEQ predicate on the "updated_by" field.
func UpdatedByNEQ(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNEQ(FieldUpdatedBy, v))
}

// UpdatedByIn applies the In predicate on the "updated_by" field.
func UpdatedByIn(vs ...string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldIn(FieldUpdatedBy, vs...))
}

// UpdatedByNotIn applies the NotIn predicate on the "updated_by" field.
func UpdatedByNotIn(vs ...string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNotIn(FieldUpdatedBy, vs...))
}

// UpdatedByGT applies the GT predicate on the "updated_by" field.
func UpdatedByGT(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldGT(FieldUpdatedBy, v))
}

// UpdatedByGTE applies the GTE predicate on the "updated_by" field.
func UpdatedByGTE(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldGTE(FieldUpdatedBy, v))
}

// UpdatedByLT applies the LT predicate on the "updated_by" field.
func UpdatedByLT(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldLT(FieldUpdatedBy, v))
}

// UpdatedByLTE applies the LTE predicate on the "updated_by" field.
func UpdatedByLTE(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldLTE(FieldUpdatedBy, v))
}

// UpdatedByContains applies the Contains predicate on the "updated_by" field.
func UpdatedByContains(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldContains(FieldUpdatedBy, v))
}

// UpdatedByHasPrefix applies the HasPrefix predicate on the "updated_by" field.
func UpdatedByHasPrefix(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldHasPrefix(FieldUpdatedBy, v))
}

// UpdatedByHasSuffix applies the HasSuffix predicate on the "updated_by" field.
func UpdatedByHasSuffix(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldHasSuffix(FieldUpdatedBy, v))
}

// UpdatedByIsNil applies the IsNil predicate on the "updated_by" field.
func UpdatedByIsNil() predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldIsNull(FieldUpdatedBy))
}

// UpdatedByNotNil applies the NotNil predicate on the "updated_by" field.
func UpdatedByNotNil() predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNotNull(FieldUpdatedBy))
}

// UpdatedByEqualFold applies the EqualFold predicate on the "updated_by" field.
func UpdatedByEqualFold(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEqualFold(FieldUpdatedBy, v))
}

// UpdatedByContainsFold applies the ContainsFold predicate on the "updated_by" field.
func UpdatedByContainsFold(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldContainsFold(FieldUpdatedBy, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldContainsFold(FieldRemark, v))
}

// OrderNoEQ applies the EQ predicate on the "order_no" field.
func OrderNoEQ(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEQ(FieldOrderNo, v))
}

// OrderNoNEQ applies the NEQ predicate on the "order_no" field.
func OrderNoNEQ(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNEQ(FieldOrderNo, v))
}

// OrderNoIn applies the In predicate on the "order_no" field.
func OrderNoIn(vs ...string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldIn(FieldOrderNo, vs...))
}

// OrderNoNotIn applies the NotIn predicate on the "order_no" field.
func OrderNoNotIn(vs ...string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNotIn(FieldOrderNo, vs...))
}

// OrderNoGT applies the GT predicate on the "order_no" field.
func OrderNoGT(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldGT(FieldOrderNo, v))
}

// OrderNoGTE applies the GTE predicate on the "order_no" field.
func OrderNoGTE(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldGTE(FieldOrderNo, v))
}

// OrderNoLT applies the LT predicate on the "order_no" field.
func OrderNoLT(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldLT(FieldOrderNo, v))
}

// OrderNoLTE applies the LTE predicate on the "order_no" field.
func OrderNoLTE(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldLTE(FieldOrderNo, v))
}

// OrderNoContains applies the Contains predicate on the "order_no" field.
func OrderNoContains(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldContains(FieldOrderNo, v))
}

// OrderNoHasPrefix applies the HasPrefix predicate on the "order_no" field.
func OrderNoHasPrefix(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldHasPrefix(FieldOrderNo, v))
}

// OrderNoHasSuffix applies the HasSuffix predicate on the "order_no" field.
func OrderNoHasSuffix(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldHasSuffix(FieldOrderNo, v))
}

// OrderNoEqualFold applies the EqualFold predicate on the "order_no" field.
func OrderNoEqualFold(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEqualFold(FieldOrderNo, v))
}

// OrderNoContainsFold applies the ContainsFold predicate on the "order_no" field.
func OrderNoContainsFold(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldContainsFold(FieldOrderNo, v))
}

// EquipmentNumEQ applies the EQ predicate on the "equipment_num" field.
func EquipmentNumEQ(v uint32) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEQ(FieldEquipmentNum, v))
}

// EquipmentNumNEQ applies the NEQ predicate on the "equipment_num" field.
func EquipmentNumNEQ(v uint32) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNEQ(FieldEquipmentNum, v))
}

// EquipmentNumIn applies the In predicate on the "equipment_num" field.
func EquipmentNumIn(vs ...uint32) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldIn(FieldEquipmentNum, vs...))
}

// EquipmentNumNotIn applies the NotIn predicate on the "equipment_num" field.
func EquipmentNumNotIn(vs ...uint32) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNotIn(FieldEquipmentNum, vs...))
}

// EquipmentNumGT applies the GT predicate on the "equipment_num" field.
func EquipmentNumGT(v uint32) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldGT(FieldEquipmentNum, v))
}

// EquipmentNumGTE applies the GTE predicate on the "equipment_num" field.
func EquipmentNumGTE(v uint32) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldGTE(FieldEquipmentNum, v))
}

// EquipmentNumLT applies the LT predicate on the "equipment_num" field.
func EquipmentNumLT(v uint32) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldLT(FieldEquipmentNum, v))
}

// EquipmentNumLTE applies the LTE predicate on the "equipment_num" field.
func EquipmentNumLTE(v uint32) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldLTE(FieldEquipmentNum, v))
}

// EquipmentNumIsNil applies the IsNil predicate on the "equipment_num" field.
func EquipmentNumIsNil() predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldIsNull(FieldEquipmentNum))
}

// EquipmentNumNotNil applies the NotNil predicate on the "equipment_num" field.
func EquipmentNumNotNil() predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNotNull(FieldEquipmentNum))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldLTE(FieldStatus, v))
}

// StatusContains applies the Contains predicate on the "status" field.
func StatusContains(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldContains(FieldStatus, v))
}

// StatusHasPrefix applies the HasPrefix predicate on the "status" field.
func StatusHasPrefix(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldHasPrefix(FieldStatus, v))
}

// StatusHasSuffix applies the HasSuffix predicate on the "status" field.
func StatusHasSuffix(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldHasSuffix(FieldStatus, v))
}

// StatusIsNil applies the IsNil predicate on the "status" field.
func StatusIsNil() predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldIsNull(FieldStatus))
}

// StatusNotNil applies the NotNil predicate on the "status" field.
func StatusNotNil() predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNotNull(FieldStatus))
}

// StatusEqualFold applies the EqualFold predicate on the "status" field.
func StatusEqualFold(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEqualFold(FieldStatus, v))
}

// StatusContainsFold applies the ContainsFold predicate on the "status" field.
func StatusContainsFold(v string) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldContainsFold(FieldStatus, v))
}

// RepairTimeEQ applies the EQ predicate on the "repair_time" field.
func RepairTimeEQ(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldEQ(FieldRepairTime, v))
}

// RepairTimeNEQ applies the NEQ predicate on the "repair_time" field.
func RepairTimeNEQ(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNEQ(FieldRepairTime, v))
}

// RepairTimeIn applies the In predicate on the "repair_time" field.
func RepairTimeIn(vs ...time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldIn(FieldRepairTime, vs...))
}

// RepairTimeNotIn applies the NotIn predicate on the "repair_time" field.
func RepairTimeNotIn(vs ...time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNotIn(FieldRepairTime, vs...))
}

// RepairTimeGT applies the GT predicate on the "repair_time" field.
func RepairTimeGT(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldGT(FieldRepairTime, v))
}

// RepairTimeGTE applies the GTE predicate on the "repair_time" field.
func RepairTimeGTE(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldGTE(FieldRepairTime, v))
}

// RepairTimeLT applies the LT predicate on the "repair_time" field.
func RepairTimeLT(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldLT(FieldRepairTime, v))
}

// RepairTimeLTE applies the LTE predicate on the "repair_time" field.
func RepairTimeLTE(v time.Time) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldLTE(FieldRepairTime, v))
}

// RepairTimeIsNil applies the IsNil predicate on the "repair_time" field.
func RepairTimeIsNil() predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldIsNull(FieldRepairTime))
}

// RepairTimeNotNil applies the NotNil predicate on the "repair_time" field.
func RepairTimeNotNil() predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.FieldNotNull(FieldRepairTime))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.WmsRepairOrder) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.WmsRepairOrder) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.WmsRepairOrder) predicate.WmsRepairOrder {
	return predicate.WmsRepairOrder(sql.NotPredicates(p))
}
