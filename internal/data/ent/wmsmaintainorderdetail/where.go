// Code generated by ent, DO NOT EDIT.

package wmsmaintainorderdetail

import (
	"kratos-mono-demo/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContainsFold(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldUpdatedAt, v))
}

// CreatedBy applies equality check predicate on the "created_by" field. It's identical to CreatedByEQ.
func CreatedBy(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldCreatedBy, v))
}

// UpdatedBy applies equality check predicate on the "updated_by" field. It's identical to UpdatedByEQ.
func UpdatedBy(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldUpdatedBy, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldRemark, v))
}

// OrderID applies equality check predicate on the "order_id" field. It's identical to OrderIDEQ.
func OrderID(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldOrderID, v))
}

// MaterialID applies equality check predicate on the "material_id" field. It's identical to MaterialIDEQ.
func MaterialID(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldMaterialID, v))
}

// MaterialName applies equality check predicate on the "material_name" field. It's identical to MaterialNameEQ.
func MaterialName(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldMaterialName, v))
}

// Code applies equality check predicate on the "code" field. It's identical to CodeEQ.
func Code(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldCode, v))
}

// EquipmentID applies equality check predicate on the "equipment_id" field. It's identical to EquipmentIDEQ.
func EquipmentID(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldEquipmentID, v))
}

// EquipmentTypeID applies equality check predicate on the "equipment_type_id" field. It's identical to EquipmentTypeIDEQ.
func EquipmentTypeID(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldEquipmentTypeID, v))
}

// OwnerID applies equality check predicate on the "owner_id" field. It's identical to OwnerIDEQ.
func OwnerID(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldOwnerID, v))
}

// ModelNo applies equality check predicate on the "ModelNo" field. It's identical to ModelNoEQ.
func ModelNo(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldModelNo, v))
}

// MeasureUnitID applies equality check predicate on the "measure_unit_id" field. It's identical to MeasureUnitIDEQ.
func MeasureUnitID(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldMeasureUnitID, v))
}

// Num applies equality check predicate on the "num" field. It's identical to NumEQ.
func Num(v uint32) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldNum, v))
}

// RepositoryID applies equality check predicate on the "repository_id" field. It's identical to RepositoryIDEQ.
func RepositoryID(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldRepositoryID, v))
}

// RepositoryAreaID applies equality check predicate on the "repository_area_id" field. It's identical to RepositoryAreaIDEQ.
func RepositoryAreaID(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldRepositoryAreaID, v))
}

// RepositoryPositionID applies equality check predicate on the "repository_position_id" field. It's identical to RepositoryPositionIDEQ.
func RepositoryPositionID(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldRepositoryPositionID, v))
}

// Reason applies equality check predicate on the "reason" field. It's identical to ReasonEQ.
func Reason(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldReason, v))
}

// MaintainTime applies equality check predicate on the "maintain_time" field. It's identical to MaintainTimeEQ.
func MaintainTime(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldMaintainTime, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldUpdatedAt, v))
}

// CreatedByEQ applies the EQ predicate on the "created_by" field.
func CreatedByEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldCreatedBy, v))
}

// CreatedByNEQ applies the NEQ predicate on the "created_by" field.
func CreatedByNEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldCreatedBy, v))
}

// CreatedByIn applies the In predicate on the "created_by" field.
func CreatedByIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldCreatedBy, vs...))
}

// CreatedByNotIn applies the NotIn predicate on the "created_by" field.
func CreatedByNotIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldCreatedBy, vs...))
}

// CreatedByGT applies the GT predicate on the "created_by" field.
func CreatedByGT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldCreatedBy, v))
}

// CreatedByGTE applies the GTE predicate on the "created_by" field.
func CreatedByGTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldCreatedBy, v))
}

// CreatedByLT applies the LT predicate on the "created_by" field.
func CreatedByLT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldCreatedBy, v))
}

// CreatedByLTE applies the LTE predicate on the "created_by" field.
func CreatedByLTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldCreatedBy, v))
}

// CreatedByContains applies the Contains predicate on the "created_by" field.
func CreatedByContains(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContains(FieldCreatedBy, v))
}

// CreatedByHasPrefix applies the HasPrefix predicate on the "created_by" field.
func CreatedByHasPrefix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasPrefix(FieldCreatedBy, v))
}

// CreatedByHasSuffix applies the HasSuffix predicate on the "created_by" field.
func CreatedByHasSuffix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasSuffix(FieldCreatedBy, v))
}

// CreatedByIsNil applies the IsNil predicate on the "created_by" field.
func CreatedByIsNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIsNull(FieldCreatedBy))
}

// CreatedByNotNil applies the NotNil predicate on the "created_by" field.
func CreatedByNotNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotNull(FieldCreatedBy))
}

// CreatedByEqualFold applies the EqualFold predicate on the "created_by" field.
func CreatedByEqualFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEqualFold(FieldCreatedBy, v))
}

// CreatedByContainsFold applies the ContainsFold predicate on the "created_by" field.
func CreatedByContainsFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContainsFold(FieldCreatedBy, v))
}

// UpdatedByEQ applies the EQ predicate on the "updated_by" field.
func UpdatedByEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldUpdatedBy, v))
}

// UpdatedByNEQ applies the NEQ predicate on the "updated_by" field.
func UpdatedByNEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldUpdatedBy, v))
}

// UpdatedByIn applies the In predicate on the "updated_by" field.
func UpdatedByIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldUpdatedBy, vs...))
}

// UpdatedByNotIn applies the NotIn predicate on the "updated_by" field.
func UpdatedByNotIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldUpdatedBy, vs...))
}

// UpdatedByGT applies the GT predicate on the "updated_by" field.
func UpdatedByGT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldUpdatedBy, v))
}

// UpdatedByGTE applies the GTE predicate on the "updated_by" field.
func UpdatedByGTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldUpdatedBy, v))
}

// UpdatedByLT applies the LT predicate on the "updated_by" field.
func UpdatedByLT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldUpdatedBy, v))
}

// UpdatedByLTE applies the LTE predicate on the "updated_by" field.
func UpdatedByLTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldUpdatedBy, v))
}

// UpdatedByContains applies the Contains predicate on the "updated_by" field.
func UpdatedByContains(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContains(FieldUpdatedBy, v))
}

// UpdatedByHasPrefix applies the HasPrefix predicate on the "updated_by" field.
func UpdatedByHasPrefix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasPrefix(FieldUpdatedBy, v))
}

// UpdatedByHasSuffix applies the HasSuffix predicate on the "updated_by" field.
func UpdatedByHasSuffix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasSuffix(FieldUpdatedBy, v))
}

// UpdatedByIsNil applies the IsNil predicate on the "updated_by" field.
func UpdatedByIsNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIsNull(FieldUpdatedBy))
}

// UpdatedByNotNil applies the NotNil predicate on the "updated_by" field.
func UpdatedByNotNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotNull(FieldUpdatedBy))
}

// UpdatedByEqualFold applies the EqualFold predicate on the "updated_by" field.
func UpdatedByEqualFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEqualFold(FieldUpdatedBy, v))
}

// UpdatedByContainsFold applies the ContainsFold predicate on the "updated_by" field.
func UpdatedByContainsFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContainsFold(FieldUpdatedBy, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContainsFold(FieldRemark, v))
}

// OrderIDEQ applies the EQ predicate on the "order_id" field.
func OrderIDEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldOrderID, v))
}

// OrderIDNEQ applies the NEQ predicate on the "order_id" field.
func OrderIDNEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldOrderID, v))
}

// OrderIDIn applies the In predicate on the "order_id" field.
func OrderIDIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldOrderID, vs...))
}

// OrderIDNotIn applies the NotIn predicate on the "order_id" field.
func OrderIDNotIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldOrderID, vs...))
}

// OrderIDGT applies the GT predicate on the "order_id" field.
func OrderIDGT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldOrderID, v))
}

// OrderIDGTE applies the GTE predicate on the "order_id" field.
func OrderIDGTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldOrderID, v))
}

// OrderIDLT applies the LT predicate on the "order_id" field.
func OrderIDLT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldOrderID, v))
}

// OrderIDLTE applies the LTE predicate on the "order_id" field.
func OrderIDLTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldOrderID, v))
}

// OrderIDContains applies the Contains predicate on the "order_id" field.
func OrderIDContains(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContains(FieldOrderID, v))
}

// OrderIDHasPrefix applies the HasPrefix predicate on the "order_id" field.
func OrderIDHasPrefix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasPrefix(FieldOrderID, v))
}

// OrderIDHasSuffix applies the HasSuffix predicate on the "order_id" field.
func OrderIDHasSuffix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasSuffix(FieldOrderID, v))
}

// OrderIDIsNil applies the IsNil predicate on the "order_id" field.
func OrderIDIsNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIsNull(FieldOrderID))
}

// OrderIDNotNil applies the NotNil predicate on the "order_id" field.
func OrderIDNotNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotNull(FieldOrderID))
}

// OrderIDEqualFold applies the EqualFold predicate on the "order_id" field.
func OrderIDEqualFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEqualFold(FieldOrderID, v))
}

// OrderIDContainsFold applies the ContainsFold predicate on the "order_id" field.
func OrderIDContainsFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContainsFold(FieldOrderID, v))
}

// MaterialIDEQ applies the EQ predicate on the "material_id" field.
func MaterialIDEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldMaterialID, v))
}

// MaterialIDNEQ applies the NEQ predicate on the "material_id" field.
func MaterialIDNEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldMaterialID, v))
}

// MaterialIDIn applies the In predicate on the "material_id" field.
func MaterialIDIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldMaterialID, vs...))
}

// MaterialIDNotIn applies the NotIn predicate on the "material_id" field.
func MaterialIDNotIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldMaterialID, vs...))
}

// MaterialIDGT applies the GT predicate on the "material_id" field.
func MaterialIDGT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldMaterialID, v))
}

// MaterialIDGTE applies the GTE predicate on the "material_id" field.
func MaterialIDGTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldMaterialID, v))
}

// MaterialIDLT applies the LT predicate on the "material_id" field.
func MaterialIDLT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldMaterialID, v))
}

// MaterialIDLTE applies the LTE predicate on the "material_id" field.
func MaterialIDLTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldMaterialID, v))
}

// MaterialIDContains applies the Contains predicate on the "material_id" field.
func MaterialIDContains(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContains(FieldMaterialID, v))
}

// MaterialIDHasPrefix applies the HasPrefix predicate on the "material_id" field.
func MaterialIDHasPrefix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasPrefix(FieldMaterialID, v))
}

// MaterialIDHasSuffix applies the HasSuffix predicate on the "material_id" field.
func MaterialIDHasSuffix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasSuffix(FieldMaterialID, v))
}

// MaterialIDIsNil applies the IsNil predicate on the "material_id" field.
func MaterialIDIsNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIsNull(FieldMaterialID))
}

// MaterialIDNotNil applies the NotNil predicate on the "material_id" field.
func MaterialIDNotNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotNull(FieldMaterialID))
}

// MaterialIDEqualFold applies the EqualFold predicate on the "material_id" field.
func MaterialIDEqualFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEqualFold(FieldMaterialID, v))
}

// MaterialIDContainsFold applies the ContainsFold predicate on the "material_id" field.
func MaterialIDContainsFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContainsFold(FieldMaterialID, v))
}

// MaterialNameEQ applies the EQ predicate on the "material_name" field.
func MaterialNameEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldMaterialName, v))
}

// MaterialNameNEQ applies the NEQ predicate on the "material_name" field.
func MaterialNameNEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldMaterialName, v))
}

// MaterialNameIn applies the In predicate on the "material_name" field.
func MaterialNameIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldMaterialName, vs...))
}

// MaterialNameNotIn applies the NotIn predicate on the "material_name" field.
func MaterialNameNotIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldMaterialName, vs...))
}

// MaterialNameGT applies the GT predicate on the "material_name" field.
func MaterialNameGT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldMaterialName, v))
}

// MaterialNameGTE applies the GTE predicate on the "material_name" field.
func MaterialNameGTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldMaterialName, v))
}

// MaterialNameLT applies the LT predicate on the "material_name" field.
func MaterialNameLT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldMaterialName, v))
}

// MaterialNameLTE applies the LTE predicate on the "material_name" field.
func MaterialNameLTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldMaterialName, v))
}

// MaterialNameContains applies the Contains predicate on the "material_name" field.
func MaterialNameContains(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContains(FieldMaterialName, v))
}

// MaterialNameHasPrefix applies the HasPrefix predicate on the "material_name" field.
func MaterialNameHasPrefix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasPrefix(FieldMaterialName, v))
}

// MaterialNameHasSuffix applies the HasSuffix predicate on the "material_name" field.
func MaterialNameHasSuffix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasSuffix(FieldMaterialName, v))
}

// MaterialNameIsNil applies the IsNil predicate on the "material_name" field.
func MaterialNameIsNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIsNull(FieldMaterialName))
}

// MaterialNameNotNil applies the NotNil predicate on the "material_name" field.
func MaterialNameNotNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotNull(FieldMaterialName))
}

// MaterialNameEqualFold applies the EqualFold predicate on the "material_name" field.
func MaterialNameEqualFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEqualFold(FieldMaterialName, v))
}

// MaterialNameContainsFold applies the ContainsFold predicate on the "material_name" field.
func MaterialNameContainsFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContainsFold(FieldMaterialName, v))
}

// CodeEQ applies the EQ predicate on the "code" field.
func CodeEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldCode, v))
}

// CodeNEQ applies the NEQ predicate on the "code" field.
func CodeNEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldCode, v))
}

// CodeIn applies the In predicate on the "code" field.
func CodeIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldCode, vs...))
}

// CodeNotIn applies the NotIn predicate on the "code" field.
func CodeNotIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldCode, vs...))
}

// CodeGT applies the GT predicate on the "code" field.
func CodeGT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldCode, v))
}

// CodeGTE applies the GTE predicate on the "code" field.
func CodeGTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldCode, v))
}

// CodeLT applies the LT predicate on the "code" field.
func CodeLT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldCode, v))
}

// CodeLTE applies the LTE predicate on the "code" field.
func CodeLTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldCode, v))
}

// CodeContains applies the Contains predicate on the "code" field.
func CodeContains(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContains(FieldCode, v))
}

// CodeHasPrefix applies the HasPrefix predicate on the "code" field.
func CodeHasPrefix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasPrefix(FieldCode, v))
}

// CodeHasSuffix applies the HasSuffix predicate on the "code" field.
func CodeHasSuffix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasSuffix(FieldCode, v))
}

// CodeEqualFold applies the EqualFold predicate on the "code" field.
func CodeEqualFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEqualFold(FieldCode, v))
}

// CodeContainsFold applies the ContainsFold predicate on the "code" field.
func CodeContainsFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContainsFold(FieldCode, v))
}

// EquipmentIDEQ applies the EQ predicate on the "equipment_id" field.
func EquipmentIDEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldEquipmentID, v))
}

// EquipmentIDNEQ applies the NEQ predicate on the "equipment_id" field.
func EquipmentIDNEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldEquipmentID, v))
}

// EquipmentIDIn applies the In predicate on the "equipment_id" field.
func EquipmentIDIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldEquipmentID, vs...))
}

// EquipmentIDNotIn applies the NotIn predicate on the "equipment_id" field.
func EquipmentIDNotIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldEquipmentID, vs...))
}

// EquipmentIDGT applies the GT predicate on the "equipment_id" field.
func EquipmentIDGT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldEquipmentID, v))
}

// EquipmentIDGTE applies the GTE predicate on the "equipment_id" field.
func EquipmentIDGTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldEquipmentID, v))
}

// EquipmentIDLT applies the LT predicate on the "equipment_id" field.
func EquipmentIDLT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldEquipmentID, v))
}

// EquipmentIDLTE applies the LTE predicate on the "equipment_id" field.
func EquipmentIDLTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldEquipmentID, v))
}

// EquipmentIDContains applies the Contains predicate on the "equipment_id" field.
func EquipmentIDContains(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContains(FieldEquipmentID, v))
}

// EquipmentIDHasPrefix applies the HasPrefix predicate on the "equipment_id" field.
func EquipmentIDHasPrefix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasPrefix(FieldEquipmentID, v))
}

// EquipmentIDHasSuffix applies the HasSuffix predicate on the "equipment_id" field.
func EquipmentIDHasSuffix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasSuffix(FieldEquipmentID, v))
}

// EquipmentIDIsNil applies the IsNil predicate on the "equipment_id" field.
func EquipmentIDIsNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIsNull(FieldEquipmentID))
}

// EquipmentIDNotNil applies the NotNil predicate on the "equipment_id" field.
func EquipmentIDNotNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotNull(FieldEquipmentID))
}

// EquipmentIDEqualFold applies the EqualFold predicate on the "equipment_id" field.
func EquipmentIDEqualFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEqualFold(FieldEquipmentID, v))
}

// EquipmentIDContainsFold applies the ContainsFold predicate on the "equipment_id" field.
func EquipmentIDContainsFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContainsFold(FieldEquipmentID, v))
}

// EquipmentTypeIDEQ applies the EQ predicate on the "equipment_type_id" field.
func EquipmentTypeIDEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDNEQ applies the NEQ predicate on the "equipment_type_id" field.
func EquipmentTypeIDNEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDIn applies the In predicate on the "equipment_type_id" field.
func EquipmentTypeIDIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldEquipmentTypeID, vs...))
}

// EquipmentTypeIDNotIn applies the NotIn predicate on the "equipment_type_id" field.
func EquipmentTypeIDNotIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldEquipmentTypeID, vs...))
}

// EquipmentTypeIDGT applies the GT predicate on the "equipment_type_id" field.
func EquipmentTypeIDGT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDGTE applies the GTE predicate on the "equipment_type_id" field.
func EquipmentTypeIDGTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDLT applies the LT predicate on the "equipment_type_id" field.
func EquipmentTypeIDLT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDLTE applies the LTE predicate on the "equipment_type_id" field.
func EquipmentTypeIDLTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDContains applies the Contains predicate on the "equipment_type_id" field.
func EquipmentTypeIDContains(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContains(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDHasPrefix applies the HasPrefix predicate on the "equipment_type_id" field.
func EquipmentTypeIDHasPrefix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasPrefix(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDHasSuffix applies the HasSuffix predicate on the "equipment_type_id" field.
func EquipmentTypeIDHasSuffix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasSuffix(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDIsNil applies the IsNil predicate on the "equipment_type_id" field.
func EquipmentTypeIDIsNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIsNull(FieldEquipmentTypeID))
}

// EquipmentTypeIDNotNil applies the NotNil predicate on the "equipment_type_id" field.
func EquipmentTypeIDNotNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotNull(FieldEquipmentTypeID))
}

// EquipmentTypeIDEqualFold applies the EqualFold predicate on the "equipment_type_id" field.
func EquipmentTypeIDEqualFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEqualFold(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDContainsFold applies the ContainsFold predicate on the "equipment_type_id" field.
func EquipmentTypeIDContainsFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContainsFold(FieldEquipmentTypeID, v))
}

// FeatureIsNil applies the IsNil predicate on the "feature" field.
func FeatureIsNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIsNull(FieldFeature))
}

// FeatureNotNil applies the NotNil predicate on the "feature" field.
func FeatureNotNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotNull(FieldFeature))
}

// OwnerIDEQ applies the EQ predicate on the "owner_id" field.
func OwnerIDEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldOwnerID, v))
}

// OwnerIDNEQ applies the NEQ predicate on the "owner_id" field.
func OwnerIDNEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldOwnerID, v))
}

// OwnerIDIn applies the In predicate on the "owner_id" field.
func OwnerIDIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldOwnerID, vs...))
}

// OwnerIDNotIn applies the NotIn predicate on the "owner_id" field.
func OwnerIDNotIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldOwnerID, vs...))
}

// OwnerIDGT applies the GT predicate on the "owner_id" field.
func OwnerIDGT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldOwnerID, v))
}

// OwnerIDGTE applies the GTE predicate on the "owner_id" field.
func OwnerIDGTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldOwnerID, v))
}

// OwnerIDLT applies the LT predicate on the "owner_id" field.
func OwnerIDLT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldOwnerID, v))
}

// OwnerIDLTE applies the LTE predicate on the "owner_id" field.
func OwnerIDLTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldOwnerID, v))
}

// OwnerIDContains applies the Contains predicate on the "owner_id" field.
func OwnerIDContains(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContains(FieldOwnerID, v))
}

// OwnerIDHasPrefix applies the HasPrefix predicate on the "owner_id" field.
func OwnerIDHasPrefix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasPrefix(FieldOwnerID, v))
}

// OwnerIDHasSuffix applies the HasSuffix predicate on the "owner_id" field.
func OwnerIDHasSuffix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasSuffix(FieldOwnerID, v))
}

// OwnerIDIsNil applies the IsNil predicate on the "owner_id" field.
func OwnerIDIsNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIsNull(FieldOwnerID))
}

// OwnerIDNotNil applies the NotNil predicate on the "owner_id" field.
func OwnerIDNotNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotNull(FieldOwnerID))
}

// OwnerIDEqualFold applies the EqualFold predicate on the "owner_id" field.
func OwnerIDEqualFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEqualFold(FieldOwnerID, v))
}

// OwnerIDContainsFold applies the ContainsFold predicate on the "owner_id" field.
func OwnerIDContainsFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContainsFold(FieldOwnerID, v))
}

// ModelNoEQ applies the EQ predicate on the "ModelNo" field.
func ModelNoEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldModelNo, v))
}

// ModelNoNEQ applies the NEQ predicate on the "ModelNo" field.
func ModelNoNEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldModelNo, v))
}

// ModelNoIn applies the In predicate on the "ModelNo" field.
func ModelNoIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldModelNo, vs...))
}

// ModelNoNotIn applies the NotIn predicate on the "ModelNo" field.
func ModelNoNotIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldModelNo, vs...))
}

// ModelNoGT applies the GT predicate on the "ModelNo" field.
func ModelNoGT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldModelNo, v))
}

// ModelNoGTE applies the GTE predicate on the "ModelNo" field.
func ModelNoGTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldModelNo, v))
}

// ModelNoLT applies the LT predicate on the "ModelNo" field.
func ModelNoLT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldModelNo, v))
}

// ModelNoLTE applies the LTE predicate on the "ModelNo" field.
func ModelNoLTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldModelNo, v))
}

// ModelNoContains applies the Contains predicate on the "ModelNo" field.
func ModelNoContains(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContains(FieldModelNo, v))
}

// ModelNoHasPrefix applies the HasPrefix predicate on the "ModelNo" field.
func ModelNoHasPrefix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasPrefix(FieldModelNo, v))
}

// ModelNoHasSuffix applies the HasSuffix predicate on the "ModelNo" field.
func ModelNoHasSuffix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasSuffix(FieldModelNo, v))
}

// ModelNoEqualFold applies the EqualFold predicate on the "ModelNo" field.
func ModelNoEqualFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEqualFold(FieldModelNo, v))
}

// ModelNoContainsFold applies the ContainsFold predicate on the "ModelNo" field.
func ModelNoContainsFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContainsFold(FieldModelNo, v))
}

// MeasureUnitIDEQ applies the EQ predicate on the "measure_unit_id" field.
func MeasureUnitIDEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldMeasureUnitID, v))
}

// MeasureUnitIDNEQ applies the NEQ predicate on the "measure_unit_id" field.
func MeasureUnitIDNEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldMeasureUnitID, v))
}

// MeasureUnitIDIn applies the In predicate on the "measure_unit_id" field.
func MeasureUnitIDIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldMeasureUnitID, vs...))
}

// MeasureUnitIDNotIn applies the NotIn predicate on the "measure_unit_id" field.
func MeasureUnitIDNotIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldMeasureUnitID, vs...))
}

// MeasureUnitIDGT applies the GT predicate on the "measure_unit_id" field.
func MeasureUnitIDGT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldMeasureUnitID, v))
}

// MeasureUnitIDGTE applies the GTE predicate on the "measure_unit_id" field.
func MeasureUnitIDGTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldMeasureUnitID, v))
}

// MeasureUnitIDLT applies the LT predicate on the "measure_unit_id" field.
func MeasureUnitIDLT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldMeasureUnitID, v))
}

// MeasureUnitIDLTE applies the LTE predicate on the "measure_unit_id" field.
func MeasureUnitIDLTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldMeasureUnitID, v))
}

// MeasureUnitIDContains applies the Contains predicate on the "measure_unit_id" field.
func MeasureUnitIDContains(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContains(FieldMeasureUnitID, v))
}

// MeasureUnitIDHasPrefix applies the HasPrefix predicate on the "measure_unit_id" field.
func MeasureUnitIDHasPrefix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasPrefix(FieldMeasureUnitID, v))
}

// MeasureUnitIDHasSuffix applies the HasSuffix predicate on the "measure_unit_id" field.
func MeasureUnitIDHasSuffix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasSuffix(FieldMeasureUnitID, v))
}

// MeasureUnitIDIsNil applies the IsNil predicate on the "measure_unit_id" field.
func MeasureUnitIDIsNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIsNull(FieldMeasureUnitID))
}

// MeasureUnitIDNotNil applies the NotNil predicate on the "measure_unit_id" field.
func MeasureUnitIDNotNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotNull(FieldMeasureUnitID))
}

// MeasureUnitIDEqualFold applies the EqualFold predicate on the "measure_unit_id" field.
func MeasureUnitIDEqualFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEqualFold(FieldMeasureUnitID, v))
}

// MeasureUnitIDContainsFold applies the ContainsFold predicate on the "measure_unit_id" field.
func MeasureUnitIDContainsFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContainsFold(FieldMeasureUnitID, v))
}

// NumEQ applies the EQ predicate on the "num" field.
func NumEQ(v uint32) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldNum, v))
}

// NumNEQ applies the NEQ predicate on the "num" field.
func NumNEQ(v uint32) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldNum, v))
}

// NumIn applies the In predicate on the "num" field.
func NumIn(vs ...uint32) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldNum, vs...))
}

// NumNotIn applies the NotIn predicate on the "num" field.
func NumNotIn(vs ...uint32) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldNum, vs...))
}

// NumGT applies the GT predicate on the "num" field.
func NumGT(v uint32) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldNum, v))
}

// NumGTE applies the GTE predicate on the "num" field.
func NumGTE(v uint32) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldNum, v))
}

// NumLT applies the LT predicate on the "num" field.
func NumLT(v uint32) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldNum, v))
}

// NumLTE applies the LTE predicate on the "num" field.
func NumLTE(v uint32) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldNum, v))
}

// RepositoryIDEQ applies the EQ predicate on the "repository_id" field.
func RepositoryIDEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldRepositoryID, v))
}

// RepositoryIDNEQ applies the NEQ predicate on the "repository_id" field.
func RepositoryIDNEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldRepositoryID, v))
}

// RepositoryIDIn applies the In predicate on the "repository_id" field.
func RepositoryIDIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldRepositoryID, vs...))
}

// RepositoryIDNotIn applies the NotIn predicate on the "repository_id" field.
func RepositoryIDNotIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldRepositoryID, vs...))
}

// RepositoryIDGT applies the GT predicate on the "repository_id" field.
func RepositoryIDGT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldRepositoryID, v))
}

// RepositoryIDGTE applies the GTE predicate on the "repository_id" field.
func RepositoryIDGTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldRepositoryID, v))
}

// RepositoryIDLT applies the LT predicate on the "repository_id" field.
func RepositoryIDLT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldRepositoryID, v))
}

// RepositoryIDLTE applies the LTE predicate on the "repository_id" field.
func RepositoryIDLTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldRepositoryID, v))
}

// RepositoryIDContains applies the Contains predicate on the "repository_id" field.
func RepositoryIDContains(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContains(FieldRepositoryID, v))
}

// RepositoryIDHasPrefix applies the HasPrefix predicate on the "repository_id" field.
func RepositoryIDHasPrefix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasPrefix(FieldRepositoryID, v))
}

// RepositoryIDHasSuffix applies the HasSuffix predicate on the "repository_id" field.
func RepositoryIDHasSuffix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasSuffix(FieldRepositoryID, v))
}

// RepositoryIDIsNil applies the IsNil predicate on the "repository_id" field.
func RepositoryIDIsNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIsNull(FieldRepositoryID))
}

// RepositoryIDNotNil applies the NotNil predicate on the "repository_id" field.
func RepositoryIDNotNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotNull(FieldRepositoryID))
}

// RepositoryIDEqualFold applies the EqualFold predicate on the "repository_id" field.
func RepositoryIDEqualFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEqualFold(FieldRepositoryID, v))
}

// RepositoryIDContainsFold applies the ContainsFold predicate on the "repository_id" field.
func RepositoryIDContainsFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContainsFold(FieldRepositoryID, v))
}

// RepositoryAreaIDEQ applies the EQ predicate on the "repository_area_id" field.
func RepositoryAreaIDEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDNEQ applies the NEQ predicate on the "repository_area_id" field.
func RepositoryAreaIDNEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDIn applies the In predicate on the "repository_area_id" field.
func RepositoryAreaIDIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldRepositoryAreaID, vs...))
}

// RepositoryAreaIDNotIn applies the NotIn predicate on the "repository_area_id" field.
func RepositoryAreaIDNotIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldRepositoryAreaID, vs...))
}

// RepositoryAreaIDGT applies the GT predicate on the "repository_area_id" field.
func RepositoryAreaIDGT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDGTE applies the GTE predicate on the "repository_area_id" field.
func RepositoryAreaIDGTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDLT applies the LT predicate on the "repository_area_id" field.
func RepositoryAreaIDLT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDLTE applies the LTE predicate on the "repository_area_id" field.
func RepositoryAreaIDLTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDContains applies the Contains predicate on the "repository_area_id" field.
func RepositoryAreaIDContains(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContains(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDHasPrefix applies the HasPrefix predicate on the "repository_area_id" field.
func RepositoryAreaIDHasPrefix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasPrefix(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDHasSuffix applies the HasSuffix predicate on the "repository_area_id" field.
func RepositoryAreaIDHasSuffix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasSuffix(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDIsNil applies the IsNil predicate on the "repository_area_id" field.
func RepositoryAreaIDIsNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIsNull(FieldRepositoryAreaID))
}

// RepositoryAreaIDNotNil applies the NotNil predicate on the "repository_area_id" field.
func RepositoryAreaIDNotNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotNull(FieldRepositoryAreaID))
}

// RepositoryAreaIDEqualFold applies the EqualFold predicate on the "repository_area_id" field.
func RepositoryAreaIDEqualFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEqualFold(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDContainsFold applies the ContainsFold predicate on the "repository_area_id" field.
func RepositoryAreaIDContainsFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContainsFold(FieldRepositoryAreaID, v))
}

// RepositoryPositionIDEQ applies the EQ predicate on the "repository_position_id" field.
func RepositoryPositionIDEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDNEQ applies the NEQ predicate on the "repository_position_id" field.
func RepositoryPositionIDNEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDIn applies the In predicate on the "repository_position_id" field.
func RepositoryPositionIDIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldRepositoryPositionID, vs...))
}

// RepositoryPositionIDNotIn applies the NotIn predicate on the "repository_position_id" field.
func RepositoryPositionIDNotIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldRepositoryPositionID, vs...))
}

// RepositoryPositionIDGT applies the GT predicate on the "repository_position_id" field.
func RepositoryPositionIDGT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDGTE applies the GTE predicate on the "repository_position_id" field.
func RepositoryPositionIDGTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDLT applies the LT predicate on the "repository_position_id" field.
func RepositoryPositionIDLT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDLTE applies the LTE predicate on the "repository_position_id" field.
func RepositoryPositionIDLTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDContains applies the Contains predicate on the "repository_position_id" field.
func RepositoryPositionIDContains(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContains(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDHasPrefix applies the HasPrefix predicate on the "repository_position_id" field.
func RepositoryPositionIDHasPrefix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasPrefix(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDHasSuffix applies the HasSuffix predicate on the "repository_position_id" field.
func RepositoryPositionIDHasSuffix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasSuffix(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDIsNil applies the IsNil predicate on the "repository_position_id" field.
func RepositoryPositionIDIsNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIsNull(FieldRepositoryPositionID))
}

// RepositoryPositionIDNotNil applies the NotNil predicate on the "repository_position_id" field.
func RepositoryPositionIDNotNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotNull(FieldRepositoryPositionID))
}

// RepositoryPositionIDEqualFold applies the EqualFold predicate on the "repository_position_id" field.
func RepositoryPositionIDEqualFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEqualFold(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDContainsFold applies the ContainsFold predicate on the "repository_position_id" field.
func RepositoryPositionIDContainsFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContainsFold(FieldRepositoryPositionID, v))
}

// ReasonEQ applies the EQ predicate on the "reason" field.
func ReasonEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldReason, v))
}

// ReasonNEQ applies the NEQ predicate on the "reason" field.
func ReasonNEQ(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldReason, v))
}

// ReasonIn applies the In predicate on the "reason" field.
func ReasonIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldReason, vs...))
}

// ReasonNotIn applies the NotIn predicate on the "reason" field.
func ReasonNotIn(vs ...string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldReason, vs...))
}

// ReasonGT applies the GT predicate on the "reason" field.
func ReasonGT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldReason, v))
}

// ReasonGTE applies the GTE predicate on the "reason" field.
func ReasonGTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldReason, v))
}

// ReasonLT applies the LT predicate on the "reason" field.
func ReasonLT(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldReason, v))
}

// ReasonLTE applies the LTE predicate on the "reason" field.
func ReasonLTE(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldReason, v))
}

// ReasonContains applies the Contains predicate on the "reason" field.
func ReasonContains(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContains(FieldReason, v))
}

// ReasonHasPrefix applies the HasPrefix predicate on the "reason" field.
func ReasonHasPrefix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasPrefix(FieldReason, v))
}

// ReasonHasSuffix applies the HasSuffix predicate on the "reason" field.
func ReasonHasSuffix(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldHasSuffix(FieldReason, v))
}

// ReasonIsNil applies the IsNil predicate on the "reason" field.
func ReasonIsNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIsNull(FieldReason))
}

// ReasonNotNil applies the NotNil predicate on the "reason" field.
func ReasonNotNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotNull(FieldReason))
}

// ReasonEqualFold applies the EqualFold predicate on the "reason" field.
func ReasonEqualFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEqualFold(FieldReason, v))
}

// ReasonContainsFold applies the ContainsFold predicate on the "reason" field.
func ReasonContainsFold(v string) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldContainsFold(FieldReason, v))
}

// MaintainTimeEQ applies the EQ predicate on the "maintain_time" field.
func MaintainTimeEQ(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldEQ(FieldMaintainTime, v))
}

// MaintainTimeNEQ applies the NEQ predicate on the "maintain_time" field.
func MaintainTimeNEQ(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNEQ(FieldMaintainTime, v))
}

// MaintainTimeIn applies the In predicate on the "maintain_time" field.
func MaintainTimeIn(vs ...time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIn(FieldMaintainTime, vs...))
}

// MaintainTimeNotIn applies the NotIn predicate on the "maintain_time" field.
func MaintainTimeNotIn(vs ...time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotIn(FieldMaintainTime, vs...))
}

// MaintainTimeGT applies the GT predicate on the "maintain_time" field.
func MaintainTimeGT(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGT(FieldMaintainTime, v))
}

// MaintainTimeGTE applies the GTE predicate on the "maintain_time" field.
func MaintainTimeGTE(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldGTE(FieldMaintainTime, v))
}

// MaintainTimeLT applies the LT predicate on the "maintain_time" field.
func MaintainTimeLT(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLT(FieldMaintainTime, v))
}

// MaintainTimeLTE applies the LTE predicate on the "maintain_time" field.
func MaintainTimeLTE(v time.Time) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldLTE(FieldMaintainTime, v))
}

// MaintainTimeIsNil applies the IsNil predicate on the "maintain_time" field.
func MaintainTimeIsNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldIsNull(FieldMaintainTime))
}

// MaintainTimeNotNil applies the NotNil predicate on the "maintain_time" field.
func MaintainTimeNotNil() predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.FieldNotNull(FieldMaintainTime))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.WmsMaintainOrderDetail) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.WmsMaintainOrderDetail) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.WmsMaintainOrderDetail) predicate.WmsMaintainOrderDetail {
	return predicate.WmsMaintainOrderDetail(sql.NotPredicates(p))
}
