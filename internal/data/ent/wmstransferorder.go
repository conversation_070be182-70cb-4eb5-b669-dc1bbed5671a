// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"kratos-mono-demo/internal/data/ent/wmstransferorder"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// 调拨单
type WmsTransferOrder struct {
	config `json:"-"`
	// ID of the ent.
	ID string `json:"id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 创建人
	CreatedBy string `json:"created_by,omitempty"`
	// 更新人
	UpdatedBy *string `json:"updated_by,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// 合同附件
	ContractUrls string `json:"contract_urls,omitempty"`
	// 发票附件
	InvoiceUrls string `json:"invoice_urls,omitempty"`
	// 会审凭证附件
	AuditUrls string `json:"audit_urls,omitempty"`
	// 会审凭证附件
	OtherUrls string `json:"other_urls,omitempty"`
	// 调拨单号
	OrderNo string `json:"order_no,omitempty"`
	// 装备数量
	EquipmentNum uint32 `json:"equipment_num,omitempty"`
	// 调出仓库
	FromRepositoryID string `json:"from_repository_id,omitempty"`
	// 调入仓库
	ToRepositoryID string `json:"to_repository_id,omitempty"`
	// 调拨状态
	Status string `json:"status,omitempty"`
	// 调拨时间
	TransferTime *time.Time `json:"transfer_time,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*WmsTransferOrder) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case wmstransferorder.FieldEquipmentNum:
			values[i] = new(sql.NullInt64)
		case wmstransferorder.FieldID, wmstransferorder.FieldCreatedBy, wmstransferorder.FieldUpdatedBy, wmstransferorder.FieldRemark, wmstransferorder.FieldContractUrls, wmstransferorder.FieldInvoiceUrls, wmstransferorder.FieldAuditUrls, wmstransferorder.FieldOtherUrls, wmstransferorder.FieldOrderNo, wmstransferorder.FieldFromRepositoryID, wmstransferorder.FieldToRepositoryID, wmstransferorder.FieldStatus:
			values[i] = new(sql.NullString)
		case wmstransferorder.FieldCreatedAt, wmstransferorder.FieldUpdatedAt, wmstransferorder.FieldTransferTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the WmsTransferOrder fields.
func (wto *WmsTransferOrder) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case wmstransferorder.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				wto.ID = value.String
			}
		case wmstransferorder.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				wto.CreatedAt = value.Time
			}
		case wmstransferorder.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				wto.UpdatedAt = value.Time
			}
		case wmstransferorder.FieldCreatedBy:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field created_by", values[i])
			} else if value.Valid {
				wto.CreatedBy = value.String
			}
		case wmstransferorder.FieldUpdatedBy:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field updated_by", values[i])
			} else if value.Valid {
				wto.UpdatedBy = new(string)
				*wto.UpdatedBy = value.String
			}
		case wmstransferorder.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				wto.Remark = new(string)
				*wto.Remark = value.String
			}
		case wmstransferorder.FieldContractUrls:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field contract_urls", values[i])
			} else if value.Valid {
				wto.ContractUrls = value.String
			}
		case wmstransferorder.FieldInvoiceUrls:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field invoice_urls", values[i])
			} else if value.Valid {
				wto.InvoiceUrls = value.String
			}
		case wmstransferorder.FieldAuditUrls:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field audit_urls", values[i])
			} else if value.Valid {
				wto.AuditUrls = value.String
			}
		case wmstransferorder.FieldOtherUrls:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field other_urls", values[i])
			} else if value.Valid {
				wto.OtherUrls = value.String
			}
		case wmstransferorder.FieldOrderNo:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field order_no", values[i])
			} else if value.Valid {
				wto.OrderNo = value.String
			}
		case wmstransferorder.FieldEquipmentNum:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field equipment_num", values[i])
			} else if value.Valid {
				wto.EquipmentNum = uint32(value.Int64)
			}
		case wmstransferorder.FieldFromRepositoryID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field from_repository_id", values[i])
			} else if value.Valid {
				wto.FromRepositoryID = value.String
			}
		case wmstransferorder.FieldToRepositoryID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field to_repository_id", values[i])
			} else if value.Valid {
				wto.ToRepositoryID = value.String
			}
		case wmstransferorder.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				wto.Status = value.String
			}
		case wmstransferorder.FieldTransferTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field transfer_time", values[i])
			} else if value.Valid {
				wto.TransferTime = new(time.Time)
				*wto.TransferTime = value.Time
			}
		default:
			wto.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the WmsTransferOrder.
// This includes values selected through modifiers, order, etc.
func (wto *WmsTransferOrder) Value(name string) (ent.Value, error) {
	return wto.selectValues.Get(name)
}

// Update returns a builder for updating this WmsTransferOrder.
// Note that you need to call WmsTransferOrder.Unwrap() before calling this method if this WmsTransferOrder
// was returned from a transaction, and the transaction was committed or rolled back.
func (wto *WmsTransferOrder) Update() *WmsTransferOrderUpdateOne {
	return NewWmsTransferOrderClient(wto.config).UpdateOne(wto)
}

// Unwrap unwraps the WmsTransferOrder entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (wto *WmsTransferOrder) Unwrap() *WmsTransferOrder {
	_tx, ok := wto.config.driver.(*txDriver)
	if !ok {
		panic("ent: WmsTransferOrder is not a transactional entity")
	}
	wto.config.driver = _tx.drv
	return wto
}

// String implements the fmt.Stringer.
func (wto *WmsTransferOrder) String() string {
	var builder strings.Builder
	builder.WriteString("WmsTransferOrder(")
	builder.WriteString(fmt.Sprintf("id=%v, ", wto.ID))
	builder.WriteString("created_at=")
	builder.WriteString(wto.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(wto.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("created_by=")
	builder.WriteString(wto.CreatedBy)
	builder.WriteString(", ")
	if v := wto.UpdatedBy; v != nil {
		builder.WriteString("updated_by=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := wto.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("contract_urls=")
	builder.WriteString(wto.ContractUrls)
	builder.WriteString(", ")
	builder.WriteString("invoice_urls=")
	builder.WriteString(wto.InvoiceUrls)
	builder.WriteString(", ")
	builder.WriteString("audit_urls=")
	builder.WriteString(wto.AuditUrls)
	builder.WriteString(", ")
	builder.WriteString("other_urls=")
	builder.WriteString(wto.OtherUrls)
	builder.WriteString(", ")
	builder.WriteString("order_no=")
	builder.WriteString(wto.OrderNo)
	builder.WriteString(", ")
	builder.WriteString("equipment_num=")
	builder.WriteString(fmt.Sprintf("%v", wto.EquipmentNum))
	builder.WriteString(", ")
	builder.WriteString("from_repository_id=")
	builder.WriteString(wto.FromRepositoryID)
	builder.WriteString(", ")
	builder.WriteString("to_repository_id=")
	builder.WriteString(wto.ToRepositoryID)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(wto.Status)
	builder.WriteString(", ")
	if v := wto.TransferTime; v != nil {
		builder.WriteString("transfer_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteByte(')')
	return builder.String()
}

// WmsTransferOrders is a parsable slice of WmsTransferOrder.
type WmsTransferOrders []*WmsTransferOrder
