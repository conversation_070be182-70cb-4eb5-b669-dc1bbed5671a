// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-mono-demo/internal/data/ent/predicate"
	"kratos-mono-demo/internal/data/ent/wmstransferorder"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WmsTransferOrderUpdate is the builder for updating WmsTransferOrder entities.
type WmsTransferOrderUpdate struct {
	config
	hooks     []Hook
	mutation  *WmsTransferOrderMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the WmsTransferOrderUpdate builder.
func (wtou *WmsTransferOrderUpdate) Where(ps ...predicate.WmsTransferOrder) *WmsTransferOrderUpdate {
	wtou.mutation.Where(ps...)
	return wtou
}

// SetUpdatedAt sets the "updated_at" field.
func (wtou *WmsTransferOrderUpdate) SetUpdatedAt(t time.Time) *WmsTransferOrderUpdate {
	wtou.mutation.SetUpdatedAt(t)
	return wtou
}

// SetCreatedBy sets the "created_by" field.
func (wtou *WmsTransferOrderUpdate) SetCreatedBy(s string) *WmsTransferOrderUpdate {
	wtou.mutation.SetCreatedBy(s)
	return wtou
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (wtou *WmsTransferOrderUpdate) SetNillableCreatedBy(s *string) *WmsTransferOrderUpdate {
	if s != nil {
		wtou.SetCreatedBy(*s)
	}
	return wtou
}

// ClearCreatedBy clears the value of the "created_by" field.
func (wtou *WmsTransferOrderUpdate) ClearCreatedBy() *WmsTransferOrderUpdate {
	wtou.mutation.ClearCreatedBy()
	return wtou
}

// SetUpdatedBy sets the "updated_by" field.
func (wtou *WmsTransferOrderUpdate) SetUpdatedBy(s string) *WmsTransferOrderUpdate {
	wtou.mutation.SetUpdatedBy(s)
	return wtou
}

// SetNillableUpdatedBy sets the "updated_by" field if the given value is not nil.
func (wtou *WmsTransferOrderUpdate) SetNillableUpdatedBy(s *string) *WmsTransferOrderUpdate {
	if s != nil {
		wtou.SetUpdatedBy(*s)
	}
	return wtou
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (wtou *WmsTransferOrderUpdate) ClearUpdatedBy() *WmsTransferOrderUpdate {
	wtou.mutation.ClearUpdatedBy()
	return wtou
}

// SetRemark sets the "remark" field.
func (wtou *WmsTransferOrderUpdate) SetRemark(s string) *WmsTransferOrderUpdate {
	wtou.mutation.SetRemark(s)
	return wtou
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (wtou *WmsTransferOrderUpdate) SetNillableRemark(s *string) *WmsTransferOrderUpdate {
	if s != nil {
		wtou.SetRemark(*s)
	}
	return wtou
}

// ClearRemark clears the value of the "remark" field.
func (wtou *WmsTransferOrderUpdate) ClearRemark() *WmsTransferOrderUpdate {
	wtou.mutation.ClearRemark()
	return wtou
}

// SetContractUrls sets the "contract_urls" field.
func (wtou *WmsTransferOrderUpdate) SetContractUrls(s string) *WmsTransferOrderUpdate {
	wtou.mutation.SetContractUrls(s)
	return wtou
}

// SetNillableContractUrls sets the "contract_urls" field if the given value is not nil.
func (wtou *WmsTransferOrderUpdate) SetNillableContractUrls(s *string) *WmsTransferOrderUpdate {
	if s != nil {
		wtou.SetContractUrls(*s)
	}
	return wtou
}

// ClearContractUrls clears the value of the "contract_urls" field.
func (wtou *WmsTransferOrderUpdate) ClearContractUrls() *WmsTransferOrderUpdate {
	wtou.mutation.ClearContractUrls()
	return wtou
}

// SetInvoiceUrls sets the "invoice_urls" field.
func (wtou *WmsTransferOrderUpdate) SetInvoiceUrls(s string) *WmsTransferOrderUpdate {
	wtou.mutation.SetInvoiceUrls(s)
	return wtou
}

// SetNillableInvoiceUrls sets the "invoice_urls" field if the given value is not nil.
func (wtou *WmsTransferOrderUpdate) SetNillableInvoiceUrls(s *string) *WmsTransferOrderUpdate {
	if s != nil {
		wtou.SetInvoiceUrls(*s)
	}
	return wtou
}

// ClearInvoiceUrls clears the value of the "invoice_urls" field.
func (wtou *WmsTransferOrderUpdate) ClearInvoiceUrls() *WmsTransferOrderUpdate {
	wtou.mutation.ClearInvoiceUrls()
	return wtou
}

// SetAuditUrls sets the "audit_urls" field.
func (wtou *WmsTransferOrderUpdate) SetAuditUrls(s string) *WmsTransferOrderUpdate {
	wtou.mutation.SetAuditUrls(s)
	return wtou
}

// SetNillableAuditUrls sets the "audit_urls" field if the given value is not nil.
func (wtou *WmsTransferOrderUpdate) SetNillableAuditUrls(s *string) *WmsTransferOrderUpdate {
	if s != nil {
		wtou.SetAuditUrls(*s)
	}
	return wtou
}

// ClearAuditUrls clears the value of the "audit_urls" field.
func (wtou *WmsTransferOrderUpdate) ClearAuditUrls() *WmsTransferOrderUpdate {
	wtou.mutation.ClearAuditUrls()
	return wtou
}

// SetOtherUrls sets the "other_urls" field.
func (wtou *WmsTransferOrderUpdate) SetOtherUrls(s string) *WmsTransferOrderUpdate {
	wtou.mutation.SetOtherUrls(s)
	return wtou
}

// SetNillableOtherUrls sets the "other_urls" field if the given value is not nil.
func (wtou *WmsTransferOrderUpdate) SetNillableOtherUrls(s *string) *WmsTransferOrderUpdate {
	if s != nil {
		wtou.SetOtherUrls(*s)
	}
	return wtou
}

// ClearOtherUrls clears the value of the "other_urls" field.
func (wtou *WmsTransferOrderUpdate) ClearOtherUrls() *WmsTransferOrderUpdate {
	wtou.mutation.ClearOtherUrls()
	return wtou
}

// SetOrderNo sets the "order_no" field.
func (wtou *WmsTransferOrderUpdate) SetOrderNo(s string) *WmsTransferOrderUpdate {
	wtou.mutation.SetOrderNo(s)
	return wtou
}

// SetNillableOrderNo sets the "order_no" field if the given value is not nil.
func (wtou *WmsTransferOrderUpdate) SetNillableOrderNo(s *string) *WmsTransferOrderUpdate {
	if s != nil {
		wtou.SetOrderNo(*s)
	}
	return wtou
}

// SetEquipmentNum sets the "equipment_num" field.
func (wtou *WmsTransferOrderUpdate) SetEquipmentNum(u uint32) *WmsTransferOrderUpdate {
	wtou.mutation.ResetEquipmentNum()
	wtou.mutation.SetEquipmentNum(u)
	return wtou
}

// SetNillableEquipmentNum sets the "equipment_num" field if the given value is not nil.
func (wtou *WmsTransferOrderUpdate) SetNillableEquipmentNum(u *uint32) *WmsTransferOrderUpdate {
	if u != nil {
		wtou.SetEquipmentNum(*u)
	}
	return wtou
}

// AddEquipmentNum adds u to the "equipment_num" field.
func (wtou *WmsTransferOrderUpdate) AddEquipmentNum(u int32) *WmsTransferOrderUpdate {
	wtou.mutation.AddEquipmentNum(u)
	return wtou
}

// ClearEquipmentNum clears the value of the "equipment_num" field.
func (wtou *WmsTransferOrderUpdate) ClearEquipmentNum() *WmsTransferOrderUpdate {
	wtou.mutation.ClearEquipmentNum()
	return wtou
}

// SetFromRepositoryID sets the "from_repository_id" field.
func (wtou *WmsTransferOrderUpdate) SetFromRepositoryID(s string) *WmsTransferOrderUpdate {
	wtou.mutation.SetFromRepositoryID(s)
	return wtou
}

// SetNillableFromRepositoryID sets the "from_repository_id" field if the given value is not nil.
func (wtou *WmsTransferOrderUpdate) SetNillableFromRepositoryID(s *string) *WmsTransferOrderUpdate {
	if s != nil {
		wtou.SetFromRepositoryID(*s)
	}
	return wtou
}

// ClearFromRepositoryID clears the value of the "from_repository_id" field.
func (wtou *WmsTransferOrderUpdate) ClearFromRepositoryID() *WmsTransferOrderUpdate {
	wtou.mutation.ClearFromRepositoryID()
	return wtou
}

// SetToRepositoryID sets the "to_repository_id" field.
func (wtou *WmsTransferOrderUpdate) SetToRepositoryID(s string) *WmsTransferOrderUpdate {
	wtou.mutation.SetToRepositoryID(s)
	return wtou
}

// SetNillableToRepositoryID sets the "to_repository_id" field if the given value is not nil.
func (wtou *WmsTransferOrderUpdate) SetNillableToRepositoryID(s *string) *WmsTransferOrderUpdate {
	if s != nil {
		wtou.SetToRepositoryID(*s)
	}
	return wtou
}

// ClearToRepositoryID clears the value of the "to_repository_id" field.
func (wtou *WmsTransferOrderUpdate) ClearToRepositoryID() *WmsTransferOrderUpdate {
	wtou.mutation.ClearToRepositoryID()
	return wtou
}

// SetStatus sets the "status" field.
func (wtou *WmsTransferOrderUpdate) SetStatus(s string) *WmsTransferOrderUpdate {
	wtou.mutation.SetStatus(s)
	return wtou
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (wtou *WmsTransferOrderUpdate) SetNillableStatus(s *string) *WmsTransferOrderUpdate {
	if s != nil {
		wtou.SetStatus(*s)
	}
	return wtou
}

// ClearStatus clears the value of the "status" field.
func (wtou *WmsTransferOrderUpdate) ClearStatus() *WmsTransferOrderUpdate {
	wtou.mutation.ClearStatus()
	return wtou
}

// SetTransferTime sets the "transfer_time" field.
func (wtou *WmsTransferOrderUpdate) SetTransferTime(t time.Time) *WmsTransferOrderUpdate {
	wtou.mutation.SetTransferTime(t)
	return wtou
}

// SetNillableTransferTime sets the "transfer_time" field if the given value is not nil.
func (wtou *WmsTransferOrderUpdate) SetNillableTransferTime(t *time.Time) *WmsTransferOrderUpdate {
	if t != nil {
		wtou.SetTransferTime(*t)
	}
	return wtou
}

// ClearTransferTime clears the value of the "transfer_time" field.
func (wtou *WmsTransferOrderUpdate) ClearTransferTime() *WmsTransferOrderUpdate {
	wtou.mutation.ClearTransferTime()
	return wtou
}

// Mutation returns the WmsTransferOrderMutation object of the builder.
func (wtou *WmsTransferOrderUpdate) Mutation() *WmsTransferOrderMutation {
	return wtou.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (wtou *WmsTransferOrderUpdate) Save(ctx context.Context) (int, error) {
	wtou.defaults()
	return withHooks(ctx, wtou.sqlSave, wtou.mutation, wtou.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (wtou *WmsTransferOrderUpdate) SaveX(ctx context.Context) int {
	affected, err := wtou.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (wtou *WmsTransferOrderUpdate) Exec(ctx context.Context) error {
	_, err := wtou.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wtou *WmsTransferOrderUpdate) ExecX(ctx context.Context) {
	if err := wtou.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wtou *WmsTransferOrderUpdate) defaults() {
	if _, ok := wtou.mutation.UpdatedAt(); !ok {
		v := wmstransferorder.UpdateDefaultUpdatedAt()
		wtou.mutation.SetUpdatedAt(v)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (wtou *WmsTransferOrderUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *WmsTransferOrderUpdate {
	wtou.modifiers = append(wtou.modifiers, modifiers...)
	return wtou
}

func (wtou *WmsTransferOrderUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(wmstransferorder.Table, wmstransferorder.Columns, sqlgraph.NewFieldSpec(wmstransferorder.FieldID, field.TypeString))
	if ps := wtou.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := wtou.mutation.UpdatedAt(); ok {
		_spec.SetField(wmstransferorder.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := wtou.mutation.CreatedBy(); ok {
		_spec.SetField(wmstransferorder.FieldCreatedBy, field.TypeString, value)
	}
	if wtou.mutation.CreatedByCleared() {
		_spec.ClearField(wmstransferorder.FieldCreatedBy, field.TypeString)
	}
	if value, ok := wtou.mutation.UpdatedBy(); ok {
		_spec.SetField(wmstransferorder.FieldUpdatedBy, field.TypeString, value)
	}
	if wtou.mutation.UpdatedByCleared() {
		_spec.ClearField(wmstransferorder.FieldUpdatedBy, field.TypeString)
	}
	if value, ok := wtou.mutation.Remark(); ok {
		_spec.SetField(wmstransferorder.FieldRemark, field.TypeString, value)
	}
	if wtou.mutation.RemarkCleared() {
		_spec.ClearField(wmstransferorder.FieldRemark, field.TypeString)
	}
	if value, ok := wtou.mutation.ContractUrls(); ok {
		_spec.SetField(wmstransferorder.FieldContractUrls, field.TypeString, value)
	}
	if wtou.mutation.ContractUrlsCleared() {
		_spec.ClearField(wmstransferorder.FieldContractUrls, field.TypeString)
	}
	if value, ok := wtou.mutation.InvoiceUrls(); ok {
		_spec.SetField(wmstransferorder.FieldInvoiceUrls, field.TypeString, value)
	}
	if wtou.mutation.InvoiceUrlsCleared() {
		_spec.ClearField(wmstransferorder.FieldInvoiceUrls, field.TypeString)
	}
	if value, ok := wtou.mutation.AuditUrls(); ok {
		_spec.SetField(wmstransferorder.FieldAuditUrls, field.TypeString, value)
	}
	if wtou.mutation.AuditUrlsCleared() {
		_spec.ClearField(wmstransferorder.FieldAuditUrls, field.TypeString)
	}
	if value, ok := wtou.mutation.OtherUrls(); ok {
		_spec.SetField(wmstransferorder.FieldOtherUrls, field.TypeString, value)
	}
	if wtou.mutation.OtherUrlsCleared() {
		_spec.ClearField(wmstransferorder.FieldOtherUrls, field.TypeString)
	}
	if value, ok := wtou.mutation.OrderNo(); ok {
		_spec.SetField(wmstransferorder.FieldOrderNo, field.TypeString, value)
	}
	if value, ok := wtou.mutation.EquipmentNum(); ok {
		_spec.SetField(wmstransferorder.FieldEquipmentNum, field.TypeUint32, value)
	}
	if value, ok := wtou.mutation.AddedEquipmentNum(); ok {
		_spec.AddField(wmstransferorder.FieldEquipmentNum, field.TypeUint32, value)
	}
	if wtou.mutation.EquipmentNumCleared() {
		_spec.ClearField(wmstransferorder.FieldEquipmentNum, field.TypeUint32)
	}
	if value, ok := wtou.mutation.FromRepositoryID(); ok {
		_spec.SetField(wmstransferorder.FieldFromRepositoryID, field.TypeString, value)
	}
	if wtou.mutation.FromRepositoryIDCleared() {
		_spec.ClearField(wmstransferorder.FieldFromRepositoryID, field.TypeString)
	}
	if value, ok := wtou.mutation.ToRepositoryID(); ok {
		_spec.SetField(wmstransferorder.FieldToRepositoryID, field.TypeString, value)
	}
	if wtou.mutation.ToRepositoryIDCleared() {
		_spec.ClearField(wmstransferorder.FieldToRepositoryID, field.TypeString)
	}
	if value, ok := wtou.mutation.Status(); ok {
		_spec.SetField(wmstransferorder.FieldStatus, field.TypeString, value)
	}
	if wtou.mutation.StatusCleared() {
		_spec.ClearField(wmstransferorder.FieldStatus, field.TypeString)
	}
	if value, ok := wtou.mutation.TransferTime(); ok {
		_spec.SetField(wmstransferorder.FieldTransferTime, field.TypeTime, value)
	}
	if wtou.mutation.TransferTimeCleared() {
		_spec.ClearField(wmstransferorder.FieldTransferTime, field.TypeTime)
	}
	_spec.AddModifiers(wtou.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, wtou.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{wmstransferorder.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	wtou.mutation.done = true
	return n, nil
}

// WmsTransferOrderUpdateOne is the builder for updating a single WmsTransferOrder entity.
type WmsTransferOrderUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *WmsTransferOrderMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdatedAt sets the "updated_at" field.
func (wtouo *WmsTransferOrderUpdateOne) SetUpdatedAt(t time.Time) *WmsTransferOrderUpdateOne {
	wtouo.mutation.SetUpdatedAt(t)
	return wtouo
}

// SetCreatedBy sets the "created_by" field.
func (wtouo *WmsTransferOrderUpdateOne) SetCreatedBy(s string) *WmsTransferOrderUpdateOne {
	wtouo.mutation.SetCreatedBy(s)
	return wtouo
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (wtouo *WmsTransferOrderUpdateOne) SetNillableCreatedBy(s *string) *WmsTransferOrderUpdateOne {
	if s != nil {
		wtouo.SetCreatedBy(*s)
	}
	return wtouo
}

// ClearCreatedBy clears the value of the "created_by" field.
func (wtouo *WmsTransferOrderUpdateOne) ClearCreatedBy() *WmsTransferOrderUpdateOne {
	wtouo.mutation.ClearCreatedBy()
	return wtouo
}

// SetUpdatedBy sets the "updated_by" field.
func (wtouo *WmsTransferOrderUpdateOne) SetUpdatedBy(s string) *WmsTransferOrderUpdateOne {
	wtouo.mutation.SetUpdatedBy(s)
	return wtouo
}

// SetNillableUpdatedBy sets the "updated_by" field if the given value is not nil.
func (wtouo *WmsTransferOrderUpdateOne) SetNillableUpdatedBy(s *string) *WmsTransferOrderUpdateOne {
	if s != nil {
		wtouo.SetUpdatedBy(*s)
	}
	return wtouo
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (wtouo *WmsTransferOrderUpdateOne) ClearUpdatedBy() *WmsTransferOrderUpdateOne {
	wtouo.mutation.ClearUpdatedBy()
	return wtouo
}

// SetRemark sets the "remark" field.
func (wtouo *WmsTransferOrderUpdateOne) SetRemark(s string) *WmsTransferOrderUpdateOne {
	wtouo.mutation.SetRemark(s)
	return wtouo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (wtouo *WmsTransferOrderUpdateOne) SetNillableRemark(s *string) *WmsTransferOrderUpdateOne {
	if s != nil {
		wtouo.SetRemark(*s)
	}
	return wtouo
}

// ClearRemark clears the value of the "remark" field.
func (wtouo *WmsTransferOrderUpdateOne) ClearRemark() *WmsTransferOrderUpdateOne {
	wtouo.mutation.ClearRemark()
	return wtouo
}

// SetContractUrls sets the "contract_urls" field.
func (wtouo *WmsTransferOrderUpdateOne) SetContractUrls(s string) *WmsTransferOrderUpdateOne {
	wtouo.mutation.SetContractUrls(s)
	return wtouo
}

// SetNillableContractUrls sets the "contract_urls" field if the given value is not nil.
func (wtouo *WmsTransferOrderUpdateOne) SetNillableContractUrls(s *string) *WmsTransferOrderUpdateOne {
	if s != nil {
		wtouo.SetContractUrls(*s)
	}
	return wtouo
}

// ClearContractUrls clears the value of the "contract_urls" field.
func (wtouo *WmsTransferOrderUpdateOne) ClearContractUrls() *WmsTransferOrderUpdateOne {
	wtouo.mutation.ClearContractUrls()
	return wtouo
}

// SetInvoiceUrls sets the "invoice_urls" field.
func (wtouo *WmsTransferOrderUpdateOne) SetInvoiceUrls(s string) *WmsTransferOrderUpdateOne {
	wtouo.mutation.SetInvoiceUrls(s)
	return wtouo
}

// SetNillableInvoiceUrls sets the "invoice_urls" field if the given value is not nil.
func (wtouo *WmsTransferOrderUpdateOne) SetNillableInvoiceUrls(s *string) *WmsTransferOrderUpdateOne {
	if s != nil {
		wtouo.SetInvoiceUrls(*s)
	}
	return wtouo
}

// ClearInvoiceUrls clears the value of the "invoice_urls" field.
func (wtouo *WmsTransferOrderUpdateOne) ClearInvoiceUrls() *WmsTransferOrderUpdateOne {
	wtouo.mutation.ClearInvoiceUrls()
	return wtouo
}

// SetAuditUrls sets the "audit_urls" field.
func (wtouo *WmsTransferOrderUpdateOne) SetAuditUrls(s string) *WmsTransferOrderUpdateOne {
	wtouo.mutation.SetAuditUrls(s)
	return wtouo
}

// SetNillableAuditUrls sets the "audit_urls" field if the given value is not nil.
func (wtouo *WmsTransferOrderUpdateOne) SetNillableAuditUrls(s *string) *WmsTransferOrderUpdateOne {
	if s != nil {
		wtouo.SetAuditUrls(*s)
	}
	return wtouo
}

// ClearAuditUrls clears the value of the "audit_urls" field.
func (wtouo *WmsTransferOrderUpdateOne) ClearAuditUrls() *WmsTransferOrderUpdateOne {
	wtouo.mutation.ClearAuditUrls()
	return wtouo
}

// SetOtherUrls sets the "other_urls" field.
func (wtouo *WmsTransferOrderUpdateOne) SetOtherUrls(s string) *WmsTransferOrderUpdateOne {
	wtouo.mutation.SetOtherUrls(s)
	return wtouo
}

// SetNillableOtherUrls sets the "other_urls" field if the given value is not nil.
func (wtouo *WmsTransferOrderUpdateOne) SetNillableOtherUrls(s *string) *WmsTransferOrderUpdateOne {
	if s != nil {
		wtouo.SetOtherUrls(*s)
	}
	return wtouo
}

// ClearOtherUrls clears the value of the "other_urls" field.
func (wtouo *WmsTransferOrderUpdateOne) ClearOtherUrls() *WmsTransferOrderUpdateOne {
	wtouo.mutation.ClearOtherUrls()
	return wtouo
}

// SetOrderNo sets the "order_no" field.
func (wtouo *WmsTransferOrderUpdateOne) SetOrderNo(s string) *WmsTransferOrderUpdateOne {
	wtouo.mutation.SetOrderNo(s)
	return wtouo
}

// SetNillableOrderNo sets the "order_no" field if the given value is not nil.
func (wtouo *WmsTransferOrderUpdateOne) SetNillableOrderNo(s *string) *WmsTransferOrderUpdateOne {
	if s != nil {
		wtouo.SetOrderNo(*s)
	}
	return wtouo
}

// SetEquipmentNum sets the "equipment_num" field.
func (wtouo *WmsTransferOrderUpdateOne) SetEquipmentNum(u uint32) *WmsTransferOrderUpdateOne {
	wtouo.mutation.ResetEquipmentNum()
	wtouo.mutation.SetEquipmentNum(u)
	return wtouo
}

// SetNillableEquipmentNum sets the "equipment_num" field if the given value is not nil.
func (wtouo *WmsTransferOrderUpdateOne) SetNillableEquipmentNum(u *uint32) *WmsTransferOrderUpdateOne {
	if u != nil {
		wtouo.SetEquipmentNum(*u)
	}
	return wtouo
}

// AddEquipmentNum adds u to the "equipment_num" field.
func (wtouo *WmsTransferOrderUpdateOne) AddEquipmentNum(u int32) *WmsTransferOrderUpdateOne {
	wtouo.mutation.AddEquipmentNum(u)
	return wtouo
}

// ClearEquipmentNum clears the value of the "equipment_num" field.
func (wtouo *WmsTransferOrderUpdateOne) ClearEquipmentNum() *WmsTransferOrderUpdateOne {
	wtouo.mutation.ClearEquipmentNum()
	return wtouo
}

// SetFromRepositoryID sets the "from_repository_id" field.
func (wtouo *WmsTransferOrderUpdateOne) SetFromRepositoryID(s string) *WmsTransferOrderUpdateOne {
	wtouo.mutation.SetFromRepositoryID(s)
	return wtouo
}

// SetNillableFromRepositoryID sets the "from_repository_id" field if the given value is not nil.
func (wtouo *WmsTransferOrderUpdateOne) SetNillableFromRepositoryID(s *string) *WmsTransferOrderUpdateOne {
	if s != nil {
		wtouo.SetFromRepositoryID(*s)
	}
	return wtouo
}

// ClearFromRepositoryID clears the value of the "from_repository_id" field.
func (wtouo *WmsTransferOrderUpdateOne) ClearFromRepositoryID() *WmsTransferOrderUpdateOne {
	wtouo.mutation.ClearFromRepositoryID()
	return wtouo
}

// SetToRepositoryID sets the "to_repository_id" field.
func (wtouo *WmsTransferOrderUpdateOne) SetToRepositoryID(s string) *WmsTransferOrderUpdateOne {
	wtouo.mutation.SetToRepositoryID(s)
	return wtouo
}

// SetNillableToRepositoryID sets the "to_repository_id" field if the given value is not nil.
func (wtouo *WmsTransferOrderUpdateOne) SetNillableToRepositoryID(s *string) *WmsTransferOrderUpdateOne {
	if s != nil {
		wtouo.SetToRepositoryID(*s)
	}
	return wtouo
}

// ClearToRepositoryID clears the value of the "to_repository_id" field.
func (wtouo *WmsTransferOrderUpdateOne) ClearToRepositoryID() *WmsTransferOrderUpdateOne {
	wtouo.mutation.ClearToRepositoryID()
	return wtouo
}

// SetStatus sets the "status" field.
func (wtouo *WmsTransferOrderUpdateOne) SetStatus(s string) *WmsTransferOrderUpdateOne {
	wtouo.mutation.SetStatus(s)
	return wtouo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (wtouo *WmsTransferOrderUpdateOne) SetNillableStatus(s *string) *WmsTransferOrderUpdateOne {
	if s != nil {
		wtouo.SetStatus(*s)
	}
	return wtouo
}

// ClearStatus clears the value of the "status" field.
func (wtouo *WmsTransferOrderUpdateOne) ClearStatus() *WmsTransferOrderUpdateOne {
	wtouo.mutation.ClearStatus()
	return wtouo
}

// SetTransferTime sets the "transfer_time" field.
func (wtouo *WmsTransferOrderUpdateOne) SetTransferTime(t time.Time) *WmsTransferOrderUpdateOne {
	wtouo.mutation.SetTransferTime(t)
	return wtouo
}

// SetNillableTransferTime sets the "transfer_time" field if the given value is not nil.
func (wtouo *WmsTransferOrderUpdateOne) SetNillableTransferTime(t *time.Time) *WmsTransferOrderUpdateOne {
	if t != nil {
		wtouo.SetTransferTime(*t)
	}
	return wtouo
}

// ClearTransferTime clears the value of the "transfer_time" field.
func (wtouo *WmsTransferOrderUpdateOne) ClearTransferTime() *WmsTransferOrderUpdateOne {
	wtouo.mutation.ClearTransferTime()
	return wtouo
}

// Mutation returns the WmsTransferOrderMutation object of the builder.
func (wtouo *WmsTransferOrderUpdateOne) Mutation() *WmsTransferOrderMutation {
	return wtouo.mutation
}

// Where appends a list predicates to the WmsTransferOrderUpdate builder.
func (wtouo *WmsTransferOrderUpdateOne) Where(ps ...predicate.WmsTransferOrder) *WmsTransferOrderUpdateOne {
	wtouo.mutation.Where(ps...)
	return wtouo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (wtouo *WmsTransferOrderUpdateOne) Select(field string, fields ...string) *WmsTransferOrderUpdateOne {
	wtouo.fields = append([]string{field}, fields...)
	return wtouo
}

// Save executes the query and returns the updated WmsTransferOrder entity.
func (wtouo *WmsTransferOrderUpdateOne) Save(ctx context.Context) (*WmsTransferOrder, error) {
	wtouo.defaults()
	return withHooks(ctx, wtouo.sqlSave, wtouo.mutation, wtouo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (wtouo *WmsTransferOrderUpdateOne) SaveX(ctx context.Context) *WmsTransferOrder {
	node, err := wtouo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (wtouo *WmsTransferOrderUpdateOne) Exec(ctx context.Context) error {
	_, err := wtouo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wtouo *WmsTransferOrderUpdateOne) ExecX(ctx context.Context) {
	if err := wtouo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wtouo *WmsTransferOrderUpdateOne) defaults() {
	if _, ok := wtouo.mutation.UpdatedAt(); !ok {
		v := wmstransferorder.UpdateDefaultUpdatedAt()
		wtouo.mutation.SetUpdatedAt(v)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (wtouo *WmsTransferOrderUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *WmsTransferOrderUpdateOne {
	wtouo.modifiers = append(wtouo.modifiers, modifiers...)
	return wtouo
}

func (wtouo *WmsTransferOrderUpdateOne) sqlSave(ctx context.Context) (_node *WmsTransferOrder, err error) {
	_spec := sqlgraph.NewUpdateSpec(wmstransferorder.Table, wmstransferorder.Columns, sqlgraph.NewFieldSpec(wmstransferorder.FieldID, field.TypeString))
	id, ok := wtouo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "WmsTransferOrder.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := wtouo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, wmstransferorder.FieldID)
		for _, f := range fields {
			if !wmstransferorder.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != wmstransferorder.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := wtouo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := wtouo.mutation.UpdatedAt(); ok {
		_spec.SetField(wmstransferorder.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := wtouo.mutation.CreatedBy(); ok {
		_spec.SetField(wmstransferorder.FieldCreatedBy, field.TypeString, value)
	}
	if wtouo.mutation.CreatedByCleared() {
		_spec.ClearField(wmstransferorder.FieldCreatedBy, field.TypeString)
	}
	if value, ok := wtouo.mutation.UpdatedBy(); ok {
		_spec.SetField(wmstransferorder.FieldUpdatedBy, field.TypeString, value)
	}
	if wtouo.mutation.UpdatedByCleared() {
		_spec.ClearField(wmstransferorder.FieldUpdatedBy, field.TypeString)
	}
	if value, ok := wtouo.mutation.Remark(); ok {
		_spec.SetField(wmstransferorder.FieldRemark, field.TypeString, value)
	}
	if wtouo.mutation.RemarkCleared() {
		_spec.ClearField(wmstransferorder.FieldRemark, field.TypeString)
	}
	if value, ok := wtouo.mutation.ContractUrls(); ok {
		_spec.SetField(wmstransferorder.FieldContractUrls, field.TypeString, value)
	}
	if wtouo.mutation.ContractUrlsCleared() {
		_spec.ClearField(wmstransferorder.FieldContractUrls, field.TypeString)
	}
	if value, ok := wtouo.mutation.InvoiceUrls(); ok {
		_spec.SetField(wmstransferorder.FieldInvoiceUrls, field.TypeString, value)
	}
	if wtouo.mutation.InvoiceUrlsCleared() {
		_spec.ClearField(wmstransferorder.FieldInvoiceUrls, field.TypeString)
	}
	if value, ok := wtouo.mutation.AuditUrls(); ok {
		_spec.SetField(wmstransferorder.FieldAuditUrls, field.TypeString, value)
	}
	if wtouo.mutation.AuditUrlsCleared() {
		_spec.ClearField(wmstransferorder.FieldAuditUrls, field.TypeString)
	}
	if value, ok := wtouo.mutation.OtherUrls(); ok {
		_spec.SetField(wmstransferorder.FieldOtherUrls, field.TypeString, value)
	}
	if wtouo.mutation.OtherUrlsCleared() {
		_spec.ClearField(wmstransferorder.FieldOtherUrls, field.TypeString)
	}
	if value, ok := wtouo.mutation.OrderNo(); ok {
		_spec.SetField(wmstransferorder.FieldOrderNo, field.TypeString, value)
	}
	if value, ok := wtouo.mutation.EquipmentNum(); ok {
		_spec.SetField(wmstransferorder.FieldEquipmentNum, field.TypeUint32, value)
	}
	if value, ok := wtouo.mutation.AddedEquipmentNum(); ok {
		_spec.AddField(wmstransferorder.FieldEquipmentNum, field.TypeUint32, value)
	}
	if wtouo.mutation.EquipmentNumCleared() {
		_spec.ClearField(wmstransferorder.FieldEquipmentNum, field.TypeUint32)
	}
	if value, ok := wtouo.mutation.FromRepositoryID(); ok {
		_spec.SetField(wmstransferorder.FieldFromRepositoryID, field.TypeString, value)
	}
	if wtouo.mutation.FromRepositoryIDCleared() {
		_spec.ClearField(wmstransferorder.FieldFromRepositoryID, field.TypeString)
	}
	if value, ok := wtouo.mutation.ToRepositoryID(); ok {
		_spec.SetField(wmstransferorder.FieldToRepositoryID, field.TypeString, value)
	}
	if wtouo.mutation.ToRepositoryIDCleared() {
		_spec.ClearField(wmstransferorder.FieldToRepositoryID, field.TypeString)
	}
	if value, ok := wtouo.mutation.Status(); ok {
		_spec.SetField(wmstransferorder.FieldStatus, field.TypeString, value)
	}
	if wtouo.mutation.StatusCleared() {
		_spec.ClearField(wmstransferorder.FieldStatus, field.TypeString)
	}
	if value, ok := wtouo.mutation.TransferTime(); ok {
		_spec.SetField(wmstransferorder.FieldTransferTime, field.TypeTime, value)
	}
	if wtouo.mutation.TransferTimeCleared() {
		_spec.ClearField(wmstransferorder.FieldTransferTime, field.TypeTime)
	}
	_spec.AddModifiers(wtouo.modifiers...)
	_node = &WmsTransferOrder{config: wtouo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, wtouo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{wmstransferorder.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	wtouo.mutation.done = true
	return _node, nil
}
