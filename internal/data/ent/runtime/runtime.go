// Code generated by ent, DO NOT EDIT.

package runtime

import (
	"kratos-mono-demo/internal/data/ent/appapproveactivity"
	"kratos-mono-demo/internal/data/ent/appapprovebasic"
	"kratos-mono-demo/internal/data/ent/appapprovegroup"
	"kratos-mono-demo/internal/data/ent/appapproverecord"
	"kratos-mono-demo/internal/data/ent/appapproverecordlog"
	"kratos-mono-demo/internal/data/ent/appapproverecordqueue"
	"kratos-mono-demo/internal/data/ent/appapproveworkflow"
	"kratos-mono-demo/internal/data/ent/demobuilding"
	"kratos-mono-demo/internal/data/ent/sysapplication"
	"kratos-mono-demo/internal/data/ent/sysapplicationmodule"
	"kratos-mono-demo/internal/data/ent/sysapplicationmoduleresource"
	"kratos-mono-demo/internal/data/ent/sysarea"
	"kratos-mono-demo/internal/data/ent/syscity"
	"kratos-mono-demo/internal/data/ent/sysconfig"
	"kratos-mono-demo/internal/data/ent/sysdictionary"
	"kratos-mono-demo/internal/data/ent/sysdictionarydetail"
	"kratos-mono-demo/internal/data/ent/sysgame"
	"kratos-mono-demo/internal/data/ent/syslabel"
	"kratos-mono-demo/internal/data/ent/syslog"
	"kratos-mono-demo/internal/data/ent/sysmenu"
	"kratos-mono-demo/internal/data/ent/sysmessagetemplate"
	"kratos-mono-demo/internal/data/ent/sysmodule"
	"kratos-mono-demo/internal/data/ent/sysmoduleresource"
	"kratos-mono-demo/internal/data/ent/sysorganization"
	"kratos-mono-demo/internal/data/ent/syspagecode"
	"kratos-mono-demo/internal/data/ent/syspagecodehistory"
	"kratos-mono-demo/internal/data/ent/sysproject"
	"kratos-mono-demo/internal/data/ent/sysprovince"
	"kratos-mono-demo/internal/data/ent/sysresource"
	"kratos-mono-demo/internal/data/ent/sysrole"
	"kratos-mono-demo/internal/data/ent/sysrolemenu"
	"kratos-mono-demo/internal/data/ent/sysroleresource"
	"kratos-mono-demo/internal/data/ent/sysstreet"
	"kratos-mono-demo/internal/data/ent/systeam"
	"kratos-mono-demo/internal/data/ent/systeamapplication"
	"kratos-mono-demo/internal/data/ent/sysupload"
	"kratos-mono-demo/internal/data/ent/sysuser"
	"kratos-mono-demo/internal/data/ent/sysuserauth"
	"kratos-mono-demo/internal/data/ent/sysuserlog"
	"kratos-mono-demo/internal/data/ent/sysuserorganization"
	"kratos-mono-demo/internal/data/ent/sysuserrole"
	"kratos-mono-demo/internal/data/ent/wmsaccessdoorlog"
	"kratos-mono-demo/internal/data/ent/wmsapprovaltask"
	"kratos-mono-demo/internal/data/ent/wmsapprovaltaskdetail"
	"kratos-mono-demo/internal/data/ent/wmsapprovaltaskoperation"
	"kratos-mono-demo/internal/data/ent/wmsapprovaltaskremark"
	"kratos-mono-demo/internal/data/ent/wmsauditplan"
	"kratos-mono-demo/internal/data/ent/wmsauditplandetail"
	"kratos-mono-demo/internal/data/ent/wmsborroworder"
	"kratos-mono-demo/internal/data/ent/wmsborroworderdetail"
	"kratos-mono-demo/internal/data/ent/wmscar"
	"kratos-mono-demo/internal/data/ent/wmsclaimorder"
	"kratos-mono-demo/internal/data/ent/wmsclaimorderdetail"
	"kratos-mono-demo/internal/data/ent/wmscompany"
	"kratos-mono-demo/internal/data/ent/wmscompanyaddress"
	"kratos-mono-demo/internal/data/ent/wmsdiscardmeeting"
	"kratos-mono-demo/internal/data/ent/wmsdiscardmeetingdetail"
	"kratos-mono-demo/internal/data/ent/wmsdiscardorder"
	"kratos-mono-demo/internal/data/ent/wmsdiscardorderdetail"
	"kratos-mono-demo/internal/data/ent/wmsdiscardplanorder"
	"kratos-mono-demo/internal/data/ent/wmsdiscardplanorderdetail"
	"kratos-mono-demo/internal/data/ent/wmsdocument"
	"kratos-mono-demo/internal/data/ent/wmsenterrepositoryorder"
	"kratos-mono-demo/internal/data/ent/wmsenterrepositoryorderdetail"
	"kratos-mono-demo/internal/data/ent/wmsequipment"
	"kratos-mono-demo/internal/data/ent/wmsequipmentdetail"
	"kratos-mono-demo/internal/data/ent/wmsequipmenttype"
	"kratos-mono-demo/internal/data/ent/wmsequipmenttypeproperty"
	"kratos-mono-demo/internal/data/ent/wmsequipmenttypepropertygroup"
	"kratos-mono-demo/internal/data/ent/wmsequipmenttypepropertyoption"
	"kratos-mono-demo/internal/data/ent/wmsequipmentusergroup"
	"kratos-mono-demo/internal/data/ent/wmsfirestation"
	"kratos-mono-demo/internal/data/ent/wmsgps"
	"kratos-mono-demo/internal/data/ent/wmsgpsrecord"
	"kratos-mono-demo/internal/data/ent/wmslearningcourse"
	"kratos-mono-demo/internal/data/ent/wmslearningcoursecourseware"
	"kratos-mono-demo/internal/data/ent/wmslearningcourselog"
	"kratos-mono-demo/internal/data/ent/wmslearningcourserecord"
	"kratos-mono-demo/internal/data/ent/wmslearningcourseware"
	"kratos-mono-demo/internal/data/ent/wmslearningcoursewarerecord"
	"kratos-mono-demo/internal/data/ent/wmslearningplan"
	"kratos-mono-demo/internal/data/ent/wmslearningplanrecord"
	"kratos-mono-demo/internal/data/ent/wmsmaintainorder"
	"kratos-mono-demo/internal/data/ent/wmsmaintainorderdetail"
	"kratos-mono-demo/internal/data/ent/wmsmaintainplan"
	"kratos-mono-demo/internal/data/ent/wmsmaintainplandetail"
	"kratos-mono-demo/internal/data/ent/wmsmaterial"
	"kratos-mono-demo/internal/data/ent/wmsmateriallog"
	"kratos-mono-demo/internal/data/ent/wmsmeasureunit"
	"kratos-mono-demo/internal/data/ent/wmsoperatelog"
	"kratos-mono-demo/internal/data/ent/wmsoutrepositoryorder"
	"kratos-mono-demo/internal/data/ent/wmsoutrepositoryorderdetail"
	"kratos-mono-demo/internal/data/ent/wmspurchaseorder"
	"kratos-mono-demo/internal/data/ent/wmspurchaseorderdetail"
	"kratos-mono-demo/internal/data/ent/wmsrepairorder"
	"kratos-mono-demo/internal/data/ent/wmsrepairorderdetail"
	"kratos-mono-demo/internal/data/ent/wmsrepairsettlementorder"
	"kratos-mono-demo/internal/data/ent/wmsrepairsettlementordersettlementfeedetail"
	"kratos-mono-demo/internal/data/ent/wmsrepairsettlementorderworkfeedetail"
	"kratos-mono-demo/internal/data/ent/wmsrepository"
	"kratos-mono-demo/internal/data/ent/wmsrepositoryadmin"
	"kratos-mono-demo/internal/data/ent/wmsrepositoryarea"
	"kratos-mono-demo/internal/data/ent/wmsrepositoryposition"
	"kratos-mono-demo/internal/data/ent/wmsrepositoryscreen"
	"kratos-mono-demo/internal/data/ent/wmsrepositoryscreenrepositoryarea"
	"kratos-mono-demo/internal/data/ent/wmsreturnorder"
	"kratos-mono-demo/internal/data/ent/wmsreturnorderdetail"
	"kratos-mono-demo/internal/data/ent/wmsrfid"
	"kratos-mono-demo/internal/data/ent/wmsrfidreader"
	"kratos-mono-demo/internal/data/ent/wmsrfidreaderrecord"
	"kratos-mono-demo/internal/data/ent/wmstransferorder"
	"kratos-mono-demo/internal/data/ent/wmstransferorderdetail"
	"kratos-mono-demo/internal/data/ent/wmsvehiclerepairorder"
	"kratos-mono-demo/internal/data/ent/wmsvehiclerepairordermaterialfeedetail"
	"kratos-mono-demo/internal/data/ent/wmsvehiclerepairordersettlementfeedetail"
	"kratos-mono-demo/internal/data/ent/wmsvehiclerepairorderworkfeedetail"
	"kratos-mono-demo/internal/data/ent/workwxapprovalmessage"
	"kratos-mono-demo/internal/data/ent/workwxapprovalnode"
	"kratos-mono-demo/internal/data/ent/workwxnotifynode"
	"kratos-mono-demo/schematype"
	"time"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	appapproveactivityMixin := schematype.AppApproveActivity{}.Mixin()
	appapproveactivityMixinFields0 := appapproveactivityMixin[0].Fields()
	_ = appapproveactivityMixinFields0
	appapproveactivityMixinFields1 := appapproveactivityMixin[1].Fields()
	_ = appapproveactivityMixinFields1
	appapproveactivityFields := schematype.AppApproveActivity{}.Fields()
	_ = appapproveactivityFields
	// appapproveactivityDescCreatedAt is the schema descriptor for created_at field.
	appapproveactivityDescCreatedAt := appapproveactivityMixinFields0[1].Descriptor()
	// appapproveactivity.DefaultCreatedAt holds the default value on creation for the created_at field.
	appapproveactivity.DefaultCreatedAt = appapproveactivityDescCreatedAt.Default.(time.Time)
	// appapproveactivityDescUpdatedAt is the schema descriptor for updated_at field.
	appapproveactivityDescUpdatedAt := appapproveactivityMixinFields0[2].Descriptor()
	// appapproveactivity.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	appapproveactivity.DefaultUpdatedAt = appapproveactivityDescUpdatedAt.Default.(func() time.Time)
	// appapproveactivity.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	appapproveactivity.UpdateDefaultUpdatedAt = appapproveactivityDescUpdatedAt.UpdateDefault.(func() time.Time)
	// appapproveactivityDescStatus is the schema descriptor for status field.
	appapproveactivityDescStatus := appapproveactivityMixinFields1[0].Descriptor()
	// appapproveactivity.DefaultStatus holds the default value on creation for the status field.
	appapproveactivity.DefaultStatus = appapproveactivityDescStatus.Default.(int32)
	// appapproveactivityDescID is the schema descriptor for id field.
	appapproveactivityDescID := appapproveactivityMixinFields0[0].Descriptor()
	// appapproveactivity.DefaultID holds the default value on creation for the id field.
	appapproveactivity.DefaultID = appapproveactivityDescID.Default.(func() string)
	appapprovebasicMixin := schematype.AppApproveBasic{}.Mixin()
	appapprovebasicMixinFields0 := appapprovebasicMixin[0].Fields()
	_ = appapprovebasicMixinFields0
	appapprovebasicMixinFields1 := appapprovebasicMixin[1].Fields()
	_ = appapprovebasicMixinFields1
	appapprovebasicFields := schematype.AppApproveBasic{}.Fields()
	_ = appapprovebasicFields
	// appapprovebasicDescCreatedAt is the schema descriptor for created_at field.
	appapprovebasicDescCreatedAt := appapprovebasicMixinFields0[1].Descriptor()
	// appapprovebasic.DefaultCreatedAt holds the default value on creation for the created_at field.
	appapprovebasic.DefaultCreatedAt = appapprovebasicDescCreatedAt.Default.(time.Time)
	// appapprovebasicDescUpdatedAt is the schema descriptor for updated_at field.
	appapprovebasicDescUpdatedAt := appapprovebasicMixinFields0[2].Descriptor()
	// appapprovebasic.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	appapprovebasic.DefaultUpdatedAt = appapprovebasicDescUpdatedAt.Default.(func() time.Time)
	// appapprovebasic.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	appapprovebasic.UpdateDefaultUpdatedAt = appapprovebasicDescUpdatedAt.UpdateDefault.(func() time.Time)
	// appapprovebasicDescStatus is the schema descriptor for status field.
	appapprovebasicDescStatus := appapprovebasicMixinFields1[0].Descriptor()
	// appapprovebasic.DefaultStatus holds the default value on creation for the status field.
	appapprovebasic.DefaultStatus = appapprovebasicDescStatus.Default.(int32)
	// appapprovebasicDescID is the schema descriptor for id field.
	appapprovebasicDescID := appapprovebasicMixinFields0[0].Descriptor()
	// appapprovebasic.DefaultID holds the default value on creation for the id field.
	appapprovebasic.DefaultID = appapprovebasicDescID.Default.(func() string)
	appapprovegroupMixin := schematype.AppApproveGroup{}.Mixin()
	appapprovegroupMixinFields0 := appapprovegroupMixin[0].Fields()
	_ = appapprovegroupMixinFields0
	appapprovegroupMixinFields1 := appapprovegroupMixin[1].Fields()
	_ = appapprovegroupMixinFields1
	appapprovegroupFields := schematype.AppApproveGroup{}.Fields()
	_ = appapprovegroupFields
	// appapprovegroupDescCreatedAt is the schema descriptor for created_at field.
	appapprovegroupDescCreatedAt := appapprovegroupMixinFields0[1].Descriptor()
	// appapprovegroup.DefaultCreatedAt holds the default value on creation for the created_at field.
	appapprovegroup.DefaultCreatedAt = appapprovegroupDescCreatedAt.Default.(time.Time)
	// appapprovegroupDescUpdatedAt is the schema descriptor for updated_at field.
	appapprovegroupDescUpdatedAt := appapprovegroupMixinFields0[2].Descriptor()
	// appapprovegroup.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	appapprovegroup.DefaultUpdatedAt = appapprovegroupDescUpdatedAt.Default.(func() time.Time)
	// appapprovegroup.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	appapprovegroup.UpdateDefaultUpdatedAt = appapprovegroupDescUpdatedAt.UpdateDefault.(func() time.Time)
	// appapprovegroupDescStatus is the schema descriptor for status field.
	appapprovegroupDescStatus := appapprovegroupMixinFields1[0].Descriptor()
	// appapprovegroup.DefaultStatus holds the default value on creation for the status field.
	appapprovegroup.DefaultStatus = appapprovegroupDescStatus.Default.(int32)
	// appapprovegroupDescID is the schema descriptor for id field.
	appapprovegroupDescID := appapprovegroupMixinFields0[0].Descriptor()
	// appapprovegroup.DefaultID holds the default value on creation for the id field.
	appapprovegroup.DefaultID = appapprovegroupDescID.Default.(func() string)
	appapproverecordMixin := schematype.AppApproveRecord{}.Mixin()
	appapproverecordMixinFields0 := appapproverecordMixin[0].Fields()
	_ = appapproverecordMixinFields0
	appapproverecordFields := schematype.AppApproveRecord{}.Fields()
	_ = appapproverecordFields
	// appapproverecordDescCreatedAt is the schema descriptor for created_at field.
	appapproverecordDescCreatedAt := appapproverecordMixinFields0[1].Descriptor()
	// appapproverecord.DefaultCreatedAt holds the default value on creation for the created_at field.
	appapproverecord.DefaultCreatedAt = appapproverecordDescCreatedAt.Default.(time.Time)
	// appapproverecordDescUpdatedAt is the schema descriptor for updated_at field.
	appapproverecordDescUpdatedAt := appapproverecordMixinFields0[2].Descriptor()
	// appapproverecord.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	appapproverecord.DefaultUpdatedAt = appapproverecordDescUpdatedAt.Default.(func() time.Time)
	// appapproverecord.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	appapproverecord.UpdateDefaultUpdatedAt = appapproverecordDescUpdatedAt.UpdateDefault.(func() time.Time)
	// appapproverecordDescID is the schema descriptor for id field.
	appapproverecordDescID := appapproverecordMixinFields0[0].Descriptor()
	// appapproverecord.DefaultID holds the default value on creation for the id field.
	appapproverecord.DefaultID = appapproverecordDescID.Default.(func() string)
	appapproverecordlogMixin := schematype.AppApproveRecordLog{}.Mixin()
	appapproverecordlogMixinFields0 := appapproverecordlogMixin[0].Fields()
	_ = appapproverecordlogMixinFields0
	appapproverecordlogFields := schematype.AppApproveRecordLog{}.Fields()
	_ = appapproverecordlogFields
	// appapproverecordlogDescCreatedAt is the schema descriptor for created_at field.
	appapproverecordlogDescCreatedAt := appapproverecordlogMixinFields0[1].Descriptor()
	// appapproverecordlog.DefaultCreatedAt holds the default value on creation for the created_at field.
	appapproverecordlog.DefaultCreatedAt = appapproverecordlogDescCreatedAt.Default.(time.Time)
	// appapproverecordlogDescUpdatedAt is the schema descriptor for updated_at field.
	appapproverecordlogDescUpdatedAt := appapproverecordlogMixinFields0[2].Descriptor()
	// appapproverecordlog.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	appapproverecordlog.DefaultUpdatedAt = appapproverecordlogDescUpdatedAt.Default.(func() time.Time)
	// appapproverecordlog.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	appapproverecordlog.UpdateDefaultUpdatedAt = appapproverecordlogDescUpdatedAt.UpdateDefault.(func() time.Time)
	// appapproverecordlogDescID is the schema descriptor for id field.
	appapproverecordlogDescID := appapproverecordlogMixinFields0[0].Descriptor()
	// appapproverecordlog.DefaultID holds the default value on creation for the id field.
	appapproverecordlog.DefaultID = appapproverecordlogDescID.Default.(func() string)
	appapproverecordqueueMixin := schematype.AppApproveRecordQueue{}.Mixin()
	appapproverecordqueueMixinFields0 := appapproverecordqueueMixin[0].Fields()
	_ = appapproverecordqueueMixinFields0
	appapproverecordqueueFields := schematype.AppApproveRecordQueue{}.Fields()
	_ = appapproverecordqueueFields
	// appapproverecordqueueDescCreatedAt is the schema descriptor for created_at field.
	appapproverecordqueueDescCreatedAt := appapproverecordqueueMixinFields0[1].Descriptor()
	// appapproverecordqueue.DefaultCreatedAt holds the default value on creation for the created_at field.
	appapproverecordqueue.DefaultCreatedAt = appapproverecordqueueDescCreatedAt.Default.(time.Time)
	// appapproverecordqueueDescUpdatedAt is the schema descriptor for updated_at field.
	appapproverecordqueueDescUpdatedAt := appapproverecordqueueMixinFields0[2].Descriptor()
	// appapproverecordqueue.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	appapproverecordqueue.DefaultUpdatedAt = appapproverecordqueueDescUpdatedAt.Default.(func() time.Time)
	// appapproverecordqueue.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	appapproverecordqueue.UpdateDefaultUpdatedAt = appapproverecordqueueDescUpdatedAt.UpdateDefault.(func() time.Time)
	// appapproverecordqueueDescNeedSign is the schema descriptor for need_sign field.
	appapproverecordqueueDescNeedSign := appapproverecordqueueFields[5].Descriptor()
	// appapproverecordqueue.DefaultNeedSign holds the default value on creation for the need_sign field.
	appapproverecordqueue.DefaultNeedSign = appapproverecordqueueDescNeedSign.Default.(bool)
	// appapproverecordqueueDescAllowRollback is the schema descriptor for allow_rollback field.
	appapproverecordqueueDescAllowRollback := appapproverecordqueueFields[6].Descriptor()
	// appapproverecordqueue.DefaultAllowRollback holds the default value on creation for the allow_rollback field.
	appapproverecordqueue.DefaultAllowRollback = appapproverecordqueueDescAllowRollback.Default.(bool)
	// appapproverecordqueueDescNeedOpinion is the schema descriptor for need_opinion field.
	appapproverecordqueueDescNeedOpinion := appapproverecordqueueFields[7].Descriptor()
	// appapproverecordqueue.DefaultNeedOpinion holds the default value on creation for the need_opinion field.
	appapproverecordqueue.DefaultNeedOpinion = appapproverecordqueueDescNeedOpinion.Default.(bool)
	// appapproverecordqueueDescAllowRemark is the schema descriptor for allow_remark field.
	appapproverecordqueueDescAllowRemark := appapproverecordqueueFields[8].Descriptor()
	// appapproverecordqueue.DefaultAllowRemark holds the default value on creation for the allow_remark field.
	appapproverecordqueue.DefaultAllowRemark = appapproverecordqueueDescAllowRemark.Default.(bool)
	// appapproverecordqueueDescID is the schema descriptor for id field.
	appapproverecordqueueDescID := appapproverecordqueueMixinFields0[0].Descriptor()
	// appapproverecordqueue.DefaultID holds the default value on creation for the id field.
	appapproverecordqueue.DefaultID = appapproverecordqueueDescID.Default.(func() string)
	appapproveworkflowMixin := schematype.AppApproveWorkflow{}.Mixin()
	appapproveworkflowMixinFields0 := appapproveworkflowMixin[0].Fields()
	_ = appapproveworkflowMixinFields0
	appapproveworkflowFields := schematype.AppApproveWorkflow{}.Fields()
	_ = appapproveworkflowFields
	// appapproveworkflowDescCreatedAt is the schema descriptor for created_at field.
	appapproveworkflowDescCreatedAt := appapproveworkflowMixinFields0[1].Descriptor()
	// appapproveworkflow.DefaultCreatedAt holds the default value on creation for the created_at field.
	appapproveworkflow.DefaultCreatedAt = appapproveworkflowDescCreatedAt.Default.(time.Time)
	// appapproveworkflowDescUpdatedAt is the schema descriptor for updated_at field.
	appapproveworkflowDescUpdatedAt := appapproveworkflowMixinFields0[2].Descriptor()
	// appapproveworkflow.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	appapproveworkflow.DefaultUpdatedAt = appapproveworkflowDescUpdatedAt.Default.(func() time.Time)
	// appapproveworkflow.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	appapproveworkflow.UpdateDefaultUpdatedAt = appapproveworkflowDescUpdatedAt.UpdateDefault.(func() time.Time)
	// appapproveworkflowDescID is the schema descriptor for id field.
	appapproveworkflowDescID := appapproveworkflowMixinFields0[0].Descriptor()
	// appapproveworkflow.DefaultID holds the default value on creation for the id field.
	appapproveworkflow.DefaultID = appapproveworkflowDescID.Default.(func() string)
	demobuildingMixin := schematype.DemoBuilding{}.Mixin()
	demobuildingMixinHooks1 := demobuildingMixin[1].Hooks()
	demobuilding.Hooks[0] = demobuildingMixinHooks1[0]
	demobuildingMixinInters1 := demobuildingMixin[1].Interceptors()
	demobuilding.Interceptors[0] = demobuildingMixinInters1[0]
	demobuildingMixinFields0 := demobuildingMixin[0].Fields()
	_ = demobuildingMixinFields0
	demobuildingFields := schematype.DemoBuilding{}.Fields()
	_ = demobuildingFields
	// demobuildingDescCreatedAt is the schema descriptor for created_at field.
	demobuildingDescCreatedAt := demobuildingMixinFields0[1].Descriptor()
	// demobuilding.DefaultCreatedAt holds the default value on creation for the created_at field.
	demobuilding.DefaultCreatedAt = demobuildingDescCreatedAt.Default.(time.Time)
	// demobuildingDescUpdatedAt is the schema descriptor for updated_at field.
	demobuildingDescUpdatedAt := demobuildingMixinFields0[2].Descriptor()
	// demobuilding.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	demobuilding.DefaultUpdatedAt = demobuildingDescUpdatedAt.Default.(func() time.Time)
	// demobuilding.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	demobuilding.UpdateDefaultUpdatedAt = demobuildingDescUpdatedAt.UpdateDefault.(func() time.Time)
	// demobuildingDescID is the schema descriptor for id field.
	demobuildingDescID := demobuildingMixinFields0[0].Descriptor()
	// demobuilding.DefaultID holds the default value on creation for the id field.
	demobuilding.DefaultID = demobuildingDescID.Default.(func() string)
	sysapplicationMixin := schematype.SysApplication{}.Mixin()
	sysapplicationMixinFields0 := sysapplicationMixin[0].Fields()
	_ = sysapplicationMixinFields0
	sysapplicationMixinFields1 := sysapplicationMixin[1].Fields()
	_ = sysapplicationMixinFields1
	sysapplicationFields := schematype.SysApplication{}.Fields()
	_ = sysapplicationFields
	// sysapplicationDescCreatedAt is the schema descriptor for created_at field.
	sysapplicationDescCreatedAt := sysapplicationMixinFields0[1].Descriptor()
	// sysapplication.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysapplication.DefaultCreatedAt = sysapplicationDescCreatedAt.Default.(time.Time)
	// sysapplicationDescUpdatedAt is the schema descriptor for updated_at field.
	sysapplicationDescUpdatedAt := sysapplicationMixinFields0[2].Descriptor()
	// sysapplication.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysapplication.DefaultUpdatedAt = sysapplicationDescUpdatedAt.Default.(func() time.Time)
	// sysapplication.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysapplication.UpdateDefaultUpdatedAt = sysapplicationDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysapplicationDescSort is the schema descriptor for sort field.
	sysapplicationDescSort := sysapplicationMixinFields1[0].Descriptor()
	// sysapplication.DefaultSort holds the default value on creation for the sort field.
	sysapplication.DefaultSort = sysapplicationDescSort.Default.(int32)
	// sysapplicationDescID is the schema descriptor for id field.
	sysapplicationDescID := sysapplicationMixinFields0[0].Descriptor()
	// sysapplication.DefaultID holds the default value on creation for the id field.
	sysapplication.DefaultID = sysapplicationDescID.Default.(func() string)
	sysapplicationmoduleMixin := schematype.SysApplicationModule{}.Mixin()
	sysapplicationmoduleMixinFields0 := sysapplicationmoduleMixin[0].Fields()
	_ = sysapplicationmoduleMixinFields0
	sysapplicationmoduleFields := schematype.SysApplicationModule{}.Fields()
	_ = sysapplicationmoduleFields
	// sysapplicationmoduleDescCreatedAt is the schema descriptor for created_at field.
	sysapplicationmoduleDescCreatedAt := sysapplicationmoduleMixinFields0[1].Descriptor()
	// sysapplicationmodule.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysapplicationmodule.DefaultCreatedAt = sysapplicationmoduleDescCreatedAt.Default.(time.Time)
	// sysapplicationmoduleDescUpdatedAt is the schema descriptor for updated_at field.
	sysapplicationmoduleDescUpdatedAt := sysapplicationmoduleMixinFields0[2].Descriptor()
	// sysapplicationmodule.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysapplicationmodule.DefaultUpdatedAt = sysapplicationmoduleDescUpdatedAt.Default.(func() time.Time)
	// sysapplicationmodule.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysapplicationmodule.UpdateDefaultUpdatedAt = sysapplicationmoduleDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysapplicationmoduleDescID is the schema descriptor for id field.
	sysapplicationmoduleDescID := sysapplicationmoduleMixinFields0[0].Descriptor()
	// sysapplicationmodule.DefaultID holds the default value on creation for the id field.
	sysapplicationmodule.DefaultID = sysapplicationmoduleDescID.Default.(func() string)
	sysapplicationmoduleresourceMixin := schematype.SysApplicationModuleResource{}.Mixin()
	sysapplicationmoduleresourceMixinFields0 := sysapplicationmoduleresourceMixin[0].Fields()
	_ = sysapplicationmoduleresourceMixinFields0
	sysapplicationmoduleresourceFields := schematype.SysApplicationModuleResource{}.Fields()
	_ = sysapplicationmoduleresourceFields
	// sysapplicationmoduleresourceDescCreatedAt is the schema descriptor for created_at field.
	sysapplicationmoduleresourceDescCreatedAt := sysapplicationmoduleresourceMixinFields0[1].Descriptor()
	// sysapplicationmoduleresource.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysapplicationmoduleresource.DefaultCreatedAt = sysapplicationmoduleresourceDescCreatedAt.Default.(time.Time)
	// sysapplicationmoduleresourceDescUpdatedAt is the schema descriptor for updated_at field.
	sysapplicationmoduleresourceDescUpdatedAt := sysapplicationmoduleresourceMixinFields0[2].Descriptor()
	// sysapplicationmoduleresource.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysapplicationmoduleresource.DefaultUpdatedAt = sysapplicationmoduleresourceDescUpdatedAt.Default.(func() time.Time)
	// sysapplicationmoduleresource.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysapplicationmoduleresource.UpdateDefaultUpdatedAt = sysapplicationmoduleresourceDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysapplicationmoduleresourceDescID is the schema descriptor for id field.
	sysapplicationmoduleresourceDescID := sysapplicationmoduleresourceMixinFields0[0].Descriptor()
	// sysapplicationmoduleresource.DefaultID holds the default value on creation for the id field.
	sysapplicationmoduleresource.DefaultID = sysapplicationmoduleresourceDescID.Default.(func() string)
	sysareaMixin := schematype.SysArea{}.Mixin()
	sysareaMixinFields0 := sysareaMixin[0].Fields()
	_ = sysareaMixinFields0
	sysareaFields := schematype.SysArea{}.Fields()
	_ = sysareaFields
	// sysareaDescCreatedAt is the schema descriptor for created_at field.
	sysareaDescCreatedAt := sysareaMixinFields0[1].Descriptor()
	// sysarea.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysarea.DefaultCreatedAt = sysareaDescCreatedAt.Default.(time.Time)
	// sysareaDescUpdatedAt is the schema descriptor for updated_at field.
	sysareaDescUpdatedAt := sysareaMixinFields0[2].Descriptor()
	// sysarea.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysarea.DefaultUpdatedAt = sysareaDescUpdatedAt.Default.(func() time.Time)
	// sysarea.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysarea.UpdateDefaultUpdatedAt = sysareaDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysareaDescID is the schema descriptor for id field.
	sysareaDescID := sysareaMixinFields0[0].Descriptor()
	// sysarea.DefaultID holds the default value on creation for the id field.
	sysarea.DefaultID = sysareaDescID.Default.(func() string)
	syscityMixin := schematype.SysCity{}.Mixin()
	syscityMixinFields0 := syscityMixin[0].Fields()
	_ = syscityMixinFields0
	syscityFields := schematype.SysCity{}.Fields()
	_ = syscityFields
	// syscityDescCreatedAt is the schema descriptor for created_at field.
	syscityDescCreatedAt := syscityMixinFields0[1].Descriptor()
	// syscity.DefaultCreatedAt holds the default value on creation for the created_at field.
	syscity.DefaultCreatedAt = syscityDescCreatedAt.Default.(time.Time)
	// syscityDescUpdatedAt is the schema descriptor for updated_at field.
	syscityDescUpdatedAt := syscityMixinFields0[2].Descriptor()
	// syscity.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	syscity.DefaultUpdatedAt = syscityDescUpdatedAt.Default.(func() time.Time)
	// syscity.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	syscity.UpdateDefaultUpdatedAt = syscityDescUpdatedAt.UpdateDefault.(func() time.Time)
	// syscityDescID is the schema descriptor for id field.
	syscityDescID := syscityMixinFields0[0].Descriptor()
	// syscity.DefaultID holds the default value on creation for the id field.
	syscity.DefaultID = syscityDescID.Default.(func() string)
	sysconfigMixin := schematype.SysConfig{}.Mixin()
	sysconfigMixinFields0 := sysconfigMixin[0].Fields()
	_ = sysconfigMixinFields0
	sysconfigFields := schematype.SysConfig{}.Fields()
	_ = sysconfigFields
	// sysconfigDescCreatedAt is the schema descriptor for created_at field.
	sysconfigDescCreatedAt := sysconfigMixinFields0[1].Descriptor()
	// sysconfig.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysconfig.DefaultCreatedAt = sysconfigDescCreatedAt.Default.(time.Time)
	// sysconfigDescUpdatedAt is the schema descriptor for updated_at field.
	sysconfigDescUpdatedAt := sysconfigMixinFields0[2].Descriptor()
	// sysconfig.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysconfig.DefaultUpdatedAt = sysconfigDescUpdatedAt.Default.(func() time.Time)
	// sysconfig.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysconfig.UpdateDefaultUpdatedAt = sysconfigDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysconfigDescID is the schema descriptor for id field.
	sysconfigDescID := sysconfigMixinFields0[0].Descriptor()
	// sysconfig.DefaultID holds the default value on creation for the id field.
	sysconfig.DefaultID = sysconfigDescID.Default.(func() string)
	sysdictionaryMixin := schematype.SysDictionary{}.Mixin()
	sysdictionaryMixinFields0 := sysdictionaryMixin[0].Fields()
	_ = sysdictionaryMixinFields0
	sysdictionaryMixinFields1 := sysdictionaryMixin[1].Fields()
	_ = sysdictionaryMixinFields1
	sysdictionaryMixinFields2 := sysdictionaryMixin[2].Fields()
	_ = sysdictionaryMixinFields2
	sysdictionaryFields := schematype.SysDictionary{}.Fields()
	_ = sysdictionaryFields
	// sysdictionaryDescCreatedAt is the schema descriptor for created_at field.
	sysdictionaryDescCreatedAt := sysdictionaryMixinFields0[1].Descriptor()
	// sysdictionary.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysdictionary.DefaultCreatedAt = sysdictionaryDescCreatedAt.Default.(time.Time)
	// sysdictionaryDescUpdatedAt is the schema descriptor for updated_at field.
	sysdictionaryDescUpdatedAt := sysdictionaryMixinFields0[2].Descriptor()
	// sysdictionary.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysdictionary.DefaultUpdatedAt = sysdictionaryDescUpdatedAt.Default.(func() time.Time)
	// sysdictionary.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysdictionary.UpdateDefaultUpdatedAt = sysdictionaryDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysdictionaryDescSort is the schema descriptor for sort field.
	sysdictionaryDescSort := sysdictionaryMixinFields1[0].Descriptor()
	// sysdictionary.DefaultSort holds the default value on creation for the sort field.
	sysdictionary.DefaultSort = sysdictionaryDescSort.Default.(int32)
	// sysdictionaryDescStatus is the schema descriptor for status field.
	sysdictionaryDescStatus := sysdictionaryMixinFields2[0].Descriptor()
	// sysdictionary.DefaultStatus holds the default value on creation for the status field.
	sysdictionary.DefaultStatus = sysdictionaryDescStatus.Default.(int32)
	// sysdictionaryDescID is the schema descriptor for id field.
	sysdictionaryDescID := sysdictionaryMixinFields0[0].Descriptor()
	// sysdictionary.DefaultID holds the default value on creation for the id field.
	sysdictionary.DefaultID = sysdictionaryDescID.Default.(func() string)
	sysdictionarydetailMixin := schematype.SysDictionaryDetail{}.Mixin()
	sysdictionarydetailMixinFields0 := sysdictionarydetailMixin[0].Fields()
	_ = sysdictionarydetailMixinFields0
	sysdictionarydetailMixinFields1 := sysdictionarydetailMixin[1].Fields()
	_ = sysdictionarydetailMixinFields1
	sysdictionarydetailMixinFields2 := sysdictionarydetailMixin[2].Fields()
	_ = sysdictionarydetailMixinFields2
	sysdictionarydetailFields := schematype.SysDictionaryDetail{}.Fields()
	_ = sysdictionarydetailFields
	// sysdictionarydetailDescCreatedAt is the schema descriptor for created_at field.
	sysdictionarydetailDescCreatedAt := sysdictionarydetailMixinFields0[1].Descriptor()
	// sysdictionarydetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysdictionarydetail.DefaultCreatedAt = sysdictionarydetailDescCreatedAt.Default.(time.Time)
	// sysdictionarydetailDescUpdatedAt is the schema descriptor for updated_at field.
	sysdictionarydetailDescUpdatedAt := sysdictionarydetailMixinFields0[2].Descriptor()
	// sysdictionarydetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysdictionarydetail.DefaultUpdatedAt = sysdictionarydetailDescUpdatedAt.Default.(func() time.Time)
	// sysdictionarydetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysdictionarydetail.UpdateDefaultUpdatedAt = sysdictionarydetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysdictionarydetailDescSort is the schema descriptor for sort field.
	sysdictionarydetailDescSort := sysdictionarydetailMixinFields1[0].Descriptor()
	// sysdictionarydetail.DefaultSort holds the default value on creation for the sort field.
	sysdictionarydetail.DefaultSort = sysdictionarydetailDescSort.Default.(int32)
	// sysdictionarydetailDescStatus is the schema descriptor for status field.
	sysdictionarydetailDescStatus := sysdictionarydetailMixinFields2[0].Descriptor()
	// sysdictionarydetail.DefaultStatus holds the default value on creation for the status field.
	sysdictionarydetail.DefaultStatus = sysdictionarydetailDescStatus.Default.(int32)
	// sysdictionarydetailDescID is the schema descriptor for id field.
	sysdictionarydetailDescID := sysdictionarydetailMixinFields0[0].Descriptor()
	// sysdictionarydetail.DefaultID holds the default value on creation for the id field.
	sysdictionarydetail.DefaultID = sysdictionarydetailDescID.Default.(func() string)
	sysgameMixin := schematype.SysGame{}.Mixin()
	sysgameMixinFields0 := sysgameMixin[0].Fields()
	_ = sysgameMixinFields0
	sysgameFields := schematype.SysGame{}.Fields()
	_ = sysgameFields
	// sysgameDescCreatedAt is the schema descriptor for created_at field.
	sysgameDescCreatedAt := sysgameMixinFields0[1].Descriptor()
	// sysgame.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysgame.DefaultCreatedAt = sysgameDescCreatedAt.Default.(time.Time)
	// sysgameDescUpdatedAt is the schema descriptor for updated_at field.
	sysgameDescUpdatedAt := sysgameMixinFields0[2].Descriptor()
	// sysgame.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysgame.DefaultUpdatedAt = sysgameDescUpdatedAt.Default.(func() time.Time)
	// sysgame.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysgame.UpdateDefaultUpdatedAt = sysgameDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysgameDescID is the schema descriptor for id field.
	sysgameDescID := sysgameMixinFields0[0].Descriptor()
	// sysgame.DefaultID holds the default value on creation for the id field.
	sysgame.DefaultID = sysgameDescID.Default.(func() string)
	syslabelMixin := schematype.SysLabel{}.Mixin()
	syslabelMixinFields0 := syslabelMixin[0].Fields()
	_ = syslabelMixinFields0
	syslabelMixinFields1 := syslabelMixin[1].Fields()
	_ = syslabelMixinFields1
	syslabelFields := schematype.SysLabel{}.Fields()
	_ = syslabelFields
	// syslabelDescCreatedAt is the schema descriptor for created_at field.
	syslabelDescCreatedAt := syslabelMixinFields0[1].Descriptor()
	// syslabel.DefaultCreatedAt holds the default value on creation for the created_at field.
	syslabel.DefaultCreatedAt = syslabelDescCreatedAt.Default.(time.Time)
	// syslabelDescUpdatedAt is the schema descriptor for updated_at field.
	syslabelDescUpdatedAt := syslabelMixinFields0[2].Descriptor()
	// syslabel.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	syslabel.DefaultUpdatedAt = syslabelDescUpdatedAt.Default.(func() time.Time)
	// syslabel.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	syslabel.UpdateDefaultUpdatedAt = syslabelDescUpdatedAt.UpdateDefault.(func() time.Time)
	// syslabelDescStatus is the schema descriptor for status field.
	syslabelDescStatus := syslabelMixinFields1[0].Descriptor()
	// syslabel.DefaultStatus holds the default value on creation for the status field.
	syslabel.DefaultStatus = syslabelDescStatus.Default.(int32)
	// syslabelDescID is the schema descriptor for id field.
	syslabelDescID := syslabelMixinFields0[0].Descriptor()
	// syslabel.DefaultID holds the default value on creation for the id field.
	syslabel.DefaultID = syslabelDescID.Default.(func() string)
	syslogMixin := schematype.SysLog{}.Mixin()
	syslogMixinFields0 := syslogMixin[0].Fields()
	_ = syslogMixinFields0
	syslogMixinFields1 := syslogMixin[1].Fields()
	_ = syslogMixinFields1
	syslogFields := schematype.SysLog{}.Fields()
	_ = syslogFields
	// syslogDescCreatedAt is the schema descriptor for created_at field.
	syslogDescCreatedAt := syslogMixinFields0[1].Descriptor()
	// syslog.DefaultCreatedAt holds the default value on creation for the created_at field.
	syslog.DefaultCreatedAt = syslogDescCreatedAt.Default.(time.Time)
	// syslogDescUpdatedAt is the schema descriptor for updated_at field.
	syslogDescUpdatedAt := syslogMixinFields0[2].Descriptor()
	// syslog.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	syslog.DefaultUpdatedAt = syslogDescUpdatedAt.Default.(func() time.Time)
	// syslog.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	syslog.UpdateDefaultUpdatedAt = syslogDescUpdatedAt.UpdateDefault.(func() time.Time)
	// syslogDescStatus is the schema descriptor for status field.
	syslogDescStatus := syslogMixinFields1[0].Descriptor()
	// syslog.DefaultStatus holds the default value on creation for the status field.
	syslog.DefaultStatus = syslogDescStatus.Default.(int32)
	// syslogDescID is the schema descriptor for id field.
	syslogDescID := syslogMixinFields0[0].Descriptor()
	// syslog.DefaultID holds the default value on creation for the id field.
	syslog.DefaultID = syslogDescID.Default.(func() string)
	sysmenuMixin := schematype.SysMenu{}.Mixin()
	sysmenuMixinFields0 := sysmenuMixin[0].Fields()
	_ = sysmenuMixinFields0
	sysmenuFields := schematype.SysMenu{}.Fields()
	_ = sysmenuFields
	// sysmenuDescCreatedAt is the schema descriptor for created_at field.
	sysmenuDescCreatedAt := sysmenuMixinFields0[1].Descriptor()
	// sysmenu.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysmenu.DefaultCreatedAt = sysmenuDescCreatedAt.Default.(time.Time)
	// sysmenuDescUpdatedAt is the schema descriptor for updated_at field.
	sysmenuDescUpdatedAt := sysmenuMixinFields0[2].Descriptor()
	// sysmenu.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysmenu.DefaultUpdatedAt = sysmenuDescUpdatedAt.Default.(func() time.Time)
	// sysmenu.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysmenu.UpdateDefaultUpdatedAt = sysmenuDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysmenuDescApplicationID is the schema descriptor for application_id field.
	sysmenuDescApplicationID := sysmenuFields[0].Descriptor()
	// sysmenu.ApplicationIDValidator is a validator for the "application_id" field. It is called by the builders before save.
	sysmenu.ApplicationIDValidator = sysmenuDescApplicationID.Validators[0].(func(string) error)
	// sysmenuDescIsSubmenu is the schema descriptor for is_submenu field.
	sysmenuDescIsSubmenu := sysmenuFields[8].Descriptor()
	// sysmenu.DefaultIsSubmenu holds the default value on creation for the is_submenu field.
	sysmenu.DefaultIsSubmenu = sysmenuDescIsSubmenu.Default.(bool)
	// sysmenuDescIsLink is the schema descriptor for is_link field.
	sysmenuDescIsLink := sysmenuFields[9].Descriptor()
	// sysmenu.DefaultIsLink holds the default value on creation for the is_link field.
	sysmenu.DefaultIsLink = sysmenuDescIsLink.Default.(bool)
	// sysmenuDescHideChildrenInMenu is the schema descriptor for hide_children_in_menu field.
	sysmenuDescHideChildrenInMenu := sysmenuFields[10].Descriptor()
	// sysmenu.DefaultHideChildrenInMenu holds the default value on creation for the hide_children_in_menu field.
	sysmenu.DefaultHideChildrenInMenu = sysmenuDescHideChildrenInMenu.Default.(bool)
	// sysmenuDescHideMenu is the schema descriptor for hideMenu field.
	sysmenuDescHideMenu := sysmenuFields[11].Descriptor()
	// sysmenu.DefaultHideMenu holds the default value on creation for the hideMenu field.
	sysmenu.DefaultHideMenu = sysmenuDescHideMenu.Default.(bool)
	// sysmenuDescSort is the schema descriptor for sort field.
	sysmenuDescSort := sysmenuFields[12].Descriptor()
	// sysmenu.DefaultSort holds the default value on creation for the sort field.
	sysmenu.DefaultSort = sysmenuDescSort.Default.(int32)
	// sysmenuDescID is the schema descriptor for id field.
	sysmenuDescID := sysmenuMixinFields0[0].Descriptor()
	// sysmenu.DefaultID holds the default value on creation for the id field.
	sysmenu.DefaultID = sysmenuDescID.Default.(func() string)
	sysmessagetemplateMixin := schematype.SysMessageTemplate{}.Mixin()
	sysmessagetemplateMixinFields0 := sysmessagetemplateMixin[0].Fields()
	_ = sysmessagetemplateMixinFields0
	sysmessagetemplateFields := schematype.SysMessageTemplate{}.Fields()
	_ = sysmessagetemplateFields
	// sysmessagetemplateDescCreatedAt is the schema descriptor for created_at field.
	sysmessagetemplateDescCreatedAt := sysmessagetemplateMixinFields0[1].Descriptor()
	// sysmessagetemplate.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysmessagetemplate.DefaultCreatedAt = sysmessagetemplateDescCreatedAt.Default.(time.Time)
	// sysmessagetemplateDescUpdatedAt is the schema descriptor for updated_at field.
	sysmessagetemplateDescUpdatedAt := sysmessagetemplateMixinFields0[2].Descriptor()
	// sysmessagetemplate.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysmessagetemplate.DefaultUpdatedAt = sysmessagetemplateDescUpdatedAt.Default.(func() time.Time)
	// sysmessagetemplate.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysmessagetemplate.UpdateDefaultUpdatedAt = sysmessagetemplateDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysmessagetemplateDescCode is the schema descriptor for code field.
	sysmessagetemplateDescCode := sysmessagetemplateFields[2].Descriptor()
	// sysmessagetemplate.DefaultCode holds the default value on creation for the code field.
	sysmessagetemplate.DefaultCode = sysmessagetemplateDescCode.Default.(string)
	// sysmessagetemplateDescID is the schema descriptor for id field.
	sysmessagetemplateDescID := sysmessagetemplateMixinFields0[0].Descriptor()
	// sysmessagetemplate.DefaultID holds the default value on creation for the id field.
	sysmessagetemplate.DefaultID = sysmessagetemplateDescID.Default.(func() string)
	sysmoduleMixin := schematype.SysModule{}.Mixin()
	sysmoduleMixinFields0 := sysmoduleMixin[0].Fields()
	_ = sysmoduleMixinFields0
	sysmoduleMixinFields1 := sysmoduleMixin[1].Fields()
	_ = sysmoduleMixinFields1
	sysmoduleFields := schematype.SysModule{}.Fields()
	_ = sysmoduleFields
	// sysmoduleDescCreatedAt is the schema descriptor for created_at field.
	sysmoduleDescCreatedAt := sysmoduleMixinFields0[1].Descriptor()
	// sysmodule.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysmodule.DefaultCreatedAt = sysmoduleDescCreatedAt.Default.(time.Time)
	// sysmoduleDescUpdatedAt is the schema descriptor for updated_at field.
	sysmoduleDescUpdatedAt := sysmoduleMixinFields0[2].Descriptor()
	// sysmodule.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysmodule.DefaultUpdatedAt = sysmoduleDescUpdatedAt.Default.(func() time.Time)
	// sysmodule.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysmodule.UpdateDefaultUpdatedAt = sysmoduleDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysmoduleDescSort is the schema descriptor for sort field.
	sysmoduleDescSort := sysmoduleMixinFields1[0].Descriptor()
	// sysmodule.DefaultSort holds the default value on creation for the sort field.
	sysmodule.DefaultSort = sysmoduleDescSort.Default.(int32)
	// sysmoduleDescID is the schema descriptor for id field.
	sysmoduleDescID := sysmoduleMixinFields0[0].Descriptor()
	// sysmodule.DefaultID holds the default value on creation for the id field.
	sysmodule.DefaultID = sysmoduleDescID.Default.(func() string)
	sysmoduleresourceMixin := schematype.SysModuleResource{}.Mixin()
	sysmoduleresourceMixinFields0 := sysmoduleresourceMixin[0].Fields()
	_ = sysmoduleresourceMixinFields0
	sysmoduleresourceFields := schematype.SysModuleResource{}.Fields()
	_ = sysmoduleresourceFields
	// sysmoduleresourceDescCreatedAt is the schema descriptor for created_at field.
	sysmoduleresourceDescCreatedAt := sysmoduleresourceMixinFields0[1].Descriptor()
	// sysmoduleresource.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysmoduleresource.DefaultCreatedAt = sysmoduleresourceDescCreatedAt.Default.(time.Time)
	// sysmoduleresourceDescUpdatedAt is the schema descriptor for updated_at field.
	sysmoduleresourceDescUpdatedAt := sysmoduleresourceMixinFields0[2].Descriptor()
	// sysmoduleresource.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysmoduleresource.DefaultUpdatedAt = sysmoduleresourceDescUpdatedAt.Default.(func() time.Time)
	// sysmoduleresource.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysmoduleresource.UpdateDefaultUpdatedAt = sysmoduleresourceDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysmoduleresourceDescID is the schema descriptor for id field.
	sysmoduleresourceDescID := sysmoduleresourceMixinFields0[0].Descriptor()
	// sysmoduleresource.DefaultID holds the default value on creation for the id field.
	sysmoduleresource.DefaultID = sysmoduleresourceDescID.Default.(func() string)
	sysorganizationMixin := schematype.SysOrganization{}.Mixin()
	sysorganizationMixinFields0 := sysorganizationMixin[0].Fields()
	_ = sysorganizationMixinFields0
	sysorganizationFields := schematype.SysOrganization{}.Fields()
	_ = sysorganizationFields
	// sysorganizationDescCreatedAt is the schema descriptor for created_at field.
	sysorganizationDescCreatedAt := sysorganizationMixinFields0[1].Descriptor()
	// sysorganization.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysorganization.DefaultCreatedAt = sysorganizationDescCreatedAt.Default.(time.Time)
	// sysorganizationDescUpdatedAt is the schema descriptor for updated_at field.
	sysorganizationDescUpdatedAt := sysorganizationMixinFields0[2].Descriptor()
	// sysorganization.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysorganization.DefaultUpdatedAt = sysorganizationDescUpdatedAt.Default.(func() time.Time)
	// sysorganization.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysorganization.UpdateDefaultUpdatedAt = sysorganizationDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysorganizationDescTeamID is the schema descriptor for team_id field.
	sysorganizationDescTeamID := sysorganizationFields[1].Descriptor()
	// sysorganization.TeamIDValidator is a validator for the "team_id" field. It is called by the builders before save.
	sysorganization.TeamIDValidator = sysorganizationDescTeamID.Validators[0].(func(string) error)
	// sysorganizationDescApplicationID is the schema descriptor for application_id field.
	sysorganizationDescApplicationID := sysorganizationFields[2].Descriptor()
	// sysorganization.ApplicationIDValidator is a validator for the "application_id" field. It is called by the builders before save.
	sysorganization.ApplicationIDValidator = sysorganizationDescApplicationID.Validators[0].(func(string) error)
	// sysorganizationDescID is the schema descriptor for id field.
	sysorganizationDescID := sysorganizationMixinFields0[0].Descriptor()
	// sysorganization.DefaultID holds the default value on creation for the id field.
	sysorganization.DefaultID = sysorganizationDescID.Default.(func() string)
	syspagecodeMixin := schematype.SysPageCode{}.Mixin()
	syspagecodeMixinFields0 := syspagecodeMixin[0].Fields()
	_ = syspagecodeMixinFields0
	syspagecodeFields := schematype.SysPageCode{}.Fields()
	_ = syspagecodeFields
	// syspagecodeDescCreatedAt is the schema descriptor for created_at field.
	syspagecodeDescCreatedAt := syspagecodeMixinFields0[1].Descriptor()
	// syspagecode.DefaultCreatedAt holds the default value on creation for the created_at field.
	syspagecode.DefaultCreatedAt = syspagecodeDescCreatedAt.Default.(time.Time)
	// syspagecodeDescUpdatedAt is the schema descriptor for updated_at field.
	syspagecodeDescUpdatedAt := syspagecodeMixinFields0[2].Descriptor()
	// syspagecode.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	syspagecode.DefaultUpdatedAt = syspagecodeDescUpdatedAt.Default.(func() time.Time)
	// syspagecode.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	syspagecode.UpdateDefaultUpdatedAt = syspagecodeDescUpdatedAt.UpdateDefault.(func() time.Time)
	// syspagecodeDescName is the schema descriptor for name field.
	syspagecodeDescName := syspagecodeFields[0].Descriptor()
	// syspagecode.NameValidator is a validator for the "name" field. It is called by the builders before save.
	syspagecode.NameValidator = syspagecodeDescName.Validators[0].(func(string) error)
	// syspagecodeDescID is the schema descriptor for id field.
	syspagecodeDescID := syspagecodeMixinFields0[0].Descriptor()
	// syspagecode.DefaultID holds the default value on creation for the id field.
	syspagecode.DefaultID = syspagecodeDescID.Default.(func() string)
	syspagecodehistoryMixin := schematype.SysPageCodeHistory{}.Mixin()
	syspagecodehistoryMixinFields0 := syspagecodehistoryMixin[0].Fields()
	_ = syspagecodehistoryMixinFields0
	syspagecodehistoryFields := schematype.SysPageCodeHistory{}.Fields()
	_ = syspagecodehistoryFields
	// syspagecodehistoryDescCreatedAt is the schema descriptor for created_at field.
	syspagecodehistoryDescCreatedAt := syspagecodehistoryMixinFields0[1].Descriptor()
	// syspagecodehistory.DefaultCreatedAt holds the default value on creation for the created_at field.
	syspagecodehistory.DefaultCreatedAt = syspagecodehistoryDescCreatedAt.Default.(time.Time)
	// syspagecodehistoryDescUpdatedAt is the schema descriptor for updated_at field.
	syspagecodehistoryDescUpdatedAt := syspagecodehistoryMixinFields0[2].Descriptor()
	// syspagecodehistory.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	syspagecodehistory.DefaultUpdatedAt = syspagecodehistoryDescUpdatedAt.Default.(func() time.Time)
	// syspagecodehistory.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	syspagecodehistory.UpdateDefaultUpdatedAt = syspagecodehistoryDescUpdatedAt.UpdateDefault.(func() time.Time)
	// syspagecodehistoryDescID is the schema descriptor for id field.
	syspagecodehistoryDescID := syspagecodehistoryMixinFields0[0].Descriptor()
	// syspagecodehistory.DefaultID holds the default value on creation for the id field.
	syspagecodehistory.DefaultID = syspagecodehistoryDescID.Default.(func() string)
	sysprojectMixin := schematype.SysProject{}.Mixin()
	sysprojectMixinFields0 := sysprojectMixin[0].Fields()
	_ = sysprojectMixinFields0
	sysprojectMixinFields1 := sysprojectMixin[1].Fields()
	_ = sysprojectMixinFields1
	sysprojectFields := schematype.SysProject{}.Fields()
	_ = sysprojectFields
	// sysprojectDescCreatedAt is the schema descriptor for created_at field.
	sysprojectDescCreatedAt := sysprojectMixinFields0[1].Descriptor()
	// sysproject.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysproject.DefaultCreatedAt = sysprojectDescCreatedAt.Default.(time.Time)
	// sysprojectDescUpdatedAt is the schema descriptor for updated_at field.
	sysprojectDescUpdatedAt := sysprojectMixinFields0[2].Descriptor()
	// sysproject.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysproject.DefaultUpdatedAt = sysprojectDescUpdatedAt.Default.(func() time.Time)
	// sysproject.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysproject.UpdateDefaultUpdatedAt = sysprojectDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysprojectDescSort is the schema descriptor for sort field.
	sysprojectDescSort := sysprojectMixinFields1[0].Descriptor()
	// sysproject.DefaultSort holds the default value on creation for the sort field.
	sysproject.DefaultSort = sysprojectDescSort.Default.(int32)
	// sysprojectDescName is the schema descriptor for name field.
	sysprojectDescName := sysprojectFields[0].Descriptor()
	// sysproject.NameValidator is a validator for the "name" field. It is called by the builders before save.
	sysproject.NameValidator = sysprojectDescName.Validators[0].(func(string) error)
	// sysprojectDescApplicationID is the schema descriptor for application_id field.
	sysprojectDescApplicationID := sysprojectFields[1].Descriptor()
	// sysproject.ApplicationIDValidator is a validator for the "application_id" field. It is called by the builders before save.
	sysproject.ApplicationIDValidator = sysprojectDescApplicationID.Validators[0].(func(string) error)
	// sysprojectDescTeamID is the schema descriptor for team_id field.
	sysprojectDescTeamID := sysprojectFields[2].Descriptor()
	// sysproject.TeamIDValidator is a validator for the "team_id" field. It is called by the builders before save.
	sysproject.TeamIDValidator = sysprojectDescTeamID.Validators[0].(func(string) error)
	// sysprojectDescID is the schema descriptor for id field.
	sysprojectDescID := sysprojectMixinFields0[0].Descriptor()
	// sysproject.DefaultID holds the default value on creation for the id field.
	sysproject.DefaultID = sysprojectDescID.Default.(func() string)
	sysprovinceMixin := schematype.SysProvince{}.Mixin()
	sysprovinceMixinFields0 := sysprovinceMixin[0].Fields()
	_ = sysprovinceMixinFields0
	sysprovinceFields := schematype.SysProvince{}.Fields()
	_ = sysprovinceFields
	// sysprovinceDescCreatedAt is the schema descriptor for created_at field.
	sysprovinceDescCreatedAt := sysprovinceMixinFields0[1].Descriptor()
	// sysprovince.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysprovince.DefaultCreatedAt = sysprovinceDescCreatedAt.Default.(time.Time)
	// sysprovinceDescUpdatedAt is the schema descriptor for updated_at field.
	sysprovinceDescUpdatedAt := sysprovinceMixinFields0[2].Descriptor()
	// sysprovince.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysprovince.DefaultUpdatedAt = sysprovinceDescUpdatedAt.Default.(func() time.Time)
	// sysprovince.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysprovince.UpdateDefaultUpdatedAt = sysprovinceDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysprovinceDescID is the schema descriptor for id field.
	sysprovinceDescID := sysprovinceMixinFields0[0].Descriptor()
	// sysprovince.DefaultID holds the default value on creation for the id field.
	sysprovince.DefaultID = sysprovinceDescID.Default.(func() string)
	sysresourceMixin := schematype.SysResource{}.Mixin()
	sysresourceMixinFields0 := sysresourceMixin[0].Fields()
	_ = sysresourceMixinFields0
	sysresourceMixinFields1 := sysresourceMixin[1].Fields()
	_ = sysresourceMixinFields1
	sysresourceMixinFields2 := sysresourceMixin[2].Fields()
	_ = sysresourceMixinFields2
	sysresourceFields := schematype.SysResource{}.Fields()
	_ = sysresourceFields
	// sysresourceDescCreatedAt is the schema descriptor for created_at field.
	sysresourceDescCreatedAt := sysresourceMixinFields0[1].Descriptor()
	// sysresource.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysresource.DefaultCreatedAt = sysresourceDescCreatedAt.Default.(time.Time)
	// sysresourceDescUpdatedAt is the schema descriptor for updated_at field.
	sysresourceDescUpdatedAt := sysresourceMixinFields0[2].Descriptor()
	// sysresource.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysresource.DefaultUpdatedAt = sysresourceDescUpdatedAt.Default.(func() time.Time)
	// sysresource.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysresource.UpdateDefaultUpdatedAt = sysresourceDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysresourceDescSort is the schema descriptor for sort field.
	sysresourceDescSort := sysresourceMixinFields1[0].Descriptor()
	// sysresource.DefaultSort holds the default value on creation for the sort field.
	sysresource.DefaultSort = sysresourceDescSort.Default.(int32)
	// sysresourceDescStatus is the schema descriptor for status field.
	sysresourceDescStatus := sysresourceMixinFields2[0].Descriptor()
	// sysresource.DefaultStatus holds the default value on creation for the status field.
	sysresource.DefaultStatus = sysresourceDescStatus.Default.(int32)
	// sysresourceDescID is the schema descriptor for id field.
	sysresourceDescID := sysresourceMixinFields0[0].Descriptor()
	// sysresource.DefaultID holds the default value on creation for the id field.
	sysresource.DefaultID = sysresourceDescID.Default.(func() string)
	sysroleMixin := schematype.SysRole{}.Mixin()
	sysroleMixinFields0 := sysroleMixin[0].Fields()
	_ = sysroleMixinFields0
	sysroleMixinFields1 := sysroleMixin[1].Fields()
	_ = sysroleMixinFields1
	sysroleFields := schematype.SysRole{}.Fields()
	_ = sysroleFields
	// sysroleDescCreatedAt is the schema descriptor for created_at field.
	sysroleDescCreatedAt := sysroleMixinFields0[1].Descriptor()
	// sysrole.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysrole.DefaultCreatedAt = sysroleDescCreatedAt.Default.(time.Time)
	// sysroleDescUpdatedAt is the schema descriptor for updated_at field.
	sysroleDescUpdatedAt := sysroleMixinFields0[2].Descriptor()
	// sysrole.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysrole.DefaultUpdatedAt = sysroleDescUpdatedAt.Default.(func() time.Time)
	// sysrole.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysrole.UpdateDefaultUpdatedAt = sysroleDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysroleDescSort is the schema descriptor for sort field.
	sysroleDescSort := sysroleMixinFields1[0].Descriptor()
	// sysrole.DefaultSort holds the default value on creation for the sort field.
	sysrole.DefaultSort = sysroleDescSort.Default.(int32)
	// sysroleDescName is the schema descriptor for name field.
	sysroleDescName := sysroleFields[0].Descriptor()
	// sysrole.NameValidator is a validator for the "name" field. It is called by the builders before save.
	sysrole.NameValidator = sysroleDescName.Validators[0].(func(string) error)
	// sysroleDescTeamID is the schema descriptor for team_id field.
	sysroleDescTeamID := sysroleFields[4].Descriptor()
	// sysrole.TeamIDValidator is a validator for the "team_id" field. It is called by the builders before save.
	sysrole.TeamIDValidator = sysroleDescTeamID.Validators[0].(func(string) error)
	// sysroleDescApplicationID is the schema descriptor for application_id field.
	sysroleDescApplicationID := sysroleFields[5].Descriptor()
	// sysrole.ApplicationIDValidator is a validator for the "application_id" field. It is called by the builders before save.
	sysrole.ApplicationIDValidator = sysroleDescApplicationID.Validators[0].(func(string) error)
	// sysroleDescID is the schema descriptor for id field.
	sysroleDescID := sysroleMixinFields0[0].Descriptor()
	// sysrole.DefaultID holds the default value on creation for the id field.
	sysrole.DefaultID = sysroleDescID.Default.(func() string)
	sysrolemenuMixin := schematype.SysRoleMenu{}.Mixin()
	sysrolemenuMixinFields0 := sysrolemenuMixin[0].Fields()
	_ = sysrolemenuMixinFields0
	sysrolemenuFields := schematype.SysRoleMenu{}.Fields()
	_ = sysrolemenuFields
	// sysrolemenuDescCreatedAt is the schema descriptor for created_at field.
	sysrolemenuDescCreatedAt := sysrolemenuMixinFields0[1].Descriptor()
	// sysrolemenu.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysrolemenu.DefaultCreatedAt = sysrolemenuDescCreatedAt.Default.(time.Time)
	// sysrolemenuDescUpdatedAt is the schema descriptor for updated_at field.
	sysrolemenuDescUpdatedAt := sysrolemenuMixinFields0[2].Descriptor()
	// sysrolemenu.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysrolemenu.DefaultUpdatedAt = sysrolemenuDescUpdatedAt.Default.(func() time.Time)
	// sysrolemenu.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysrolemenu.UpdateDefaultUpdatedAt = sysrolemenuDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysrolemenuDescID is the schema descriptor for id field.
	sysrolemenuDescID := sysrolemenuMixinFields0[0].Descriptor()
	// sysrolemenu.DefaultID holds the default value on creation for the id field.
	sysrolemenu.DefaultID = sysrolemenuDescID.Default.(func() string)
	sysroleresourceMixin := schematype.SysRoleResource{}.Mixin()
	sysroleresourceMixinFields0 := sysroleresourceMixin[0].Fields()
	_ = sysroleresourceMixinFields0
	sysroleresourceFields := schematype.SysRoleResource{}.Fields()
	_ = sysroleresourceFields
	// sysroleresourceDescCreatedAt is the schema descriptor for created_at field.
	sysroleresourceDescCreatedAt := sysroleresourceMixinFields0[1].Descriptor()
	// sysroleresource.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysroleresource.DefaultCreatedAt = sysroleresourceDescCreatedAt.Default.(time.Time)
	// sysroleresourceDescUpdatedAt is the schema descriptor for updated_at field.
	sysroleresourceDescUpdatedAt := sysroleresourceMixinFields0[2].Descriptor()
	// sysroleresource.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysroleresource.DefaultUpdatedAt = sysroleresourceDescUpdatedAt.Default.(func() time.Time)
	// sysroleresource.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysroleresource.UpdateDefaultUpdatedAt = sysroleresourceDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysroleresourceDescID is the schema descriptor for id field.
	sysroleresourceDescID := sysroleresourceMixinFields0[0].Descriptor()
	// sysroleresource.DefaultID holds the default value on creation for the id field.
	sysroleresource.DefaultID = sysroleresourceDescID.Default.(func() string)
	sysstreetMixin := schematype.SysStreet{}.Mixin()
	sysstreetMixinFields0 := sysstreetMixin[0].Fields()
	_ = sysstreetMixinFields0
	sysstreetFields := schematype.SysStreet{}.Fields()
	_ = sysstreetFields
	// sysstreetDescCreatedAt is the schema descriptor for created_at field.
	sysstreetDescCreatedAt := sysstreetMixinFields0[1].Descriptor()
	// sysstreet.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysstreet.DefaultCreatedAt = sysstreetDescCreatedAt.Default.(time.Time)
	// sysstreetDescUpdatedAt is the schema descriptor for updated_at field.
	sysstreetDescUpdatedAt := sysstreetMixinFields0[2].Descriptor()
	// sysstreet.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysstreet.DefaultUpdatedAt = sysstreetDescUpdatedAt.Default.(func() time.Time)
	// sysstreet.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysstreet.UpdateDefaultUpdatedAt = sysstreetDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysstreetDescID is the schema descriptor for id field.
	sysstreetDescID := sysstreetMixinFields0[0].Descriptor()
	// sysstreet.DefaultID holds the default value on creation for the id field.
	sysstreet.DefaultID = sysstreetDescID.Default.(func() string)
	systeamMixin := schematype.SysTeam{}.Mixin()
	systeamMixinFields0 := systeamMixin[0].Fields()
	_ = systeamMixinFields0
	systeamFields := schematype.SysTeam{}.Fields()
	_ = systeamFields
	// systeamDescCreatedAt is the schema descriptor for created_at field.
	systeamDescCreatedAt := systeamMixinFields0[1].Descriptor()
	// systeam.DefaultCreatedAt holds the default value on creation for the created_at field.
	systeam.DefaultCreatedAt = systeamDescCreatedAt.Default.(time.Time)
	// systeamDescUpdatedAt is the schema descriptor for updated_at field.
	systeamDescUpdatedAt := systeamMixinFields0[2].Descriptor()
	// systeam.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	systeam.DefaultUpdatedAt = systeamDescUpdatedAt.Default.(func() time.Time)
	// systeam.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	systeam.UpdateDefaultUpdatedAt = systeamDescUpdatedAt.UpdateDefault.(func() time.Time)
	// systeamDescID is the schema descriptor for id field.
	systeamDescID := systeamMixinFields0[0].Descriptor()
	// systeam.DefaultID holds the default value on creation for the id field.
	systeam.DefaultID = systeamDescID.Default.(func() string)
	systeamapplicationMixin := schematype.SysTeamApplication{}.Mixin()
	systeamapplicationMixinFields0 := systeamapplicationMixin[0].Fields()
	_ = systeamapplicationMixinFields0
	systeamapplicationFields := schematype.SysTeamApplication{}.Fields()
	_ = systeamapplicationFields
	// systeamapplicationDescCreatedAt is the schema descriptor for created_at field.
	systeamapplicationDescCreatedAt := systeamapplicationMixinFields0[1].Descriptor()
	// systeamapplication.DefaultCreatedAt holds the default value on creation for the created_at field.
	systeamapplication.DefaultCreatedAt = systeamapplicationDescCreatedAt.Default.(time.Time)
	// systeamapplicationDescUpdatedAt is the schema descriptor for updated_at field.
	systeamapplicationDescUpdatedAt := systeamapplicationMixinFields0[2].Descriptor()
	// systeamapplication.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	systeamapplication.DefaultUpdatedAt = systeamapplicationDescUpdatedAt.Default.(func() time.Time)
	// systeamapplication.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	systeamapplication.UpdateDefaultUpdatedAt = systeamapplicationDescUpdatedAt.UpdateDefault.(func() time.Time)
	// systeamapplicationDescID is the schema descriptor for id field.
	systeamapplicationDescID := systeamapplicationMixinFields0[0].Descriptor()
	// systeamapplication.DefaultID holds the default value on creation for the id field.
	systeamapplication.DefaultID = systeamapplicationDescID.Default.(func() string)
	sysuploadMixin := schematype.SysUpload{}.Mixin()
	sysuploadMixinFields0 := sysuploadMixin[0].Fields()
	_ = sysuploadMixinFields0
	sysuploadFields := schematype.SysUpload{}.Fields()
	_ = sysuploadFields
	// sysuploadDescCreatedAt is the schema descriptor for created_at field.
	sysuploadDescCreatedAt := sysuploadMixinFields0[1].Descriptor()
	// sysupload.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysupload.DefaultCreatedAt = sysuploadDescCreatedAt.Default.(time.Time)
	// sysuploadDescUpdatedAt is the schema descriptor for updated_at field.
	sysuploadDescUpdatedAt := sysuploadMixinFields0[2].Descriptor()
	// sysupload.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysupload.DefaultUpdatedAt = sysuploadDescUpdatedAt.Default.(func() time.Time)
	// sysupload.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysupload.UpdateDefaultUpdatedAt = sysuploadDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysuploadDescStatus is the schema descriptor for status field.
	sysuploadDescStatus := sysuploadFields[9].Descriptor()
	// sysupload.DefaultStatus holds the default value on creation for the status field.
	sysupload.DefaultStatus = sysuploadDescStatus.Default.(int)
	// sysuploadDescUploadedSize is the schema descriptor for uploaded_size field.
	sysuploadDescUploadedSize := sysuploadFields[10].Descriptor()
	// sysupload.DefaultUploadedSize holds the default value on creation for the uploaded_size field.
	sysupload.DefaultUploadedSize = sysuploadDescUploadedSize.Default.(int64)
	// sysuploadDescID is the schema descriptor for id field.
	sysuploadDescID := sysuploadMixinFields0[0].Descriptor()
	// sysupload.DefaultID holds the default value on creation for the id field.
	sysupload.DefaultID = sysuploadDescID.Default.(func() string)
	sysuserMixin := schematype.SysUser{}.Mixin()
	sysuserMixinFields0 := sysuserMixin[0].Fields()
	_ = sysuserMixinFields0
	sysuserMixinFields1 := sysuserMixin[1].Fields()
	_ = sysuserMixinFields1
	sysuserFields := schematype.SysUser{}.Fields()
	_ = sysuserFields
	// sysuserDescCreatedAt is the schema descriptor for created_at field.
	sysuserDescCreatedAt := sysuserMixinFields0[1].Descriptor()
	// sysuser.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysuser.DefaultCreatedAt = sysuserDescCreatedAt.Default.(time.Time)
	// sysuserDescUpdatedAt is the schema descriptor for updated_at field.
	sysuserDescUpdatedAt := sysuserMixinFields0[2].Descriptor()
	// sysuser.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysuser.DefaultUpdatedAt = sysuserDescUpdatedAt.Default.(func() time.Time)
	// sysuser.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysuser.UpdateDefaultUpdatedAt = sysuserDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysuserDescStatus is the schema descriptor for status field.
	sysuserDescStatus := sysuserMixinFields1[0].Descriptor()
	// sysuser.DefaultStatus holds the default value on creation for the status field.
	sysuser.DefaultStatus = sysuserDescStatus.Default.(int32)
	// sysuserDescUsername is the schema descriptor for username field.
	sysuserDescUsername := sysuserFields[0].Descriptor()
	// sysuser.UsernameValidator is a validator for the "username" field. It is called by the builders before save.
	sysuser.UsernameValidator = sysuserDescUsername.Validators[0].(func(string) error)
	// sysuserDescJobTitle is the schema descriptor for job_title field.
	sysuserDescJobTitle := sysuserFields[8].Descriptor()
	// sysuser.DefaultJobTitle holds the default value on creation for the job_title field.
	sysuser.DefaultJobTitle = sysuserDescJobTitle.Default.(string)
	// sysuserDescAllowNotify is the schema descriptor for allow_notify field.
	sysuserDescAllowNotify := sysuserFields[9].Descriptor()
	// sysuser.DefaultAllowNotify holds the default value on creation for the allow_notify field.
	sysuser.DefaultAllowNotify = sysuserDescAllowNotify.Default.(bool)
	// sysuserDescID is the schema descriptor for id field.
	sysuserDescID := sysuserMixinFields0[0].Descriptor()
	// sysuser.DefaultID holds the default value on creation for the id field.
	sysuser.DefaultID = sysuserDescID.Default.(func() string)
	sysuserauthMixin := schematype.SysUserAuth{}.Mixin()
	sysuserauthMixinFields0 := sysuserauthMixin[0].Fields()
	_ = sysuserauthMixinFields0
	sysuserauthFields := schematype.SysUserAuth{}.Fields()
	_ = sysuserauthFields
	// sysuserauthDescCreatedAt is the schema descriptor for created_at field.
	sysuserauthDescCreatedAt := sysuserauthMixinFields0[1].Descriptor()
	// sysuserauth.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysuserauth.DefaultCreatedAt = sysuserauthDescCreatedAt.Default.(time.Time)
	// sysuserauthDescUpdatedAt is the schema descriptor for updated_at field.
	sysuserauthDescUpdatedAt := sysuserauthMixinFields0[2].Descriptor()
	// sysuserauth.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysuserauth.DefaultUpdatedAt = sysuserauthDescUpdatedAt.Default.(func() time.Time)
	// sysuserauth.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysuserauth.UpdateDefaultUpdatedAt = sysuserauthDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysuserauthDescID is the schema descriptor for id field.
	sysuserauthDescID := sysuserauthMixinFields0[0].Descriptor()
	// sysuserauth.DefaultID holds the default value on creation for the id field.
	sysuserauth.DefaultID = sysuserauthDescID.Default.(func() string)
	sysuserlogMixin := schematype.SysUserLog{}.Mixin()
	sysuserlogMixinFields0 := sysuserlogMixin[0].Fields()
	_ = sysuserlogMixinFields0
	sysuserlogFields := schematype.SysUserLog{}.Fields()
	_ = sysuserlogFields
	// sysuserlogDescCreatedAt is the schema descriptor for created_at field.
	sysuserlogDescCreatedAt := sysuserlogMixinFields0[1].Descriptor()
	// sysuserlog.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysuserlog.DefaultCreatedAt = sysuserlogDescCreatedAt.Default.(time.Time)
	// sysuserlogDescUpdatedAt is the schema descriptor for updated_at field.
	sysuserlogDescUpdatedAt := sysuserlogMixinFields0[2].Descriptor()
	// sysuserlog.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysuserlog.DefaultUpdatedAt = sysuserlogDescUpdatedAt.Default.(func() time.Time)
	// sysuserlog.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysuserlog.UpdateDefaultUpdatedAt = sysuserlogDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysuserlogDescUsername is the schema descriptor for username field.
	sysuserlogDescUsername := sysuserlogFields[1].Descriptor()
	// sysuserlog.UsernameValidator is a validator for the "username" field. It is called by the builders before save.
	sysuserlog.UsernameValidator = sysuserlogDescUsername.Validators[0].(func(string) error)
	// sysuserlogDescID is the schema descriptor for id field.
	sysuserlogDescID := sysuserlogMixinFields0[0].Descriptor()
	// sysuserlog.DefaultID holds the default value on creation for the id field.
	sysuserlog.DefaultID = sysuserlogDescID.Default.(func() string)
	sysuserorganizationMixin := schematype.SysUserOrganization{}.Mixin()
	sysuserorganizationMixinFields0 := sysuserorganizationMixin[0].Fields()
	_ = sysuserorganizationMixinFields0
	sysuserorganizationFields := schematype.SysUserOrganization{}.Fields()
	_ = sysuserorganizationFields
	// sysuserorganizationDescCreatedAt is the schema descriptor for created_at field.
	sysuserorganizationDescCreatedAt := sysuserorganizationMixinFields0[1].Descriptor()
	// sysuserorganization.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysuserorganization.DefaultCreatedAt = sysuserorganizationDescCreatedAt.Default.(time.Time)
	// sysuserorganizationDescUpdatedAt is the schema descriptor for updated_at field.
	sysuserorganizationDescUpdatedAt := sysuserorganizationMixinFields0[2].Descriptor()
	// sysuserorganization.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysuserorganization.DefaultUpdatedAt = sysuserorganizationDescUpdatedAt.Default.(func() time.Time)
	// sysuserorganization.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysuserorganization.UpdateDefaultUpdatedAt = sysuserorganizationDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysuserorganizationDescApplicationID is the schema descriptor for application_id field.
	sysuserorganizationDescApplicationID := sysuserorganizationFields[3].Descriptor()
	// sysuserorganization.ApplicationIDValidator is a validator for the "application_id" field. It is called by the builders before save.
	sysuserorganization.ApplicationIDValidator = sysuserorganizationDescApplicationID.Validators[0].(func(string) error)
	// sysuserorganizationDescIsMain is the schema descriptor for is_main field.
	sysuserorganizationDescIsMain := sysuserorganizationFields[5].Descriptor()
	// sysuserorganization.DefaultIsMain holds the default value on creation for the is_main field.
	sysuserorganization.DefaultIsMain = sysuserorganizationDescIsMain.Default.(bool)
	// sysuserorganizationDescID is the schema descriptor for id field.
	sysuserorganizationDescID := sysuserorganizationMixinFields0[0].Descriptor()
	// sysuserorganization.DefaultID holds the default value on creation for the id field.
	sysuserorganization.DefaultID = sysuserorganizationDescID.Default.(func() string)
	sysuserroleMixin := schematype.SysUserRole{}.Mixin()
	sysuserroleMixinFields0 := sysuserroleMixin[0].Fields()
	_ = sysuserroleMixinFields0
	sysuserroleFields := schematype.SysUserRole{}.Fields()
	_ = sysuserroleFields
	// sysuserroleDescCreatedAt is the schema descriptor for created_at field.
	sysuserroleDescCreatedAt := sysuserroleMixinFields0[1].Descriptor()
	// sysuserrole.DefaultCreatedAt holds the default value on creation for the created_at field.
	sysuserrole.DefaultCreatedAt = sysuserroleDescCreatedAt.Default.(time.Time)
	// sysuserroleDescUpdatedAt is the schema descriptor for updated_at field.
	sysuserroleDescUpdatedAt := sysuserroleMixinFields0[2].Descriptor()
	// sysuserrole.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	sysuserrole.DefaultUpdatedAt = sysuserroleDescUpdatedAt.Default.(func() time.Time)
	// sysuserrole.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	sysuserrole.UpdateDefaultUpdatedAt = sysuserroleDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sysuserroleDescApplicationID is the schema descriptor for application_id field.
	sysuserroleDescApplicationID := sysuserroleFields[3].Descriptor()
	// sysuserrole.ApplicationIDValidator is a validator for the "application_id" field. It is called by the builders before save.
	sysuserrole.ApplicationIDValidator = sysuserroleDescApplicationID.Validators[0].(func(string) error)
	// sysuserroleDescID is the schema descriptor for id field.
	sysuserroleDescID := sysuserroleMixinFields0[0].Descriptor()
	// sysuserrole.DefaultID holds the default value on creation for the id field.
	sysuserrole.DefaultID = sysuserroleDescID.Default.(func() string)
	wmsaccessdoorlogMixin := schematype.WmsAccessDoorLog{}.Mixin()
	wmsaccessdoorlogMixinFields0 := wmsaccessdoorlogMixin[0].Fields()
	_ = wmsaccessdoorlogMixinFields0
	wmsaccessdoorlogFields := schematype.WmsAccessDoorLog{}.Fields()
	_ = wmsaccessdoorlogFields
	// wmsaccessdoorlogDescCreatedAt is the schema descriptor for created_at field.
	wmsaccessdoorlogDescCreatedAt := wmsaccessdoorlogMixinFields0[1].Descriptor()
	// wmsaccessdoorlog.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsaccessdoorlog.DefaultCreatedAt = wmsaccessdoorlogDescCreatedAt.Default.(time.Time)
	// wmsaccessdoorlogDescUpdatedAt is the schema descriptor for updated_at field.
	wmsaccessdoorlogDescUpdatedAt := wmsaccessdoorlogMixinFields0[2].Descriptor()
	// wmsaccessdoorlog.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsaccessdoorlog.DefaultUpdatedAt = wmsaccessdoorlogDescUpdatedAt.Default.(func() time.Time)
	// wmsaccessdoorlog.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsaccessdoorlog.UpdateDefaultUpdatedAt = wmsaccessdoorlogDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsaccessdoorlogDescID is the schema descriptor for id field.
	wmsaccessdoorlogDescID := wmsaccessdoorlogMixinFields0[0].Descriptor()
	// wmsaccessdoorlog.DefaultID holds the default value on creation for the id field.
	wmsaccessdoorlog.DefaultID = wmsaccessdoorlogDescID.Default.(func() string)
	wmsapprovaltaskMixin := schematype.WmsApprovalTask{}.Mixin()
	wmsapprovaltaskMixinFields0 := wmsapprovaltaskMixin[0].Fields()
	_ = wmsapprovaltaskMixinFields0
	wmsapprovaltaskMixinFields2 := wmsapprovaltaskMixin[2].Fields()
	_ = wmsapprovaltaskMixinFields2
	wmsapprovaltaskFields := schematype.WmsApprovalTask{}.Fields()
	_ = wmsapprovaltaskFields
	// wmsapprovaltaskDescCreatedAt is the schema descriptor for created_at field.
	wmsapprovaltaskDescCreatedAt := wmsapprovaltaskMixinFields0[1].Descriptor()
	// wmsapprovaltask.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsapprovaltask.DefaultCreatedAt = wmsapprovaltaskDescCreatedAt.Default.(time.Time)
	// wmsapprovaltaskDescUpdatedAt is the schema descriptor for updated_at field.
	wmsapprovaltaskDescUpdatedAt := wmsapprovaltaskMixinFields0[2].Descriptor()
	// wmsapprovaltask.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsapprovaltask.DefaultUpdatedAt = wmsapprovaltaskDescUpdatedAt.Default.(func() time.Time)
	// wmsapprovaltask.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsapprovaltask.UpdateDefaultUpdatedAt = wmsapprovaltaskDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsapprovaltaskDescStatus is the schema descriptor for status field.
	wmsapprovaltaskDescStatus := wmsapprovaltaskMixinFields2[0].Descriptor()
	// wmsapprovaltask.DefaultStatus holds the default value on creation for the status field.
	wmsapprovaltask.DefaultStatus = wmsapprovaltaskDescStatus.Default.(int32)
	// wmsapprovaltaskDescID is the schema descriptor for id field.
	wmsapprovaltaskDescID := wmsapprovaltaskMixinFields0[0].Descriptor()
	// wmsapprovaltask.DefaultID holds the default value on creation for the id field.
	wmsapprovaltask.DefaultID = wmsapprovaltaskDescID.Default.(func() string)
	wmsapprovaltaskdetailMixin := schematype.WmsApprovalTaskDetail{}.Mixin()
	wmsapprovaltaskdetailMixinFields0 := wmsapprovaltaskdetailMixin[0].Fields()
	_ = wmsapprovaltaskdetailMixinFields0
	wmsapprovaltaskdetailMixinFields2 := wmsapprovaltaskdetailMixin[2].Fields()
	_ = wmsapprovaltaskdetailMixinFields2
	wmsapprovaltaskdetailFields := schematype.WmsApprovalTaskDetail{}.Fields()
	_ = wmsapprovaltaskdetailFields
	// wmsapprovaltaskdetailDescCreatedAt is the schema descriptor for created_at field.
	wmsapprovaltaskdetailDescCreatedAt := wmsapprovaltaskdetailMixinFields0[1].Descriptor()
	// wmsapprovaltaskdetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsapprovaltaskdetail.DefaultCreatedAt = wmsapprovaltaskdetailDescCreatedAt.Default.(time.Time)
	// wmsapprovaltaskdetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmsapprovaltaskdetailDescUpdatedAt := wmsapprovaltaskdetailMixinFields0[2].Descriptor()
	// wmsapprovaltaskdetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsapprovaltaskdetail.DefaultUpdatedAt = wmsapprovaltaskdetailDescUpdatedAt.Default.(func() time.Time)
	// wmsapprovaltaskdetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsapprovaltaskdetail.UpdateDefaultUpdatedAt = wmsapprovaltaskdetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsapprovaltaskdetailDescStatus is the schema descriptor for status field.
	wmsapprovaltaskdetailDescStatus := wmsapprovaltaskdetailMixinFields2[0].Descriptor()
	// wmsapprovaltaskdetail.DefaultStatus holds the default value on creation for the status field.
	wmsapprovaltaskdetail.DefaultStatus = wmsapprovaltaskdetailDescStatus.Default.(int32)
	// wmsapprovaltaskdetailDescFeature is the schema descriptor for feature field.
	wmsapprovaltaskdetailDescFeature := wmsapprovaltaskdetailFields[14].Descriptor()
	// wmsapprovaltaskdetail.DefaultFeature holds the default value on creation for the feature field.
	wmsapprovaltaskdetail.DefaultFeature = wmsapprovaltaskdetailDescFeature.Default.(map[string]interface{})
	// wmsapprovaltaskdetailDescID is the schema descriptor for id field.
	wmsapprovaltaskdetailDescID := wmsapprovaltaskdetailMixinFields0[0].Descriptor()
	// wmsapprovaltaskdetail.DefaultID holds the default value on creation for the id field.
	wmsapprovaltaskdetail.DefaultID = wmsapprovaltaskdetailDescID.Default.(func() string)
	wmsapprovaltaskoperationMixin := schematype.WmsApprovalTaskOperation{}.Mixin()
	wmsapprovaltaskoperationMixinFields0 := wmsapprovaltaskoperationMixin[0].Fields()
	_ = wmsapprovaltaskoperationMixinFields0
	wmsapprovaltaskoperationMixinFields1 := wmsapprovaltaskoperationMixin[1].Fields()
	_ = wmsapprovaltaskoperationMixinFields1
	wmsapprovaltaskoperationFields := schematype.WmsApprovalTaskOperation{}.Fields()
	_ = wmsapprovaltaskoperationFields
	// wmsapprovaltaskoperationDescCreatedAt is the schema descriptor for created_at field.
	wmsapprovaltaskoperationDescCreatedAt := wmsapprovaltaskoperationMixinFields0[1].Descriptor()
	// wmsapprovaltaskoperation.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsapprovaltaskoperation.DefaultCreatedAt = wmsapprovaltaskoperationDescCreatedAt.Default.(time.Time)
	// wmsapprovaltaskoperationDescUpdatedAt is the schema descriptor for updated_at field.
	wmsapprovaltaskoperationDescUpdatedAt := wmsapprovaltaskoperationMixinFields0[2].Descriptor()
	// wmsapprovaltaskoperation.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsapprovaltaskoperation.DefaultUpdatedAt = wmsapprovaltaskoperationDescUpdatedAt.Default.(func() time.Time)
	// wmsapprovaltaskoperation.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsapprovaltaskoperation.UpdateDefaultUpdatedAt = wmsapprovaltaskoperationDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsapprovaltaskoperationDescStatus is the schema descriptor for status field.
	wmsapprovaltaskoperationDescStatus := wmsapprovaltaskoperationMixinFields1[0].Descriptor()
	// wmsapprovaltaskoperation.DefaultStatus holds the default value on creation for the status field.
	wmsapprovaltaskoperation.DefaultStatus = wmsapprovaltaskoperationDescStatus.Default.(int32)
	// wmsapprovaltaskoperationDescOperationTime is the schema descriptor for operation_time field.
	wmsapprovaltaskoperationDescOperationTime := wmsapprovaltaskoperationFields[0].Descriptor()
	// wmsapprovaltaskoperation.DefaultOperationTime holds the default value on creation for the operation_time field.
	wmsapprovaltaskoperation.DefaultOperationTime = wmsapprovaltaskoperationDescOperationTime.Default.(func() time.Time)
	// wmsapprovaltaskoperationDescID is the schema descriptor for id field.
	wmsapprovaltaskoperationDescID := wmsapprovaltaskoperationMixinFields0[0].Descriptor()
	// wmsapprovaltaskoperation.DefaultID holds the default value on creation for the id field.
	wmsapprovaltaskoperation.DefaultID = wmsapprovaltaskoperationDescID.Default.(func() string)
	wmsapprovaltaskremarkMixin := schematype.WmsApprovalTaskRemark{}.Mixin()
	wmsapprovaltaskremarkMixinFields0 := wmsapprovaltaskremarkMixin[0].Fields()
	_ = wmsapprovaltaskremarkMixinFields0
	wmsapprovaltaskremarkFields := schematype.WmsApprovalTaskRemark{}.Fields()
	_ = wmsapprovaltaskremarkFields
	// wmsapprovaltaskremarkDescCreatedAt is the schema descriptor for created_at field.
	wmsapprovaltaskremarkDescCreatedAt := wmsapprovaltaskremarkMixinFields0[1].Descriptor()
	// wmsapprovaltaskremark.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsapprovaltaskremark.DefaultCreatedAt = wmsapprovaltaskremarkDescCreatedAt.Default.(time.Time)
	// wmsapprovaltaskremarkDescUpdatedAt is the schema descriptor for updated_at field.
	wmsapprovaltaskremarkDescUpdatedAt := wmsapprovaltaskremarkMixinFields0[2].Descriptor()
	// wmsapprovaltaskremark.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsapprovaltaskremark.DefaultUpdatedAt = wmsapprovaltaskremarkDescUpdatedAt.Default.(func() time.Time)
	// wmsapprovaltaskremark.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsapprovaltaskremark.UpdateDefaultUpdatedAt = wmsapprovaltaskremarkDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsapprovaltaskremarkDescID is the schema descriptor for id field.
	wmsapprovaltaskremarkDescID := wmsapprovaltaskremarkMixinFields0[0].Descriptor()
	// wmsapprovaltaskremark.DefaultID holds the default value on creation for the id field.
	wmsapprovaltaskremark.DefaultID = wmsapprovaltaskremarkDescID.Default.(func() string)
	wmsauditplanMixin := schematype.WmsAuditPlan{}.Mixin()
	wmsauditplanMixinFields0 := wmsauditplanMixin[0].Fields()
	_ = wmsauditplanMixinFields0
	wmsauditplanMixinFields2 := wmsauditplanMixin[2].Fields()
	_ = wmsauditplanMixinFields2
	wmsauditplanFields := schematype.WmsAuditPlan{}.Fields()
	_ = wmsauditplanFields
	// wmsauditplanDescCreatedAt is the schema descriptor for created_at field.
	wmsauditplanDescCreatedAt := wmsauditplanMixinFields0[1].Descriptor()
	// wmsauditplan.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsauditplan.DefaultCreatedAt = wmsauditplanDescCreatedAt.Default.(time.Time)
	// wmsauditplanDescUpdatedAt is the schema descriptor for updated_at field.
	wmsauditplanDescUpdatedAt := wmsauditplanMixinFields0[2].Descriptor()
	// wmsauditplan.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsauditplan.DefaultUpdatedAt = wmsauditplanDescUpdatedAt.Default.(func() time.Time)
	// wmsauditplan.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsauditplan.UpdateDefaultUpdatedAt = wmsauditplanDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsauditplanDescStatus is the schema descriptor for status field.
	wmsauditplanDescStatus := wmsauditplanMixinFields2[0].Descriptor()
	// wmsauditplan.DefaultStatus holds the default value on creation for the status field.
	wmsauditplan.DefaultStatus = wmsauditplanDescStatus.Default.(int32)
	// wmsauditplanDescName is the schema descriptor for name field.
	wmsauditplanDescName := wmsauditplanFields[0].Descriptor()
	// wmsauditplan.NameValidator is a validator for the "name" field. It is called by the builders before save.
	wmsauditplan.NameValidator = wmsauditplanDescName.Validators[0].(func(string) error)
	// wmsauditplanDescID is the schema descriptor for id field.
	wmsauditplanDescID := wmsauditplanMixinFields0[0].Descriptor()
	// wmsauditplan.DefaultID holds the default value on creation for the id field.
	wmsauditplan.DefaultID = wmsauditplanDescID.Default.(func() string)
	wmsauditplandetailMixin := schematype.WmsAuditPlanDetail{}.Mixin()
	wmsauditplandetailMixinFields0 := wmsauditplandetailMixin[0].Fields()
	_ = wmsauditplandetailMixinFields0
	wmsauditplandetailMixinFields2 := wmsauditplandetailMixin[2].Fields()
	_ = wmsauditplandetailMixinFields2
	wmsauditplandetailFields := schematype.WmsAuditPlanDetail{}.Fields()
	_ = wmsauditplandetailFields
	// wmsauditplandetailDescCreatedAt is the schema descriptor for created_at field.
	wmsauditplandetailDescCreatedAt := wmsauditplandetailMixinFields0[1].Descriptor()
	// wmsauditplandetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsauditplandetail.DefaultCreatedAt = wmsauditplandetailDescCreatedAt.Default.(time.Time)
	// wmsauditplandetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmsauditplandetailDescUpdatedAt := wmsauditplandetailMixinFields0[2].Descriptor()
	// wmsauditplandetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsauditplandetail.DefaultUpdatedAt = wmsauditplandetailDescUpdatedAt.Default.(func() time.Time)
	// wmsauditplandetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsauditplandetail.UpdateDefaultUpdatedAt = wmsauditplandetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsauditplandetailDescStatus is the schema descriptor for status field.
	wmsauditplandetailDescStatus := wmsauditplandetailMixinFields2[0].Descriptor()
	// wmsauditplandetail.DefaultStatus holds the default value on creation for the status field.
	wmsauditplandetail.DefaultStatus = wmsauditplandetailDescStatus.Default.(int32)
	// wmsauditplandetailDescIsAudit is the schema descriptor for is_audit field.
	wmsauditplandetailDescIsAudit := wmsauditplandetailFields[4].Descriptor()
	// wmsauditplandetail.DefaultIsAudit holds the default value on creation for the is_audit field.
	wmsauditplandetail.DefaultIsAudit = wmsauditplandetailDescIsAudit.Default.(bool)
	// wmsauditplandetailDescIsReader is the schema descriptor for is_reader field.
	wmsauditplandetailDescIsReader := wmsauditplandetailFields[5].Descriptor()
	// wmsauditplandetail.DefaultIsReader holds the default value on creation for the is_reader field.
	wmsauditplandetail.DefaultIsReader = wmsauditplandetailDescIsReader.Default.(bool)
	// wmsauditplandetailDescNum is the schema descriptor for num field.
	wmsauditplandetailDescNum := wmsauditplandetailFields[16].Descriptor()
	// wmsauditplandetail.DefaultNum holds the default value on creation for the num field.
	wmsauditplandetail.DefaultNum = wmsauditplandetailDescNum.Default.(uint32)
	// wmsauditplandetailDescPrice is the schema descriptor for price field.
	wmsauditplandetailDescPrice := wmsauditplandetailFields[17].Descriptor()
	// wmsauditplandetail.DefaultPrice holds the default value on creation for the price field.
	wmsauditplandetail.DefaultPrice = wmsauditplandetailDescPrice.Default.(uint64)
	// wmsauditplandetailDescEquipmentStatus is the schema descriptor for equipment_status field.
	wmsauditplandetailDescEquipmentStatus := wmsauditplandetailFields[23].Descriptor()
	// wmsauditplandetail.DefaultEquipmentStatus holds the default value on creation for the equipment_status field.
	wmsauditplandetail.DefaultEquipmentStatus = wmsauditplandetailDescEquipmentStatus.Default.(int32)
	// wmsauditplandetailDescFeature is the schema descriptor for feature field.
	wmsauditplandetailDescFeature := wmsauditplandetailFields[25].Descriptor()
	// wmsauditplandetail.DefaultFeature holds the default value on creation for the feature field.
	wmsauditplandetail.DefaultFeature = wmsauditplandetailDescFeature.Default.(map[string]interface{})
	// wmsauditplandetailDescID is the schema descriptor for id field.
	wmsauditplandetailDescID := wmsauditplandetailMixinFields0[0].Descriptor()
	// wmsauditplandetail.DefaultID holds the default value on creation for the id field.
	wmsauditplandetail.DefaultID = wmsauditplandetailDescID.Default.(func() string)
	wmsborroworderMixin := schematype.WmsBorrowOrder{}.Mixin()
	wmsborroworderMixinFields0 := wmsborroworderMixin[0].Fields()
	_ = wmsborroworderMixinFields0
	wmsborroworderFields := schematype.WmsBorrowOrder{}.Fields()
	_ = wmsborroworderFields
	// wmsborroworderDescCreatedAt is the schema descriptor for created_at field.
	wmsborroworderDescCreatedAt := wmsborroworderMixinFields0[1].Descriptor()
	// wmsborroworder.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsborroworder.DefaultCreatedAt = wmsborroworderDescCreatedAt.Default.(time.Time)
	// wmsborroworderDescUpdatedAt is the schema descriptor for updated_at field.
	wmsborroworderDescUpdatedAt := wmsborroworderMixinFields0[2].Descriptor()
	// wmsborroworder.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsborroworder.DefaultUpdatedAt = wmsborroworderDescUpdatedAt.Default.(func() time.Time)
	// wmsborroworder.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsborroworder.UpdateDefaultUpdatedAt = wmsborroworderDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsborroworderDescID is the schema descriptor for id field.
	wmsborroworderDescID := wmsborroworderMixinFields0[0].Descriptor()
	// wmsborroworder.DefaultID holds the default value on creation for the id field.
	wmsborroworder.DefaultID = wmsborroworderDescID.Default.(func() string)
	wmsborroworderdetailMixin := schematype.WmsBorrowOrderDetail{}.Mixin()
	wmsborroworderdetailMixinFields0 := wmsborroworderdetailMixin[0].Fields()
	_ = wmsborroworderdetailMixinFields0
	wmsborroworderdetailFields := schematype.WmsBorrowOrderDetail{}.Fields()
	_ = wmsborroworderdetailFields
	// wmsborroworderdetailDescCreatedAt is the schema descriptor for created_at field.
	wmsborroworderdetailDescCreatedAt := wmsborroworderdetailMixinFields0[1].Descriptor()
	// wmsborroworderdetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsborroworderdetail.DefaultCreatedAt = wmsborroworderdetailDescCreatedAt.Default.(time.Time)
	// wmsborroworderdetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmsborroworderdetailDescUpdatedAt := wmsborroworderdetailMixinFields0[2].Descriptor()
	// wmsborroworderdetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsborroworderdetail.DefaultUpdatedAt = wmsborroworderdetailDescUpdatedAt.Default.(func() time.Time)
	// wmsborroworderdetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsborroworderdetail.UpdateDefaultUpdatedAt = wmsborroworderdetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsborroworderdetailDescIsReturn is the schema descriptor for is_return field.
	wmsborroworderdetailDescIsReturn := wmsborroworderdetailFields[9].Descriptor()
	// wmsborroworderdetail.DefaultIsReturn holds the default value on creation for the is_return field.
	wmsborroworderdetail.DefaultIsReturn = wmsborroworderdetailDescIsReturn.Default.(bool)
	// wmsborroworderdetailDescFeature is the schema descriptor for feature field.
	wmsborroworderdetailDescFeature := wmsborroworderdetailFields[10].Descriptor()
	// wmsborroworderdetail.DefaultFeature holds the default value on creation for the feature field.
	wmsborroworderdetail.DefaultFeature = wmsborroworderdetailDescFeature.Default.(map[string]interface{})
	// wmsborroworderdetailDescID is the schema descriptor for id field.
	wmsborroworderdetailDescID := wmsborroworderdetailMixinFields0[0].Descriptor()
	// wmsborroworderdetail.DefaultID holds the default value on creation for the id field.
	wmsborroworderdetail.DefaultID = wmsborroworderdetailDescID.Default.(func() string)
	wmscarMixin := schematype.WmsCar{}.Mixin()
	wmscarMixinFields0 := wmscarMixin[0].Fields()
	_ = wmscarMixinFields0
	wmscarFields := schematype.WmsCar{}.Fields()
	_ = wmscarFields
	// wmscarDescCreatedAt is the schema descriptor for created_at field.
	wmscarDescCreatedAt := wmscarMixinFields0[1].Descriptor()
	// wmscar.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmscar.DefaultCreatedAt = wmscarDescCreatedAt.Default.(time.Time)
	// wmscarDescUpdatedAt is the schema descriptor for updated_at field.
	wmscarDescUpdatedAt := wmscarMixinFields0[2].Descriptor()
	// wmscar.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmscar.DefaultUpdatedAt = wmscarDescUpdatedAt.Default.(func() time.Time)
	// wmscar.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmscar.UpdateDefaultUpdatedAt = wmscarDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmscarDescColor is the schema descriptor for color field.
	wmscarDescColor := wmscarFields[1].Descriptor()
	// wmscar.DefaultColor holds the default value on creation for the color field.
	wmscar.DefaultColor = wmscarDescColor.Default.(string)
	// wmscarDescID is the schema descriptor for id field.
	wmscarDescID := wmscarMixinFields0[0].Descriptor()
	// wmscar.DefaultID holds the default value on creation for the id field.
	wmscar.DefaultID = wmscarDescID.Default.(func() string)
	wmsclaimorderMixin := schematype.WmsClaimOrder{}.Mixin()
	wmsclaimorderMixinFields0 := wmsclaimorderMixin[0].Fields()
	_ = wmsclaimorderMixinFields0
	wmsclaimorderFields := schematype.WmsClaimOrder{}.Fields()
	_ = wmsclaimorderFields
	// wmsclaimorderDescCreatedAt is the schema descriptor for created_at field.
	wmsclaimorderDescCreatedAt := wmsclaimorderMixinFields0[1].Descriptor()
	// wmsclaimorder.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsclaimorder.DefaultCreatedAt = wmsclaimorderDescCreatedAt.Default.(time.Time)
	// wmsclaimorderDescUpdatedAt is the schema descriptor for updated_at field.
	wmsclaimorderDescUpdatedAt := wmsclaimorderMixinFields0[2].Descriptor()
	// wmsclaimorder.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsclaimorder.DefaultUpdatedAt = wmsclaimorderDescUpdatedAt.Default.(func() time.Time)
	// wmsclaimorder.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsclaimorder.UpdateDefaultUpdatedAt = wmsclaimorderDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsclaimorderDescID is the schema descriptor for id field.
	wmsclaimorderDescID := wmsclaimorderMixinFields0[0].Descriptor()
	// wmsclaimorder.DefaultID holds the default value on creation for the id field.
	wmsclaimorder.DefaultID = wmsclaimorderDescID.Default.(func() string)
	wmsclaimorderdetailMixin := schematype.WmsClaimOrderDetail{}.Mixin()
	wmsclaimorderdetailMixinFields0 := wmsclaimorderdetailMixin[0].Fields()
	_ = wmsclaimorderdetailMixinFields0
	wmsclaimorderdetailFields := schematype.WmsClaimOrderDetail{}.Fields()
	_ = wmsclaimorderdetailFields
	// wmsclaimorderdetailDescCreatedAt is the schema descriptor for created_at field.
	wmsclaimorderdetailDescCreatedAt := wmsclaimorderdetailMixinFields0[1].Descriptor()
	// wmsclaimorderdetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsclaimorderdetail.DefaultCreatedAt = wmsclaimorderdetailDescCreatedAt.Default.(time.Time)
	// wmsclaimorderdetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmsclaimorderdetailDescUpdatedAt := wmsclaimorderdetailMixinFields0[2].Descriptor()
	// wmsclaimorderdetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsclaimorderdetail.DefaultUpdatedAt = wmsclaimorderdetailDescUpdatedAt.Default.(func() time.Time)
	// wmsclaimorderdetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsclaimorderdetail.UpdateDefaultUpdatedAt = wmsclaimorderdetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsclaimorderdetailDescFeature is the schema descriptor for feature field.
	wmsclaimorderdetailDescFeature := wmsclaimorderdetailFields[8].Descriptor()
	// wmsclaimorderdetail.DefaultFeature holds the default value on creation for the feature field.
	wmsclaimorderdetail.DefaultFeature = wmsclaimorderdetailDescFeature.Default.(map[string]interface{})
	// wmsclaimorderdetailDescID is the schema descriptor for id field.
	wmsclaimorderdetailDescID := wmsclaimorderdetailMixinFields0[0].Descriptor()
	// wmsclaimorderdetail.DefaultID holds the default value on creation for the id field.
	wmsclaimorderdetail.DefaultID = wmsclaimorderdetailDescID.Default.(func() string)
	wmscompanyMixin := schematype.WmsCompany{}.Mixin()
	wmscompanyMixinFields0 := wmscompanyMixin[0].Fields()
	_ = wmscompanyMixinFields0
	wmscompanyMixinFields1 := wmscompanyMixin[1].Fields()
	_ = wmscompanyMixinFields1
	wmscompanyFields := schematype.WmsCompany{}.Fields()
	_ = wmscompanyFields
	// wmscompanyDescCreatedAt is the schema descriptor for created_at field.
	wmscompanyDescCreatedAt := wmscompanyMixinFields0[1].Descriptor()
	// wmscompany.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmscompany.DefaultCreatedAt = wmscompanyDescCreatedAt.Default.(time.Time)
	// wmscompanyDescUpdatedAt is the schema descriptor for updated_at field.
	wmscompanyDescUpdatedAt := wmscompanyMixinFields0[2].Descriptor()
	// wmscompany.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmscompany.DefaultUpdatedAt = wmscompanyDescUpdatedAt.Default.(func() time.Time)
	// wmscompany.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmscompany.UpdateDefaultUpdatedAt = wmscompanyDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmscompanyDescStatus is the schema descriptor for status field.
	wmscompanyDescStatus := wmscompanyMixinFields1[0].Descriptor()
	// wmscompany.DefaultStatus holds the default value on creation for the status field.
	wmscompany.DefaultStatus = wmscompanyDescStatus.Default.(int32)
	// wmscompanyDescID is the schema descriptor for id field.
	wmscompanyDescID := wmscompanyMixinFields0[0].Descriptor()
	// wmscompany.DefaultID holds the default value on creation for the id field.
	wmscompany.DefaultID = wmscompanyDescID.Default.(func() string)
	wmscompanyaddressMixin := schematype.WmsCompanyAddress{}.Mixin()
	wmscompanyaddressMixinFields0 := wmscompanyaddressMixin[0].Fields()
	_ = wmscompanyaddressMixinFields0
	wmscompanyaddressFields := schematype.WmsCompanyAddress{}.Fields()
	_ = wmscompanyaddressFields
	// wmscompanyaddressDescCreatedAt is the schema descriptor for created_at field.
	wmscompanyaddressDescCreatedAt := wmscompanyaddressMixinFields0[1].Descriptor()
	// wmscompanyaddress.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmscompanyaddress.DefaultCreatedAt = wmscompanyaddressDescCreatedAt.Default.(time.Time)
	// wmscompanyaddressDescUpdatedAt is the schema descriptor for updated_at field.
	wmscompanyaddressDescUpdatedAt := wmscompanyaddressMixinFields0[2].Descriptor()
	// wmscompanyaddress.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmscompanyaddress.DefaultUpdatedAt = wmscompanyaddressDescUpdatedAt.Default.(func() time.Time)
	// wmscompanyaddress.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmscompanyaddress.UpdateDefaultUpdatedAt = wmscompanyaddressDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmscompanyaddressDescID is the schema descriptor for id field.
	wmscompanyaddressDescID := wmscompanyaddressMixinFields0[0].Descriptor()
	// wmscompanyaddress.DefaultID holds the default value on creation for the id field.
	wmscompanyaddress.DefaultID = wmscompanyaddressDescID.Default.(func() string)
	wmsdiscardmeetingMixin := schematype.WmsDiscardMeeting{}.Mixin()
	wmsdiscardmeetingMixinFields0 := wmsdiscardmeetingMixin[0].Fields()
	_ = wmsdiscardmeetingMixinFields0
	wmsdiscardmeetingMixinFields3 := wmsdiscardmeetingMixin[3].Fields()
	_ = wmsdiscardmeetingMixinFields3
	wmsdiscardmeetingFields := schematype.WmsDiscardMeeting{}.Fields()
	_ = wmsdiscardmeetingFields
	// wmsdiscardmeetingDescCreatedAt is the schema descriptor for created_at field.
	wmsdiscardmeetingDescCreatedAt := wmsdiscardmeetingMixinFields0[1].Descriptor()
	// wmsdiscardmeeting.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsdiscardmeeting.DefaultCreatedAt = wmsdiscardmeetingDescCreatedAt.Default.(time.Time)
	// wmsdiscardmeetingDescUpdatedAt is the schema descriptor for updated_at field.
	wmsdiscardmeetingDescUpdatedAt := wmsdiscardmeetingMixinFields0[2].Descriptor()
	// wmsdiscardmeeting.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsdiscardmeeting.DefaultUpdatedAt = wmsdiscardmeetingDescUpdatedAt.Default.(func() time.Time)
	// wmsdiscardmeeting.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsdiscardmeeting.UpdateDefaultUpdatedAt = wmsdiscardmeetingDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsdiscardmeetingDescStatus is the schema descriptor for status field.
	wmsdiscardmeetingDescStatus := wmsdiscardmeetingMixinFields3[0].Descriptor()
	// wmsdiscardmeeting.DefaultStatus holds the default value on creation for the status field.
	wmsdiscardmeeting.DefaultStatus = wmsdiscardmeetingDescStatus.Default.(int32)
	// wmsdiscardmeetingDescID is the schema descriptor for id field.
	wmsdiscardmeetingDescID := wmsdiscardmeetingMixinFields0[0].Descriptor()
	// wmsdiscardmeeting.DefaultID holds the default value on creation for the id field.
	wmsdiscardmeeting.DefaultID = wmsdiscardmeetingDescID.Default.(func() string)
	wmsdiscardmeetingdetailMixin := schematype.WmsDiscardMeetingDetail{}.Mixin()
	wmsdiscardmeetingdetailMixinFields0 := wmsdiscardmeetingdetailMixin[0].Fields()
	_ = wmsdiscardmeetingdetailMixinFields0
	wmsdiscardmeetingdetailFields := schematype.WmsDiscardMeetingDetail{}.Fields()
	_ = wmsdiscardmeetingdetailFields
	// wmsdiscardmeetingdetailDescCreatedAt is the schema descriptor for created_at field.
	wmsdiscardmeetingdetailDescCreatedAt := wmsdiscardmeetingdetailMixinFields0[1].Descriptor()
	// wmsdiscardmeetingdetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsdiscardmeetingdetail.DefaultCreatedAt = wmsdiscardmeetingdetailDescCreatedAt.Default.(time.Time)
	// wmsdiscardmeetingdetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmsdiscardmeetingdetailDescUpdatedAt := wmsdiscardmeetingdetailMixinFields0[2].Descriptor()
	// wmsdiscardmeetingdetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsdiscardmeetingdetail.DefaultUpdatedAt = wmsdiscardmeetingdetailDescUpdatedAt.Default.(func() time.Time)
	// wmsdiscardmeetingdetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsdiscardmeetingdetail.UpdateDefaultUpdatedAt = wmsdiscardmeetingdetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsdiscardmeetingdetailDescFeature is the schema descriptor for feature field.
	wmsdiscardmeetingdetailDescFeature := wmsdiscardmeetingdetailFields[18].Descriptor()
	// wmsdiscardmeetingdetail.DefaultFeature holds the default value on creation for the feature field.
	wmsdiscardmeetingdetail.DefaultFeature = wmsdiscardmeetingdetailDescFeature.Default.(map[string]interface{})
	// wmsdiscardmeetingdetailDescID is the schema descriptor for id field.
	wmsdiscardmeetingdetailDescID := wmsdiscardmeetingdetailMixinFields0[0].Descriptor()
	// wmsdiscardmeetingdetail.DefaultID holds the default value on creation for the id field.
	wmsdiscardmeetingdetail.DefaultID = wmsdiscardmeetingdetailDescID.Default.(func() string)
	wmsdiscardorderMixin := schematype.WmsDiscardOrder{}.Mixin()
	wmsdiscardorderMixinFields0 := wmsdiscardorderMixin[0].Fields()
	_ = wmsdiscardorderMixinFields0
	wmsdiscardorderFields := schematype.WmsDiscardOrder{}.Fields()
	_ = wmsdiscardorderFields
	// wmsdiscardorderDescCreatedAt is the schema descriptor for created_at field.
	wmsdiscardorderDescCreatedAt := wmsdiscardorderMixinFields0[1].Descriptor()
	// wmsdiscardorder.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsdiscardorder.DefaultCreatedAt = wmsdiscardorderDescCreatedAt.Default.(time.Time)
	// wmsdiscardorderDescUpdatedAt is the schema descriptor for updated_at field.
	wmsdiscardorderDescUpdatedAt := wmsdiscardorderMixinFields0[2].Descriptor()
	// wmsdiscardorder.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsdiscardorder.DefaultUpdatedAt = wmsdiscardorderDescUpdatedAt.Default.(func() time.Time)
	// wmsdiscardorder.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsdiscardorder.UpdateDefaultUpdatedAt = wmsdiscardorderDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsdiscardorderDescID is the schema descriptor for id field.
	wmsdiscardorderDescID := wmsdiscardorderMixinFields0[0].Descriptor()
	// wmsdiscardorder.DefaultID holds the default value on creation for the id field.
	wmsdiscardorder.DefaultID = wmsdiscardorderDescID.Default.(func() string)
	wmsdiscardorderdetailMixin := schematype.WmsDiscardOrderDetail{}.Mixin()
	wmsdiscardorderdetailMixinFields0 := wmsdiscardorderdetailMixin[0].Fields()
	_ = wmsdiscardorderdetailMixinFields0
	wmsdiscardorderdetailFields := schematype.WmsDiscardOrderDetail{}.Fields()
	_ = wmsdiscardorderdetailFields
	// wmsdiscardorderdetailDescCreatedAt is the schema descriptor for created_at field.
	wmsdiscardorderdetailDescCreatedAt := wmsdiscardorderdetailMixinFields0[1].Descriptor()
	// wmsdiscardorderdetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsdiscardorderdetail.DefaultCreatedAt = wmsdiscardorderdetailDescCreatedAt.Default.(time.Time)
	// wmsdiscardorderdetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmsdiscardorderdetailDescUpdatedAt := wmsdiscardorderdetailMixinFields0[2].Descriptor()
	// wmsdiscardorderdetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsdiscardorderdetail.DefaultUpdatedAt = wmsdiscardorderdetailDescUpdatedAt.Default.(func() time.Time)
	// wmsdiscardorderdetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsdiscardorderdetail.UpdateDefaultUpdatedAt = wmsdiscardorderdetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsdiscardorderdetailDescFeature is the schema descriptor for feature field.
	wmsdiscardorderdetailDescFeature := wmsdiscardorderdetailFields[6].Descriptor()
	// wmsdiscardorderdetail.DefaultFeature holds the default value on creation for the feature field.
	wmsdiscardorderdetail.DefaultFeature = wmsdiscardorderdetailDescFeature.Default.(map[string]interface{})
	// wmsdiscardorderdetailDescID is the schema descriptor for id field.
	wmsdiscardorderdetailDescID := wmsdiscardorderdetailMixinFields0[0].Descriptor()
	// wmsdiscardorderdetail.DefaultID holds the default value on creation for the id field.
	wmsdiscardorderdetail.DefaultID = wmsdiscardorderdetailDescID.Default.(func() string)
	wmsdiscardplanorderMixin := schematype.WmsDiscardPlanOrder{}.Mixin()
	wmsdiscardplanorderMixinFields0 := wmsdiscardplanorderMixin[0].Fields()
	_ = wmsdiscardplanorderMixinFields0
	wmsdiscardplanorderFields := schematype.WmsDiscardPlanOrder{}.Fields()
	_ = wmsdiscardplanorderFields
	// wmsdiscardplanorderDescCreatedAt is the schema descriptor for created_at field.
	wmsdiscardplanorderDescCreatedAt := wmsdiscardplanorderMixinFields0[1].Descriptor()
	// wmsdiscardplanorder.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsdiscardplanorder.DefaultCreatedAt = wmsdiscardplanorderDescCreatedAt.Default.(time.Time)
	// wmsdiscardplanorderDescUpdatedAt is the schema descriptor for updated_at field.
	wmsdiscardplanorderDescUpdatedAt := wmsdiscardplanorderMixinFields0[2].Descriptor()
	// wmsdiscardplanorder.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsdiscardplanorder.DefaultUpdatedAt = wmsdiscardplanorderDescUpdatedAt.Default.(func() time.Time)
	// wmsdiscardplanorder.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsdiscardplanorder.UpdateDefaultUpdatedAt = wmsdiscardplanorderDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsdiscardplanorderDescID is the schema descriptor for id field.
	wmsdiscardplanorderDescID := wmsdiscardplanorderMixinFields0[0].Descriptor()
	// wmsdiscardplanorder.DefaultID holds the default value on creation for the id field.
	wmsdiscardplanorder.DefaultID = wmsdiscardplanorderDescID.Default.(func() string)
	wmsdiscardplanorderdetailMixin := schematype.WmsDiscardPlanOrderDetail{}.Mixin()
	wmsdiscardplanorderdetailMixinFields0 := wmsdiscardplanorderdetailMixin[0].Fields()
	_ = wmsdiscardplanorderdetailMixinFields0
	wmsdiscardplanorderdetailFields := schematype.WmsDiscardPlanOrderDetail{}.Fields()
	_ = wmsdiscardplanorderdetailFields
	// wmsdiscardplanorderdetailDescCreatedAt is the schema descriptor for created_at field.
	wmsdiscardplanorderdetailDescCreatedAt := wmsdiscardplanorderdetailMixinFields0[1].Descriptor()
	// wmsdiscardplanorderdetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsdiscardplanorderdetail.DefaultCreatedAt = wmsdiscardplanorderdetailDescCreatedAt.Default.(time.Time)
	// wmsdiscardplanorderdetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmsdiscardplanorderdetailDescUpdatedAt := wmsdiscardplanorderdetailMixinFields0[2].Descriptor()
	// wmsdiscardplanorderdetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsdiscardplanorderdetail.DefaultUpdatedAt = wmsdiscardplanorderdetailDescUpdatedAt.Default.(func() time.Time)
	// wmsdiscardplanorderdetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsdiscardplanorderdetail.UpdateDefaultUpdatedAt = wmsdiscardplanorderdetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsdiscardplanorderdetailDescFeature is the schema descriptor for feature field.
	wmsdiscardplanorderdetailDescFeature := wmsdiscardplanorderdetailFields[18].Descriptor()
	// wmsdiscardplanorderdetail.DefaultFeature holds the default value on creation for the feature field.
	wmsdiscardplanorderdetail.DefaultFeature = wmsdiscardplanorderdetailDescFeature.Default.(map[string]interface{})
	// wmsdiscardplanorderdetailDescID is the schema descriptor for id field.
	wmsdiscardplanorderdetailDescID := wmsdiscardplanorderdetailMixinFields0[0].Descriptor()
	// wmsdiscardplanorderdetail.DefaultID holds the default value on creation for the id field.
	wmsdiscardplanorderdetail.DefaultID = wmsdiscardplanorderdetailDescID.Default.(func() string)
	wmsdocumentMixin := schematype.WmsDocument{}.Mixin()
	wmsdocumentMixinFields0 := wmsdocumentMixin[0].Fields()
	_ = wmsdocumentMixinFields0
	wmsdocumentFields := schematype.WmsDocument{}.Fields()
	_ = wmsdocumentFields
	// wmsdocumentDescCreatedAt is the schema descriptor for created_at field.
	wmsdocumentDescCreatedAt := wmsdocumentMixinFields0[1].Descriptor()
	// wmsdocument.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsdocument.DefaultCreatedAt = wmsdocumentDescCreatedAt.Default.(time.Time)
	// wmsdocumentDescUpdatedAt is the schema descriptor for updated_at field.
	wmsdocumentDescUpdatedAt := wmsdocumentMixinFields0[2].Descriptor()
	// wmsdocument.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsdocument.DefaultUpdatedAt = wmsdocumentDescUpdatedAt.Default.(func() time.Time)
	// wmsdocument.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsdocument.UpdateDefaultUpdatedAt = wmsdocumentDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsdocumentDescURL is the schema descriptor for url field.
	wmsdocumentDescURL := wmsdocumentFields[0].Descriptor()
	// wmsdocument.URLValidator is a validator for the "url" field. It is called by the builders before save.
	wmsdocument.URLValidator = wmsdocumentDescURL.Validators[0].(func(string) error)
	// wmsdocumentDescType is the schema descriptor for type field.
	wmsdocumentDescType := wmsdocumentFields[1].Descriptor()
	// wmsdocument.TypeValidator is a validator for the "type" field. It is called by the builders before save.
	wmsdocument.TypeValidator = wmsdocumentDescType.Validators[0].(func(string) error)
	// wmsdocumentDescEnterWay is the schema descriptor for enter_way field.
	wmsdocumentDescEnterWay := wmsdocumentFields[2].Descriptor()
	// wmsdocument.EnterWayValidator is a validator for the "enter_way" field. It is called by the builders before save.
	wmsdocument.EnterWayValidator = wmsdocumentDescEnterWay.Validators[0].(func(string) error)
	// wmsdocumentDescSource is the schema descriptor for source field.
	wmsdocumentDescSource := wmsdocumentFields[3].Descriptor()
	// wmsdocument.SourceValidator is a validator for the "source" field. It is called by the builders before save.
	wmsdocument.SourceValidator = wmsdocumentDescSource.Validators[0].(func(string) error)
	// wmsdocumentDescSourceType is the schema descriptor for source_type field.
	wmsdocumentDescSourceType := wmsdocumentFields[4].Descriptor()
	// wmsdocument.SourceTypeValidator is a validator for the "source_type" field. It is called by the builders before save.
	wmsdocument.SourceTypeValidator = wmsdocumentDescSourceType.Validators[0].(func(string) error)
	// wmsdocumentDescSourceID is the schema descriptor for source_id field.
	wmsdocumentDescSourceID := wmsdocumentFields[5].Descriptor()
	// wmsdocument.SourceIDValidator is a validator for the "source_id" field. It is called by the builders before save.
	wmsdocument.SourceIDValidator = wmsdocumentDescSourceID.Validators[0].(func(string) error)
	// wmsdocumentDescID is the schema descriptor for id field.
	wmsdocumentDescID := wmsdocumentMixinFields0[0].Descriptor()
	// wmsdocument.DefaultID holds the default value on creation for the id field.
	wmsdocument.DefaultID = wmsdocumentDescID.Default.(func() string)
	wmsenterrepositoryorderMixin := schematype.WmsEnterRepositoryOrder{}.Mixin()
	wmsenterrepositoryorderMixinFields0 := wmsenterrepositoryorderMixin[0].Fields()
	_ = wmsenterrepositoryorderMixinFields0
	wmsenterrepositoryorderFields := schematype.WmsEnterRepositoryOrder{}.Fields()
	_ = wmsenterrepositoryorderFields
	// wmsenterrepositoryorderDescCreatedAt is the schema descriptor for created_at field.
	wmsenterrepositoryorderDescCreatedAt := wmsenterrepositoryorderMixinFields0[1].Descriptor()
	// wmsenterrepositoryorder.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsenterrepositoryorder.DefaultCreatedAt = wmsenterrepositoryorderDescCreatedAt.Default.(time.Time)
	// wmsenterrepositoryorderDescUpdatedAt is the schema descriptor for updated_at field.
	wmsenterrepositoryorderDescUpdatedAt := wmsenterrepositoryorderMixinFields0[2].Descriptor()
	// wmsenterrepositoryorder.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsenterrepositoryorder.DefaultUpdatedAt = wmsenterrepositoryorderDescUpdatedAt.Default.(func() time.Time)
	// wmsenterrepositoryorder.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsenterrepositoryorder.UpdateDefaultUpdatedAt = wmsenterrepositoryorderDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsenterrepositoryorderDescID is the schema descriptor for id field.
	wmsenterrepositoryorderDescID := wmsenterrepositoryorderMixinFields0[0].Descriptor()
	// wmsenterrepositoryorder.DefaultID holds the default value on creation for the id field.
	wmsenterrepositoryorder.DefaultID = wmsenterrepositoryorderDescID.Default.(func() string)
	wmsenterrepositoryorderdetailMixin := schematype.WmsEnterRepositoryOrderDetail{}.Mixin()
	wmsenterrepositoryorderdetailMixinFields0 := wmsenterrepositoryorderdetailMixin[0].Fields()
	_ = wmsenterrepositoryorderdetailMixinFields0
	wmsenterrepositoryorderdetailFields := schematype.WmsEnterRepositoryOrderDetail{}.Fields()
	_ = wmsenterrepositoryorderdetailFields
	// wmsenterrepositoryorderdetailDescCreatedAt is the schema descriptor for created_at field.
	wmsenterrepositoryorderdetailDescCreatedAt := wmsenterrepositoryorderdetailMixinFields0[1].Descriptor()
	// wmsenterrepositoryorderdetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsenterrepositoryorderdetail.DefaultCreatedAt = wmsenterrepositoryorderdetailDescCreatedAt.Default.(time.Time)
	// wmsenterrepositoryorderdetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmsenterrepositoryorderdetailDescUpdatedAt := wmsenterrepositoryorderdetailMixinFields0[2].Descriptor()
	// wmsenterrepositoryorderdetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsenterrepositoryorderdetail.DefaultUpdatedAt = wmsenterrepositoryorderdetailDescUpdatedAt.Default.(func() time.Time)
	// wmsenterrepositoryorderdetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsenterrepositoryorderdetail.UpdateDefaultUpdatedAt = wmsenterrepositoryorderdetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsenterrepositoryorderdetailDescFeature is the schema descriptor for feature field.
	wmsenterrepositoryorderdetailDescFeature := wmsenterrepositoryorderdetailFields[17].Descriptor()
	// wmsenterrepositoryorderdetail.DefaultFeature holds the default value on creation for the feature field.
	wmsenterrepositoryorderdetail.DefaultFeature = wmsenterrepositoryorderdetailDescFeature.Default.(map[string]interface{})
	// wmsenterrepositoryorderdetailDescID is the schema descriptor for id field.
	wmsenterrepositoryorderdetailDescID := wmsenterrepositoryorderdetailMixinFields0[0].Descriptor()
	// wmsenterrepositoryorderdetail.DefaultID holds the default value on creation for the id field.
	wmsenterrepositoryorderdetail.DefaultID = wmsenterrepositoryorderdetailDescID.Default.(func() string)
	wmsequipmentMixin := schematype.WmsEquipment{}.Mixin()
	wmsequipmentMixinFields0 := wmsequipmentMixin[0].Fields()
	_ = wmsequipmentMixinFields0
	wmsequipmentMixinFields1 := wmsequipmentMixin[1].Fields()
	_ = wmsequipmentMixinFields1
	wmsequipmentMixinFields3 := wmsequipmentMixin[3].Fields()
	_ = wmsequipmentMixinFields3
	wmsequipmentFields := schematype.WmsEquipment{}.Fields()
	_ = wmsequipmentFields
	// wmsequipmentDescCreatedAt is the schema descriptor for created_at field.
	wmsequipmentDescCreatedAt := wmsequipmentMixinFields0[1].Descriptor()
	// wmsequipment.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsequipment.DefaultCreatedAt = wmsequipmentDescCreatedAt.Default.(time.Time)
	// wmsequipmentDescUpdatedAt is the schema descriptor for updated_at field.
	wmsequipmentDescUpdatedAt := wmsequipmentMixinFields0[2].Descriptor()
	// wmsequipment.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsequipment.DefaultUpdatedAt = wmsequipmentDescUpdatedAt.Default.(func() time.Time)
	// wmsequipment.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsequipment.UpdateDefaultUpdatedAt = wmsequipmentDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsequipmentDescStatus is the schema descriptor for status field.
	wmsequipmentDescStatus := wmsequipmentMixinFields1[0].Descriptor()
	// wmsequipment.DefaultStatus holds the default value on creation for the status field.
	wmsequipment.DefaultStatus = wmsequipmentDescStatus.Default.(int32)
	// wmsequipmentDescIsOneMaterialOneCode is the schema descriptor for is_one_material_one_code field.
	wmsequipmentDescIsOneMaterialOneCode := wmsequipmentFields[18].Descriptor()
	// wmsequipment.DefaultIsOneMaterialOneCode holds the default value on creation for the is_one_material_one_code field.
	wmsequipment.DefaultIsOneMaterialOneCode = wmsequipmentDescIsOneMaterialOneCode.Default.(bool)
	// wmsequipmentDescDiscardMethod is the schema descriptor for discard_method field.
	wmsequipmentDescDiscardMethod := wmsequipmentFields[19].Descriptor()
	// wmsequipment.DefaultDiscardMethod holds the default value on creation for the discard_method field.
	wmsequipment.DefaultDiscardMethod = wmsequipmentDescDiscardMethod.Default.(int32)
	// wmsequipmentDescID is the schema descriptor for id field.
	wmsequipmentDescID := wmsequipmentMixinFields0[0].Descriptor()
	// wmsequipment.DefaultID holds the default value on creation for the id field.
	wmsequipment.DefaultID = wmsequipmentDescID.Default.(func() string)
	wmsequipmentdetailMixin := schematype.WmsEquipmentDetail{}.Mixin()
	wmsequipmentdetailMixinFields0 := wmsequipmentdetailMixin[0].Fields()
	_ = wmsequipmentdetailMixinFields0
	wmsequipmentdetailFields := schematype.WmsEquipmentDetail{}.Fields()
	_ = wmsequipmentdetailFields
	// wmsequipmentdetailDescCreatedAt is the schema descriptor for created_at field.
	wmsequipmentdetailDescCreatedAt := wmsequipmentdetailMixinFields0[1].Descriptor()
	// wmsequipmentdetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsequipmentdetail.DefaultCreatedAt = wmsequipmentdetailDescCreatedAt.Default.(time.Time)
	// wmsequipmentdetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmsequipmentdetailDescUpdatedAt := wmsequipmentdetailMixinFields0[2].Descriptor()
	// wmsequipmentdetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsequipmentdetail.DefaultUpdatedAt = wmsequipmentdetailDescUpdatedAt.Default.(func() time.Time)
	// wmsequipmentdetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsequipmentdetail.UpdateDefaultUpdatedAt = wmsequipmentdetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsequipmentdetailDescIsShowToList is the schema descriptor for is_show_to_list field.
	wmsequipmentdetailDescIsShowToList := wmsequipmentdetailFields[5].Descriptor()
	// wmsequipmentdetail.DefaultIsShowToList holds the default value on creation for the is_show_to_list field.
	wmsequipmentdetail.DefaultIsShowToList = wmsequipmentdetailDescIsShowToList.Default.(int32)
	// wmsequipmentdetailDescID is the schema descriptor for id field.
	wmsequipmentdetailDescID := wmsequipmentdetailMixinFields0[0].Descriptor()
	// wmsequipmentdetail.DefaultID holds the default value on creation for the id field.
	wmsequipmentdetail.DefaultID = wmsequipmentdetailDescID.Default.(func() string)
	wmsequipmenttypeMixin := schematype.WmsEquipmentType{}.Mixin()
	wmsequipmenttypeMixinFields0 := wmsequipmenttypeMixin[0].Fields()
	_ = wmsequipmenttypeMixinFields0
	wmsequipmenttypeMixinFields2 := wmsequipmenttypeMixin[2].Fields()
	_ = wmsequipmenttypeMixinFields2
	wmsequipmenttypeFields := schematype.WmsEquipmentType{}.Fields()
	_ = wmsequipmenttypeFields
	// wmsequipmenttypeDescCreatedAt is the schema descriptor for created_at field.
	wmsequipmenttypeDescCreatedAt := wmsequipmenttypeMixinFields0[1].Descriptor()
	// wmsequipmenttype.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsequipmenttype.DefaultCreatedAt = wmsequipmenttypeDescCreatedAt.Default.(time.Time)
	// wmsequipmenttypeDescUpdatedAt is the schema descriptor for updated_at field.
	wmsequipmenttypeDescUpdatedAt := wmsequipmenttypeMixinFields0[2].Descriptor()
	// wmsequipmenttype.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsequipmenttype.DefaultUpdatedAt = wmsequipmenttypeDescUpdatedAt.Default.(func() time.Time)
	// wmsequipmenttype.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsequipmenttype.UpdateDefaultUpdatedAt = wmsequipmenttypeDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsequipmenttypeDescSort is the schema descriptor for sort field.
	wmsequipmenttypeDescSort := wmsequipmenttypeMixinFields2[0].Descriptor()
	// wmsequipmenttype.DefaultSort holds the default value on creation for the sort field.
	wmsequipmenttype.DefaultSort = wmsequipmenttypeDescSort.Default.(int32)
	// wmsequipmenttypeDescID is the schema descriptor for id field.
	wmsequipmenttypeDescID := wmsequipmenttypeMixinFields0[0].Descriptor()
	// wmsequipmenttype.DefaultID holds the default value on creation for the id field.
	wmsequipmenttype.DefaultID = wmsequipmenttypeDescID.Default.(func() string)
	wmsequipmenttypepropertyMixin := schematype.WmsEquipmentTypeProperty{}.Mixin()
	wmsequipmenttypepropertyMixinFields0 := wmsequipmenttypepropertyMixin[0].Fields()
	_ = wmsequipmenttypepropertyMixinFields0
	wmsequipmenttypepropertyMixinFields2 := wmsequipmenttypepropertyMixin[2].Fields()
	_ = wmsequipmenttypepropertyMixinFields2
	wmsequipmenttypepropertyFields := schematype.WmsEquipmentTypeProperty{}.Fields()
	_ = wmsequipmenttypepropertyFields
	// wmsequipmenttypepropertyDescCreatedAt is the schema descriptor for created_at field.
	wmsequipmenttypepropertyDescCreatedAt := wmsequipmenttypepropertyMixinFields0[1].Descriptor()
	// wmsequipmenttypeproperty.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsequipmenttypeproperty.DefaultCreatedAt = wmsequipmenttypepropertyDescCreatedAt.Default.(time.Time)
	// wmsequipmenttypepropertyDescUpdatedAt is the schema descriptor for updated_at field.
	wmsequipmenttypepropertyDescUpdatedAt := wmsequipmenttypepropertyMixinFields0[2].Descriptor()
	// wmsequipmenttypeproperty.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsequipmenttypeproperty.DefaultUpdatedAt = wmsequipmenttypepropertyDescUpdatedAt.Default.(func() time.Time)
	// wmsequipmenttypeproperty.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsequipmenttypeproperty.UpdateDefaultUpdatedAt = wmsequipmenttypepropertyDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsequipmenttypepropertyDescSort is the schema descriptor for sort field.
	wmsequipmenttypepropertyDescSort := wmsequipmenttypepropertyMixinFields2[0].Descriptor()
	// wmsequipmenttypeproperty.DefaultSort holds the default value on creation for the sort field.
	wmsequipmenttypeproperty.DefaultSort = wmsequipmenttypepropertyDescSort.Default.(int32)
	// wmsequipmenttypepropertyDescPropertyType is the schema descriptor for property_type field.
	wmsequipmenttypepropertyDescPropertyType := wmsequipmenttypepropertyFields[5].Descriptor()
	// wmsequipmenttypeproperty.DefaultPropertyType holds the default value on creation for the property_type field.
	wmsequipmenttypeproperty.DefaultPropertyType = wmsequipmenttypepropertyDescPropertyType.Default.(string)
	// wmsequipmenttypepropertyDescID is the schema descriptor for id field.
	wmsequipmenttypepropertyDescID := wmsequipmenttypepropertyMixinFields0[0].Descriptor()
	// wmsequipmenttypeproperty.DefaultID holds the default value on creation for the id field.
	wmsequipmenttypeproperty.DefaultID = wmsequipmenttypepropertyDescID.Default.(func() string)
	wmsequipmenttypepropertygroupMixin := schematype.WmsEquipmentTypePropertyGroup{}.Mixin()
	wmsequipmenttypepropertygroupMixinFields0 := wmsequipmenttypepropertygroupMixin[0].Fields()
	_ = wmsequipmenttypepropertygroupMixinFields0
	wmsequipmenttypepropertygroupMixinFields1 := wmsequipmenttypepropertygroupMixin[1].Fields()
	_ = wmsequipmenttypepropertygroupMixinFields1
	wmsequipmenttypepropertygroupFields := schematype.WmsEquipmentTypePropertyGroup{}.Fields()
	_ = wmsequipmenttypepropertygroupFields
	// wmsequipmenttypepropertygroupDescCreatedAt is the schema descriptor for created_at field.
	wmsequipmenttypepropertygroupDescCreatedAt := wmsequipmenttypepropertygroupMixinFields0[1].Descriptor()
	// wmsequipmenttypepropertygroup.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsequipmenttypepropertygroup.DefaultCreatedAt = wmsequipmenttypepropertygroupDescCreatedAt.Default.(time.Time)
	// wmsequipmenttypepropertygroupDescUpdatedAt is the schema descriptor for updated_at field.
	wmsequipmenttypepropertygroupDescUpdatedAt := wmsequipmenttypepropertygroupMixinFields0[2].Descriptor()
	// wmsequipmenttypepropertygroup.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsequipmenttypepropertygroup.DefaultUpdatedAt = wmsequipmenttypepropertygroupDescUpdatedAt.Default.(func() time.Time)
	// wmsequipmenttypepropertygroup.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsequipmenttypepropertygroup.UpdateDefaultUpdatedAt = wmsequipmenttypepropertygroupDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsequipmenttypepropertygroupDescSort is the schema descriptor for sort field.
	wmsequipmenttypepropertygroupDescSort := wmsequipmenttypepropertygroupMixinFields1[0].Descriptor()
	// wmsequipmenttypepropertygroup.DefaultSort holds the default value on creation for the sort field.
	wmsequipmenttypepropertygroup.DefaultSort = wmsequipmenttypepropertygroupDescSort.Default.(int32)
	// wmsequipmenttypepropertygroupDescPropertyGroupType is the schema descriptor for property_group_type field.
	wmsequipmenttypepropertygroupDescPropertyGroupType := wmsequipmenttypepropertygroupFields[2].Descriptor()
	// wmsequipmenttypepropertygroup.DefaultPropertyGroupType holds the default value on creation for the property_group_type field.
	wmsequipmenttypepropertygroup.DefaultPropertyGroupType = wmsequipmenttypepropertygroupDescPropertyGroupType.Default.(string)
	// wmsequipmenttypepropertygroupDescID is the schema descriptor for id field.
	wmsequipmenttypepropertygroupDescID := wmsequipmenttypepropertygroupMixinFields0[0].Descriptor()
	// wmsequipmenttypepropertygroup.DefaultID holds the default value on creation for the id field.
	wmsequipmenttypepropertygroup.DefaultID = wmsequipmenttypepropertygroupDescID.Default.(func() string)
	wmsequipmenttypepropertyoptionMixin := schematype.WmsEquipmentTypePropertyOption{}.Mixin()
	wmsequipmenttypepropertyoptionMixinFields0 := wmsequipmenttypepropertyoptionMixin[0].Fields()
	_ = wmsequipmenttypepropertyoptionMixinFields0
	wmsequipmenttypepropertyoptionMixinFields1 := wmsequipmenttypepropertyoptionMixin[1].Fields()
	_ = wmsequipmenttypepropertyoptionMixinFields1
	wmsequipmenttypepropertyoptionFields := schematype.WmsEquipmentTypePropertyOption{}.Fields()
	_ = wmsequipmenttypepropertyoptionFields
	// wmsequipmenttypepropertyoptionDescCreatedAt is the schema descriptor for created_at field.
	wmsequipmenttypepropertyoptionDescCreatedAt := wmsequipmenttypepropertyoptionMixinFields0[1].Descriptor()
	// wmsequipmenttypepropertyoption.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsequipmenttypepropertyoption.DefaultCreatedAt = wmsequipmenttypepropertyoptionDescCreatedAt.Default.(time.Time)
	// wmsequipmenttypepropertyoptionDescUpdatedAt is the schema descriptor for updated_at field.
	wmsequipmenttypepropertyoptionDescUpdatedAt := wmsequipmenttypepropertyoptionMixinFields0[2].Descriptor()
	// wmsequipmenttypepropertyoption.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsequipmenttypepropertyoption.DefaultUpdatedAt = wmsequipmenttypepropertyoptionDescUpdatedAt.Default.(func() time.Time)
	// wmsequipmenttypepropertyoption.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsequipmenttypepropertyoption.UpdateDefaultUpdatedAt = wmsequipmenttypepropertyoptionDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsequipmenttypepropertyoptionDescSort is the schema descriptor for sort field.
	wmsequipmenttypepropertyoptionDescSort := wmsequipmenttypepropertyoptionMixinFields1[0].Descriptor()
	// wmsequipmenttypepropertyoption.DefaultSort holds the default value on creation for the sort field.
	wmsequipmenttypepropertyoption.DefaultSort = wmsequipmenttypepropertyoptionDescSort.Default.(int32)
	// wmsequipmenttypepropertyoptionDescID is the schema descriptor for id field.
	wmsequipmenttypepropertyoptionDescID := wmsequipmenttypepropertyoptionMixinFields0[0].Descriptor()
	// wmsequipmenttypepropertyoption.DefaultID holds the default value on creation for the id field.
	wmsequipmenttypepropertyoption.DefaultID = wmsequipmenttypepropertyoptionDescID.Default.(func() string)
	wmsequipmentusergroupMixin := schematype.WmsEquipmentUserGroup{}.Mixin()
	wmsequipmentusergroupMixinFields0 := wmsequipmentusergroupMixin[0].Fields()
	_ = wmsequipmentusergroupMixinFields0
	wmsequipmentusergroupFields := schematype.WmsEquipmentUserGroup{}.Fields()
	_ = wmsequipmentusergroupFields
	// wmsequipmentusergroupDescCreatedAt is the schema descriptor for created_at field.
	wmsequipmentusergroupDescCreatedAt := wmsequipmentusergroupMixinFields0[1].Descriptor()
	// wmsequipmentusergroup.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsequipmentusergroup.DefaultCreatedAt = wmsequipmentusergroupDescCreatedAt.Default.(time.Time)
	// wmsequipmentusergroupDescUpdatedAt is the schema descriptor for updated_at field.
	wmsequipmentusergroupDescUpdatedAt := wmsequipmentusergroupMixinFields0[2].Descriptor()
	// wmsequipmentusergroup.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsequipmentusergroup.DefaultUpdatedAt = wmsequipmentusergroupDescUpdatedAt.Default.(func() time.Time)
	// wmsequipmentusergroup.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsequipmentusergroup.UpdateDefaultUpdatedAt = wmsequipmentusergroupDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsequipmentusergroupDescID is the schema descriptor for id field.
	wmsequipmentusergroupDescID := wmsequipmentusergroupMixinFields0[0].Descriptor()
	// wmsequipmentusergroup.DefaultID holds the default value on creation for the id field.
	wmsequipmentusergroup.DefaultID = wmsequipmentusergroupDescID.Default.(func() string)
	wmsfirestationMixin := schematype.WmsFireStation{}.Mixin()
	wmsfirestationMixinFields0 := wmsfirestationMixin[0].Fields()
	_ = wmsfirestationMixinFields0
	wmsfirestationMixinFields1 := wmsfirestationMixin[1].Fields()
	_ = wmsfirestationMixinFields1
	wmsfirestationFields := schematype.WmsFireStation{}.Fields()
	_ = wmsfirestationFields
	// wmsfirestationDescCreatedAt is the schema descriptor for created_at field.
	wmsfirestationDescCreatedAt := wmsfirestationMixinFields0[1].Descriptor()
	// wmsfirestation.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsfirestation.DefaultCreatedAt = wmsfirestationDescCreatedAt.Default.(time.Time)
	// wmsfirestationDescUpdatedAt is the schema descriptor for updated_at field.
	wmsfirestationDescUpdatedAt := wmsfirestationMixinFields0[2].Descriptor()
	// wmsfirestation.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsfirestation.DefaultUpdatedAt = wmsfirestationDescUpdatedAt.Default.(func() time.Time)
	// wmsfirestation.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsfirestation.UpdateDefaultUpdatedAt = wmsfirestationDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsfirestationDescStatus is the schema descriptor for status field.
	wmsfirestationDescStatus := wmsfirestationMixinFields1[0].Descriptor()
	// wmsfirestation.DefaultStatus holds the default value on creation for the status field.
	wmsfirestation.DefaultStatus = wmsfirestationDescStatus.Default.(int32)
	// wmsfirestationDescName is the schema descriptor for name field.
	wmsfirestationDescName := wmsfirestationFields[0].Descriptor()
	// wmsfirestation.NameValidator is a validator for the "name" field. It is called by the builders before save.
	wmsfirestation.NameValidator = wmsfirestationDescName.Validators[0].(func(string) error)
	// wmsfirestationDescPersonCount is the schema descriptor for person_count field.
	wmsfirestationDescPersonCount := wmsfirestationFields[1].Descriptor()
	// wmsfirestation.DefaultPersonCount holds the default value on creation for the person_count field.
	wmsfirestation.DefaultPersonCount = wmsfirestationDescPersonCount.Default.(int32)
	// wmsfirestationDescID is the schema descriptor for id field.
	wmsfirestationDescID := wmsfirestationMixinFields0[0].Descriptor()
	// wmsfirestation.DefaultID holds the default value on creation for the id field.
	wmsfirestation.DefaultID = wmsfirestationDescID.Default.(func() string)
	wmsgpsMixin := schematype.WmsGPS{}.Mixin()
	wmsgpsMixinFields0 := wmsgpsMixin[0].Fields()
	_ = wmsgpsMixinFields0
	wmsgpsFields := schematype.WmsGPS{}.Fields()
	_ = wmsgpsFields
	// wmsgpsDescCreatedAt is the schema descriptor for created_at field.
	wmsgpsDescCreatedAt := wmsgpsMixinFields0[1].Descriptor()
	// wmsgps.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsgps.DefaultCreatedAt = wmsgpsDescCreatedAt.Default.(time.Time)
	// wmsgpsDescUpdatedAt is the schema descriptor for updated_at field.
	wmsgpsDescUpdatedAt := wmsgpsMixinFields0[2].Descriptor()
	// wmsgps.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsgps.DefaultUpdatedAt = wmsgpsDescUpdatedAt.Default.(func() time.Time)
	// wmsgps.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsgps.UpdateDefaultUpdatedAt = wmsgpsDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsgpsDescID is the schema descriptor for id field.
	wmsgpsDescID := wmsgpsMixinFields0[0].Descriptor()
	// wmsgps.DefaultID holds the default value on creation for the id field.
	wmsgps.DefaultID = wmsgpsDescID.Default.(func() string)
	wmsgpsrecordMixin := schematype.WmsGPSRecord{}.Mixin()
	wmsgpsrecordMixinFields0 := wmsgpsrecordMixin[0].Fields()
	_ = wmsgpsrecordMixinFields0
	wmsgpsrecordFields := schematype.WmsGPSRecord{}.Fields()
	_ = wmsgpsrecordFields
	// wmsgpsrecordDescCreatedAt is the schema descriptor for created_at field.
	wmsgpsrecordDescCreatedAt := wmsgpsrecordMixinFields0[1].Descriptor()
	// wmsgpsrecord.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsgpsrecord.DefaultCreatedAt = wmsgpsrecordDescCreatedAt.Default.(time.Time)
	// wmsgpsrecordDescUpdatedAt is the schema descriptor for updated_at field.
	wmsgpsrecordDescUpdatedAt := wmsgpsrecordMixinFields0[2].Descriptor()
	// wmsgpsrecord.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsgpsrecord.DefaultUpdatedAt = wmsgpsrecordDescUpdatedAt.Default.(func() time.Time)
	// wmsgpsrecord.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsgpsrecord.UpdateDefaultUpdatedAt = wmsgpsrecordDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsgpsrecordDescID is the schema descriptor for id field.
	wmsgpsrecordDescID := wmsgpsrecordMixinFields0[0].Descriptor()
	// wmsgpsrecord.DefaultID holds the default value on creation for the id field.
	wmsgpsrecord.DefaultID = wmsgpsrecordDescID.Default.(func() string)
	wmslearningcourseMixin := schematype.WmsLearningCourse{}.Mixin()
	wmslearningcourseMixinFields0 := wmslearningcourseMixin[0].Fields()
	_ = wmslearningcourseMixinFields0
	wmslearningcourseMixinFields1 := wmslearningcourseMixin[1].Fields()
	_ = wmslearningcourseMixinFields1
	wmslearningcourseMixinFields3 := wmslearningcourseMixin[3].Fields()
	_ = wmslearningcourseMixinFields3
	wmslearningcourseFields := schematype.WmsLearningCourse{}.Fields()
	_ = wmslearningcourseFields
	// wmslearningcourseDescCreatedAt is the schema descriptor for created_at field.
	wmslearningcourseDescCreatedAt := wmslearningcourseMixinFields0[1].Descriptor()
	// wmslearningcourse.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmslearningcourse.DefaultCreatedAt = wmslearningcourseDescCreatedAt.Default.(time.Time)
	// wmslearningcourseDescUpdatedAt is the schema descriptor for updated_at field.
	wmslearningcourseDescUpdatedAt := wmslearningcourseMixinFields0[2].Descriptor()
	// wmslearningcourse.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmslearningcourse.DefaultUpdatedAt = wmslearningcourseDescUpdatedAt.Default.(func() time.Time)
	// wmslearningcourse.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmslearningcourse.UpdateDefaultUpdatedAt = wmslearningcourseDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmslearningcourseDescStatus is the schema descriptor for status field.
	wmslearningcourseDescStatus := wmslearningcourseMixinFields1[0].Descriptor()
	// wmslearningcourse.DefaultStatus holds the default value on creation for the status field.
	wmslearningcourse.DefaultStatus = wmslearningcourseDescStatus.Default.(int32)
	// wmslearningcourseDescName is the schema descriptor for name field.
	wmslearningcourseDescName := wmslearningcourseFields[0].Descriptor()
	// wmslearningcourse.NameValidator is a validator for the "name" field. It is called by the builders before save.
	wmslearningcourse.NameValidator = wmslearningcourseDescName.Validators[0].(func(string) error)
	// wmslearningcourseDescID is the schema descriptor for id field.
	wmslearningcourseDescID := wmslearningcourseMixinFields0[0].Descriptor()
	// wmslearningcourse.DefaultID holds the default value on creation for the id field.
	wmslearningcourse.DefaultID = wmslearningcourseDescID.Default.(func() string)
	wmslearningcoursecoursewareMixin := schematype.WmsLearningCourseCourseware{}.Mixin()
	wmslearningcoursecoursewareMixinFields0 := wmslearningcoursecoursewareMixin[0].Fields()
	_ = wmslearningcoursecoursewareMixinFields0
	wmslearningcoursecoursewareMixinFields1 := wmslearningcoursecoursewareMixin[1].Fields()
	_ = wmslearningcoursecoursewareMixinFields1
	wmslearningcoursecoursewareFields := schematype.WmsLearningCourseCourseware{}.Fields()
	_ = wmslearningcoursecoursewareFields
	// wmslearningcoursecoursewareDescCreatedAt is the schema descriptor for created_at field.
	wmslearningcoursecoursewareDescCreatedAt := wmslearningcoursecoursewareMixinFields0[1].Descriptor()
	// wmslearningcoursecourseware.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmslearningcoursecourseware.DefaultCreatedAt = wmslearningcoursecoursewareDescCreatedAt.Default.(time.Time)
	// wmslearningcoursecoursewareDescUpdatedAt is the schema descriptor for updated_at field.
	wmslearningcoursecoursewareDescUpdatedAt := wmslearningcoursecoursewareMixinFields0[2].Descriptor()
	// wmslearningcoursecourseware.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmslearningcoursecourseware.DefaultUpdatedAt = wmslearningcoursecoursewareDescUpdatedAt.Default.(func() time.Time)
	// wmslearningcoursecourseware.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmslearningcoursecourseware.UpdateDefaultUpdatedAt = wmslearningcoursecoursewareDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmslearningcoursecoursewareDescSort is the schema descriptor for sort field.
	wmslearningcoursecoursewareDescSort := wmslearningcoursecoursewareMixinFields1[0].Descriptor()
	// wmslearningcoursecourseware.DefaultSort holds the default value on creation for the sort field.
	wmslearningcoursecourseware.DefaultSort = wmslearningcoursecoursewareDescSort.Default.(int32)
	// wmslearningcoursecoursewareDescID is the schema descriptor for id field.
	wmslearningcoursecoursewareDescID := wmslearningcoursecoursewareMixinFields0[0].Descriptor()
	// wmslearningcoursecourseware.DefaultID holds the default value on creation for the id field.
	wmslearningcoursecourseware.DefaultID = wmslearningcoursecoursewareDescID.Default.(func() string)
	wmslearningcourselogMixin := schematype.WmsLearningCourseLog{}.Mixin()
	wmslearningcourselogMixinFields0 := wmslearningcourselogMixin[0].Fields()
	_ = wmslearningcourselogMixinFields0
	wmslearningcourselogFields := schematype.WmsLearningCourseLog{}.Fields()
	_ = wmslearningcourselogFields
	// wmslearningcourselogDescCreatedAt is the schema descriptor for created_at field.
	wmslearningcourselogDescCreatedAt := wmslearningcourselogMixinFields0[1].Descriptor()
	// wmslearningcourselog.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmslearningcourselog.DefaultCreatedAt = wmslearningcourselogDescCreatedAt.Default.(time.Time)
	// wmslearningcourselogDescUpdatedAt is the schema descriptor for updated_at field.
	wmslearningcourselogDescUpdatedAt := wmslearningcourselogMixinFields0[2].Descriptor()
	// wmslearningcourselog.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmslearningcourselog.DefaultUpdatedAt = wmslearningcourselogDescUpdatedAt.Default.(func() time.Time)
	// wmslearningcourselog.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmslearningcourselog.UpdateDefaultUpdatedAt = wmslearningcourselogDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmslearningcourselogDescID is the schema descriptor for id field.
	wmslearningcourselogDescID := wmslearningcourselogMixinFields0[0].Descriptor()
	// wmslearningcourselog.DefaultID holds the default value on creation for the id field.
	wmslearningcourselog.DefaultID = wmslearningcourselogDescID.Default.(func() string)
	wmslearningcourserecordMixin := schematype.WmsLearningCourseRecord{}.Mixin()
	wmslearningcourserecordMixinFields0 := wmslearningcourserecordMixin[0].Fields()
	_ = wmslearningcourserecordMixinFields0
	wmslearningcourserecordFields := schematype.WmsLearningCourseRecord{}.Fields()
	_ = wmslearningcourserecordFields
	// wmslearningcourserecordDescCreatedAt is the schema descriptor for created_at field.
	wmslearningcourserecordDescCreatedAt := wmslearningcourserecordMixinFields0[1].Descriptor()
	// wmslearningcourserecord.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmslearningcourserecord.DefaultCreatedAt = wmslearningcourserecordDescCreatedAt.Default.(time.Time)
	// wmslearningcourserecordDescUpdatedAt is the schema descriptor for updated_at field.
	wmslearningcourserecordDescUpdatedAt := wmslearningcourserecordMixinFields0[2].Descriptor()
	// wmslearningcourserecord.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmslearningcourserecord.DefaultUpdatedAt = wmslearningcourserecordDescUpdatedAt.Default.(func() time.Time)
	// wmslearningcourserecord.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmslearningcourserecord.UpdateDefaultUpdatedAt = wmslearningcourserecordDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmslearningcourserecordDescLearningPlanID is the schema descriptor for learning_plan_id field.
	wmslearningcourserecordDescLearningPlanID := wmslearningcourserecordFields[0].Descriptor()
	// wmslearningcourserecord.LearningPlanIDValidator is a validator for the "learning_plan_id" field. It is called by the builders before save.
	wmslearningcourserecord.LearningPlanIDValidator = wmslearningcourserecordDescLearningPlanID.Validators[0].(func(string) error)
	// wmslearningcourserecordDescLearningCourseID is the schema descriptor for learning_course_id field.
	wmslearningcourserecordDescLearningCourseID := wmslearningcourserecordFields[1].Descriptor()
	// wmslearningcourserecord.LearningCourseIDValidator is a validator for the "learning_course_id" field. It is called by the builders before save.
	wmslearningcourserecord.LearningCourseIDValidator = wmslearningcourserecordDescLearningCourseID.Validators[0].(func(string) error)
	// wmslearningcourserecordDescUserID is the schema descriptor for user_id field.
	wmslearningcourserecordDescUserID := wmslearningcourserecordFields[2].Descriptor()
	// wmslearningcourserecord.UserIDValidator is a validator for the "user_id" field. It is called by the builders before save.
	wmslearningcourserecord.UserIDValidator = wmslearningcourserecordDescUserID.Validators[0].(func(string) error)
	// wmslearningcourserecordDescProcess is the schema descriptor for process field.
	wmslearningcourserecordDescProcess := wmslearningcourserecordFields[3].Descriptor()
	// wmslearningcourserecord.DefaultProcess holds the default value on creation for the process field.
	wmslearningcourserecord.DefaultProcess = wmslearningcourserecordDescProcess.Default.(uint32)
	// wmslearningcourserecordDescID is the schema descriptor for id field.
	wmslearningcourserecordDescID := wmslearningcourserecordMixinFields0[0].Descriptor()
	// wmslearningcourserecord.DefaultID holds the default value on creation for the id field.
	wmslearningcourserecord.DefaultID = wmslearningcourserecordDescID.Default.(func() string)
	wmslearningcoursewareMixin := schematype.WmsLearningCourseware{}.Mixin()
	wmslearningcoursewareMixinFields0 := wmslearningcoursewareMixin[0].Fields()
	_ = wmslearningcoursewareMixinFields0
	wmslearningcoursewareMixinFields1 := wmslearningcoursewareMixin[1].Fields()
	_ = wmslearningcoursewareMixinFields1
	wmslearningcoursewareFields := schematype.WmsLearningCourseware{}.Fields()
	_ = wmslearningcoursewareFields
	// wmslearningcoursewareDescCreatedAt is the schema descriptor for created_at field.
	wmslearningcoursewareDescCreatedAt := wmslearningcoursewareMixinFields0[1].Descriptor()
	// wmslearningcourseware.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmslearningcourseware.DefaultCreatedAt = wmslearningcoursewareDescCreatedAt.Default.(time.Time)
	// wmslearningcoursewareDescUpdatedAt is the schema descriptor for updated_at field.
	wmslearningcoursewareDescUpdatedAt := wmslearningcoursewareMixinFields0[2].Descriptor()
	// wmslearningcourseware.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmslearningcourseware.DefaultUpdatedAt = wmslearningcoursewareDescUpdatedAt.Default.(func() time.Time)
	// wmslearningcourseware.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmslearningcourseware.UpdateDefaultUpdatedAt = wmslearningcoursewareDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmslearningcoursewareDescStatus is the schema descriptor for status field.
	wmslearningcoursewareDescStatus := wmslearningcoursewareMixinFields1[0].Descriptor()
	// wmslearningcourseware.DefaultStatus holds the default value on creation for the status field.
	wmslearningcourseware.DefaultStatus = wmslearningcoursewareDescStatus.Default.(int32)
	// wmslearningcoursewareDescName is the schema descriptor for name field.
	wmslearningcoursewareDescName := wmslearningcoursewareFields[0].Descriptor()
	// wmslearningcourseware.NameValidator is a validator for the "name" field. It is called by the builders before save.
	wmslearningcourseware.NameValidator = wmslearningcoursewareDescName.Validators[0].(func(string) error)
	// wmslearningcoursewareDescTimeLength is the schema descriptor for time_length field.
	wmslearningcoursewareDescTimeLength := wmslearningcoursewareFields[8].Descriptor()
	// wmslearningcourseware.DefaultTimeLength holds the default value on creation for the time_length field.
	wmslearningcourseware.DefaultTimeLength = wmslearningcoursewareDescTimeLength.Default.(uint32)
	// wmslearningcoursewareDescID is the schema descriptor for id field.
	wmslearningcoursewareDescID := wmslearningcoursewareMixinFields0[0].Descriptor()
	// wmslearningcourseware.DefaultID holds the default value on creation for the id field.
	wmslearningcourseware.DefaultID = wmslearningcoursewareDescID.Default.(func() string)
	wmslearningcoursewarerecordMixin := schematype.WmsLearningCoursewareRecord{}.Mixin()
	wmslearningcoursewarerecordMixinFields0 := wmslearningcoursewarerecordMixin[0].Fields()
	_ = wmslearningcoursewarerecordMixinFields0
	wmslearningcoursewarerecordFields := schematype.WmsLearningCoursewareRecord{}.Fields()
	_ = wmslearningcoursewarerecordFields
	// wmslearningcoursewarerecordDescCreatedAt is the schema descriptor for created_at field.
	wmslearningcoursewarerecordDescCreatedAt := wmslearningcoursewarerecordMixinFields0[1].Descriptor()
	// wmslearningcoursewarerecord.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmslearningcoursewarerecord.DefaultCreatedAt = wmslearningcoursewarerecordDescCreatedAt.Default.(time.Time)
	// wmslearningcoursewarerecordDescUpdatedAt is the schema descriptor for updated_at field.
	wmslearningcoursewarerecordDescUpdatedAt := wmslearningcoursewarerecordMixinFields0[2].Descriptor()
	// wmslearningcoursewarerecord.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmslearningcoursewarerecord.DefaultUpdatedAt = wmslearningcoursewarerecordDescUpdatedAt.Default.(func() time.Time)
	// wmslearningcoursewarerecord.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmslearningcoursewarerecord.UpdateDefaultUpdatedAt = wmslearningcoursewarerecordDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmslearningcoursewarerecordDescLearningPlanID is the schema descriptor for learning_plan_id field.
	wmslearningcoursewarerecordDescLearningPlanID := wmslearningcoursewarerecordFields[0].Descriptor()
	// wmslearningcoursewarerecord.LearningPlanIDValidator is a validator for the "learning_plan_id" field. It is called by the builders before save.
	wmslearningcoursewarerecord.LearningPlanIDValidator = wmslearningcoursewarerecordDescLearningPlanID.Validators[0].(func(string) error)
	// wmslearningcoursewarerecordDescLearningCourseID is the schema descriptor for learning_course_id field.
	wmslearningcoursewarerecordDescLearningCourseID := wmslearningcoursewarerecordFields[1].Descriptor()
	// wmslearningcoursewarerecord.LearningCourseIDValidator is a validator for the "learning_course_id" field. It is called by the builders before save.
	wmslearningcoursewarerecord.LearningCourseIDValidator = wmslearningcoursewarerecordDescLearningCourseID.Validators[0].(func(string) error)
	// wmslearningcoursewarerecordDescLearningCoursewareID is the schema descriptor for learning_courseware_id field.
	wmslearningcoursewarerecordDescLearningCoursewareID := wmslearningcoursewarerecordFields[2].Descriptor()
	// wmslearningcoursewarerecord.LearningCoursewareIDValidator is a validator for the "learning_courseware_id" field. It is called by the builders before save.
	wmslearningcoursewarerecord.LearningCoursewareIDValidator = wmslearningcoursewarerecordDescLearningCoursewareID.Validators[0].(func(string) error)
	// wmslearningcoursewarerecordDescUserID is the schema descriptor for user_id field.
	wmslearningcoursewarerecordDescUserID := wmslearningcoursewarerecordFields[3].Descriptor()
	// wmslearningcoursewarerecord.UserIDValidator is a validator for the "user_id" field. It is called by the builders before save.
	wmslearningcoursewarerecord.UserIDValidator = wmslearningcoursewarerecordDescUserID.Validators[0].(func(string) error)
	// wmslearningcoursewarerecordDescProcess is the schema descriptor for process field.
	wmslearningcoursewarerecordDescProcess := wmslearningcoursewarerecordFields[4].Descriptor()
	// wmslearningcoursewarerecord.DefaultProcess holds the default value on creation for the process field.
	wmslearningcoursewarerecord.DefaultProcess = wmslearningcoursewarerecordDescProcess.Default.(uint32)
	// wmslearningcoursewarerecordDescID is the schema descriptor for id field.
	wmslearningcoursewarerecordDescID := wmslearningcoursewarerecordMixinFields0[0].Descriptor()
	// wmslearningcoursewarerecord.DefaultID holds the default value on creation for the id field.
	wmslearningcoursewarerecord.DefaultID = wmslearningcoursewarerecordDescID.Default.(func() string)
	wmslearningplanMixin := schematype.WmsLearningPlan{}.Mixin()
	wmslearningplanMixinFields0 := wmslearningplanMixin[0].Fields()
	_ = wmslearningplanMixinFields0
	wmslearningplanMixinFields1 := wmslearningplanMixin[1].Fields()
	_ = wmslearningplanMixinFields1
	wmslearningplanMixinFields3 := wmslearningplanMixin[3].Fields()
	_ = wmslearningplanMixinFields3
	wmslearningplanFields := schematype.WmsLearningPlan{}.Fields()
	_ = wmslearningplanFields
	// wmslearningplanDescCreatedAt is the schema descriptor for created_at field.
	wmslearningplanDescCreatedAt := wmslearningplanMixinFields0[1].Descriptor()
	// wmslearningplan.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmslearningplan.DefaultCreatedAt = wmslearningplanDescCreatedAt.Default.(time.Time)
	// wmslearningplanDescUpdatedAt is the schema descriptor for updated_at field.
	wmslearningplanDescUpdatedAt := wmslearningplanMixinFields0[2].Descriptor()
	// wmslearningplan.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmslearningplan.DefaultUpdatedAt = wmslearningplanDescUpdatedAt.Default.(func() time.Time)
	// wmslearningplan.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmslearningplan.UpdateDefaultUpdatedAt = wmslearningplanDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmslearningplanDescStatus is the schema descriptor for status field.
	wmslearningplanDescStatus := wmslearningplanMixinFields1[0].Descriptor()
	// wmslearningplan.DefaultStatus holds the default value on creation for the status field.
	wmslearningplan.DefaultStatus = wmslearningplanDescStatus.Default.(int32)
	// wmslearningplanDescName is the schema descriptor for name field.
	wmslearningplanDescName := wmslearningplanFields[0].Descriptor()
	// wmslearningplan.NameValidator is a validator for the "name" field. It is called by the builders before save.
	wmslearningplan.NameValidator = wmslearningplanDescName.Validators[0].(func(string) error)
	// wmslearningplanDescNoticeEventPublished is the schema descriptor for notice_event_published field.
	wmslearningplanDescNoticeEventPublished := wmslearningplanFields[5].Descriptor()
	// wmslearningplan.DefaultNoticeEventPublished holds the default value on creation for the notice_event_published field.
	wmslearningplan.DefaultNoticeEventPublished = wmslearningplanDescNoticeEventPublished.Default.(bool)
	// wmslearningplanDescNoticeEventChanged is the schema descriptor for notice_event_changed field.
	wmslearningplanDescNoticeEventChanged := wmslearningplanFields[6].Descriptor()
	// wmslearningplan.DefaultNoticeEventChanged holds the default value on creation for the notice_event_changed field.
	wmslearningplan.DefaultNoticeEventChanged = wmslearningplanDescNoticeEventChanged.Default.(bool)
	// wmslearningplanDescNoticeEventBeforeStared is the schema descriptor for notice_event_before_stared field.
	wmslearningplanDescNoticeEventBeforeStared := wmslearningplanFields[7].Descriptor()
	// wmslearningplan.DefaultNoticeEventBeforeStared holds the default value on creation for the notice_event_before_stared field.
	wmslearningplan.DefaultNoticeEventBeforeStared = wmslearningplanDescNoticeEventBeforeStared.Default.(bool)
	// wmslearningplanDescNoticeEventBeforeMinutes is the schema descriptor for notice_event_before_minutes field.
	wmslearningplanDescNoticeEventBeforeMinutes := wmslearningplanFields[8].Descriptor()
	// wmslearningplan.DefaultNoticeEventBeforeMinutes holds the default value on creation for the notice_event_before_minutes field.
	wmslearningplan.DefaultNoticeEventBeforeMinutes = wmslearningplanDescNoticeEventBeforeMinutes.Default.(int32)
	// wmslearningplanDescID is the schema descriptor for id field.
	wmslearningplanDescID := wmslearningplanMixinFields0[0].Descriptor()
	// wmslearningplan.DefaultID holds the default value on creation for the id field.
	wmslearningplan.DefaultID = wmslearningplanDescID.Default.(func() string)
	wmslearningplanrecordMixin := schematype.WmsLearningPlanRecord{}.Mixin()
	wmslearningplanrecordMixinFields0 := wmslearningplanrecordMixin[0].Fields()
	_ = wmslearningplanrecordMixinFields0
	wmslearningplanrecordFields := schematype.WmsLearningPlanRecord{}.Fields()
	_ = wmslearningplanrecordFields
	// wmslearningplanrecordDescCreatedAt is the schema descriptor for created_at field.
	wmslearningplanrecordDescCreatedAt := wmslearningplanrecordMixinFields0[1].Descriptor()
	// wmslearningplanrecord.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmslearningplanrecord.DefaultCreatedAt = wmslearningplanrecordDescCreatedAt.Default.(time.Time)
	// wmslearningplanrecordDescUpdatedAt is the schema descriptor for updated_at field.
	wmslearningplanrecordDescUpdatedAt := wmslearningplanrecordMixinFields0[2].Descriptor()
	// wmslearningplanrecord.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmslearningplanrecord.DefaultUpdatedAt = wmslearningplanrecordDescUpdatedAt.Default.(func() time.Time)
	// wmslearningplanrecord.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmslearningplanrecord.UpdateDefaultUpdatedAt = wmslearningplanrecordDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmslearningplanrecordDescLearningPlanID is the schema descriptor for learning_plan_id field.
	wmslearningplanrecordDescLearningPlanID := wmslearningplanrecordFields[0].Descriptor()
	// wmslearningplanrecord.LearningPlanIDValidator is a validator for the "learning_plan_id" field. It is called by the builders before save.
	wmslearningplanrecord.LearningPlanIDValidator = wmslearningplanrecordDescLearningPlanID.Validators[0].(func(string) error)
	// wmslearningplanrecordDescUserID is the schema descriptor for user_id field.
	wmslearningplanrecordDescUserID := wmslearningplanrecordFields[1].Descriptor()
	// wmslearningplanrecord.UserIDValidator is a validator for the "user_id" field. It is called by the builders before save.
	wmslearningplanrecord.UserIDValidator = wmslearningplanrecordDescUserID.Validators[0].(func(string) error)
	// wmslearningplanrecordDescProcess is the schema descriptor for process field.
	wmslearningplanrecordDescProcess := wmslearningplanrecordFields[2].Descriptor()
	// wmslearningplanrecord.DefaultProcess holds the default value on creation for the process field.
	wmslearningplanrecord.DefaultProcess = wmslearningplanrecordDescProcess.Default.(uint32)
	// wmslearningplanrecordDescID is the schema descriptor for id field.
	wmslearningplanrecordDescID := wmslearningplanrecordMixinFields0[0].Descriptor()
	// wmslearningplanrecord.DefaultID holds the default value on creation for the id field.
	wmslearningplanrecord.DefaultID = wmslearningplanrecordDescID.Default.(func() string)
	wmsmaintainorderMixin := schematype.WmsMaintainOrder{}.Mixin()
	wmsmaintainorderMixinFields0 := wmsmaintainorderMixin[0].Fields()
	_ = wmsmaintainorderMixinFields0
	wmsmaintainorderFields := schematype.WmsMaintainOrder{}.Fields()
	_ = wmsmaintainorderFields
	// wmsmaintainorderDescCreatedAt is the schema descriptor for created_at field.
	wmsmaintainorderDescCreatedAt := wmsmaintainorderMixinFields0[1].Descriptor()
	// wmsmaintainorder.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsmaintainorder.DefaultCreatedAt = wmsmaintainorderDescCreatedAt.Default.(time.Time)
	// wmsmaintainorderDescUpdatedAt is the schema descriptor for updated_at field.
	wmsmaintainorderDescUpdatedAt := wmsmaintainorderMixinFields0[2].Descriptor()
	// wmsmaintainorder.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsmaintainorder.DefaultUpdatedAt = wmsmaintainorderDescUpdatedAt.Default.(func() time.Time)
	// wmsmaintainorder.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsmaintainorder.UpdateDefaultUpdatedAt = wmsmaintainorderDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsmaintainorderDescID is the schema descriptor for id field.
	wmsmaintainorderDescID := wmsmaintainorderMixinFields0[0].Descriptor()
	// wmsmaintainorder.DefaultID holds the default value on creation for the id field.
	wmsmaintainorder.DefaultID = wmsmaintainorderDescID.Default.(func() string)
	wmsmaintainorderdetailMixin := schematype.WmsMaintainOrderDetail{}.Mixin()
	wmsmaintainorderdetailMixinFields0 := wmsmaintainorderdetailMixin[0].Fields()
	_ = wmsmaintainorderdetailMixinFields0
	wmsmaintainorderdetailFields := schematype.WmsMaintainOrderDetail{}.Fields()
	_ = wmsmaintainorderdetailFields
	// wmsmaintainorderdetailDescCreatedAt is the schema descriptor for created_at field.
	wmsmaintainorderdetailDescCreatedAt := wmsmaintainorderdetailMixinFields0[1].Descriptor()
	// wmsmaintainorderdetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsmaintainorderdetail.DefaultCreatedAt = wmsmaintainorderdetailDescCreatedAt.Default.(time.Time)
	// wmsmaintainorderdetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmsmaintainorderdetailDescUpdatedAt := wmsmaintainorderdetailMixinFields0[2].Descriptor()
	// wmsmaintainorderdetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsmaintainorderdetail.DefaultUpdatedAt = wmsmaintainorderdetailDescUpdatedAt.Default.(func() time.Time)
	// wmsmaintainorderdetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsmaintainorderdetail.UpdateDefaultUpdatedAt = wmsmaintainorderdetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsmaintainorderdetailDescFeature is the schema descriptor for feature field.
	wmsmaintainorderdetailDescFeature := wmsmaintainorderdetailFields[6].Descriptor()
	// wmsmaintainorderdetail.DefaultFeature holds the default value on creation for the feature field.
	wmsmaintainorderdetail.DefaultFeature = wmsmaintainorderdetailDescFeature.Default.(map[string]interface{})
	// wmsmaintainorderdetailDescID is the schema descriptor for id field.
	wmsmaintainorderdetailDescID := wmsmaintainorderdetailMixinFields0[0].Descriptor()
	// wmsmaintainorderdetail.DefaultID holds the default value on creation for the id field.
	wmsmaintainorderdetail.DefaultID = wmsmaintainorderdetailDescID.Default.(func() string)
	wmsmaintainplanMixin := schematype.WmsMaintainPlan{}.Mixin()
	wmsmaintainplanMixinFields0 := wmsmaintainplanMixin[0].Fields()
	_ = wmsmaintainplanMixinFields0
	wmsmaintainplanFields := schematype.WmsMaintainPlan{}.Fields()
	_ = wmsmaintainplanFields
	// wmsmaintainplanDescCreatedAt is the schema descriptor for created_at field.
	wmsmaintainplanDescCreatedAt := wmsmaintainplanMixinFields0[1].Descriptor()
	// wmsmaintainplan.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsmaintainplan.DefaultCreatedAt = wmsmaintainplanDescCreatedAt.Default.(time.Time)
	// wmsmaintainplanDescUpdatedAt is the schema descriptor for updated_at field.
	wmsmaintainplanDescUpdatedAt := wmsmaintainplanMixinFields0[2].Descriptor()
	// wmsmaintainplan.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsmaintainplan.DefaultUpdatedAt = wmsmaintainplanDescUpdatedAt.Default.(func() time.Time)
	// wmsmaintainplan.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsmaintainplan.UpdateDefaultUpdatedAt = wmsmaintainplanDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsmaintainplanDescID is the schema descriptor for id field.
	wmsmaintainplanDescID := wmsmaintainplanMixinFields0[0].Descriptor()
	// wmsmaintainplan.DefaultID holds the default value on creation for the id field.
	wmsmaintainplan.DefaultID = wmsmaintainplanDescID.Default.(func() string)
	wmsmaintainplandetailMixin := schematype.WmsMaintainPlanDetail{}.Mixin()
	wmsmaintainplandetailMixinFields0 := wmsmaintainplandetailMixin[0].Fields()
	_ = wmsmaintainplandetailMixinFields0
	wmsmaintainplandetailFields := schematype.WmsMaintainPlanDetail{}.Fields()
	_ = wmsmaintainplandetailFields
	// wmsmaintainplandetailDescCreatedAt is the schema descriptor for created_at field.
	wmsmaintainplandetailDescCreatedAt := wmsmaintainplandetailMixinFields0[1].Descriptor()
	// wmsmaintainplandetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsmaintainplandetail.DefaultCreatedAt = wmsmaintainplandetailDescCreatedAt.Default.(time.Time)
	// wmsmaintainplandetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmsmaintainplandetailDescUpdatedAt := wmsmaintainplandetailMixinFields0[2].Descriptor()
	// wmsmaintainplandetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsmaintainplandetail.DefaultUpdatedAt = wmsmaintainplandetailDescUpdatedAt.Default.(func() time.Time)
	// wmsmaintainplandetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsmaintainplandetail.UpdateDefaultUpdatedAt = wmsmaintainplandetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsmaintainplandetailDescFeature is the schema descriptor for feature field.
	wmsmaintainplandetailDescFeature := wmsmaintainplandetailFields[16].Descriptor()
	// wmsmaintainplandetail.DefaultFeature holds the default value on creation for the feature field.
	wmsmaintainplandetail.DefaultFeature = wmsmaintainplandetailDescFeature.Default.(map[string]interface{})
	// wmsmaintainplandetailDescID is the schema descriptor for id field.
	wmsmaintainplandetailDescID := wmsmaintainplandetailMixinFields0[0].Descriptor()
	// wmsmaintainplandetail.DefaultID holds the default value on creation for the id field.
	wmsmaintainplandetail.DefaultID = wmsmaintainplandetailDescID.Default.(func() string)
	wmsmaterialMixin := schematype.WmsMaterial{}.Mixin()
	wmsmaterialMixinFields0 := wmsmaterialMixin[0].Fields()
	_ = wmsmaterialMixinFields0
	wmsmaterialMixinFields1 := wmsmaterialMixin[1].Fields()
	_ = wmsmaterialMixinFields1
	wmsmaterialFields := schematype.WmsMaterial{}.Fields()
	_ = wmsmaterialFields
	// wmsmaterialDescCreatedAt is the schema descriptor for created_at field.
	wmsmaterialDescCreatedAt := wmsmaterialMixinFields0[1].Descriptor()
	// wmsmaterial.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsmaterial.DefaultCreatedAt = wmsmaterialDescCreatedAt.Default.(time.Time)
	// wmsmaterialDescUpdatedAt is the schema descriptor for updated_at field.
	wmsmaterialDescUpdatedAt := wmsmaterialMixinFields0[2].Descriptor()
	// wmsmaterial.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsmaterial.DefaultUpdatedAt = wmsmaterialDescUpdatedAt.Default.(func() time.Time)
	// wmsmaterial.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsmaterial.UpdateDefaultUpdatedAt = wmsmaterialDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsmaterialDescStatus is the schema descriptor for status field.
	wmsmaterialDescStatus := wmsmaterialMixinFields1[0].Descriptor()
	// wmsmaterial.DefaultStatus holds the default value on creation for the status field.
	wmsmaterial.DefaultStatus = wmsmaterialDescStatus.Default.(int32)
	// wmsmaterialDescNum is the schema descriptor for num field.
	wmsmaterialDescNum := wmsmaterialFields[10].Descriptor()
	// wmsmaterial.DefaultNum holds the default value on creation for the num field.
	wmsmaterial.DefaultNum = wmsmaterialDescNum.Default.(uint32)
	// wmsmaterialDescPrice is the schema descriptor for price field.
	wmsmaterialDescPrice := wmsmaterialFields[11].Descriptor()
	// wmsmaterial.DefaultPrice holds the default value on creation for the price field.
	wmsmaterial.DefaultPrice = wmsmaterialDescPrice.Default.(uint64)
	// wmsmaterialDescEquipmentStatus is the schema descriptor for equipment_status field.
	wmsmaterialDescEquipmentStatus := wmsmaterialFields[17].Descriptor()
	// wmsmaterial.DefaultEquipmentStatus holds the default value on creation for the equipment_status field.
	wmsmaterial.DefaultEquipmentStatus = wmsmaterialDescEquipmentStatus.Default.(int32)
	// wmsmaterialDescIsApproving is the schema descriptor for is_approving field.
	wmsmaterialDescIsApproving := wmsmaterialFields[18].Descriptor()
	// wmsmaterial.DefaultIsApproving holds the default value on creation for the is_approving field.
	wmsmaterial.DefaultIsApproving = wmsmaterialDescIsApproving.Default.(bool)
	// wmsmaterialDescIsOneMaterialOneCode is the schema descriptor for is_one_material_one_code field.
	wmsmaterialDescIsOneMaterialOneCode := wmsmaterialFields[21].Descriptor()
	// wmsmaterial.DefaultIsOneMaterialOneCode holds the default value on creation for the is_one_material_one_code field.
	wmsmaterial.DefaultIsOneMaterialOneCode = wmsmaterialDescIsOneMaterialOneCode.Default.(bool)
	// wmsmaterialDescDiscardMethod is the schema descriptor for discard_method field.
	wmsmaterialDescDiscardMethod := wmsmaterialFields[22].Descriptor()
	// wmsmaterial.DefaultDiscardMethod holds the default value on creation for the discard_method field.
	wmsmaterial.DefaultDiscardMethod = wmsmaterialDescDiscardMethod.Default.(int32)
	// wmsmaterialDescFeature is the schema descriptor for feature field.
	wmsmaterialDescFeature := wmsmaterialFields[23].Descriptor()
	// wmsmaterial.DefaultFeature holds the default value on creation for the feature field.
	wmsmaterial.DefaultFeature = wmsmaterialDescFeature.Default.(map[string]interface{})
	// wmsmaterialDescID is the schema descriptor for id field.
	wmsmaterialDescID := wmsmaterialMixinFields0[0].Descriptor()
	// wmsmaterial.DefaultID holds the default value on creation for the id field.
	wmsmaterial.DefaultID = wmsmaterialDescID.Default.(func() string)
	wmsmateriallogMixin := schematype.WmsMaterialLog{}.Mixin()
	wmsmateriallogMixinFields0 := wmsmateriallogMixin[0].Fields()
	_ = wmsmateriallogMixinFields0
	wmsmateriallogMixinFields1 := wmsmateriallogMixin[1].Fields()
	_ = wmsmateriallogMixinFields1
	wmsmateriallogFields := schematype.WmsMaterialLog{}.Fields()
	_ = wmsmateriallogFields
	// wmsmateriallogDescCreatedAt is the schema descriptor for created_at field.
	wmsmateriallogDescCreatedAt := wmsmateriallogMixinFields0[1].Descriptor()
	// wmsmateriallog.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsmateriallog.DefaultCreatedAt = wmsmateriallogDescCreatedAt.Default.(time.Time)
	// wmsmateriallogDescUpdatedAt is the schema descriptor for updated_at field.
	wmsmateriallogDescUpdatedAt := wmsmateriallogMixinFields0[2].Descriptor()
	// wmsmateriallog.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsmateriallog.DefaultUpdatedAt = wmsmateriallogDescUpdatedAt.Default.(func() time.Time)
	// wmsmateriallog.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsmateriallog.UpdateDefaultUpdatedAt = wmsmateriallogDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsmateriallogDescStatus is the schema descriptor for status field.
	wmsmateriallogDescStatus := wmsmateriallogMixinFields1[0].Descriptor()
	// wmsmateriallog.DefaultStatus holds the default value on creation for the status field.
	wmsmateriallog.DefaultStatus = wmsmateriallogDescStatus.Default.(int32)
	// wmsmateriallogDescNum is the schema descriptor for num field.
	wmsmateriallogDescNum := wmsmateriallogFields[8].Descriptor()
	// wmsmateriallog.DefaultNum holds the default value on creation for the num field.
	wmsmateriallog.DefaultNum = wmsmateriallogDescNum.Default.(uint32)
	// wmsmateriallogDescPrice is the schema descriptor for price field.
	wmsmateriallogDescPrice := wmsmateriallogFields[9].Descriptor()
	// wmsmateriallog.DefaultPrice holds the default value on creation for the price field.
	wmsmateriallog.DefaultPrice = wmsmateriallogDescPrice.Default.(uint64)
	// wmsmateriallogDescFeature is the schema descriptor for feature field.
	wmsmateriallogDescFeature := wmsmateriallogFields[16].Descriptor()
	// wmsmateriallog.DefaultFeature holds the default value on creation for the feature field.
	wmsmateriallog.DefaultFeature = wmsmateriallogDescFeature.Default.(map[string]interface{})
	// wmsmateriallogDescID is the schema descriptor for id field.
	wmsmateriallogDescID := wmsmateriallogMixinFields0[0].Descriptor()
	// wmsmateriallog.DefaultID holds the default value on creation for the id field.
	wmsmateriallog.DefaultID = wmsmateriallogDescID.Default.(func() string)
	wmsmeasureunitMixin := schematype.WmsMeasureUnit{}.Mixin()
	wmsmeasureunitMixinFields0 := wmsmeasureunitMixin[0].Fields()
	_ = wmsmeasureunitMixinFields0
	wmsmeasureunitFields := schematype.WmsMeasureUnit{}.Fields()
	_ = wmsmeasureunitFields
	// wmsmeasureunitDescCreatedAt is the schema descriptor for created_at field.
	wmsmeasureunitDescCreatedAt := wmsmeasureunitMixinFields0[1].Descriptor()
	// wmsmeasureunit.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsmeasureunit.DefaultCreatedAt = wmsmeasureunitDescCreatedAt.Default.(time.Time)
	// wmsmeasureunitDescUpdatedAt is the schema descriptor for updated_at field.
	wmsmeasureunitDescUpdatedAt := wmsmeasureunitMixinFields0[2].Descriptor()
	// wmsmeasureunit.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsmeasureunit.DefaultUpdatedAt = wmsmeasureunitDescUpdatedAt.Default.(func() time.Time)
	// wmsmeasureunit.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsmeasureunit.UpdateDefaultUpdatedAt = wmsmeasureunitDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsmeasureunitDescID is the schema descriptor for id field.
	wmsmeasureunitDescID := wmsmeasureunitMixinFields0[0].Descriptor()
	// wmsmeasureunit.DefaultID holds the default value on creation for the id field.
	wmsmeasureunit.DefaultID = wmsmeasureunitDescID.Default.(func() string)
	wmsoperatelogMixin := schematype.WmsOperateLog{}.Mixin()
	wmsoperatelogMixinFields0 := wmsoperatelogMixin[0].Fields()
	_ = wmsoperatelogMixinFields0
	wmsoperatelogFields := schematype.WmsOperateLog{}.Fields()
	_ = wmsoperatelogFields
	// wmsoperatelogDescCreatedAt is the schema descriptor for created_at field.
	wmsoperatelogDescCreatedAt := wmsoperatelogMixinFields0[1].Descriptor()
	// wmsoperatelog.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsoperatelog.DefaultCreatedAt = wmsoperatelogDescCreatedAt.Default.(time.Time)
	// wmsoperatelogDescUpdatedAt is the schema descriptor for updated_at field.
	wmsoperatelogDescUpdatedAt := wmsoperatelogMixinFields0[2].Descriptor()
	// wmsoperatelog.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsoperatelog.DefaultUpdatedAt = wmsoperatelogDescUpdatedAt.Default.(func() time.Time)
	// wmsoperatelog.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsoperatelog.UpdateDefaultUpdatedAt = wmsoperatelogDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsoperatelogDescID is the schema descriptor for id field.
	wmsoperatelogDescID := wmsoperatelogMixinFields0[0].Descriptor()
	// wmsoperatelog.DefaultID holds the default value on creation for the id field.
	wmsoperatelog.DefaultID = wmsoperatelogDescID.Default.(func() string)
	wmsoutrepositoryorderMixin := schematype.WmsOutRepositoryOrder{}.Mixin()
	wmsoutrepositoryorderMixinFields0 := wmsoutrepositoryorderMixin[0].Fields()
	_ = wmsoutrepositoryorderMixinFields0
	wmsoutrepositoryorderFields := schematype.WmsOutRepositoryOrder{}.Fields()
	_ = wmsoutrepositoryorderFields
	// wmsoutrepositoryorderDescCreatedAt is the schema descriptor for created_at field.
	wmsoutrepositoryorderDescCreatedAt := wmsoutrepositoryorderMixinFields0[1].Descriptor()
	// wmsoutrepositoryorder.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsoutrepositoryorder.DefaultCreatedAt = wmsoutrepositoryorderDescCreatedAt.Default.(time.Time)
	// wmsoutrepositoryorderDescUpdatedAt is the schema descriptor for updated_at field.
	wmsoutrepositoryorderDescUpdatedAt := wmsoutrepositoryorderMixinFields0[2].Descriptor()
	// wmsoutrepositoryorder.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsoutrepositoryorder.DefaultUpdatedAt = wmsoutrepositoryorderDescUpdatedAt.Default.(func() time.Time)
	// wmsoutrepositoryorder.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsoutrepositoryorder.UpdateDefaultUpdatedAt = wmsoutrepositoryorderDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsoutrepositoryorderDescIsConfirmReceive is the schema descriptor for is_confirm_receive field.
	wmsoutrepositoryorderDescIsConfirmReceive := wmsoutrepositoryorderFields[8].Descriptor()
	// wmsoutrepositoryorder.DefaultIsConfirmReceive holds the default value on creation for the is_confirm_receive field.
	wmsoutrepositoryorder.DefaultIsConfirmReceive = wmsoutrepositoryorderDescIsConfirmReceive.Default.(bool)
	// wmsoutrepositoryorderDescID is the schema descriptor for id field.
	wmsoutrepositoryorderDescID := wmsoutrepositoryorderMixinFields0[0].Descriptor()
	// wmsoutrepositoryorder.DefaultID holds the default value on creation for the id field.
	wmsoutrepositoryorder.DefaultID = wmsoutrepositoryorderDescID.Default.(func() string)
	wmsoutrepositoryorderdetailMixin := schematype.WmsOutRepositoryOrderDetail{}.Mixin()
	wmsoutrepositoryorderdetailMixinFields0 := wmsoutrepositoryorderdetailMixin[0].Fields()
	_ = wmsoutrepositoryorderdetailMixinFields0
	wmsoutrepositoryorderdetailFields := schematype.WmsOutRepositoryOrderDetail{}.Fields()
	_ = wmsoutrepositoryorderdetailFields
	// wmsoutrepositoryorderdetailDescCreatedAt is the schema descriptor for created_at field.
	wmsoutrepositoryorderdetailDescCreatedAt := wmsoutrepositoryorderdetailMixinFields0[1].Descriptor()
	// wmsoutrepositoryorderdetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsoutrepositoryorderdetail.DefaultCreatedAt = wmsoutrepositoryorderdetailDescCreatedAt.Default.(time.Time)
	// wmsoutrepositoryorderdetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmsoutrepositoryorderdetailDescUpdatedAt := wmsoutrepositoryorderdetailMixinFields0[2].Descriptor()
	// wmsoutrepositoryorderdetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsoutrepositoryorderdetail.DefaultUpdatedAt = wmsoutrepositoryorderdetailDescUpdatedAt.Default.(func() time.Time)
	// wmsoutrepositoryorderdetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsoutrepositoryorderdetail.UpdateDefaultUpdatedAt = wmsoutrepositoryorderdetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsoutrepositoryorderdetailDescFeature is the schema descriptor for feature field.
	wmsoutrepositoryorderdetailDescFeature := wmsoutrepositoryorderdetailFields[16].Descriptor()
	// wmsoutrepositoryorderdetail.DefaultFeature holds the default value on creation for the feature field.
	wmsoutrepositoryorderdetail.DefaultFeature = wmsoutrepositoryorderdetailDescFeature.Default.(map[string]interface{})
	// wmsoutrepositoryorderdetailDescID is the schema descriptor for id field.
	wmsoutrepositoryorderdetailDescID := wmsoutrepositoryorderdetailMixinFields0[0].Descriptor()
	// wmsoutrepositoryorderdetail.DefaultID holds the default value on creation for the id field.
	wmsoutrepositoryorderdetail.DefaultID = wmsoutrepositoryorderdetailDescID.Default.(func() string)
	wmspurchaseorderMixin := schematype.WmsPurchaseOrder{}.Mixin()
	wmspurchaseorderMixinFields0 := wmspurchaseorderMixin[0].Fields()
	_ = wmspurchaseorderMixinFields0
	wmspurchaseorderFields := schematype.WmsPurchaseOrder{}.Fields()
	_ = wmspurchaseorderFields
	// wmspurchaseorderDescCreatedAt is the schema descriptor for created_at field.
	wmspurchaseorderDescCreatedAt := wmspurchaseorderMixinFields0[1].Descriptor()
	// wmspurchaseorder.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmspurchaseorder.DefaultCreatedAt = wmspurchaseorderDescCreatedAt.Default.(time.Time)
	// wmspurchaseorderDescUpdatedAt is the schema descriptor for updated_at field.
	wmspurchaseorderDescUpdatedAt := wmspurchaseorderMixinFields0[2].Descriptor()
	// wmspurchaseorder.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmspurchaseorder.DefaultUpdatedAt = wmspurchaseorderDescUpdatedAt.Default.(func() time.Time)
	// wmspurchaseorder.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmspurchaseorder.UpdateDefaultUpdatedAt = wmspurchaseorderDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmspurchaseorderDescIsNotifyRepositoryAdmin is the schema descriptor for is_notify_repository_admin field.
	wmspurchaseorderDescIsNotifyRepositoryAdmin := wmspurchaseorderFields[8].Descriptor()
	// wmspurchaseorder.DefaultIsNotifyRepositoryAdmin holds the default value on creation for the is_notify_repository_admin field.
	wmspurchaseorder.DefaultIsNotifyRepositoryAdmin = wmspurchaseorderDescIsNotifyRepositoryAdmin.Default.(int32)
	// wmspurchaseorderDescStatus is the schema descriptor for status field.
	wmspurchaseorderDescStatus := wmspurchaseorderFields[9].Descriptor()
	// wmspurchaseorder.DefaultStatus holds the default value on creation for the status field.
	wmspurchaseorder.DefaultStatus = wmspurchaseorderDescStatus.Default.(int32)
	// wmspurchaseorderDescID is the schema descriptor for id field.
	wmspurchaseorderDescID := wmspurchaseorderMixinFields0[0].Descriptor()
	// wmspurchaseorder.DefaultID holds the default value on creation for the id field.
	wmspurchaseorder.DefaultID = wmspurchaseorderDescID.Default.(func() string)
	wmspurchaseorderdetailMixin := schematype.WmsPurchaseOrderDetail{}.Mixin()
	wmspurchaseorderdetailMixinFields0 := wmspurchaseorderdetailMixin[0].Fields()
	_ = wmspurchaseorderdetailMixinFields0
	wmspurchaseorderdetailFields := schematype.WmsPurchaseOrderDetail{}.Fields()
	_ = wmspurchaseorderdetailFields
	// wmspurchaseorderdetailDescCreatedAt is the schema descriptor for created_at field.
	wmspurchaseorderdetailDescCreatedAt := wmspurchaseorderdetailMixinFields0[1].Descriptor()
	// wmspurchaseorderdetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmspurchaseorderdetail.DefaultCreatedAt = wmspurchaseorderdetailDescCreatedAt.Default.(time.Time)
	// wmspurchaseorderdetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmspurchaseorderdetailDescUpdatedAt := wmspurchaseorderdetailMixinFields0[2].Descriptor()
	// wmspurchaseorderdetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmspurchaseorderdetail.DefaultUpdatedAt = wmspurchaseorderdetailDescUpdatedAt.Default.(func() time.Time)
	// wmspurchaseorderdetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmspurchaseorderdetail.UpdateDefaultUpdatedAt = wmspurchaseorderdetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmspurchaseorderdetailDescFeature is the schema descriptor for feature field.
	wmspurchaseorderdetailDescFeature := wmspurchaseorderdetailFields[11].Descriptor()
	// wmspurchaseorderdetail.DefaultFeature holds the default value on creation for the feature field.
	wmspurchaseorderdetail.DefaultFeature = wmspurchaseorderdetailDescFeature.Default.(map[string]interface{})
	// wmspurchaseorderdetailDescID is the schema descriptor for id field.
	wmspurchaseorderdetailDescID := wmspurchaseorderdetailMixinFields0[0].Descriptor()
	// wmspurchaseorderdetail.DefaultID holds the default value on creation for the id field.
	wmspurchaseorderdetail.DefaultID = wmspurchaseorderdetailDescID.Default.(func() string)
	wmsrepairorderMixin := schematype.WmsRepairOrder{}.Mixin()
	wmsrepairorderMixinFields0 := wmsrepairorderMixin[0].Fields()
	_ = wmsrepairorderMixinFields0
	wmsrepairorderFields := schematype.WmsRepairOrder{}.Fields()
	_ = wmsrepairorderFields
	// wmsrepairorderDescCreatedAt is the schema descriptor for created_at field.
	wmsrepairorderDescCreatedAt := wmsrepairorderMixinFields0[1].Descriptor()
	// wmsrepairorder.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsrepairorder.DefaultCreatedAt = wmsrepairorderDescCreatedAt.Default.(time.Time)
	// wmsrepairorderDescUpdatedAt is the schema descriptor for updated_at field.
	wmsrepairorderDescUpdatedAt := wmsrepairorderMixinFields0[2].Descriptor()
	// wmsrepairorder.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsrepairorder.DefaultUpdatedAt = wmsrepairorderDescUpdatedAt.Default.(func() time.Time)
	// wmsrepairorder.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsrepairorder.UpdateDefaultUpdatedAt = wmsrepairorderDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsrepairorderDescID is the schema descriptor for id field.
	wmsrepairorderDescID := wmsrepairorderMixinFields0[0].Descriptor()
	// wmsrepairorder.DefaultID holds the default value on creation for the id field.
	wmsrepairorder.DefaultID = wmsrepairorderDescID.Default.(func() string)
	wmsrepairorderdetailMixin := schematype.WmsRepairOrderDetail{}.Mixin()
	wmsrepairorderdetailMixinFields0 := wmsrepairorderdetailMixin[0].Fields()
	_ = wmsrepairorderdetailMixinFields0
	wmsrepairorderdetailFields := schematype.WmsRepairOrderDetail{}.Fields()
	_ = wmsrepairorderdetailFields
	// wmsrepairorderdetailDescCreatedAt is the schema descriptor for created_at field.
	wmsrepairorderdetailDescCreatedAt := wmsrepairorderdetailMixinFields0[1].Descriptor()
	// wmsrepairorderdetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsrepairorderdetail.DefaultCreatedAt = wmsrepairorderdetailDescCreatedAt.Default.(time.Time)
	// wmsrepairorderdetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmsrepairorderdetailDescUpdatedAt := wmsrepairorderdetailMixinFields0[2].Descriptor()
	// wmsrepairorderdetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsrepairorderdetail.DefaultUpdatedAt = wmsrepairorderdetailDescUpdatedAt.Default.(func() time.Time)
	// wmsrepairorderdetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsrepairorderdetail.UpdateDefaultUpdatedAt = wmsrepairorderdetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsrepairorderdetailDescFeature is the schema descriptor for feature field.
	wmsrepairorderdetailDescFeature := wmsrepairorderdetailFields[6].Descriptor()
	// wmsrepairorderdetail.DefaultFeature holds the default value on creation for the feature field.
	wmsrepairorderdetail.DefaultFeature = wmsrepairorderdetailDescFeature.Default.(map[string]interface{})
	// wmsrepairorderdetailDescID is the schema descriptor for id field.
	wmsrepairorderdetailDescID := wmsrepairorderdetailMixinFields0[0].Descriptor()
	// wmsrepairorderdetail.DefaultID holds the default value on creation for the id field.
	wmsrepairorderdetail.DefaultID = wmsrepairorderdetailDescID.Default.(func() string)
	wmsrepairsettlementorderMixin := schematype.WmsRepairSettlementOrder{}.Mixin()
	wmsrepairsettlementorderMixinFields0 := wmsrepairsettlementorderMixin[0].Fields()
	_ = wmsrepairsettlementorderMixinFields0
	wmsrepairsettlementorderFields := schematype.WmsRepairSettlementOrder{}.Fields()
	_ = wmsrepairsettlementorderFields
	// wmsrepairsettlementorderDescCreatedAt is the schema descriptor for created_at field.
	wmsrepairsettlementorderDescCreatedAt := wmsrepairsettlementorderMixinFields0[1].Descriptor()
	// wmsrepairsettlementorder.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsrepairsettlementorder.DefaultCreatedAt = wmsrepairsettlementorderDescCreatedAt.Default.(time.Time)
	// wmsrepairsettlementorderDescUpdatedAt is the schema descriptor for updated_at field.
	wmsrepairsettlementorderDescUpdatedAt := wmsrepairsettlementorderMixinFields0[2].Descriptor()
	// wmsrepairsettlementorder.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsrepairsettlementorder.DefaultUpdatedAt = wmsrepairsettlementorderDescUpdatedAt.Default.(func() time.Time)
	// wmsrepairsettlementorder.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsrepairsettlementorder.UpdateDefaultUpdatedAt = wmsrepairsettlementorderDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsrepairsettlementorderDescID is the schema descriptor for id field.
	wmsrepairsettlementorderDescID := wmsrepairsettlementorderMixinFields0[0].Descriptor()
	// wmsrepairsettlementorder.DefaultID holds the default value on creation for the id field.
	wmsrepairsettlementorder.DefaultID = wmsrepairsettlementorderDescID.Default.(func() string)
	wmsrepairsettlementordersettlementfeedetailMixin := schematype.WmsRepairSettlementOrderSettlementfeeDetail{}.Mixin()
	wmsrepairsettlementordersettlementfeedetailMixinFields0 := wmsrepairsettlementordersettlementfeedetailMixin[0].Fields()
	_ = wmsrepairsettlementordersettlementfeedetailMixinFields0
	wmsrepairsettlementordersettlementfeedetailFields := schematype.WmsRepairSettlementOrderSettlementfeeDetail{}.Fields()
	_ = wmsrepairsettlementordersettlementfeedetailFields
	// wmsrepairsettlementordersettlementfeedetailDescCreatedAt is the schema descriptor for created_at field.
	wmsrepairsettlementordersettlementfeedetailDescCreatedAt := wmsrepairsettlementordersettlementfeedetailMixinFields0[1].Descriptor()
	// wmsrepairsettlementordersettlementfeedetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsrepairsettlementordersettlementfeedetail.DefaultCreatedAt = wmsrepairsettlementordersettlementfeedetailDescCreatedAt.Default.(time.Time)
	// wmsrepairsettlementordersettlementfeedetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmsrepairsettlementordersettlementfeedetailDescUpdatedAt := wmsrepairsettlementordersettlementfeedetailMixinFields0[2].Descriptor()
	// wmsrepairsettlementordersettlementfeedetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsrepairsettlementordersettlementfeedetail.DefaultUpdatedAt = wmsrepairsettlementordersettlementfeedetailDescUpdatedAt.Default.(func() time.Time)
	// wmsrepairsettlementordersettlementfeedetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsrepairsettlementordersettlementfeedetail.UpdateDefaultUpdatedAt = wmsrepairsettlementordersettlementfeedetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsrepairsettlementordersettlementfeedetailDescID is the schema descriptor for id field.
	wmsrepairsettlementordersettlementfeedetailDescID := wmsrepairsettlementordersettlementfeedetailMixinFields0[0].Descriptor()
	// wmsrepairsettlementordersettlementfeedetail.DefaultID holds the default value on creation for the id field.
	wmsrepairsettlementordersettlementfeedetail.DefaultID = wmsrepairsettlementordersettlementfeedetailDescID.Default.(func() string)
	wmsrepairsettlementorderworkfeedetailMixin := schematype.WmsRepairSettlementOrderWorkfeeDetail{}.Mixin()
	wmsrepairsettlementorderworkfeedetailMixinFields0 := wmsrepairsettlementorderworkfeedetailMixin[0].Fields()
	_ = wmsrepairsettlementorderworkfeedetailMixinFields0
	wmsrepairsettlementorderworkfeedetailFields := schematype.WmsRepairSettlementOrderWorkfeeDetail{}.Fields()
	_ = wmsrepairsettlementorderworkfeedetailFields
	// wmsrepairsettlementorderworkfeedetailDescCreatedAt is the schema descriptor for created_at field.
	wmsrepairsettlementorderworkfeedetailDescCreatedAt := wmsrepairsettlementorderworkfeedetailMixinFields0[1].Descriptor()
	// wmsrepairsettlementorderworkfeedetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsrepairsettlementorderworkfeedetail.DefaultCreatedAt = wmsrepairsettlementorderworkfeedetailDescCreatedAt.Default.(time.Time)
	// wmsrepairsettlementorderworkfeedetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmsrepairsettlementorderworkfeedetailDescUpdatedAt := wmsrepairsettlementorderworkfeedetailMixinFields0[2].Descriptor()
	// wmsrepairsettlementorderworkfeedetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsrepairsettlementorderworkfeedetail.DefaultUpdatedAt = wmsrepairsettlementorderworkfeedetailDescUpdatedAt.Default.(func() time.Time)
	// wmsrepairsettlementorderworkfeedetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsrepairsettlementorderworkfeedetail.UpdateDefaultUpdatedAt = wmsrepairsettlementorderworkfeedetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsrepairsettlementorderworkfeedetailDescID is the schema descriptor for id field.
	wmsrepairsettlementorderworkfeedetailDescID := wmsrepairsettlementorderworkfeedetailMixinFields0[0].Descriptor()
	// wmsrepairsettlementorderworkfeedetail.DefaultID holds the default value on creation for the id field.
	wmsrepairsettlementorderworkfeedetail.DefaultID = wmsrepairsettlementorderworkfeedetailDescID.Default.(func() string)
	wmsrepositoryMixin := schematype.WmsRepository{}.Mixin()
	wmsrepositoryMixinFields0 := wmsrepositoryMixin[0].Fields()
	_ = wmsrepositoryMixinFields0
	wmsrepositoryMixinFields1 := wmsrepositoryMixin[1].Fields()
	_ = wmsrepositoryMixinFields1
	wmsrepositoryFields := schematype.WmsRepository{}.Fields()
	_ = wmsrepositoryFields
	// wmsrepositoryDescCreatedAt is the schema descriptor for created_at field.
	wmsrepositoryDescCreatedAt := wmsrepositoryMixinFields0[1].Descriptor()
	// wmsrepository.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsrepository.DefaultCreatedAt = wmsrepositoryDescCreatedAt.Default.(time.Time)
	// wmsrepositoryDescUpdatedAt is the schema descriptor for updated_at field.
	wmsrepositoryDescUpdatedAt := wmsrepositoryMixinFields0[2].Descriptor()
	// wmsrepository.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsrepository.DefaultUpdatedAt = wmsrepositoryDescUpdatedAt.Default.(func() time.Time)
	// wmsrepository.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsrepository.UpdateDefaultUpdatedAt = wmsrepositoryDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsrepositoryDescStatus is the schema descriptor for status field.
	wmsrepositoryDescStatus := wmsrepositoryMixinFields1[0].Descriptor()
	// wmsrepository.DefaultStatus holds the default value on creation for the status field.
	wmsrepository.DefaultStatus = wmsrepositoryDescStatus.Default.(int32)
	// wmsrepositoryDescID is the schema descriptor for id field.
	wmsrepositoryDescID := wmsrepositoryMixinFields0[0].Descriptor()
	// wmsrepository.DefaultID holds the default value on creation for the id field.
	wmsrepository.DefaultID = wmsrepositoryDescID.Default.(func() string)
	wmsrepositoryadminMixin := schematype.WmsRepositoryAdmin{}.Mixin()
	wmsrepositoryadminMixinFields0 := wmsrepositoryadminMixin[0].Fields()
	_ = wmsrepositoryadminMixinFields0
	wmsrepositoryadminMixinFields1 := wmsrepositoryadminMixin[1].Fields()
	_ = wmsrepositoryadminMixinFields1
	wmsrepositoryadminFields := schematype.WmsRepositoryAdmin{}.Fields()
	_ = wmsrepositoryadminFields
	// wmsrepositoryadminDescCreatedAt is the schema descriptor for created_at field.
	wmsrepositoryadminDescCreatedAt := wmsrepositoryadminMixinFields0[1].Descriptor()
	// wmsrepositoryadmin.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsrepositoryadmin.DefaultCreatedAt = wmsrepositoryadminDescCreatedAt.Default.(time.Time)
	// wmsrepositoryadminDescUpdatedAt is the schema descriptor for updated_at field.
	wmsrepositoryadminDescUpdatedAt := wmsrepositoryadminMixinFields0[2].Descriptor()
	// wmsrepositoryadmin.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsrepositoryadmin.DefaultUpdatedAt = wmsrepositoryadminDescUpdatedAt.Default.(func() time.Time)
	// wmsrepositoryadmin.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsrepositoryadmin.UpdateDefaultUpdatedAt = wmsrepositoryadminDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsrepositoryadminDescStatus is the schema descriptor for status field.
	wmsrepositoryadminDescStatus := wmsrepositoryadminMixinFields1[0].Descriptor()
	// wmsrepositoryadmin.DefaultStatus holds the default value on creation for the status field.
	wmsrepositoryadmin.DefaultStatus = wmsrepositoryadminDescStatus.Default.(int32)
	// wmsrepositoryadminDescID is the schema descriptor for id field.
	wmsrepositoryadminDescID := wmsrepositoryadminMixinFields0[0].Descriptor()
	// wmsrepositoryadmin.DefaultID holds the default value on creation for the id field.
	wmsrepositoryadmin.DefaultID = wmsrepositoryadminDescID.Default.(func() string)
	wmsrepositoryareaMixin := schematype.WmsRepositoryArea{}.Mixin()
	wmsrepositoryareaMixinFields0 := wmsrepositoryareaMixin[0].Fields()
	_ = wmsrepositoryareaMixinFields0
	wmsrepositoryareaMixinFields1 := wmsrepositoryareaMixin[1].Fields()
	_ = wmsrepositoryareaMixinFields1
	wmsrepositoryareaFields := schematype.WmsRepositoryArea{}.Fields()
	_ = wmsrepositoryareaFields
	// wmsrepositoryareaDescCreatedAt is the schema descriptor for created_at field.
	wmsrepositoryareaDescCreatedAt := wmsrepositoryareaMixinFields0[1].Descriptor()
	// wmsrepositoryarea.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsrepositoryarea.DefaultCreatedAt = wmsrepositoryareaDescCreatedAt.Default.(time.Time)
	// wmsrepositoryareaDescUpdatedAt is the schema descriptor for updated_at field.
	wmsrepositoryareaDescUpdatedAt := wmsrepositoryareaMixinFields0[2].Descriptor()
	// wmsrepositoryarea.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsrepositoryarea.DefaultUpdatedAt = wmsrepositoryareaDescUpdatedAt.Default.(func() time.Time)
	// wmsrepositoryarea.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsrepositoryarea.UpdateDefaultUpdatedAt = wmsrepositoryareaDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsrepositoryareaDescStatus is the schema descriptor for status field.
	wmsrepositoryareaDescStatus := wmsrepositoryareaMixinFields1[0].Descriptor()
	// wmsrepositoryarea.DefaultStatus holds the default value on creation for the status field.
	wmsrepositoryarea.DefaultStatus = wmsrepositoryareaDescStatus.Default.(int32)
	// wmsrepositoryareaDescImages is the schema descriptor for images field.
	wmsrepositoryareaDescImages := wmsrepositoryareaFields[4].Descriptor()
	// wmsrepositoryarea.DefaultImages holds the default value on creation for the images field.
	wmsrepositoryarea.DefaultImages = wmsrepositoryareaDescImages.Default.([]string)
	// wmsrepositoryareaDescID is the schema descriptor for id field.
	wmsrepositoryareaDescID := wmsrepositoryareaMixinFields0[0].Descriptor()
	// wmsrepositoryarea.DefaultID holds the default value on creation for the id field.
	wmsrepositoryarea.DefaultID = wmsrepositoryareaDescID.Default.(func() string)
	wmsrepositorypositionMixin := schematype.WmsRepositoryPosition{}.Mixin()
	wmsrepositorypositionMixinFields0 := wmsrepositorypositionMixin[0].Fields()
	_ = wmsrepositorypositionMixinFields0
	wmsrepositorypositionMixinFields1 := wmsrepositorypositionMixin[1].Fields()
	_ = wmsrepositorypositionMixinFields1
	wmsrepositorypositionFields := schematype.WmsRepositoryPosition{}.Fields()
	_ = wmsrepositorypositionFields
	// wmsrepositorypositionDescCreatedAt is the schema descriptor for created_at field.
	wmsrepositorypositionDescCreatedAt := wmsrepositorypositionMixinFields0[1].Descriptor()
	// wmsrepositoryposition.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsrepositoryposition.DefaultCreatedAt = wmsrepositorypositionDescCreatedAt.Default.(time.Time)
	// wmsrepositorypositionDescUpdatedAt is the schema descriptor for updated_at field.
	wmsrepositorypositionDescUpdatedAt := wmsrepositorypositionMixinFields0[2].Descriptor()
	// wmsrepositoryposition.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsrepositoryposition.DefaultUpdatedAt = wmsrepositorypositionDescUpdatedAt.Default.(func() time.Time)
	// wmsrepositoryposition.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsrepositoryposition.UpdateDefaultUpdatedAt = wmsrepositorypositionDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsrepositorypositionDescStatus is the schema descriptor for status field.
	wmsrepositorypositionDescStatus := wmsrepositorypositionMixinFields1[0].Descriptor()
	// wmsrepositoryposition.DefaultStatus holds the default value on creation for the status field.
	wmsrepositoryposition.DefaultStatus = wmsrepositorypositionDescStatus.Default.(int32)
	// wmsrepositorypositionDescImages is the schema descriptor for images field.
	wmsrepositorypositionDescImages := wmsrepositorypositionFields[4].Descriptor()
	// wmsrepositoryposition.DefaultImages holds the default value on creation for the images field.
	wmsrepositoryposition.DefaultImages = wmsrepositorypositionDescImages.Default.([]string)
	// wmsrepositorypositionDescID is the schema descriptor for id field.
	wmsrepositorypositionDescID := wmsrepositorypositionMixinFields0[0].Descriptor()
	// wmsrepositoryposition.DefaultID holds the default value on creation for the id field.
	wmsrepositoryposition.DefaultID = wmsrepositorypositionDescID.Default.(func() string)
	wmsrepositoryscreenMixin := schematype.WmsRepositoryScreen{}.Mixin()
	wmsrepositoryscreenMixinFields0 := wmsrepositoryscreenMixin[0].Fields()
	_ = wmsrepositoryscreenMixinFields0
	wmsrepositoryscreenMixinFields2 := wmsrepositoryscreenMixin[2].Fields()
	_ = wmsrepositoryscreenMixinFields2
	wmsrepositoryscreenFields := schematype.WmsRepositoryScreen{}.Fields()
	_ = wmsrepositoryscreenFields
	// wmsrepositoryscreenDescCreatedAt is the schema descriptor for created_at field.
	wmsrepositoryscreenDescCreatedAt := wmsrepositoryscreenMixinFields0[1].Descriptor()
	// wmsrepositoryscreen.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsrepositoryscreen.DefaultCreatedAt = wmsrepositoryscreenDescCreatedAt.Default.(time.Time)
	// wmsrepositoryscreenDescUpdatedAt is the schema descriptor for updated_at field.
	wmsrepositoryscreenDescUpdatedAt := wmsrepositoryscreenMixinFields0[2].Descriptor()
	// wmsrepositoryscreen.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsrepositoryscreen.DefaultUpdatedAt = wmsrepositoryscreenDescUpdatedAt.Default.(func() time.Time)
	// wmsrepositoryscreen.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsrepositoryscreen.UpdateDefaultUpdatedAt = wmsrepositoryscreenDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsrepositoryscreenDescStatus is the schema descriptor for status field.
	wmsrepositoryscreenDescStatus := wmsrepositoryscreenMixinFields2[0].Descriptor()
	// wmsrepositoryscreen.DefaultStatus holds the default value on creation for the status field.
	wmsrepositoryscreen.DefaultStatus = wmsrepositoryscreenDescStatus.Default.(int32)
	// wmsrepositoryscreenDescEquipmentTypeCarouselInterval is the schema descriptor for equipment_type_carousel_interval field.
	wmsrepositoryscreenDescEquipmentTypeCarouselInterval := wmsrepositoryscreenFields[3].Descriptor()
	// wmsrepositoryscreen.DefaultEquipmentTypeCarouselInterval holds the default value on creation for the equipment_type_carousel_interval field.
	wmsrepositoryscreen.DefaultEquipmentTypeCarouselInterval = wmsrepositoryscreenDescEquipmentTypeCarouselInterval.Default.(uint32)
	// wmsrepositoryscreenDescEquipmentTypeSubCarouselInterval is the schema descriptor for equipment_type_sub_carousel_interval field.
	wmsrepositoryscreenDescEquipmentTypeSubCarouselInterval := wmsrepositoryscreenFields[4].Descriptor()
	// wmsrepositoryscreen.DefaultEquipmentTypeSubCarouselInterval holds the default value on creation for the equipment_type_sub_carousel_interval field.
	wmsrepositoryscreen.DefaultEquipmentTypeSubCarouselInterval = wmsrepositoryscreenDescEquipmentTypeSubCarouselInterval.Default.(uint32)
	// wmsrepositoryscreenDescMaterialCarouselInterval is the schema descriptor for material_carousel_interval field.
	wmsrepositoryscreenDescMaterialCarouselInterval := wmsrepositoryscreenFields[5].Descriptor()
	// wmsrepositoryscreen.DefaultMaterialCarouselInterval holds the default value on creation for the material_carousel_interval field.
	wmsrepositoryscreen.DefaultMaterialCarouselInterval = wmsrepositoryscreenDescMaterialCarouselInterval.Default.(uint32)
	// wmsrepositoryscreenDescID is the schema descriptor for id field.
	wmsrepositoryscreenDescID := wmsrepositoryscreenMixinFields0[0].Descriptor()
	// wmsrepositoryscreen.DefaultID holds the default value on creation for the id field.
	wmsrepositoryscreen.DefaultID = wmsrepositoryscreenDescID.Default.(func() string)
	wmsrepositoryscreenrepositoryareaMixin := schematype.WmsRepositoryScreenRepositoryArea{}.Mixin()
	wmsrepositoryscreenrepositoryareaMixinFields0 := wmsrepositoryscreenrepositoryareaMixin[0].Fields()
	_ = wmsrepositoryscreenrepositoryareaMixinFields0
	wmsrepositoryscreenrepositoryareaFields := schematype.WmsRepositoryScreenRepositoryArea{}.Fields()
	_ = wmsrepositoryscreenrepositoryareaFields
	// wmsrepositoryscreenrepositoryareaDescCreatedAt is the schema descriptor for created_at field.
	wmsrepositoryscreenrepositoryareaDescCreatedAt := wmsrepositoryscreenrepositoryareaMixinFields0[1].Descriptor()
	// wmsrepositoryscreenrepositoryarea.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsrepositoryscreenrepositoryarea.DefaultCreatedAt = wmsrepositoryscreenrepositoryareaDescCreatedAt.Default.(time.Time)
	// wmsrepositoryscreenrepositoryareaDescUpdatedAt is the schema descriptor for updated_at field.
	wmsrepositoryscreenrepositoryareaDescUpdatedAt := wmsrepositoryscreenrepositoryareaMixinFields0[2].Descriptor()
	// wmsrepositoryscreenrepositoryarea.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsrepositoryscreenrepositoryarea.DefaultUpdatedAt = wmsrepositoryscreenrepositoryareaDescUpdatedAt.Default.(func() time.Time)
	// wmsrepositoryscreenrepositoryarea.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsrepositoryscreenrepositoryarea.UpdateDefaultUpdatedAt = wmsrepositoryscreenrepositoryareaDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsrepositoryscreenrepositoryareaDescID is the schema descriptor for id field.
	wmsrepositoryscreenrepositoryareaDescID := wmsrepositoryscreenrepositoryareaMixinFields0[0].Descriptor()
	// wmsrepositoryscreenrepositoryarea.DefaultID holds the default value on creation for the id field.
	wmsrepositoryscreenrepositoryarea.DefaultID = wmsrepositoryscreenrepositoryareaDescID.Default.(func() string)
	wmsreturnorderMixin := schematype.WmsReturnOrder{}.Mixin()
	wmsreturnorderMixinFields0 := wmsreturnorderMixin[0].Fields()
	_ = wmsreturnorderMixinFields0
	wmsreturnorderFields := schematype.WmsReturnOrder{}.Fields()
	_ = wmsreturnorderFields
	// wmsreturnorderDescCreatedAt is the schema descriptor for created_at field.
	wmsreturnorderDescCreatedAt := wmsreturnorderMixinFields0[1].Descriptor()
	// wmsreturnorder.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsreturnorder.DefaultCreatedAt = wmsreturnorderDescCreatedAt.Default.(time.Time)
	// wmsreturnorderDescUpdatedAt is the schema descriptor for updated_at field.
	wmsreturnorderDescUpdatedAt := wmsreturnorderMixinFields0[2].Descriptor()
	// wmsreturnorder.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsreturnorder.DefaultUpdatedAt = wmsreturnorderDescUpdatedAt.Default.(func() time.Time)
	// wmsreturnorder.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsreturnorder.UpdateDefaultUpdatedAt = wmsreturnorderDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsreturnorderDescID is the schema descriptor for id field.
	wmsreturnorderDescID := wmsreturnorderMixinFields0[0].Descriptor()
	// wmsreturnorder.DefaultID holds the default value on creation for the id field.
	wmsreturnorder.DefaultID = wmsreturnorderDescID.Default.(func() string)
	wmsreturnorderdetailMixin := schematype.WmsReturnOrderDetail{}.Mixin()
	wmsreturnorderdetailMixinFields0 := wmsreturnorderdetailMixin[0].Fields()
	_ = wmsreturnorderdetailMixinFields0
	wmsreturnorderdetailFields := schematype.WmsReturnOrderDetail{}.Fields()
	_ = wmsreturnorderdetailFields
	// wmsreturnorderdetailDescCreatedAt is the schema descriptor for created_at field.
	wmsreturnorderdetailDescCreatedAt := wmsreturnorderdetailMixinFields0[1].Descriptor()
	// wmsreturnorderdetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsreturnorderdetail.DefaultCreatedAt = wmsreturnorderdetailDescCreatedAt.Default.(time.Time)
	// wmsreturnorderdetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmsreturnorderdetailDescUpdatedAt := wmsreturnorderdetailMixinFields0[2].Descriptor()
	// wmsreturnorderdetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsreturnorderdetail.DefaultUpdatedAt = wmsreturnorderdetailDescUpdatedAt.Default.(func() time.Time)
	// wmsreturnorderdetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsreturnorderdetail.UpdateDefaultUpdatedAt = wmsreturnorderdetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsreturnorderdetailDescFeature is the schema descriptor for feature field.
	wmsreturnorderdetailDescFeature := wmsreturnorderdetailFields[6].Descriptor()
	// wmsreturnorderdetail.DefaultFeature holds the default value on creation for the feature field.
	wmsreturnorderdetail.DefaultFeature = wmsreturnorderdetailDescFeature.Default.(map[string]interface{})
	// wmsreturnorderdetailDescID is the schema descriptor for id field.
	wmsreturnorderdetailDescID := wmsreturnorderdetailMixinFields0[0].Descriptor()
	// wmsreturnorderdetail.DefaultID holds the default value on creation for the id field.
	wmsreturnorderdetail.DefaultID = wmsreturnorderdetailDescID.Default.(func() string)
	wmsrfidMixin := schematype.WmsRfid{}.Mixin()
	wmsrfidMixinFields0 := wmsrfidMixin[0].Fields()
	_ = wmsrfidMixinFields0
	wmsrfidFields := schematype.WmsRfid{}.Fields()
	_ = wmsrfidFields
	// wmsrfidDescCreatedAt is the schema descriptor for created_at field.
	wmsrfidDescCreatedAt := wmsrfidMixinFields0[1].Descriptor()
	// wmsrfid.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsrfid.DefaultCreatedAt = wmsrfidDescCreatedAt.Default.(time.Time)
	// wmsrfidDescUpdatedAt is the schema descriptor for updated_at field.
	wmsrfidDescUpdatedAt := wmsrfidMixinFields0[2].Descriptor()
	// wmsrfid.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsrfid.DefaultUpdatedAt = wmsrfidDescUpdatedAt.Default.(func() time.Time)
	// wmsrfid.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsrfid.UpdateDefaultUpdatedAt = wmsrfidDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsrfidDescIsUsed is the schema descriptor for is_used field.
	wmsrfidDescIsUsed := wmsrfidFields[1].Descriptor()
	// wmsrfid.DefaultIsUsed holds the default value on creation for the is_used field.
	wmsrfid.DefaultIsUsed = wmsrfidDescIsUsed.Default.(bool)
	// wmsrfidDescID is the schema descriptor for id field.
	wmsrfidDescID := wmsrfidMixinFields0[0].Descriptor()
	// wmsrfid.DefaultID holds the default value on creation for the id field.
	wmsrfid.DefaultID = wmsrfidDescID.Default.(func() string)
	wmsrfidreaderMixin := schematype.WmsRfidReader{}.Mixin()
	wmsrfidreaderMixinFields0 := wmsrfidreaderMixin[0].Fields()
	_ = wmsrfidreaderMixinFields0
	wmsrfidreaderMixinFields1 := wmsrfidreaderMixin[1].Fields()
	_ = wmsrfidreaderMixinFields1
	wmsrfidreaderMixinFields2 := wmsrfidreaderMixin[2].Fields()
	_ = wmsrfidreaderMixinFields2
	wmsrfidreaderFields := schematype.WmsRfidReader{}.Fields()
	_ = wmsrfidreaderFields
	// wmsrfidreaderDescCreatedAt is the schema descriptor for created_at field.
	wmsrfidreaderDescCreatedAt := wmsrfidreaderMixinFields0[1].Descriptor()
	// wmsrfidreader.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsrfidreader.DefaultCreatedAt = wmsrfidreaderDescCreatedAt.Default.(time.Time)
	// wmsrfidreaderDescUpdatedAt is the schema descriptor for updated_at field.
	wmsrfidreaderDescUpdatedAt := wmsrfidreaderMixinFields0[2].Descriptor()
	// wmsrfidreader.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsrfidreader.DefaultUpdatedAt = wmsrfidreaderDescUpdatedAt.Default.(func() time.Time)
	// wmsrfidreader.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsrfidreader.UpdateDefaultUpdatedAt = wmsrfidreaderDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsrfidreaderDescSort is the schema descriptor for sort field.
	wmsrfidreaderDescSort := wmsrfidreaderMixinFields1[0].Descriptor()
	// wmsrfidreader.DefaultSort holds the default value on creation for the sort field.
	wmsrfidreader.DefaultSort = wmsrfidreaderDescSort.Default.(int32)
	// wmsrfidreaderDescStatus is the schema descriptor for status field.
	wmsrfidreaderDescStatus := wmsrfidreaderMixinFields2[0].Descriptor()
	// wmsrfidreader.DefaultStatus holds the default value on creation for the status field.
	wmsrfidreader.DefaultStatus = wmsrfidreaderDescStatus.Default.(int32)
	// wmsrfidreaderDescID is the schema descriptor for id field.
	wmsrfidreaderDescID := wmsrfidreaderMixinFields0[0].Descriptor()
	// wmsrfidreader.DefaultID holds the default value on creation for the id field.
	wmsrfidreader.DefaultID = wmsrfidreaderDescID.Default.(func() string)
	wmsrfidreaderrecordMixin := schematype.WmsRfidReaderRecord{}.Mixin()
	wmsrfidreaderrecordMixinFields0 := wmsrfidreaderrecordMixin[0].Fields()
	_ = wmsrfidreaderrecordMixinFields0
	wmsrfidreaderrecordFields := schematype.WmsRfidReaderRecord{}.Fields()
	_ = wmsrfidreaderrecordFields
	// wmsrfidreaderrecordDescCreatedAt is the schema descriptor for created_at field.
	wmsrfidreaderrecordDescCreatedAt := wmsrfidreaderrecordMixinFields0[1].Descriptor()
	// wmsrfidreaderrecord.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsrfidreaderrecord.DefaultCreatedAt = wmsrfidreaderrecordDescCreatedAt.Default.(time.Time)
	// wmsrfidreaderrecordDescUpdatedAt is the schema descriptor for updated_at field.
	wmsrfidreaderrecordDescUpdatedAt := wmsrfidreaderrecordMixinFields0[2].Descriptor()
	// wmsrfidreaderrecord.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsrfidreaderrecord.DefaultUpdatedAt = wmsrfidreaderrecordDescUpdatedAt.Default.(func() time.Time)
	// wmsrfidreaderrecord.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsrfidreaderrecord.UpdateDefaultUpdatedAt = wmsrfidreaderrecordDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsrfidreaderrecordDescID is the schema descriptor for id field.
	wmsrfidreaderrecordDescID := wmsrfidreaderrecordMixinFields0[0].Descriptor()
	// wmsrfidreaderrecord.DefaultID holds the default value on creation for the id field.
	wmsrfidreaderrecord.DefaultID = wmsrfidreaderrecordDescID.Default.(func() string)
	wmstransferorderMixin := schematype.WmsTransferOrder{}.Mixin()
	wmstransferorderMixinFields0 := wmstransferorderMixin[0].Fields()
	_ = wmstransferorderMixinFields0
	wmstransferorderFields := schematype.WmsTransferOrder{}.Fields()
	_ = wmstransferorderFields
	// wmstransferorderDescCreatedAt is the schema descriptor for created_at field.
	wmstransferorderDescCreatedAt := wmstransferorderMixinFields0[1].Descriptor()
	// wmstransferorder.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmstransferorder.DefaultCreatedAt = wmstransferorderDescCreatedAt.Default.(time.Time)
	// wmstransferorderDescUpdatedAt is the schema descriptor for updated_at field.
	wmstransferorderDescUpdatedAt := wmstransferorderMixinFields0[2].Descriptor()
	// wmstransferorder.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmstransferorder.DefaultUpdatedAt = wmstransferorderDescUpdatedAt.Default.(func() time.Time)
	// wmstransferorder.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmstransferorder.UpdateDefaultUpdatedAt = wmstransferorderDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmstransferorderDescID is the schema descriptor for id field.
	wmstransferorderDescID := wmstransferorderMixinFields0[0].Descriptor()
	// wmstransferorder.DefaultID holds the default value on creation for the id field.
	wmstransferorder.DefaultID = wmstransferorderDescID.Default.(func() string)
	wmstransferorderdetailMixin := schematype.WmsTransferOrderDetail{}.Mixin()
	wmstransferorderdetailMixinFields0 := wmstransferorderdetailMixin[0].Fields()
	_ = wmstransferorderdetailMixinFields0
	wmstransferorderdetailFields := schematype.WmsTransferOrderDetail{}.Fields()
	_ = wmstransferorderdetailFields
	// wmstransferorderdetailDescCreatedAt is the schema descriptor for created_at field.
	wmstransferorderdetailDescCreatedAt := wmstransferorderdetailMixinFields0[1].Descriptor()
	// wmstransferorderdetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmstransferorderdetail.DefaultCreatedAt = wmstransferorderdetailDescCreatedAt.Default.(time.Time)
	// wmstransferorderdetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmstransferorderdetailDescUpdatedAt := wmstransferorderdetailMixinFields0[2].Descriptor()
	// wmstransferorderdetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmstransferorderdetail.DefaultUpdatedAt = wmstransferorderdetailDescUpdatedAt.Default.(func() time.Time)
	// wmstransferorderdetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmstransferorderdetail.UpdateDefaultUpdatedAt = wmstransferorderdetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmstransferorderdetailDescFeature is the schema descriptor for feature field.
	wmstransferorderdetailDescFeature := wmstransferorderdetailFields[6].Descriptor()
	// wmstransferorderdetail.DefaultFeature holds the default value on creation for the feature field.
	wmstransferorderdetail.DefaultFeature = wmstransferorderdetailDescFeature.Default.(map[string]interface{})
	// wmstransferorderdetailDescID is the schema descriptor for id field.
	wmstransferorderdetailDescID := wmstransferorderdetailMixinFields0[0].Descriptor()
	// wmstransferorderdetail.DefaultID holds the default value on creation for the id field.
	wmstransferorderdetail.DefaultID = wmstransferorderdetailDescID.Default.(func() string)
	wmsvehiclerepairorderMixin := schematype.WmsVehicleRepairOrder{}.Mixin()
	wmsvehiclerepairorderMixinFields0 := wmsvehiclerepairorderMixin[0].Fields()
	_ = wmsvehiclerepairorderMixinFields0
	wmsvehiclerepairorderFields := schematype.WmsVehicleRepairOrder{}.Fields()
	_ = wmsvehiclerepairorderFields
	// wmsvehiclerepairorderDescCreatedAt is the schema descriptor for created_at field.
	wmsvehiclerepairorderDescCreatedAt := wmsvehiclerepairorderMixinFields0[1].Descriptor()
	// wmsvehiclerepairorder.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsvehiclerepairorder.DefaultCreatedAt = wmsvehiclerepairorderDescCreatedAt.Default.(time.Time)
	// wmsvehiclerepairorderDescUpdatedAt is the schema descriptor for updated_at field.
	wmsvehiclerepairorderDescUpdatedAt := wmsvehiclerepairorderMixinFields0[2].Descriptor()
	// wmsvehiclerepairorder.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsvehiclerepairorder.DefaultUpdatedAt = wmsvehiclerepairorderDescUpdatedAt.Default.(func() time.Time)
	// wmsvehiclerepairorder.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsvehiclerepairorder.UpdateDefaultUpdatedAt = wmsvehiclerepairorderDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsvehiclerepairorderDescID is the schema descriptor for id field.
	wmsvehiclerepairorderDescID := wmsvehiclerepairorderMixinFields0[0].Descriptor()
	// wmsvehiclerepairorder.DefaultID holds the default value on creation for the id field.
	wmsvehiclerepairorder.DefaultID = wmsvehiclerepairorderDescID.Default.(func() string)
	wmsvehiclerepairordermaterialfeedetailMixin := schematype.WmsVehicleRepairOrderMaterialfeeDetail{}.Mixin()
	wmsvehiclerepairordermaterialfeedetailMixinFields0 := wmsvehiclerepairordermaterialfeedetailMixin[0].Fields()
	_ = wmsvehiclerepairordermaterialfeedetailMixinFields0
	wmsvehiclerepairordermaterialfeedetailFields := schematype.WmsVehicleRepairOrderMaterialfeeDetail{}.Fields()
	_ = wmsvehiclerepairordermaterialfeedetailFields
	// wmsvehiclerepairordermaterialfeedetailDescCreatedAt is the schema descriptor for created_at field.
	wmsvehiclerepairordermaterialfeedetailDescCreatedAt := wmsvehiclerepairordermaterialfeedetailMixinFields0[1].Descriptor()
	// wmsvehiclerepairordermaterialfeedetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsvehiclerepairordermaterialfeedetail.DefaultCreatedAt = wmsvehiclerepairordermaterialfeedetailDescCreatedAt.Default.(time.Time)
	// wmsvehiclerepairordermaterialfeedetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmsvehiclerepairordermaterialfeedetailDescUpdatedAt := wmsvehiclerepairordermaterialfeedetailMixinFields0[2].Descriptor()
	// wmsvehiclerepairordermaterialfeedetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsvehiclerepairordermaterialfeedetail.DefaultUpdatedAt = wmsvehiclerepairordermaterialfeedetailDescUpdatedAt.Default.(func() time.Time)
	// wmsvehiclerepairordermaterialfeedetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsvehiclerepairordermaterialfeedetail.UpdateDefaultUpdatedAt = wmsvehiclerepairordermaterialfeedetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsvehiclerepairordermaterialfeedetailDescID is the schema descriptor for id field.
	wmsvehiclerepairordermaterialfeedetailDescID := wmsvehiclerepairordermaterialfeedetailMixinFields0[0].Descriptor()
	// wmsvehiclerepairordermaterialfeedetail.DefaultID holds the default value on creation for the id field.
	wmsvehiclerepairordermaterialfeedetail.DefaultID = wmsvehiclerepairordermaterialfeedetailDescID.Default.(func() string)
	wmsvehiclerepairordersettlementfeedetailMixin := schematype.WmsVehicleRepairOrderSettlementfeeDetail{}.Mixin()
	wmsvehiclerepairordersettlementfeedetailMixinFields0 := wmsvehiclerepairordersettlementfeedetailMixin[0].Fields()
	_ = wmsvehiclerepairordersettlementfeedetailMixinFields0
	wmsvehiclerepairordersettlementfeedetailFields := schematype.WmsVehicleRepairOrderSettlementfeeDetail{}.Fields()
	_ = wmsvehiclerepairordersettlementfeedetailFields
	// wmsvehiclerepairordersettlementfeedetailDescCreatedAt is the schema descriptor for created_at field.
	wmsvehiclerepairordersettlementfeedetailDescCreatedAt := wmsvehiclerepairordersettlementfeedetailMixinFields0[1].Descriptor()
	// wmsvehiclerepairordersettlementfeedetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsvehiclerepairordersettlementfeedetail.DefaultCreatedAt = wmsvehiclerepairordersettlementfeedetailDescCreatedAt.Default.(time.Time)
	// wmsvehiclerepairordersettlementfeedetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmsvehiclerepairordersettlementfeedetailDescUpdatedAt := wmsvehiclerepairordersettlementfeedetailMixinFields0[2].Descriptor()
	// wmsvehiclerepairordersettlementfeedetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsvehiclerepairordersettlementfeedetail.DefaultUpdatedAt = wmsvehiclerepairordersettlementfeedetailDescUpdatedAt.Default.(func() time.Time)
	// wmsvehiclerepairordersettlementfeedetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsvehiclerepairordersettlementfeedetail.UpdateDefaultUpdatedAt = wmsvehiclerepairordersettlementfeedetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsvehiclerepairordersettlementfeedetailDescID is the schema descriptor for id field.
	wmsvehiclerepairordersettlementfeedetailDescID := wmsvehiclerepairordersettlementfeedetailMixinFields0[0].Descriptor()
	// wmsvehiclerepairordersettlementfeedetail.DefaultID holds the default value on creation for the id field.
	wmsvehiclerepairordersettlementfeedetail.DefaultID = wmsvehiclerepairordersettlementfeedetailDescID.Default.(func() string)
	wmsvehiclerepairorderworkfeedetailMixin := schematype.WmsVehicleRepairOrderWorkfeeDetail{}.Mixin()
	wmsvehiclerepairorderworkfeedetailMixinFields0 := wmsvehiclerepairorderworkfeedetailMixin[0].Fields()
	_ = wmsvehiclerepairorderworkfeedetailMixinFields0
	wmsvehiclerepairorderworkfeedetailFields := schematype.WmsVehicleRepairOrderWorkfeeDetail{}.Fields()
	_ = wmsvehiclerepairorderworkfeedetailFields
	// wmsvehiclerepairorderworkfeedetailDescCreatedAt is the schema descriptor for created_at field.
	wmsvehiclerepairorderworkfeedetailDescCreatedAt := wmsvehiclerepairorderworkfeedetailMixinFields0[1].Descriptor()
	// wmsvehiclerepairorderworkfeedetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	wmsvehiclerepairorderworkfeedetail.DefaultCreatedAt = wmsvehiclerepairorderworkfeedetailDescCreatedAt.Default.(time.Time)
	// wmsvehiclerepairorderworkfeedetailDescUpdatedAt is the schema descriptor for updated_at field.
	wmsvehiclerepairorderworkfeedetailDescUpdatedAt := wmsvehiclerepairorderworkfeedetailMixinFields0[2].Descriptor()
	// wmsvehiclerepairorderworkfeedetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wmsvehiclerepairorderworkfeedetail.DefaultUpdatedAt = wmsvehiclerepairorderworkfeedetailDescUpdatedAt.Default.(func() time.Time)
	// wmsvehiclerepairorderworkfeedetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wmsvehiclerepairorderworkfeedetail.UpdateDefaultUpdatedAt = wmsvehiclerepairorderworkfeedetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wmsvehiclerepairorderworkfeedetailDescID is the schema descriptor for id field.
	wmsvehiclerepairorderworkfeedetailDescID := wmsvehiclerepairorderworkfeedetailMixinFields0[0].Descriptor()
	// wmsvehiclerepairorderworkfeedetail.DefaultID holds the default value on creation for the id field.
	wmsvehiclerepairorderworkfeedetail.DefaultID = wmsvehiclerepairorderworkfeedetailDescID.Default.(func() string)
	workwxapprovalmessageMixin := schematype.WorkWxApprovalMessage{}.Mixin()
	workwxapprovalmessageMixinFields0 := workwxapprovalmessageMixin[0].Fields()
	_ = workwxapprovalmessageMixinFields0
	workwxapprovalmessageMixinFields2 := workwxapprovalmessageMixin[2].Fields()
	_ = workwxapprovalmessageMixinFields2
	workwxapprovalmessageFields := schematype.WorkWxApprovalMessage{}.Fields()
	_ = workwxapprovalmessageFields
	// workwxapprovalmessageDescCreatedAt is the schema descriptor for created_at field.
	workwxapprovalmessageDescCreatedAt := workwxapprovalmessageMixinFields0[1].Descriptor()
	// workwxapprovalmessage.DefaultCreatedAt holds the default value on creation for the created_at field.
	workwxapprovalmessage.DefaultCreatedAt = workwxapprovalmessageDescCreatedAt.Default.(time.Time)
	// workwxapprovalmessageDescUpdatedAt is the schema descriptor for updated_at field.
	workwxapprovalmessageDescUpdatedAt := workwxapprovalmessageMixinFields0[2].Descriptor()
	// workwxapprovalmessage.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	workwxapprovalmessage.DefaultUpdatedAt = workwxapprovalmessageDescUpdatedAt.Default.(func() time.Time)
	// workwxapprovalmessage.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	workwxapprovalmessage.UpdateDefaultUpdatedAt = workwxapprovalmessageDescUpdatedAt.UpdateDefault.(func() time.Time)
	// workwxapprovalmessageDescStatus is the schema descriptor for status field.
	workwxapprovalmessageDescStatus := workwxapprovalmessageMixinFields2[0].Descriptor()
	// workwxapprovalmessage.DefaultStatus holds the default value on creation for the status field.
	workwxapprovalmessage.DefaultStatus = workwxapprovalmessageDescStatus.Default.(int32)
	// workwxapprovalmessageDescID is the schema descriptor for id field.
	workwxapprovalmessageDescID := workwxapprovalmessageMixinFields0[0].Descriptor()
	// workwxapprovalmessage.DefaultID holds the default value on creation for the id field.
	workwxapprovalmessage.DefaultID = workwxapprovalmessageDescID.Default.(func() string)
	workwxapprovalnodeMixin := schematype.WorkWxApprovalNode{}.Mixin()
	workwxapprovalnodeMixinFields0 := workwxapprovalnodeMixin[0].Fields()
	_ = workwxapprovalnodeMixinFields0
	workwxapprovalnodeFields := schematype.WorkWxApprovalNode{}.Fields()
	_ = workwxapprovalnodeFields
	// workwxapprovalnodeDescCreatedAt is the schema descriptor for created_at field.
	workwxapprovalnodeDescCreatedAt := workwxapprovalnodeMixinFields0[1].Descriptor()
	// workwxapprovalnode.DefaultCreatedAt holds the default value on creation for the created_at field.
	workwxapprovalnode.DefaultCreatedAt = workwxapprovalnodeDescCreatedAt.Default.(time.Time)
	// workwxapprovalnodeDescUpdatedAt is the schema descriptor for updated_at field.
	workwxapprovalnodeDescUpdatedAt := workwxapprovalnodeMixinFields0[2].Descriptor()
	// workwxapprovalnode.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	workwxapprovalnode.DefaultUpdatedAt = workwxapprovalnodeDescUpdatedAt.Default.(func() time.Time)
	// workwxapprovalnode.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	workwxapprovalnode.UpdateDefaultUpdatedAt = workwxapprovalnodeDescUpdatedAt.UpdateDefault.(func() time.Time)
	// workwxapprovalnodeDescID is the schema descriptor for id field.
	workwxapprovalnodeDescID := workwxapprovalnodeMixinFields0[0].Descriptor()
	// workwxapprovalnode.DefaultID holds the default value on creation for the id field.
	workwxapprovalnode.DefaultID = workwxapprovalnodeDescID.Default.(func() string)
	workwxnotifynodeMixin := schematype.WorkWxNotifyNode{}.Mixin()
	workwxnotifynodeMixinFields0 := workwxnotifynodeMixin[0].Fields()
	_ = workwxnotifynodeMixinFields0
	workwxnotifynodeFields := schematype.WorkWxNotifyNode{}.Fields()
	_ = workwxnotifynodeFields
	// workwxnotifynodeDescCreatedAt is the schema descriptor for created_at field.
	workwxnotifynodeDescCreatedAt := workwxnotifynodeMixinFields0[1].Descriptor()
	// workwxnotifynode.DefaultCreatedAt holds the default value on creation for the created_at field.
	workwxnotifynode.DefaultCreatedAt = workwxnotifynodeDescCreatedAt.Default.(time.Time)
	// workwxnotifynodeDescUpdatedAt is the schema descriptor for updated_at field.
	workwxnotifynodeDescUpdatedAt := workwxnotifynodeMixinFields0[2].Descriptor()
	// workwxnotifynode.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	workwxnotifynode.DefaultUpdatedAt = workwxnotifynodeDescUpdatedAt.Default.(func() time.Time)
	// workwxnotifynode.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	workwxnotifynode.UpdateDefaultUpdatedAt = workwxnotifynodeDescUpdatedAt.UpdateDefault.(func() time.Time)
	// workwxnotifynodeDescID is the schema descriptor for id field.
	workwxnotifynodeDescID := workwxnotifynodeMixinFields0[0].Descriptor()
	// workwxnotifynode.DefaultID holds the default value on creation for the id field.
	workwxnotifynode.DefaultID = workwxnotifynodeDescID.Default.(func() string)
}

const (
	Version = "v0.14.4"                                         // Version of ent codegen.
	Sum     = "h1:/DhDraSLXIkBhyiVoJeSshr4ZYi7femzhj6/TckzZuI=" // Sum of ent codegen.
)
