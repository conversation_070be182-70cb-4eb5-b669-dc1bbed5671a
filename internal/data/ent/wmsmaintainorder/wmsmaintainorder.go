// Code generated by ent, DO NOT EDIT.

package wmsmaintainorder

import (
	"time"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the wmsmaintainorder type in the database.
	Label = "wms_maintain_order"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldCreatedBy holds the string denoting the created_by field in the database.
	FieldCreatedBy = "created_by"
	// FieldUpdatedBy holds the string denoting the updated_by field in the database.
	FieldUpdatedBy = "updated_by"
	// FieldRemark holds the string denoting the remark field in the database.
	FieldRemark = "remark"
	// FieldContractUrls holds the string denoting the contract_urls field in the database.
	FieldContractUrls = "contract_urls"
	// FieldInvoiceUrls holds the string denoting the invoice_urls field in the database.
	FieldInvoiceUrls = "invoice_urls"
	// FieldAuditUrls holds the string denoting the audit_urls field in the database.
	FieldAuditUrls = "audit_urls"
	// FieldOtherUrls holds the string denoting the other_urls field in the database.
	FieldOtherUrls = "other_urls"
	// FieldOrderNo holds the string denoting the order_no field in the database.
	FieldOrderNo = "order_no"
	// FieldEquipmentNum holds the string denoting the equipment_num field in the database.
	FieldEquipmentNum = "equipment_num"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldMaintainTime holds the string denoting the maintain_time field in the database.
	FieldMaintainTime = "maintain_time"
	// Table holds the table name of the wmsmaintainorder in the database.
	Table = "wms_maintain_orders"
)

// Columns holds all SQL columns for wmsmaintainorder fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldCreatedBy,
	FieldUpdatedBy,
	FieldRemark,
	FieldContractUrls,
	FieldInvoiceUrls,
	FieldAuditUrls,
	FieldOtherUrls,
	FieldOrderNo,
	FieldEquipmentNum,
	FieldStatus,
	FieldMaintainTime,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() string
)

// OrderOption defines the ordering options for the WmsMaintainOrder queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByCreatedBy orders the results by the created_by field.
func ByCreatedBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedBy, opts...).ToFunc()
}

// ByUpdatedBy orders the results by the updated_by field.
func ByUpdatedBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedBy, opts...).ToFunc()
}

// ByRemark orders the results by the remark field.
func ByRemark(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRemark, opts...).ToFunc()
}

// ByContractUrls orders the results by the contract_urls field.
func ByContractUrls(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldContractUrls, opts...).ToFunc()
}

// ByInvoiceUrls orders the results by the invoice_urls field.
func ByInvoiceUrls(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldInvoiceUrls, opts...).ToFunc()
}

// ByAuditUrls orders the results by the audit_urls field.
func ByAuditUrls(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAuditUrls, opts...).ToFunc()
}

// ByOtherUrls orders the results by the other_urls field.
func ByOtherUrls(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOtherUrls, opts...).ToFunc()
}

// ByOrderNo orders the results by the order_no field.
func ByOrderNo(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOrderNo, opts...).ToFunc()
}

// ByEquipmentNum orders the results by the equipment_num field.
func ByEquipmentNum(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEquipmentNum, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByMaintainTime orders the results by the maintain_time field.
func ByMaintainTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMaintainTime, opts...).ToFunc()
}
