// Code generated by ent, DO NOT EDIT.

package wmstransferorder

import (
	"kratos-mono-demo/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContainsFold(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldUpdatedAt, v))
}

// CreatedBy applies equality check predicate on the "created_by" field. It's identical to CreatedByEQ.
func CreatedBy(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldCreatedBy, v))
}

// UpdatedBy applies equality check predicate on the "updated_by" field. It's identical to UpdatedByEQ.
func UpdatedBy(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldUpdatedBy, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldRemark, v))
}

// ContractUrls applies equality check predicate on the "contract_urls" field. It's identical to ContractUrlsEQ.
func ContractUrls(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldContractUrls, v))
}

// InvoiceUrls applies equality check predicate on the "invoice_urls" field. It's identical to InvoiceUrlsEQ.
func InvoiceUrls(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldInvoiceUrls, v))
}

// AuditUrls applies equality check predicate on the "audit_urls" field. It's identical to AuditUrlsEQ.
func AuditUrls(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldAuditUrls, v))
}

// OtherUrls applies equality check predicate on the "other_urls" field. It's identical to OtherUrlsEQ.
func OtherUrls(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldOtherUrls, v))
}

// OrderNo applies equality check predicate on the "order_no" field. It's identical to OrderNoEQ.
func OrderNo(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldOrderNo, v))
}

// EquipmentNum applies equality check predicate on the "equipment_num" field. It's identical to EquipmentNumEQ.
func EquipmentNum(v uint32) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldEquipmentNum, v))
}

// FromRepositoryID applies equality check predicate on the "from_repository_id" field. It's identical to FromRepositoryIDEQ.
func FromRepositoryID(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldFromRepositoryID, v))
}

// ToRepositoryID applies equality check predicate on the "to_repository_id" field. It's identical to ToRepositoryIDEQ.
func ToRepositoryID(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldToRepositoryID, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldStatus, v))
}

// TransferTime applies equality check predicate on the "transfer_time" field. It's identical to TransferTimeEQ.
func TransferTime(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldTransferTime, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLTE(FieldUpdatedAt, v))
}

// CreatedByEQ applies the EQ predicate on the "created_by" field.
func CreatedByEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldCreatedBy, v))
}

// CreatedByNEQ applies the NEQ predicate on the "created_by" field.
func CreatedByNEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNEQ(FieldCreatedBy, v))
}

// CreatedByIn applies the In predicate on the "created_by" field.
func CreatedByIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIn(FieldCreatedBy, vs...))
}

// CreatedByNotIn applies the NotIn predicate on the "created_by" field.
func CreatedByNotIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotIn(FieldCreatedBy, vs...))
}

// CreatedByGT applies the GT predicate on the "created_by" field.
func CreatedByGT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGT(FieldCreatedBy, v))
}

// CreatedByGTE applies the GTE predicate on the "created_by" field.
func CreatedByGTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGTE(FieldCreatedBy, v))
}

// CreatedByLT applies the LT predicate on the "created_by" field.
func CreatedByLT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLT(FieldCreatedBy, v))
}

// CreatedByLTE applies the LTE predicate on the "created_by" field.
func CreatedByLTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLTE(FieldCreatedBy, v))
}

// CreatedByContains applies the Contains predicate on the "created_by" field.
func CreatedByContains(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContains(FieldCreatedBy, v))
}

// CreatedByHasPrefix applies the HasPrefix predicate on the "created_by" field.
func CreatedByHasPrefix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasPrefix(FieldCreatedBy, v))
}

// CreatedByHasSuffix applies the HasSuffix predicate on the "created_by" field.
func CreatedByHasSuffix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasSuffix(FieldCreatedBy, v))
}

// CreatedByIsNil applies the IsNil predicate on the "created_by" field.
func CreatedByIsNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIsNull(FieldCreatedBy))
}

// CreatedByNotNil applies the NotNil predicate on the "created_by" field.
func CreatedByNotNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotNull(FieldCreatedBy))
}

// CreatedByEqualFold applies the EqualFold predicate on the "created_by" field.
func CreatedByEqualFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEqualFold(FieldCreatedBy, v))
}

// CreatedByContainsFold applies the ContainsFold predicate on the "created_by" field.
func CreatedByContainsFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContainsFold(FieldCreatedBy, v))
}

// UpdatedByEQ applies the EQ predicate on the "updated_by" field.
func UpdatedByEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldUpdatedBy, v))
}

// UpdatedByNEQ applies the NEQ predicate on the "updated_by" field.
func UpdatedByNEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNEQ(FieldUpdatedBy, v))
}

// UpdatedByIn applies the In predicate on the "updated_by" field.
func UpdatedByIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIn(FieldUpdatedBy, vs...))
}

// UpdatedByNotIn applies the NotIn predicate on the "updated_by" field.
func UpdatedByNotIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotIn(FieldUpdatedBy, vs...))
}

// UpdatedByGT applies the GT predicate on the "updated_by" field.
func UpdatedByGT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGT(FieldUpdatedBy, v))
}

// UpdatedByGTE applies the GTE predicate on the "updated_by" field.
func UpdatedByGTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGTE(FieldUpdatedBy, v))
}

// UpdatedByLT applies the LT predicate on the "updated_by" field.
func UpdatedByLT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLT(FieldUpdatedBy, v))
}

// UpdatedByLTE applies the LTE predicate on the "updated_by" field.
func UpdatedByLTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLTE(FieldUpdatedBy, v))
}

// UpdatedByContains applies the Contains predicate on the "updated_by" field.
func UpdatedByContains(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContains(FieldUpdatedBy, v))
}

// UpdatedByHasPrefix applies the HasPrefix predicate on the "updated_by" field.
func UpdatedByHasPrefix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasPrefix(FieldUpdatedBy, v))
}

// UpdatedByHasSuffix applies the HasSuffix predicate on the "updated_by" field.
func UpdatedByHasSuffix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasSuffix(FieldUpdatedBy, v))
}

// UpdatedByIsNil applies the IsNil predicate on the "updated_by" field.
func UpdatedByIsNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIsNull(FieldUpdatedBy))
}

// UpdatedByNotNil applies the NotNil predicate on the "updated_by" field.
func UpdatedByNotNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotNull(FieldUpdatedBy))
}

// UpdatedByEqualFold applies the EqualFold predicate on the "updated_by" field.
func UpdatedByEqualFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEqualFold(FieldUpdatedBy, v))
}

// UpdatedByContainsFold applies the ContainsFold predicate on the "updated_by" field.
func UpdatedByContainsFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContainsFold(FieldUpdatedBy, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContainsFold(FieldRemark, v))
}

// ContractUrlsEQ applies the EQ predicate on the "contract_urls" field.
func ContractUrlsEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldContractUrls, v))
}

// ContractUrlsNEQ applies the NEQ predicate on the "contract_urls" field.
func ContractUrlsNEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNEQ(FieldContractUrls, v))
}

// ContractUrlsIn applies the In predicate on the "contract_urls" field.
func ContractUrlsIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIn(FieldContractUrls, vs...))
}

// ContractUrlsNotIn applies the NotIn predicate on the "contract_urls" field.
func ContractUrlsNotIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotIn(FieldContractUrls, vs...))
}

// ContractUrlsGT applies the GT predicate on the "contract_urls" field.
func ContractUrlsGT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGT(FieldContractUrls, v))
}

// ContractUrlsGTE applies the GTE predicate on the "contract_urls" field.
func ContractUrlsGTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGTE(FieldContractUrls, v))
}

// ContractUrlsLT applies the LT predicate on the "contract_urls" field.
func ContractUrlsLT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLT(FieldContractUrls, v))
}

// ContractUrlsLTE applies the LTE predicate on the "contract_urls" field.
func ContractUrlsLTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLTE(FieldContractUrls, v))
}

// ContractUrlsContains applies the Contains predicate on the "contract_urls" field.
func ContractUrlsContains(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContains(FieldContractUrls, v))
}

// ContractUrlsHasPrefix applies the HasPrefix predicate on the "contract_urls" field.
func ContractUrlsHasPrefix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasPrefix(FieldContractUrls, v))
}

// ContractUrlsHasSuffix applies the HasSuffix predicate on the "contract_urls" field.
func ContractUrlsHasSuffix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasSuffix(FieldContractUrls, v))
}

// ContractUrlsIsNil applies the IsNil predicate on the "contract_urls" field.
func ContractUrlsIsNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIsNull(FieldContractUrls))
}

// ContractUrlsNotNil applies the NotNil predicate on the "contract_urls" field.
func ContractUrlsNotNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotNull(FieldContractUrls))
}

// ContractUrlsEqualFold applies the EqualFold predicate on the "contract_urls" field.
func ContractUrlsEqualFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEqualFold(FieldContractUrls, v))
}

// ContractUrlsContainsFold applies the ContainsFold predicate on the "contract_urls" field.
func ContractUrlsContainsFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContainsFold(FieldContractUrls, v))
}

// InvoiceUrlsEQ applies the EQ predicate on the "invoice_urls" field.
func InvoiceUrlsEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldInvoiceUrls, v))
}

// InvoiceUrlsNEQ applies the NEQ predicate on the "invoice_urls" field.
func InvoiceUrlsNEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNEQ(FieldInvoiceUrls, v))
}

// InvoiceUrlsIn applies the In predicate on the "invoice_urls" field.
func InvoiceUrlsIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIn(FieldInvoiceUrls, vs...))
}

// InvoiceUrlsNotIn applies the NotIn predicate on the "invoice_urls" field.
func InvoiceUrlsNotIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotIn(FieldInvoiceUrls, vs...))
}

// InvoiceUrlsGT applies the GT predicate on the "invoice_urls" field.
func InvoiceUrlsGT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGT(FieldInvoiceUrls, v))
}

// InvoiceUrlsGTE applies the GTE predicate on the "invoice_urls" field.
func InvoiceUrlsGTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGTE(FieldInvoiceUrls, v))
}

// InvoiceUrlsLT applies the LT predicate on the "invoice_urls" field.
func InvoiceUrlsLT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLT(FieldInvoiceUrls, v))
}

// InvoiceUrlsLTE applies the LTE predicate on the "invoice_urls" field.
func InvoiceUrlsLTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLTE(FieldInvoiceUrls, v))
}

// InvoiceUrlsContains applies the Contains predicate on the "invoice_urls" field.
func InvoiceUrlsContains(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContains(FieldInvoiceUrls, v))
}

// InvoiceUrlsHasPrefix applies the HasPrefix predicate on the "invoice_urls" field.
func InvoiceUrlsHasPrefix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasPrefix(FieldInvoiceUrls, v))
}

// InvoiceUrlsHasSuffix applies the HasSuffix predicate on the "invoice_urls" field.
func InvoiceUrlsHasSuffix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasSuffix(FieldInvoiceUrls, v))
}

// InvoiceUrlsIsNil applies the IsNil predicate on the "invoice_urls" field.
func InvoiceUrlsIsNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIsNull(FieldInvoiceUrls))
}

// InvoiceUrlsNotNil applies the NotNil predicate on the "invoice_urls" field.
func InvoiceUrlsNotNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotNull(FieldInvoiceUrls))
}

// InvoiceUrlsEqualFold applies the EqualFold predicate on the "invoice_urls" field.
func InvoiceUrlsEqualFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEqualFold(FieldInvoiceUrls, v))
}

// InvoiceUrlsContainsFold applies the ContainsFold predicate on the "invoice_urls" field.
func InvoiceUrlsContainsFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContainsFold(FieldInvoiceUrls, v))
}

// AuditUrlsEQ applies the EQ predicate on the "audit_urls" field.
func AuditUrlsEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldAuditUrls, v))
}

// AuditUrlsNEQ applies the NEQ predicate on the "audit_urls" field.
func AuditUrlsNEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNEQ(FieldAuditUrls, v))
}

// AuditUrlsIn applies the In predicate on the "audit_urls" field.
func AuditUrlsIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIn(FieldAuditUrls, vs...))
}

// AuditUrlsNotIn applies the NotIn predicate on the "audit_urls" field.
func AuditUrlsNotIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotIn(FieldAuditUrls, vs...))
}

// AuditUrlsGT applies the GT predicate on the "audit_urls" field.
func AuditUrlsGT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGT(FieldAuditUrls, v))
}

// AuditUrlsGTE applies the GTE predicate on the "audit_urls" field.
func AuditUrlsGTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGTE(FieldAuditUrls, v))
}

// AuditUrlsLT applies the LT predicate on the "audit_urls" field.
func AuditUrlsLT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLT(FieldAuditUrls, v))
}

// AuditUrlsLTE applies the LTE predicate on the "audit_urls" field.
func AuditUrlsLTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLTE(FieldAuditUrls, v))
}

// AuditUrlsContains applies the Contains predicate on the "audit_urls" field.
func AuditUrlsContains(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContains(FieldAuditUrls, v))
}

// AuditUrlsHasPrefix applies the HasPrefix predicate on the "audit_urls" field.
func AuditUrlsHasPrefix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasPrefix(FieldAuditUrls, v))
}

// AuditUrlsHasSuffix applies the HasSuffix predicate on the "audit_urls" field.
func AuditUrlsHasSuffix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasSuffix(FieldAuditUrls, v))
}

// AuditUrlsIsNil applies the IsNil predicate on the "audit_urls" field.
func AuditUrlsIsNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIsNull(FieldAuditUrls))
}

// AuditUrlsNotNil applies the NotNil predicate on the "audit_urls" field.
func AuditUrlsNotNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotNull(FieldAuditUrls))
}

// AuditUrlsEqualFold applies the EqualFold predicate on the "audit_urls" field.
func AuditUrlsEqualFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEqualFold(FieldAuditUrls, v))
}

// AuditUrlsContainsFold applies the ContainsFold predicate on the "audit_urls" field.
func AuditUrlsContainsFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContainsFold(FieldAuditUrls, v))
}

// OtherUrlsEQ applies the EQ predicate on the "other_urls" field.
func OtherUrlsEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldOtherUrls, v))
}

// OtherUrlsNEQ applies the NEQ predicate on the "other_urls" field.
func OtherUrlsNEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNEQ(FieldOtherUrls, v))
}

// OtherUrlsIn applies the In predicate on the "other_urls" field.
func OtherUrlsIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIn(FieldOtherUrls, vs...))
}

// OtherUrlsNotIn applies the NotIn predicate on the "other_urls" field.
func OtherUrlsNotIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotIn(FieldOtherUrls, vs...))
}

// OtherUrlsGT applies the GT predicate on the "other_urls" field.
func OtherUrlsGT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGT(FieldOtherUrls, v))
}

// OtherUrlsGTE applies the GTE predicate on the "other_urls" field.
func OtherUrlsGTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGTE(FieldOtherUrls, v))
}

// OtherUrlsLT applies the LT predicate on the "other_urls" field.
func OtherUrlsLT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLT(FieldOtherUrls, v))
}

// OtherUrlsLTE applies the LTE predicate on the "other_urls" field.
func OtherUrlsLTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLTE(FieldOtherUrls, v))
}

// OtherUrlsContains applies the Contains predicate on the "other_urls" field.
func OtherUrlsContains(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContains(FieldOtherUrls, v))
}

// OtherUrlsHasPrefix applies the HasPrefix predicate on the "other_urls" field.
func OtherUrlsHasPrefix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasPrefix(FieldOtherUrls, v))
}

// OtherUrlsHasSuffix applies the HasSuffix predicate on the "other_urls" field.
func OtherUrlsHasSuffix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasSuffix(FieldOtherUrls, v))
}

// OtherUrlsIsNil applies the IsNil predicate on the "other_urls" field.
func OtherUrlsIsNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIsNull(FieldOtherUrls))
}

// OtherUrlsNotNil applies the NotNil predicate on the "other_urls" field.
func OtherUrlsNotNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotNull(FieldOtherUrls))
}

// OtherUrlsEqualFold applies the EqualFold predicate on the "other_urls" field.
func OtherUrlsEqualFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEqualFold(FieldOtherUrls, v))
}

// OtherUrlsContainsFold applies the ContainsFold predicate on the "other_urls" field.
func OtherUrlsContainsFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContainsFold(FieldOtherUrls, v))
}

// OrderNoEQ applies the EQ predicate on the "order_no" field.
func OrderNoEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldOrderNo, v))
}

// OrderNoNEQ applies the NEQ predicate on the "order_no" field.
func OrderNoNEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNEQ(FieldOrderNo, v))
}

// OrderNoIn applies the In predicate on the "order_no" field.
func OrderNoIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIn(FieldOrderNo, vs...))
}

// OrderNoNotIn applies the NotIn predicate on the "order_no" field.
func OrderNoNotIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotIn(FieldOrderNo, vs...))
}

// OrderNoGT applies the GT predicate on the "order_no" field.
func OrderNoGT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGT(FieldOrderNo, v))
}

// OrderNoGTE applies the GTE predicate on the "order_no" field.
func OrderNoGTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGTE(FieldOrderNo, v))
}

// OrderNoLT applies the LT predicate on the "order_no" field.
func OrderNoLT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLT(FieldOrderNo, v))
}

// OrderNoLTE applies the LTE predicate on the "order_no" field.
func OrderNoLTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLTE(FieldOrderNo, v))
}

// OrderNoContains applies the Contains predicate on the "order_no" field.
func OrderNoContains(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContains(FieldOrderNo, v))
}

// OrderNoHasPrefix applies the HasPrefix predicate on the "order_no" field.
func OrderNoHasPrefix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasPrefix(FieldOrderNo, v))
}

// OrderNoHasSuffix applies the HasSuffix predicate on the "order_no" field.
func OrderNoHasSuffix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasSuffix(FieldOrderNo, v))
}

// OrderNoEqualFold applies the EqualFold predicate on the "order_no" field.
func OrderNoEqualFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEqualFold(FieldOrderNo, v))
}

// OrderNoContainsFold applies the ContainsFold predicate on the "order_no" field.
func OrderNoContainsFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContainsFold(FieldOrderNo, v))
}

// EquipmentNumEQ applies the EQ predicate on the "equipment_num" field.
func EquipmentNumEQ(v uint32) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldEquipmentNum, v))
}

// EquipmentNumNEQ applies the NEQ predicate on the "equipment_num" field.
func EquipmentNumNEQ(v uint32) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNEQ(FieldEquipmentNum, v))
}

// EquipmentNumIn applies the In predicate on the "equipment_num" field.
func EquipmentNumIn(vs ...uint32) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIn(FieldEquipmentNum, vs...))
}

// EquipmentNumNotIn applies the NotIn predicate on the "equipment_num" field.
func EquipmentNumNotIn(vs ...uint32) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotIn(FieldEquipmentNum, vs...))
}

// EquipmentNumGT applies the GT predicate on the "equipment_num" field.
func EquipmentNumGT(v uint32) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGT(FieldEquipmentNum, v))
}

// EquipmentNumGTE applies the GTE predicate on the "equipment_num" field.
func EquipmentNumGTE(v uint32) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGTE(FieldEquipmentNum, v))
}

// EquipmentNumLT applies the LT predicate on the "equipment_num" field.
func EquipmentNumLT(v uint32) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLT(FieldEquipmentNum, v))
}

// EquipmentNumLTE applies the LTE predicate on the "equipment_num" field.
func EquipmentNumLTE(v uint32) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLTE(FieldEquipmentNum, v))
}

// EquipmentNumIsNil applies the IsNil predicate on the "equipment_num" field.
func EquipmentNumIsNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIsNull(FieldEquipmentNum))
}

// EquipmentNumNotNil applies the NotNil predicate on the "equipment_num" field.
func EquipmentNumNotNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotNull(FieldEquipmentNum))
}

// FromRepositoryIDEQ applies the EQ predicate on the "from_repository_id" field.
func FromRepositoryIDEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldFromRepositoryID, v))
}

// FromRepositoryIDNEQ applies the NEQ predicate on the "from_repository_id" field.
func FromRepositoryIDNEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNEQ(FieldFromRepositoryID, v))
}

// FromRepositoryIDIn applies the In predicate on the "from_repository_id" field.
func FromRepositoryIDIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIn(FieldFromRepositoryID, vs...))
}

// FromRepositoryIDNotIn applies the NotIn predicate on the "from_repository_id" field.
func FromRepositoryIDNotIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotIn(FieldFromRepositoryID, vs...))
}

// FromRepositoryIDGT applies the GT predicate on the "from_repository_id" field.
func FromRepositoryIDGT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGT(FieldFromRepositoryID, v))
}

// FromRepositoryIDGTE applies the GTE predicate on the "from_repository_id" field.
func FromRepositoryIDGTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGTE(FieldFromRepositoryID, v))
}

// FromRepositoryIDLT applies the LT predicate on the "from_repository_id" field.
func FromRepositoryIDLT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLT(FieldFromRepositoryID, v))
}

// FromRepositoryIDLTE applies the LTE predicate on the "from_repository_id" field.
func FromRepositoryIDLTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLTE(FieldFromRepositoryID, v))
}

// FromRepositoryIDContains applies the Contains predicate on the "from_repository_id" field.
func FromRepositoryIDContains(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContains(FieldFromRepositoryID, v))
}

// FromRepositoryIDHasPrefix applies the HasPrefix predicate on the "from_repository_id" field.
func FromRepositoryIDHasPrefix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasPrefix(FieldFromRepositoryID, v))
}

// FromRepositoryIDHasSuffix applies the HasSuffix predicate on the "from_repository_id" field.
func FromRepositoryIDHasSuffix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasSuffix(FieldFromRepositoryID, v))
}

// FromRepositoryIDIsNil applies the IsNil predicate on the "from_repository_id" field.
func FromRepositoryIDIsNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIsNull(FieldFromRepositoryID))
}

// FromRepositoryIDNotNil applies the NotNil predicate on the "from_repository_id" field.
func FromRepositoryIDNotNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotNull(FieldFromRepositoryID))
}

// FromRepositoryIDEqualFold applies the EqualFold predicate on the "from_repository_id" field.
func FromRepositoryIDEqualFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEqualFold(FieldFromRepositoryID, v))
}

// FromRepositoryIDContainsFold applies the ContainsFold predicate on the "from_repository_id" field.
func FromRepositoryIDContainsFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContainsFold(FieldFromRepositoryID, v))
}

// ToRepositoryIDEQ applies the EQ predicate on the "to_repository_id" field.
func ToRepositoryIDEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldToRepositoryID, v))
}

// ToRepositoryIDNEQ applies the NEQ predicate on the "to_repository_id" field.
func ToRepositoryIDNEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNEQ(FieldToRepositoryID, v))
}

// ToRepositoryIDIn applies the In predicate on the "to_repository_id" field.
func ToRepositoryIDIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIn(FieldToRepositoryID, vs...))
}

// ToRepositoryIDNotIn applies the NotIn predicate on the "to_repository_id" field.
func ToRepositoryIDNotIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotIn(FieldToRepositoryID, vs...))
}

// ToRepositoryIDGT applies the GT predicate on the "to_repository_id" field.
func ToRepositoryIDGT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGT(FieldToRepositoryID, v))
}

// ToRepositoryIDGTE applies the GTE predicate on the "to_repository_id" field.
func ToRepositoryIDGTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGTE(FieldToRepositoryID, v))
}

// ToRepositoryIDLT applies the LT predicate on the "to_repository_id" field.
func ToRepositoryIDLT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLT(FieldToRepositoryID, v))
}

// ToRepositoryIDLTE applies the LTE predicate on the "to_repository_id" field.
func ToRepositoryIDLTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLTE(FieldToRepositoryID, v))
}

// ToRepositoryIDContains applies the Contains predicate on the "to_repository_id" field.
func ToRepositoryIDContains(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContains(FieldToRepositoryID, v))
}

// ToRepositoryIDHasPrefix applies the HasPrefix predicate on the "to_repository_id" field.
func ToRepositoryIDHasPrefix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasPrefix(FieldToRepositoryID, v))
}

// ToRepositoryIDHasSuffix applies the HasSuffix predicate on the "to_repository_id" field.
func ToRepositoryIDHasSuffix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasSuffix(FieldToRepositoryID, v))
}

// ToRepositoryIDIsNil applies the IsNil predicate on the "to_repository_id" field.
func ToRepositoryIDIsNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIsNull(FieldToRepositoryID))
}

// ToRepositoryIDNotNil applies the NotNil predicate on the "to_repository_id" field.
func ToRepositoryIDNotNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotNull(FieldToRepositoryID))
}

// ToRepositoryIDEqualFold applies the EqualFold predicate on the "to_repository_id" field.
func ToRepositoryIDEqualFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEqualFold(FieldToRepositoryID, v))
}

// ToRepositoryIDContainsFold applies the ContainsFold predicate on the "to_repository_id" field.
func ToRepositoryIDContainsFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContainsFold(FieldToRepositoryID, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLTE(FieldStatus, v))
}

// StatusContains applies the Contains predicate on the "status" field.
func StatusContains(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContains(FieldStatus, v))
}

// StatusHasPrefix applies the HasPrefix predicate on the "status" field.
func StatusHasPrefix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasPrefix(FieldStatus, v))
}

// StatusHasSuffix applies the HasSuffix predicate on the "status" field.
func StatusHasSuffix(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldHasSuffix(FieldStatus, v))
}

// StatusIsNil applies the IsNil predicate on the "status" field.
func StatusIsNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIsNull(FieldStatus))
}

// StatusNotNil applies the NotNil predicate on the "status" field.
func StatusNotNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotNull(FieldStatus))
}

// StatusEqualFold applies the EqualFold predicate on the "status" field.
func StatusEqualFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEqualFold(FieldStatus, v))
}

// StatusContainsFold applies the ContainsFold predicate on the "status" field.
func StatusContainsFold(v string) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldContainsFold(FieldStatus, v))
}

// TransferTimeEQ applies the EQ predicate on the "transfer_time" field.
func TransferTimeEQ(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldEQ(FieldTransferTime, v))
}

// TransferTimeNEQ applies the NEQ predicate on the "transfer_time" field.
func TransferTimeNEQ(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNEQ(FieldTransferTime, v))
}

// TransferTimeIn applies the In predicate on the "transfer_time" field.
func TransferTimeIn(vs ...time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIn(FieldTransferTime, vs...))
}

// TransferTimeNotIn applies the NotIn predicate on the "transfer_time" field.
func TransferTimeNotIn(vs ...time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotIn(FieldTransferTime, vs...))
}

// TransferTimeGT applies the GT predicate on the "transfer_time" field.
func TransferTimeGT(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGT(FieldTransferTime, v))
}

// TransferTimeGTE applies the GTE predicate on the "transfer_time" field.
func TransferTimeGTE(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldGTE(FieldTransferTime, v))
}

// TransferTimeLT applies the LT predicate on the "transfer_time" field.
func TransferTimeLT(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLT(FieldTransferTime, v))
}

// TransferTimeLTE applies the LTE predicate on the "transfer_time" field.
func TransferTimeLTE(v time.Time) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldLTE(FieldTransferTime, v))
}

// TransferTimeIsNil applies the IsNil predicate on the "transfer_time" field.
func TransferTimeIsNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldIsNull(FieldTransferTime))
}

// TransferTimeNotNil applies the NotNil predicate on the "transfer_time" field.
func TransferTimeNotNil() predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.FieldNotNull(FieldTransferTime))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.WmsTransferOrder) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.WmsTransferOrder) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.WmsTransferOrder) predicate.WmsTransferOrder {
	return predicate.WmsTransferOrder(sql.NotPredicates(p))
}
