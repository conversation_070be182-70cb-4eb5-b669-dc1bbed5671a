// Code generated by ent, DO NOT EDIT.

package wmsenterrepositoryorderdetail

import (
	"kratos-mono-demo/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContainsFold(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldUpdatedAt, v))
}

// CreatedBy applies equality check predicate on the "created_by" field. It's identical to CreatedByEQ.
func CreatedBy(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldCreatedBy, v))
}

// UpdatedBy applies equality check predicate on the "updated_by" field. It's identical to UpdatedByEQ.
func UpdatedBy(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldUpdatedBy, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldRemark, v))
}

// EnterRepositoryOrderID applies equality check predicate on the "enter_repository_order_id" field. It's identical to EnterRepositoryOrderIDEQ.
func EnterRepositoryOrderID(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldEnterRepositoryOrderID, v))
}

// EquipmentTypeID applies equality check predicate on the "equipment_type_id" field. It's identical to EquipmentTypeIDEQ.
func EquipmentTypeID(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldEquipmentTypeID, v))
}

// EquipmentID applies equality check predicate on the "equipment_id" field. It's identical to EquipmentIDEQ.
func EquipmentID(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldEquipmentID, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldName, v))
}

// Code applies equality check predicate on the "code" field. It's identical to CodeEQ.
func Code(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldCode, v))
}

// CodeType applies equality check predicate on the "code_type" field. It's identical to CodeTypeEQ.
func CodeType(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldCodeType, v))
}

// ModelNo applies equality check predicate on the "model_no" field. It's identical to ModelNoEQ.
func ModelNo(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldModelNo, v))
}

// MeasureUnitID applies equality check predicate on the "measure_unit_id" field. It's identical to MeasureUnitIDEQ.
func MeasureUnitID(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldMeasureUnitID, v))
}

// ProviderID applies equality check predicate on the "provider_id" field. It's identical to ProviderIDEQ.
func ProviderID(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldProviderID, v))
}

// Num applies equality check predicate on the "num" field. It's identical to NumEQ.
func Num(v uint32) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldNum, v))
}

// Price applies equality check predicate on the "price" field. It's identical to PriceEQ.
func Price(v uint64) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldPrice, v))
}

// RepositoryAreaID applies equality check predicate on the "repository_area_id" field. It's identical to RepositoryAreaIDEQ.
func RepositoryAreaID(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldRepositoryAreaID, v))
}

// RepositoryPositionID applies equality check predicate on the "repository_position_id" field. It's identical to RepositoryPositionIDEQ.
func RepositoryPositionID(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldRepositoryPositionID, v))
}

// FromRepositoryID applies equality check predicate on the "from_repository_id" field. It's identical to FromRepositoryIDEQ.
func FromRepositoryID(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldFromRepositoryID, v))
}

// ToRepositoryID applies equality check predicate on the "to_repository_id" field. It's identical to ToRepositoryIDEQ.
func ToRepositoryID(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldToRepositoryID, v))
}

// OrderNo applies equality check predicate on the "order_no" field. It's identical to OrderNoEQ.
func OrderNo(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldOrderNo, v))
}

// ExpireTime applies equality check predicate on the "expire_time" field. It's identical to ExpireTimeEQ.
func ExpireTime(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldExpireTime, v))
}

// MaterialID applies equality check predicate on the "material_id" field. It's identical to MaterialIDEQ.
func MaterialID(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldMaterialID, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldUpdatedAt, v))
}

// CreatedByEQ applies the EQ predicate on the "created_by" field.
func CreatedByEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldCreatedBy, v))
}

// CreatedByNEQ applies the NEQ predicate on the "created_by" field.
func CreatedByNEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldCreatedBy, v))
}

// CreatedByIn applies the In predicate on the "created_by" field.
func CreatedByIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldCreatedBy, vs...))
}

// CreatedByNotIn applies the NotIn predicate on the "created_by" field.
func CreatedByNotIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldCreatedBy, vs...))
}

// CreatedByGT applies the GT predicate on the "created_by" field.
func CreatedByGT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldCreatedBy, v))
}

// CreatedByGTE applies the GTE predicate on the "created_by" field.
func CreatedByGTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldCreatedBy, v))
}

// CreatedByLT applies the LT predicate on the "created_by" field.
func CreatedByLT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldCreatedBy, v))
}

// CreatedByLTE applies the LTE predicate on the "created_by" field.
func CreatedByLTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldCreatedBy, v))
}

// CreatedByContains applies the Contains predicate on the "created_by" field.
func CreatedByContains(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContains(FieldCreatedBy, v))
}

// CreatedByHasPrefix applies the HasPrefix predicate on the "created_by" field.
func CreatedByHasPrefix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasPrefix(FieldCreatedBy, v))
}

// CreatedByHasSuffix applies the HasSuffix predicate on the "created_by" field.
func CreatedByHasSuffix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasSuffix(FieldCreatedBy, v))
}

// CreatedByIsNil applies the IsNil predicate on the "created_by" field.
func CreatedByIsNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIsNull(FieldCreatedBy))
}

// CreatedByNotNil applies the NotNil predicate on the "created_by" field.
func CreatedByNotNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotNull(FieldCreatedBy))
}

// CreatedByEqualFold applies the EqualFold predicate on the "created_by" field.
func CreatedByEqualFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEqualFold(FieldCreatedBy, v))
}

// CreatedByContainsFold applies the ContainsFold predicate on the "created_by" field.
func CreatedByContainsFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContainsFold(FieldCreatedBy, v))
}

// UpdatedByEQ applies the EQ predicate on the "updated_by" field.
func UpdatedByEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldUpdatedBy, v))
}

// UpdatedByNEQ applies the NEQ predicate on the "updated_by" field.
func UpdatedByNEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldUpdatedBy, v))
}

// UpdatedByIn applies the In predicate on the "updated_by" field.
func UpdatedByIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldUpdatedBy, vs...))
}

// UpdatedByNotIn applies the NotIn predicate on the "updated_by" field.
func UpdatedByNotIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldUpdatedBy, vs...))
}

// UpdatedByGT applies the GT predicate on the "updated_by" field.
func UpdatedByGT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldUpdatedBy, v))
}

// UpdatedByGTE applies the GTE predicate on the "updated_by" field.
func UpdatedByGTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldUpdatedBy, v))
}

// UpdatedByLT applies the LT predicate on the "updated_by" field.
func UpdatedByLT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldUpdatedBy, v))
}

// UpdatedByLTE applies the LTE predicate on the "updated_by" field.
func UpdatedByLTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldUpdatedBy, v))
}

// UpdatedByContains applies the Contains predicate on the "updated_by" field.
func UpdatedByContains(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContains(FieldUpdatedBy, v))
}

// UpdatedByHasPrefix applies the HasPrefix predicate on the "updated_by" field.
func UpdatedByHasPrefix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasPrefix(FieldUpdatedBy, v))
}

// UpdatedByHasSuffix applies the HasSuffix predicate on the "updated_by" field.
func UpdatedByHasSuffix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasSuffix(FieldUpdatedBy, v))
}

// UpdatedByIsNil applies the IsNil predicate on the "updated_by" field.
func UpdatedByIsNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIsNull(FieldUpdatedBy))
}

// UpdatedByNotNil applies the NotNil predicate on the "updated_by" field.
func UpdatedByNotNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotNull(FieldUpdatedBy))
}

// UpdatedByEqualFold applies the EqualFold predicate on the "updated_by" field.
func UpdatedByEqualFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEqualFold(FieldUpdatedBy, v))
}

// UpdatedByContainsFold applies the ContainsFold predicate on the "updated_by" field.
func UpdatedByContainsFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContainsFold(FieldUpdatedBy, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContainsFold(FieldRemark, v))
}

// EnterRepositoryOrderIDEQ applies the EQ predicate on the "enter_repository_order_id" field.
func EnterRepositoryOrderIDEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldEnterRepositoryOrderID, v))
}

// EnterRepositoryOrderIDNEQ applies the NEQ predicate on the "enter_repository_order_id" field.
func EnterRepositoryOrderIDNEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldEnterRepositoryOrderID, v))
}

// EnterRepositoryOrderIDIn applies the In predicate on the "enter_repository_order_id" field.
func EnterRepositoryOrderIDIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldEnterRepositoryOrderID, vs...))
}

// EnterRepositoryOrderIDNotIn applies the NotIn predicate on the "enter_repository_order_id" field.
func EnterRepositoryOrderIDNotIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldEnterRepositoryOrderID, vs...))
}

// EnterRepositoryOrderIDGT applies the GT predicate on the "enter_repository_order_id" field.
func EnterRepositoryOrderIDGT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldEnterRepositoryOrderID, v))
}

// EnterRepositoryOrderIDGTE applies the GTE predicate on the "enter_repository_order_id" field.
func EnterRepositoryOrderIDGTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldEnterRepositoryOrderID, v))
}

// EnterRepositoryOrderIDLT applies the LT predicate on the "enter_repository_order_id" field.
func EnterRepositoryOrderIDLT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldEnterRepositoryOrderID, v))
}

// EnterRepositoryOrderIDLTE applies the LTE predicate on the "enter_repository_order_id" field.
func EnterRepositoryOrderIDLTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldEnterRepositoryOrderID, v))
}

// EnterRepositoryOrderIDContains applies the Contains predicate on the "enter_repository_order_id" field.
func EnterRepositoryOrderIDContains(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContains(FieldEnterRepositoryOrderID, v))
}

// EnterRepositoryOrderIDHasPrefix applies the HasPrefix predicate on the "enter_repository_order_id" field.
func EnterRepositoryOrderIDHasPrefix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasPrefix(FieldEnterRepositoryOrderID, v))
}

// EnterRepositoryOrderIDHasSuffix applies the HasSuffix predicate on the "enter_repository_order_id" field.
func EnterRepositoryOrderIDHasSuffix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasSuffix(FieldEnterRepositoryOrderID, v))
}

// EnterRepositoryOrderIDIsNil applies the IsNil predicate on the "enter_repository_order_id" field.
func EnterRepositoryOrderIDIsNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIsNull(FieldEnterRepositoryOrderID))
}

// EnterRepositoryOrderIDNotNil applies the NotNil predicate on the "enter_repository_order_id" field.
func EnterRepositoryOrderIDNotNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotNull(FieldEnterRepositoryOrderID))
}

// EnterRepositoryOrderIDEqualFold applies the EqualFold predicate on the "enter_repository_order_id" field.
func EnterRepositoryOrderIDEqualFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEqualFold(FieldEnterRepositoryOrderID, v))
}

// EnterRepositoryOrderIDContainsFold applies the ContainsFold predicate on the "enter_repository_order_id" field.
func EnterRepositoryOrderIDContainsFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContainsFold(FieldEnterRepositoryOrderID, v))
}

// EquipmentTypeIDEQ applies the EQ predicate on the "equipment_type_id" field.
func EquipmentTypeIDEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDNEQ applies the NEQ predicate on the "equipment_type_id" field.
func EquipmentTypeIDNEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDIn applies the In predicate on the "equipment_type_id" field.
func EquipmentTypeIDIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldEquipmentTypeID, vs...))
}

// EquipmentTypeIDNotIn applies the NotIn predicate on the "equipment_type_id" field.
func EquipmentTypeIDNotIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldEquipmentTypeID, vs...))
}

// EquipmentTypeIDGT applies the GT predicate on the "equipment_type_id" field.
func EquipmentTypeIDGT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDGTE applies the GTE predicate on the "equipment_type_id" field.
func EquipmentTypeIDGTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDLT applies the LT predicate on the "equipment_type_id" field.
func EquipmentTypeIDLT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDLTE applies the LTE predicate on the "equipment_type_id" field.
func EquipmentTypeIDLTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDContains applies the Contains predicate on the "equipment_type_id" field.
func EquipmentTypeIDContains(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContains(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDHasPrefix applies the HasPrefix predicate on the "equipment_type_id" field.
func EquipmentTypeIDHasPrefix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasPrefix(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDHasSuffix applies the HasSuffix predicate on the "equipment_type_id" field.
func EquipmentTypeIDHasSuffix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasSuffix(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDIsNil applies the IsNil predicate on the "equipment_type_id" field.
func EquipmentTypeIDIsNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIsNull(FieldEquipmentTypeID))
}

// EquipmentTypeIDNotNil applies the NotNil predicate on the "equipment_type_id" field.
func EquipmentTypeIDNotNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotNull(FieldEquipmentTypeID))
}

// EquipmentTypeIDEqualFold applies the EqualFold predicate on the "equipment_type_id" field.
func EquipmentTypeIDEqualFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEqualFold(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDContainsFold applies the ContainsFold predicate on the "equipment_type_id" field.
func EquipmentTypeIDContainsFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContainsFold(FieldEquipmentTypeID, v))
}

// EquipmentIDEQ applies the EQ predicate on the "equipment_id" field.
func EquipmentIDEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldEquipmentID, v))
}

// EquipmentIDNEQ applies the NEQ predicate on the "equipment_id" field.
func EquipmentIDNEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldEquipmentID, v))
}

// EquipmentIDIn applies the In predicate on the "equipment_id" field.
func EquipmentIDIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldEquipmentID, vs...))
}

// EquipmentIDNotIn applies the NotIn predicate on the "equipment_id" field.
func EquipmentIDNotIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldEquipmentID, vs...))
}

// EquipmentIDGT applies the GT predicate on the "equipment_id" field.
func EquipmentIDGT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldEquipmentID, v))
}

// EquipmentIDGTE applies the GTE predicate on the "equipment_id" field.
func EquipmentIDGTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldEquipmentID, v))
}

// EquipmentIDLT applies the LT predicate on the "equipment_id" field.
func EquipmentIDLT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldEquipmentID, v))
}

// EquipmentIDLTE applies the LTE predicate on the "equipment_id" field.
func EquipmentIDLTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldEquipmentID, v))
}

// EquipmentIDContains applies the Contains predicate on the "equipment_id" field.
func EquipmentIDContains(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContains(FieldEquipmentID, v))
}

// EquipmentIDHasPrefix applies the HasPrefix predicate on the "equipment_id" field.
func EquipmentIDHasPrefix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasPrefix(FieldEquipmentID, v))
}

// EquipmentIDHasSuffix applies the HasSuffix predicate on the "equipment_id" field.
func EquipmentIDHasSuffix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasSuffix(FieldEquipmentID, v))
}

// EquipmentIDIsNil applies the IsNil predicate on the "equipment_id" field.
func EquipmentIDIsNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIsNull(FieldEquipmentID))
}

// EquipmentIDNotNil applies the NotNil predicate on the "equipment_id" field.
func EquipmentIDNotNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotNull(FieldEquipmentID))
}

// EquipmentIDEqualFold applies the EqualFold predicate on the "equipment_id" field.
func EquipmentIDEqualFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEqualFold(FieldEquipmentID, v))
}

// EquipmentIDContainsFold applies the ContainsFold predicate on the "equipment_id" field.
func EquipmentIDContainsFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContainsFold(FieldEquipmentID, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasSuffix(FieldName, v))
}

// NameIsNil applies the IsNil predicate on the "name" field.
func NameIsNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIsNull(FieldName))
}

// NameNotNil applies the NotNil predicate on the "name" field.
func NameNotNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotNull(FieldName))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContainsFold(FieldName, v))
}

// CodeEQ applies the EQ predicate on the "code" field.
func CodeEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldCode, v))
}

// CodeNEQ applies the NEQ predicate on the "code" field.
func CodeNEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldCode, v))
}

// CodeIn applies the In predicate on the "code" field.
func CodeIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldCode, vs...))
}

// CodeNotIn applies the NotIn predicate on the "code" field.
func CodeNotIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldCode, vs...))
}

// CodeGT applies the GT predicate on the "code" field.
func CodeGT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldCode, v))
}

// CodeGTE applies the GTE predicate on the "code" field.
func CodeGTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldCode, v))
}

// CodeLT applies the LT predicate on the "code" field.
func CodeLT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldCode, v))
}

// CodeLTE applies the LTE predicate on the "code" field.
func CodeLTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldCode, v))
}

// CodeContains applies the Contains predicate on the "code" field.
func CodeContains(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContains(FieldCode, v))
}

// CodeHasPrefix applies the HasPrefix predicate on the "code" field.
func CodeHasPrefix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasPrefix(FieldCode, v))
}

// CodeHasSuffix applies the HasSuffix predicate on the "code" field.
func CodeHasSuffix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasSuffix(FieldCode, v))
}

// CodeIsNil applies the IsNil predicate on the "code" field.
func CodeIsNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIsNull(FieldCode))
}

// CodeNotNil applies the NotNil predicate on the "code" field.
func CodeNotNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotNull(FieldCode))
}

// CodeEqualFold applies the EqualFold predicate on the "code" field.
func CodeEqualFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEqualFold(FieldCode, v))
}

// CodeContainsFold applies the ContainsFold predicate on the "code" field.
func CodeContainsFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContainsFold(FieldCode, v))
}

// CodeTypeEQ applies the EQ predicate on the "code_type" field.
func CodeTypeEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldCodeType, v))
}

// CodeTypeNEQ applies the NEQ predicate on the "code_type" field.
func CodeTypeNEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldCodeType, v))
}

// CodeTypeIn applies the In predicate on the "code_type" field.
func CodeTypeIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldCodeType, vs...))
}

// CodeTypeNotIn applies the NotIn predicate on the "code_type" field.
func CodeTypeNotIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldCodeType, vs...))
}

// CodeTypeGT applies the GT predicate on the "code_type" field.
func CodeTypeGT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldCodeType, v))
}

// CodeTypeGTE applies the GTE predicate on the "code_type" field.
func CodeTypeGTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldCodeType, v))
}

// CodeTypeLT applies the LT predicate on the "code_type" field.
func CodeTypeLT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldCodeType, v))
}

// CodeTypeLTE applies the LTE predicate on the "code_type" field.
func CodeTypeLTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldCodeType, v))
}

// CodeTypeContains applies the Contains predicate on the "code_type" field.
func CodeTypeContains(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContains(FieldCodeType, v))
}

// CodeTypeHasPrefix applies the HasPrefix predicate on the "code_type" field.
func CodeTypeHasPrefix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasPrefix(FieldCodeType, v))
}

// CodeTypeHasSuffix applies the HasSuffix predicate on the "code_type" field.
func CodeTypeHasSuffix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasSuffix(FieldCodeType, v))
}

// CodeTypeIsNil applies the IsNil predicate on the "code_type" field.
func CodeTypeIsNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIsNull(FieldCodeType))
}

// CodeTypeNotNil applies the NotNil predicate on the "code_type" field.
func CodeTypeNotNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotNull(FieldCodeType))
}

// CodeTypeEqualFold applies the EqualFold predicate on the "code_type" field.
func CodeTypeEqualFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEqualFold(FieldCodeType, v))
}

// CodeTypeContainsFold applies the ContainsFold predicate on the "code_type" field.
func CodeTypeContainsFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContainsFold(FieldCodeType, v))
}

// ModelNoEQ applies the EQ predicate on the "model_no" field.
func ModelNoEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldModelNo, v))
}

// ModelNoNEQ applies the NEQ predicate on the "model_no" field.
func ModelNoNEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldModelNo, v))
}

// ModelNoIn applies the In predicate on the "model_no" field.
func ModelNoIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldModelNo, vs...))
}

// ModelNoNotIn applies the NotIn predicate on the "model_no" field.
func ModelNoNotIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldModelNo, vs...))
}

// ModelNoGT applies the GT predicate on the "model_no" field.
func ModelNoGT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldModelNo, v))
}

// ModelNoGTE applies the GTE predicate on the "model_no" field.
func ModelNoGTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldModelNo, v))
}

// ModelNoLT applies the LT predicate on the "model_no" field.
func ModelNoLT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldModelNo, v))
}

// ModelNoLTE applies the LTE predicate on the "model_no" field.
func ModelNoLTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldModelNo, v))
}

// ModelNoContains applies the Contains predicate on the "model_no" field.
func ModelNoContains(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContains(FieldModelNo, v))
}

// ModelNoHasPrefix applies the HasPrefix predicate on the "model_no" field.
func ModelNoHasPrefix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasPrefix(FieldModelNo, v))
}

// ModelNoHasSuffix applies the HasSuffix predicate on the "model_no" field.
func ModelNoHasSuffix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasSuffix(FieldModelNo, v))
}

// ModelNoEqualFold applies the EqualFold predicate on the "model_no" field.
func ModelNoEqualFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEqualFold(FieldModelNo, v))
}

// ModelNoContainsFold applies the ContainsFold predicate on the "model_no" field.
func ModelNoContainsFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContainsFold(FieldModelNo, v))
}

// MeasureUnitIDEQ applies the EQ predicate on the "measure_unit_id" field.
func MeasureUnitIDEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldMeasureUnitID, v))
}

// MeasureUnitIDNEQ applies the NEQ predicate on the "measure_unit_id" field.
func MeasureUnitIDNEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldMeasureUnitID, v))
}

// MeasureUnitIDIn applies the In predicate on the "measure_unit_id" field.
func MeasureUnitIDIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldMeasureUnitID, vs...))
}

// MeasureUnitIDNotIn applies the NotIn predicate on the "measure_unit_id" field.
func MeasureUnitIDNotIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldMeasureUnitID, vs...))
}

// MeasureUnitIDGT applies the GT predicate on the "measure_unit_id" field.
func MeasureUnitIDGT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldMeasureUnitID, v))
}

// MeasureUnitIDGTE applies the GTE predicate on the "measure_unit_id" field.
func MeasureUnitIDGTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldMeasureUnitID, v))
}

// MeasureUnitIDLT applies the LT predicate on the "measure_unit_id" field.
func MeasureUnitIDLT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldMeasureUnitID, v))
}

// MeasureUnitIDLTE applies the LTE predicate on the "measure_unit_id" field.
func MeasureUnitIDLTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldMeasureUnitID, v))
}

// MeasureUnitIDContains applies the Contains predicate on the "measure_unit_id" field.
func MeasureUnitIDContains(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContains(FieldMeasureUnitID, v))
}

// MeasureUnitIDHasPrefix applies the HasPrefix predicate on the "measure_unit_id" field.
func MeasureUnitIDHasPrefix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasPrefix(FieldMeasureUnitID, v))
}

// MeasureUnitIDHasSuffix applies the HasSuffix predicate on the "measure_unit_id" field.
func MeasureUnitIDHasSuffix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasSuffix(FieldMeasureUnitID, v))
}

// MeasureUnitIDIsNil applies the IsNil predicate on the "measure_unit_id" field.
func MeasureUnitIDIsNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIsNull(FieldMeasureUnitID))
}

// MeasureUnitIDNotNil applies the NotNil predicate on the "measure_unit_id" field.
func MeasureUnitIDNotNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotNull(FieldMeasureUnitID))
}

// MeasureUnitIDEqualFold applies the EqualFold predicate on the "measure_unit_id" field.
func MeasureUnitIDEqualFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEqualFold(FieldMeasureUnitID, v))
}

// MeasureUnitIDContainsFold applies the ContainsFold predicate on the "measure_unit_id" field.
func MeasureUnitIDContainsFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContainsFold(FieldMeasureUnitID, v))
}

// ProviderIDEQ applies the EQ predicate on the "provider_id" field.
func ProviderIDEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldProviderID, v))
}

// ProviderIDNEQ applies the NEQ predicate on the "provider_id" field.
func ProviderIDNEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldProviderID, v))
}

// ProviderIDIn applies the In predicate on the "provider_id" field.
func ProviderIDIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldProviderID, vs...))
}

// ProviderIDNotIn applies the NotIn predicate on the "provider_id" field.
func ProviderIDNotIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldProviderID, vs...))
}

// ProviderIDGT applies the GT predicate on the "provider_id" field.
func ProviderIDGT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldProviderID, v))
}

// ProviderIDGTE applies the GTE predicate on the "provider_id" field.
func ProviderIDGTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldProviderID, v))
}

// ProviderIDLT applies the LT predicate on the "provider_id" field.
func ProviderIDLT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldProviderID, v))
}

// ProviderIDLTE applies the LTE predicate on the "provider_id" field.
func ProviderIDLTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldProviderID, v))
}

// ProviderIDContains applies the Contains predicate on the "provider_id" field.
func ProviderIDContains(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContains(FieldProviderID, v))
}

// ProviderIDHasPrefix applies the HasPrefix predicate on the "provider_id" field.
func ProviderIDHasPrefix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasPrefix(FieldProviderID, v))
}

// ProviderIDHasSuffix applies the HasSuffix predicate on the "provider_id" field.
func ProviderIDHasSuffix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasSuffix(FieldProviderID, v))
}

// ProviderIDEqualFold applies the EqualFold predicate on the "provider_id" field.
func ProviderIDEqualFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEqualFold(FieldProviderID, v))
}

// ProviderIDContainsFold applies the ContainsFold predicate on the "provider_id" field.
func ProviderIDContainsFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContainsFold(FieldProviderID, v))
}

// NumEQ applies the EQ predicate on the "num" field.
func NumEQ(v uint32) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldNum, v))
}

// NumNEQ applies the NEQ predicate on the "num" field.
func NumNEQ(v uint32) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldNum, v))
}

// NumIn applies the In predicate on the "num" field.
func NumIn(vs ...uint32) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldNum, vs...))
}

// NumNotIn applies the NotIn predicate on the "num" field.
func NumNotIn(vs ...uint32) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldNum, vs...))
}

// NumGT applies the GT predicate on the "num" field.
func NumGT(v uint32) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldNum, v))
}

// NumGTE applies the GTE predicate on the "num" field.
func NumGTE(v uint32) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldNum, v))
}

// NumLT applies the LT predicate on the "num" field.
func NumLT(v uint32) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldNum, v))
}

// NumLTE applies the LTE predicate on the "num" field.
func NumLTE(v uint32) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldNum, v))
}

// PriceEQ applies the EQ predicate on the "price" field.
func PriceEQ(v uint64) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldPrice, v))
}

// PriceNEQ applies the NEQ predicate on the "price" field.
func PriceNEQ(v uint64) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldPrice, v))
}

// PriceIn applies the In predicate on the "price" field.
func PriceIn(vs ...uint64) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldPrice, vs...))
}

// PriceNotIn applies the NotIn predicate on the "price" field.
func PriceNotIn(vs ...uint64) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldPrice, vs...))
}

// PriceGT applies the GT predicate on the "price" field.
func PriceGT(v uint64) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldPrice, v))
}

// PriceGTE applies the GTE predicate on the "price" field.
func PriceGTE(v uint64) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldPrice, v))
}

// PriceLT applies the LT predicate on the "price" field.
func PriceLT(v uint64) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldPrice, v))
}

// PriceLTE applies the LTE predicate on the "price" field.
func PriceLTE(v uint64) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldPrice, v))
}

// RepositoryAreaIDEQ applies the EQ predicate on the "repository_area_id" field.
func RepositoryAreaIDEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDNEQ applies the NEQ predicate on the "repository_area_id" field.
func RepositoryAreaIDNEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDIn applies the In predicate on the "repository_area_id" field.
func RepositoryAreaIDIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldRepositoryAreaID, vs...))
}

// RepositoryAreaIDNotIn applies the NotIn predicate on the "repository_area_id" field.
func RepositoryAreaIDNotIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldRepositoryAreaID, vs...))
}

// RepositoryAreaIDGT applies the GT predicate on the "repository_area_id" field.
func RepositoryAreaIDGT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDGTE applies the GTE predicate on the "repository_area_id" field.
func RepositoryAreaIDGTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDLT applies the LT predicate on the "repository_area_id" field.
func RepositoryAreaIDLT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDLTE applies the LTE predicate on the "repository_area_id" field.
func RepositoryAreaIDLTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDContains applies the Contains predicate on the "repository_area_id" field.
func RepositoryAreaIDContains(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContains(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDHasPrefix applies the HasPrefix predicate on the "repository_area_id" field.
func RepositoryAreaIDHasPrefix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasPrefix(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDHasSuffix applies the HasSuffix predicate on the "repository_area_id" field.
func RepositoryAreaIDHasSuffix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasSuffix(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDIsNil applies the IsNil predicate on the "repository_area_id" field.
func RepositoryAreaIDIsNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIsNull(FieldRepositoryAreaID))
}

// RepositoryAreaIDNotNil applies the NotNil predicate on the "repository_area_id" field.
func RepositoryAreaIDNotNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotNull(FieldRepositoryAreaID))
}

// RepositoryAreaIDEqualFold applies the EqualFold predicate on the "repository_area_id" field.
func RepositoryAreaIDEqualFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEqualFold(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDContainsFold applies the ContainsFold predicate on the "repository_area_id" field.
func RepositoryAreaIDContainsFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContainsFold(FieldRepositoryAreaID, v))
}

// RepositoryPositionIDEQ applies the EQ predicate on the "repository_position_id" field.
func RepositoryPositionIDEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDNEQ applies the NEQ predicate on the "repository_position_id" field.
func RepositoryPositionIDNEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDIn applies the In predicate on the "repository_position_id" field.
func RepositoryPositionIDIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldRepositoryPositionID, vs...))
}

// RepositoryPositionIDNotIn applies the NotIn predicate on the "repository_position_id" field.
func RepositoryPositionIDNotIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldRepositoryPositionID, vs...))
}

// RepositoryPositionIDGT applies the GT predicate on the "repository_position_id" field.
func RepositoryPositionIDGT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDGTE applies the GTE predicate on the "repository_position_id" field.
func RepositoryPositionIDGTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDLT applies the LT predicate on the "repository_position_id" field.
func RepositoryPositionIDLT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDLTE applies the LTE predicate on the "repository_position_id" field.
func RepositoryPositionIDLTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDContains applies the Contains predicate on the "repository_position_id" field.
func RepositoryPositionIDContains(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContains(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDHasPrefix applies the HasPrefix predicate on the "repository_position_id" field.
func RepositoryPositionIDHasPrefix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasPrefix(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDHasSuffix applies the HasSuffix predicate on the "repository_position_id" field.
func RepositoryPositionIDHasSuffix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasSuffix(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDIsNil applies the IsNil predicate on the "repository_position_id" field.
func RepositoryPositionIDIsNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIsNull(FieldRepositoryPositionID))
}

// RepositoryPositionIDNotNil applies the NotNil predicate on the "repository_position_id" field.
func RepositoryPositionIDNotNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotNull(FieldRepositoryPositionID))
}

// RepositoryPositionIDEqualFold applies the EqualFold predicate on the "repository_position_id" field.
func RepositoryPositionIDEqualFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEqualFold(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDContainsFold applies the ContainsFold predicate on the "repository_position_id" field.
func RepositoryPositionIDContainsFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContainsFold(FieldRepositoryPositionID, v))
}

// FromRepositoryIDEQ applies the EQ predicate on the "from_repository_id" field.
func FromRepositoryIDEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldFromRepositoryID, v))
}

// FromRepositoryIDNEQ applies the NEQ predicate on the "from_repository_id" field.
func FromRepositoryIDNEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldFromRepositoryID, v))
}

// FromRepositoryIDIn applies the In predicate on the "from_repository_id" field.
func FromRepositoryIDIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldFromRepositoryID, vs...))
}

// FromRepositoryIDNotIn applies the NotIn predicate on the "from_repository_id" field.
func FromRepositoryIDNotIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldFromRepositoryID, vs...))
}

// FromRepositoryIDGT applies the GT predicate on the "from_repository_id" field.
func FromRepositoryIDGT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldFromRepositoryID, v))
}

// FromRepositoryIDGTE applies the GTE predicate on the "from_repository_id" field.
func FromRepositoryIDGTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldFromRepositoryID, v))
}

// FromRepositoryIDLT applies the LT predicate on the "from_repository_id" field.
func FromRepositoryIDLT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldFromRepositoryID, v))
}

// FromRepositoryIDLTE applies the LTE predicate on the "from_repository_id" field.
func FromRepositoryIDLTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldFromRepositoryID, v))
}

// FromRepositoryIDContains applies the Contains predicate on the "from_repository_id" field.
func FromRepositoryIDContains(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContains(FieldFromRepositoryID, v))
}

// FromRepositoryIDHasPrefix applies the HasPrefix predicate on the "from_repository_id" field.
func FromRepositoryIDHasPrefix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasPrefix(FieldFromRepositoryID, v))
}

// FromRepositoryIDHasSuffix applies the HasSuffix predicate on the "from_repository_id" field.
func FromRepositoryIDHasSuffix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasSuffix(FieldFromRepositoryID, v))
}

// FromRepositoryIDIsNil applies the IsNil predicate on the "from_repository_id" field.
func FromRepositoryIDIsNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIsNull(FieldFromRepositoryID))
}

// FromRepositoryIDNotNil applies the NotNil predicate on the "from_repository_id" field.
func FromRepositoryIDNotNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotNull(FieldFromRepositoryID))
}

// FromRepositoryIDEqualFold applies the EqualFold predicate on the "from_repository_id" field.
func FromRepositoryIDEqualFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEqualFold(FieldFromRepositoryID, v))
}

// FromRepositoryIDContainsFold applies the ContainsFold predicate on the "from_repository_id" field.
func FromRepositoryIDContainsFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContainsFold(FieldFromRepositoryID, v))
}

// ToRepositoryIDEQ applies the EQ predicate on the "to_repository_id" field.
func ToRepositoryIDEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldToRepositoryID, v))
}

// ToRepositoryIDNEQ applies the NEQ predicate on the "to_repository_id" field.
func ToRepositoryIDNEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldToRepositoryID, v))
}

// ToRepositoryIDIn applies the In predicate on the "to_repository_id" field.
func ToRepositoryIDIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldToRepositoryID, vs...))
}

// ToRepositoryIDNotIn applies the NotIn predicate on the "to_repository_id" field.
func ToRepositoryIDNotIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldToRepositoryID, vs...))
}

// ToRepositoryIDGT applies the GT predicate on the "to_repository_id" field.
func ToRepositoryIDGT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldToRepositoryID, v))
}

// ToRepositoryIDGTE applies the GTE predicate on the "to_repository_id" field.
func ToRepositoryIDGTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldToRepositoryID, v))
}

// ToRepositoryIDLT applies the LT predicate on the "to_repository_id" field.
func ToRepositoryIDLT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldToRepositoryID, v))
}

// ToRepositoryIDLTE applies the LTE predicate on the "to_repository_id" field.
func ToRepositoryIDLTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldToRepositoryID, v))
}

// ToRepositoryIDContains applies the Contains predicate on the "to_repository_id" field.
func ToRepositoryIDContains(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContains(FieldToRepositoryID, v))
}

// ToRepositoryIDHasPrefix applies the HasPrefix predicate on the "to_repository_id" field.
func ToRepositoryIDHasPrefix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasPrefix(FieldToRepositoryID, v))
}

// ToRepositoryIDHasSuffix applies the HasSuffix predicate on the "to_repository_id" field.
func ToRepositoryIDHasSuffix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasSuffix(FieldToRepositoryID, v))
}

// ToRepositoryIDIsNil applies the IsNil predicate on the "to_repository_id" field.
func ToRepositoryIDIsNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIsNull(FieldToRepositoryID))
}

// ToRepositoryIDNotNil applies the NotNil predicate on the "to_repository_id" field.
func ToRepositoryIDNotNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotNull(FieldToRepositoryID))
}

// ToRepositoryIDEqualFold applies the EqualFold predicate on the "to_repository_id" field.
func ToRepositoryIDEqualFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEqualFold(FieldToRepositoryID, v))
}

// ToRepositoryIDContainsFold applies the ContainsFold predicate on the "to_repository_id" field.
func ToRepositoryIDContainsFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContainsFold(FieldToRepositoryID, v))
}

// OrderNoEQ applies the EQ predicate on the "order_no" field.
func OrderNoEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldOrderNo, v))
}

// OrderNoNEQ applies the NEQ predicate on the "order_no" field.
func OrderNoNEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldOrderNo, v))
}

// OrderNoIn applies the In predicate on the "order_no" field.
func OrderNoIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldOrderNo, vs...))
}

// OrderNoNotIn applies the NotIn predicate on the "order_no" field.
func OrderNoNotIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldOrderNo, vs...))
}

// OrderNoGT applies the GT predicate on the "order_no" field.
func OrderNoGT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldOrderNo, v))
}

// OrderNoGTE applies the GTE predicate on the "order_no" field.
func OrderNoGTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldOrderNo, v))
}

// OrderNoLT applies the LT predicate on the "order_no" field.
func OrderNoLT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldOrderNo, v))
}

// OrderNoLTE applies the LTE predicate on the "order_no" field.
func OrderNoLTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldOrderNo, v))
}

// OrderNoContains applies the Contains predicate on the "order_no" field.
func OrderNoContains(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContains(FieldOrderNo, v))
}

// OrderNoHasPrefix applies the HasPrefix predicate on the "order_no" field.
func OrderNoHasPrefix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasPrefix(FieldOrderNo, v))
}

// OrderNoHasSuffix applies the HasSuffix predicate on the "order_no" field.
func OrderNoHasSuffix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasSuffix(FieldOrderNo, v))
}

// OrderNoIsNil applies the IsNil predicate on the "order_no" field.
func OrderNoIsNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIsNull(FieldOrderNo))
}

// OrderNoNotNil applies the NotNil predicate on the "order_no" field.
func OrderNoNotNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotNull(FieldOrderNo))
}

// OrderNoEqualFold applies the EqualFold predicate on the "order_no" field.
func OrderNoEqualFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEqualFold(FieldOrderNo, v))
}

// OrderNoContainsFold applies the ContainsFold predicate on the "order_no" field.
func OrderNoContainsFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContainsFold(FieldOrderNo, v))
}

// ExpireTimeEQ applies the EQ predicate on the "expire_time" field.
func ExpireTimeEQ(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldExpireTime, v))
}

// ExpireTimeNEQ applies the NEQ predicate on the "expire_time" field.
func ExpireTimeNEQ(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldExpireTime, v))
}

// ExpireTimeIn applies the In predicate on the "expire_time" field.
func ExpireTimeIn(vs ...time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldExpireTime, vs...))
}

// ExpireTimeNotIn applies the NotIn predicate on the "expire_time" field.
func ExpireTimeNotIn(vs ...time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldExpireTime, vs...))
}

// ExpireTimeGT applies the GT predicate on the "expire_time" field.
func ExpireTimeGT(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldExpireTime, v))
}

// ExpireTimeGTE applies the GTE predicate on the "expire_time" field.
func ExpireTimeGTE(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldExpireTime, v))
}

// ExpireTimeLT applies the LT predicate on the "expire_time" field.
func ExpireTimeLT(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldExpireTime, v))
}

// ExpireTimeLTE applies the LTE predicate on the "expire_time" field.
func ExpireTimeLTE(v time.Time) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldExpireTime, v))
}

// ExpireTimeIsNil applies the IsNil predicate on the "expire_time" field.
func ExpireTimeIsNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIsNull(FieldExpireTime))
}

// ExpireTimeNotNil applies the NotNil predicate on the "expire_time" field.
func ExpireTimeNotNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotNull(FieldExpireTime))
}

// FeatureIsNil applies the IsNil predicate on the "feature" field.
func FeatureIsNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIsNull(FieldFeature))
}

// FeatureNotNil applies the NotNil predicate on the "feature" field.
func FeatureNotNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotNull(FieldFeature))
}

// MaterialIDEQ applies the EQ predicate on the "material_id" field.
func MaterialIDEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEQ(FieldMaterialID, v))
}

// MaterialIDNEQ applies the NEQ predicate on the "material_id" field.
func MaterialIDNEQ(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNEQ(FieldMaterialID, v))
}

// MaterialIDIn applies the In predicate on the "material_id" field.
func MaterialIDIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIn(FieldMaterialID, vs...))
}

// MaterialIDNotIn applies the NotIn predicate on the "material_id" field.
func MaterialIDNotIn(vs ...string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotIn(FieldMaterialID, vs...))
}

// MaterialIDGT applies the GT predicate on the "material_id" field.
func MaterialIDGT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGT(FieldMaterialID, v))
}

// MaterialIDGTE applies the GTE predicate on the "material_id" field.
func MaterialIDGTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldGTE(FieldMaterialID, v))
}

// MaterialIDLT applies the LT predicate on the "material_id" field.
func MaterialIDLT(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLT(FieldMaterialID, v))
}

// MaterialIDLTE applies the LTE predicate on the "material_id" field.
func MaterialIDLTE(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldLTE(FieldMaterialID, v))
}

// MaterialIDContains applies the Contains predicate on the "material_id" field.
func MaterialIDContains(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContains(FieldMaterialID, v))
}

// MaterialIDHasPrefix applies the HasPrefix predicate on the "material_id" field.
func MaterialIDHasPrefix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasPrefix(FieldMaterialID, v))
}

// MaterialIDHasSuffix applies the HasSuffix predicate on the "material_id" field.
func MaterialIDHasSuffix(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldHasSuffix(FieldMaterialID, v))
}

// MaterialIDIsNil applies the IsNil predicate on the "material_id" field.
func MaterialIDIsNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldIsNull(FieldMaterialID))
}

// MaterialIDNotNil applies the NotNil predicate on the "material_id" field.
func MaterialIDNotNil() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldNotNull(FieldMaterialID))
}

// MaterialIDEqualFold applies the EqualFold predicate on the "material_id" field.
func MaterialIDEqualFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldEqualFold(FieldMaterialID, v))
}

// MaterialIDContainsFold applies the ContainsFold predicate on the "material_id" field.
func MaterialIDContainsFold(v string) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.FieldContainsFold(FieldMaterialID, v))
}

// HasEnterRepositoryOrder applies the HasEdge predicate on the "enter_repository_order" edge.
func HasEnterRepositoryOrder() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, EnterRepositoryOrderTable, EnterRepositoryOrderColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasEnterRepositoryOrderWith applies the HasEdge predicate on the "enter_repository_order" edge with a given conditions (other predicates).
func HasEnterRepositoryOrderWith(preds ...predicate.WmsEnterRepositoryOrder) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(func(s *sql.Selector) {
		step := newEnterRepositoryOrderStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasEquipmentType applies the HasEdge predicate on the "equipment_type" edge.
func HasEquipmentType() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, EquipmentTypeTable, EquipmentTypeColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasEquipmentTypeWith applies the HasEdge predicate on the "equipment_type" edge with a given conditions (other predicates).
func HasEquipmentTypeWith(preds ...predicate.WmsEquipmentType) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(func(s *sql.Selector) {
		step := newEquipmentTypeStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasEquipment applies the HasEdge predicate on the "equipment" edge.
func HasEquipment() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, EquipmentTable, EquipmentColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasEquipmentWith applies the HasEdge predicate on the "equipment" edge with a given conditions (other predicates).
func HasEquipmentWith(preds ...predicate.WmsEquipment) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(func(s *sql.Selector) {
		step := newEquipmentStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasMeasureUnit applies the HasEdge predicate on the "measure_unit" edge.
func HasMeasureUnit() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, MeasureUnitTable, MeasureUnitColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasMeasureUnitWith applies the HasEdge predicate on the "measure_unit" edge with a given conditions (other predicates).
func HasMeasureUnitWith(preds ...predicate.WmsMeasureUnit) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(func(s *sql.Selector) {
		step := newMeasureUnitStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasRepositoryPosition applies the HasEdge predicate on the "repository_position" edge.
func HasRepositoryPosition() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, RepositoryPositionTable, RepositoryPositionColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasRepositoryPositionWith applies the HasEdge predicate on the "repository_position" edge with a given conditions (other predicates).
func HasRepositoryPositionWith(preds ...predicate.WmsRepositoryPosition) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(func(s *sql.Selector) {
		step := newRepositoryPositionStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasFromRepository applies the HasEdge predicate on the "from_repository" edge.
func HasFromRepository() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, FromRepositoryTable, FromRepositoryColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasFromRepositoryWith applies the HasEdge predicate on the "from_repository" edge with a given conditions (other predicates).
func HasFromRepositoryWith(preds ...predicate.WmsRepository) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(func(s *sql.Selector) {
		step := newFromRepositoryStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasToRepository applies the HasEdge predicate on the "to_repository" edge.
func HasToRepository() predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, ToRepositoryTable, ToRepositoryColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasToRepositoryWith applies the HasEdge predicate on the "to_repository" edge with a given conditions (other predicates).
func HasToRepositoryWith(preds ...predicate.WmsRepository) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(func(s *sql.Selector) {
		step := newToRepositoryStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.WmsEnterRepositoryOrderDetail) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.WmsEnterRepositoryOrderDetail) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.WmsEnterRepositoryOrderDetail) predicate.WmsEnterRepositoryOrderDetail {
	return predicate.WmsEnterRepositoryOrderDetail(sql.NotPredicates(p))
}
