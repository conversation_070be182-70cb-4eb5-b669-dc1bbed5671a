// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"kratos-mono-demo/internal/data/ent/predicate"
	"kratos-mono-demo/internal/data/ent/wmstransferorder"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WmsTransferOrderQuery is the builder for querying WmsTransferOrder entities.
type WmsTransferOrderQuery struct {
	config
	ctx        *QueryContext
	order      []wmstransferorder.OrderOption
	inters     []Interceptor
	predicates []predicate.WmsTransferOrder
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the WmsTransferOrderQuery builder.
func (wtoq *WmsTransferOrderQuery) Where(ps ...predicate.WmsTransferOrder) *WmsTransferOrderQuery {
	wtoq.predicates = append(wtoq.predicates, ps...)
	return wtoq
}

// Limit the number of records to be returned by this query.
func (wtoq *WmsTransferOrderQuery) Limit(limit int) *WmsTransferOrderQuery {
	wtoq.ctx.Limit = &limit
	return wtoq
}

// Offset to start from.
func (wtoq *WmsTransferOrderQuery) Offset(offset int) *WmsTransferOrderQuery {
	wtoq.ctx.Offset = &offset
	return wtoq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (wtoq *WmsTransferOrderQuery) Unique(unique bool) *WmsTransferOrderQuery {
	wtoq.ctx.Unique = &unique
	return wtoq
}

// Order specifies how the records should be ordered.
func (wtoq *WmsTransferOrderQuery) Order(o ...wmstransferorder.OrderOption) *WmsTransferOrderQuery {
	wtoq.order = append(wtoq.order, o...)
	return wtoq
}

// First returns the first WmsTransferOrder entity from the query.
// Returns a *NotFoundError when no WmsTransferOrder was found.
func (wtoq *WmsTransferOrderQuery) First(ctx context.Context) (*WmsTransferOrder, error) {
	nodes, err := wtoq.Limit(1).All(setContextOp(ctx, wtoq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{wmstransferorder.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (wtoq *WmsTransferOrderQuery) FirstX(ctx context.Context) *WmsTransferOrder {
	node, err := wtoq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first WmsTransferOrder ID from the query.
// Returns a *NotFoundError when no WmsTransferOrder ID was found.
func (wtoq *WmsTransferOrderQuery) FirstID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = wtoq.Limit(1).IDs(setContextOp(ctx, wtoq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{wmstransferorder.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (wtoq *WmsTransferOrderQuery) FirstIDX(ctx context.Context) string {
	id, err := wtoq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single WmsTransferOrder entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one WmsTransferOrder entity is found.
// Returns a *NotFoundError when no WmsTransferOrder entities are found.
func (wtoq *WmsTransferOrderQuery) Only(ctx context.Context) (*WmsTransferOrder, error) {
	nodes, err := wtoq.Limit(2).All(setContextOp(ctx, wtoq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{wmstransferorder.Label}
	default:
		return nil, &NotSingularError{wmstransferorder.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (wtoq *WmsTransferOrderQuery) OnlyX(ctx context.Context) *WmsTransferOrder {
	node, err := wtoq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only WmsTransferOrder ID in the query.
// Returns a *NotSingularError when more than one WmsTransferOrder ID is found.
// Returns a *NotFoundError when no entities are found.
func (wtoq *WmsTransferOrderQuery) OnlyID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = wtoq.Limit(2).IDs(setContextOp(ctx, wtoq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{wmstransferorder.Label}
	default:
		err = &NotSingularError{wmstransferorder.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (wtoq *WmsTransferOrderQuery) OnlyIDX(ctx context.Context) string {
	id, err := wtoq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of WmsTransferOrders.
func (wtoq *WmsTransferOrderQuery) All(ctx context.Context) ([]*WmsTransferOrder, error) {
	ctx = setContextOp(ctx, wtoq.ctx, ent.OpQueryAll)
	if err := wtoq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*WmsTransferOrder, *WmsTransferOrderQuery]()
	return withInterceptors[[]*WmsTransferOrder](ctx, wtoq, qr, wtoq.inters)
}

// AllX is like All, but panics if an error occurs.
func (wtoq *WmsTransferOrderQuery) AllX(ctx context.Context) []*WmsTransferOrder {
	nodes, err := wtoq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of WmsTransferOrder IDs.
func (wtoq *WmsTransferOrderQuery) IDs(ctx context.Context) (ids []string, err error) {
	if wtoq.ctx.Unique == nil && wtoq.path != nil {
		wtoq.Unique(true)
	}
	ctx = setContextOp(ctx, wtoq.ctx, ent.OpQueryIDs)
	if err = wtoq.Select(wmstransferorder.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (wtoq *WmsTransferOrderQuery) IDsX(ctx context.Context) []string {
	ids, err := wtoq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (wtoq *WmsTransferOrderQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, wtoq.ctx, ent.OpQueryCount)
	if err := wtoq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, wtoq, querierCount[*WmsTransferOrderQuery](), wtoq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (wtoq *WmsTransferOrderQuery) CountX(ctx context.Context) int {
	count, err := wtoq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (wtoq *WmsTransferOrderQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, wtoq.ctx, ent.OpQueryExist)
	switch _, err := wtoq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (wtoq *WmsTransferOrderQuery) ExistX(ctx context.Context) bool {
	exist, err := wtoq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the WmsTransferOrderQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (wtoq *WmsTransferOrderQuery) Clone() *WmsTransferOrderQuery {
	if wtoq == nil {
		return nil
	}
	return &WmsTransferOrderQuery{
		config:     wtoq.config,
		ctx:        wtoq.ctx.Clone(),
		order:      append([]wmstransferorder.OrderOption{}, wtoq.order...),
		inters:     append([]Interceptor{}, wtoq.inters...),
		predicates: append([]predicate.WmsTransferOrder{}, wtoq.predicates...),
		// clone intermediate query.
		sql:       wtoq.sql.Clone(),
		path:      wtoq.path,
		modifiers: append([]func(*sql.Selector){}, wtoq.modifiers...),
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.WmsTransferOrder.Query().
//		GroupBy(wmstransferorder.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (wtoq *WmsTransferOrderQuery) GroupBy(field string, fields ...string) *WmsTransferOrderGroupBy {
	wtoq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &WmsTransferOrderGroupBy{build: wtoq}
	grbuild.flds = &wtoq.ctx.Fields
	grbuild.label = wmstransferorder.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.WmsTransferOrder.Query().
//		Select(wmstransferorder.FieldCreatedAt).
//		Scan(ctx, &v)
func (wtoq *WmsTransferOrderQuery) Select(fields ...string) *WmsTransferOrderSelect {
	wtoq.ctx.Fields = append(wtoq.ctx.Fields, fields...)
	sbuild := &WmsTransferOrderSelect{WmsTransferOrderQuery: wtoq}
	sbuild.label = wmstransferorder.Label
	sbuild.flds, sbuild.scan = &wtoq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a WmsTransferOrderSelect configured with the given aggregations.
func (wtoq *WmsTransferOrderQuery) Aggregate(fns ...AggregateFunc) *WmsTransferOrderSelect {
	return wtoq.Select().Aggregate(fns...)
}

func (wtoq *WmsTransferOrderQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range wtoq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, wtoq); err != nil {
				return err
			}
		}
	}
	for _, f := range wtoq.ctx.Fields {
		if !wmstransferorder.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if wtoq.path != nil {
		prev, err := wtoq.path(ctx)
		if err != nil {
			return err
		}
		wtoq.sql = prev
	}
	return nil
}

func (wtoq *WmsTransferOrderQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*WmsTransferOrder, error) {
	var (
		nodes = []*WmsTransferOrder{}
		_spec = wtoq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*WmsTransferOrder).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &WmsTransferOrder{config: wtoq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(wtoq.modifiers) > 0 {
		_spec.Modifiers = wtoq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, wtoq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (wtoq *WmsTransferOrderQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := wtoq.querySpec()
	if len(wtoq.modifiers) > 0 {
		_spec.Modifiers = wtoq.modifiers
	}
	_spec.Node.Columns = wtoq.ctx.Fields
	if len(wtoq.ctx.Fields) > 0 {
		_spec.Unique = wtoq.ctx.Unique != nil && *wtoq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, wtoq.driver, _spec)
}

func (wtoq *WmsTransferOrderQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(wmstransferorder.Table, wmstransferorder.Columns, sqlgraph.NewFieldSpec(wmstransferorder.FieldID, field.TypeString))
	_spec.From = wtoq.sql
	if unique := wtoq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if wtoq.path != nil {
		_spec.Unique = true
	}
	if fields := wtoq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, wmstransferorder.FieldID)
		for i := range fields {
			if fields[i] != wmstransferorder.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := wtoq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := wtoq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := wtoq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := wtoq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (wtoq *WmsTransferOrderQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(wtoq.driver.Dialect())
	t1 := builder.Table(wmstransferorder.Table)
	columns := wtoq.ctx.Fields
	if len(columns) == 0 {
		columns = wmstransferorder.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if wtoq.sql != nil {
		selector = wtoq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if wtoq.ctx.Unique != nil && *wtoq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range wtoq.modifiers {
		m(selector)
	}
	for _, p := range wtoq.predicates {
		p(selector)
	}
	for _, p := range wtoq.order {
		p(selector)
	}
	if offset := wtoq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := wtoq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// Modify adds a query modifier for attaching custom logic to queries.
func (wtoq *WmsTransferOrderQuery) Modify(modifiers ...func(s *sql.Selector)) *WmsTransferOrderSelect {
	wtoq.modifiers = append(wtoq.modifiers, modifiers...)
	return wtoq.Select()
}

// WmsTransferOrderGroupBy is the group-by builder for WmsTransferOrder entities.
type WmsTransferOrderGroupBy struct {
	selector
	build *WmsTransferOrderQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (wtogb *WmsTransferOrderGroupBy) Aggregate(fns ...AggregateFunc) *WmsTransferOrderGroupBy {
	wtogb.fns = append(wtogb.fns, fns...)
	return wtogb
}

// Scan applies the selector query and scans the result into the given value.
func (wtogb *WmsTransferOrderGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, wtogb.build.ctx, ent.OpQueryGroupBy)
	if err := wtogb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*WmsTransferOrderQuery, *WmsTransferOrderGroupBy](ctx, wtogb.build, wtogb, wtogb.build.inters, v)
}

func (wtogb *WmsTransferOrderGroupBy) sqlScan(ctx context.Context, root *WmsTransferOrderQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(wtogb.fns))
	for _, fn := range wtogb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*wtogb.flds)+len(wtogb.fns))
		for _, f := range *wtogb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*wtogb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := wtogb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// WmsTransferOrderSelect is the builder for selecting fields of WmsTransferOrder entities.
type WmsTransferOrderSelect struct {
	*WmsTransferOrderQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (wtos *WmsTransferOrderSelect) Aggregate(fns ...AggregateFunc) *WmsTransferOrderSelect {
	wtos.fns = append(wtos.fns, fns...)
	return wtos
}

// Scan applies the selector query and scans the result into the given value.
func (wtos *WmsTransferOrderSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, wtos.ctx, ent.OpQuerySelect)
	if err := wtos.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*WmsTransferOrderQuery, *WmsTransferOrderSelect](ctx, wtos.WmsTransferOrderQuery, wtos, wtos.inters, v)
}

func (wtos *WmsTransferOrderSelect) sqlScan(ctx context.Context, root *WmsTransferOrderQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(wtos.fns))
	for _, fn := range wtos.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*wtos.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := wtos.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (wtos *WmsTransferOrderSelect) Modify(modifiers ...func(s *sql.Selector)) *WmsTransferOrderSelect {
	wtos.modifiers = append(wtos.modifiers, modifiers...)
	return wtos
}
