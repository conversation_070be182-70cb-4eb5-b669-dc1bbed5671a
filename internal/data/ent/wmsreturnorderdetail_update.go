// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-mono-demo/internal/data/ent/predicate"
	"kratos-mono-demo/internal/data/ent/wmsreturnorderdetail"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WmsReturnOrderDetailUpdate is the builder for updating WmsReturnOrderDetail entities.
type WmsReturnOrderDetailUpdate struct {
	config
	hooks     []Hook
	mutation  *WmsReturnOrderDetailMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the WmsReturnOrderDetailUpdate builder.
func (wrodu *WmsReturnOrderDetailUpdate) Where(ps ...predicate.WmsReturnOrderDetail) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.Where(ps...)
	return wrodu
}

// SetUpdatedAt sets the "updated_at" field.
func (wrodu *WmsReturnOrderDetailUpdate) SetUpdatedAt(t time.Time) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.SetUpdatedAt(t)
	return wrodu
}

// SetCreatedBy sets the "created_by" field.
func (wrodu *WmsReturnOrderDetailUpdate) SetCreatedBy(s string) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.SetCreatedBy(s)
	return wrodu
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (wrodu *WmsReturnOrderDetailUpdate) SetNillableCreatedBy(s *string) *WmsReturnOrderDetailUpdate {
	if s != nil {
		wrodu.SetCreatedBy(*s)
	}
	return wrodu
}

// ClearCreatedBy clears the value of the "created_by" field.
func (wrodu *WmsReturnOrderDetailUpdate) ClearCreatedBy() *WmsReturnOrderDetailUpdate {
	wrodu.mutation.ClearCreatedBy()
	return wrodu
}

// SetUpdatedBy sets the "updated_by" field.
func (wrodu *WmsReturnOrderDetailUpdate) SetUpdatedBy(s string) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.SetUpdatedBy(s)
	return wrodu
}

// SetNillableUpdatedBy sets the "updated_by" field if the given value is not nil.
func (wrodu *WmsReturnOrderDetailUpdate) SetNillableUpdatedBy(s *string) *WmsReturnOrderDetailUpdate {
	if s != nil {
		wrodu.SetUpdatedBy(*s)
	}
	return wrodu
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (wrodu *WmsReturnOrderDetailUpdate) ClearUpdatedBy() *WmsReturnOrderDetailUpdate {
	wrodu.mutation.ClearUpdatedBy()
	return wrodu
}

// SetRemark sets the "remark" field.
func (wrodu *WmsReturnOrderDetailUpdate) SetRemark(s string) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.SetRemark(s)
	return wrodu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (wrodu *WmsReturnOrderDetailUpdate) SetNillableRemark(s *string) *WmsReturnOrderDetailUpdate {
	if s != nil {
		wrodu.SetRemark(*s)
	}
	return wrodu
}

// ClearRemark clears the value of the "remark" field.
func (wrodu *WmsReturnOrderDetailUpdate) ClearRemark() *WmsReturnOrderDetailUpdate {
	wrodu.mutation.ClearRemark()
	return wrodu
}

// SetOrderID sets the "order_id" field.
func (wrodu *WmsReturnOrderDetailUpdate) SetOrderID(s string) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.SetOrderID(s)
	return wrodu
}

// SetNillableOrderID sets the "order_id" field if the given value is not nil.
func (wrodu *WmsReturnOrderDetailUpdate) SetNillableOrderID(s *string) *WmsReturnOrderDetailUpdate {
	if s != nil {
		wrodu.SetOrderID(*s)
	}
	return wrodu
}

// ClearOrderID clears the value of the "order_id" field.
func (wrodu *WmsReturnOrderDetailUpdate) ClearOrderID() *WmsReturnOrderDetailUpdate {
	wrodu.mutation.ClearOrderID()
	return wrodu
}

// SetMaterialID sets the "material_id" field.
func (wrodu *WmsReturnOrderDetailUpdate) SetMaterialID(s string) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.SetMaterialID(s)
	return wrodu
}

// SetNillableMaterialID sets the "material_id" field if the given value is not nil.
func (wrodu *WmsReturnOrderDetailUpdate) SetNillableMaterialID(s *string) *WmsReturnOrderDetailUpdate {
	if s != nil {
		wrodu.SetMaterialID(*s)
	}
	return wrodu
}

// ClearMaterialID clears the value of the "material_id" field.
func (wrodu *WmsReturnOrderDetailUpdate) ClearMaterialID() *WmsReturnOrderDetailUpdate {
	wrodu.mutation.ClearMaterialID()
	return wrodu
}

// SetMaterialName sets the "material_name" field.
func (wrodu *WmsReturnOrderDetailUpdate) SetMaterialName(s string) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.SetMaterialName(s)
	return wrodu
}

// SetNillableMaterialName sets the "material_name" field if the given value is not nil.
func (wrodu *WmsReturnOrderDetailUpdate) SetNillableMaterialName(s *string) *WmsReturnOrderDetailUpdate {
	if s != nil {
		wrodu.SetMaterialName(*s)
	}
	return wrodu
}

// ClearMaterialName clears the value of the "material_name" field.
func (wrodu *WmsReturnOrderDetailUpdate) ClearMaterialName() *WmsReturnOrderDetailUpdate {
	wrodu.mutation.ClearMaterialName()
	return wrodu
}

// SetCode sets the "code" field.
func (wrodu *WmsReturnOrderDetailUpdate) SetCode(s string) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.SetCode(s)
	return wrodu
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (wrodu *WmsReturnOrderDetailUpdate) SetNillableCode(s *string) *WmsReturnOrderDetailUpdate {
	if s != nil {
		wrodu.SetCode(*s)
	}
	return wrodu
}

// SetEquipmentID sets the "equipment_id" field.
func (wrodu *WmsReturnOrderDetailUpdate) SetEquipmentID(s string) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.SetEquipmentID(s)
	return wrodu
}

// SetNillableEquipmentID sets the "equipment_id" field if the given value is not nil.
func (wrodu *WmsReturnOrderDetailUpdate) SetNillableEquipmentID(s *string) *WmsReturnOrderDetailUpdate {
	if s != nil {
		wrodu.SetEquipmentID(*s)
	}
	return wrodu
}

// ClearEquipmentID clears the value of the "equipment_id" field.
func (wrodu *WmsReturnOrderDetailUpdate) ClearEquipmentID() *WmsReturnOrderDetailUpdate {
	wrodu.mutation.ClearEquipmentID()
	return wrodu
}

// SetEquipmentTypeID sets the "equipment_type_id" field.
func (wrodu *WmsReturnOrderDetailUpdate) SetEquipmentTypeID(s string) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.SetEquipmentTypeID(s)
	return wrodu
}

// SetNillableEquipmentTypeID sets the "equipment_type_id" field if the given value is not nil.
func (wrodu *WmsReturnOrderDetailUpdate) SetNillableEquipmentTypeID(s *string) *WmsReturnOrderDetailUpdate {
	if s != nil {
		wrodu.SetEquipmentTypeID(*s)
	}
	return wrodu
}

// ClearEquipmentTypeID clears the value of the "equipment_type_id" field.
func (wrodu *WmsReturnOrderDetailUpdate) ClearEquipmentTypeID() *WmsReturnOrderDetailUpdate {
	wrodu.mutation.ClearEquipmentTypeID()
	return wrodu
}

// SetFeature sets the "feature" field.
func (wrodu *WmsReturnOrderDetailUpdate) SetFeature(m map[string]interface{}) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.SetFeature(m)
	return wrodu
}

// ClearFeature clears the value of the "feature" field.
func (wrodu *WmsReturnOrderDetailUpdate) ClearFeature() *WmsReturnOrderDetailUpdate {
	wrodu.mutation.ClearFeature()
	return wrodu
}

// SetOwnerID sets the "owner_id" field.
func (wrodu *WmsReturnOrderDetailUpdate) SetOwnerID(s string) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.SetOwnerID(s)
	return wrodu
}

// SetNillableOwnerID sets the "owner_id" field if the given value is not nil.
func (wrodu *WmsReturnOrderDetailUpdate) SetNillableOwnerID(s *string) *WmsReturnOrderDetailUpdate {
	if s != nil {
		wrodu.SetOwnerID(*s)
	}
	return wrodu
}

// ClearOwnerID clears the value of the "owner_id" field.
func (wrodu *WmsReturnOrderDetailUpdate) ClearOwnerID() *WmsReturnOrderDetailUpdate {
	wrodu.mutation.ClearOwnerID()
	return wrodu
}

// SetModelNo sets the "ModelNo" field.
func (wrodu *WmsReturnOrderDetailUpdate) SetModelNo(s string) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.SetModelNo(s)
	return wrodu
}

// SetNillableModelNo sets the "ModelNo" field if the given value is not nil.
func (wrodu *WmsReturnOrderDetailUpdate) SetNillableModelNo(s *string) *WmsReturnOrderDetailUpdate {
	if s != nil {
		wrodu.SetModelNo(*s)
	}
	return wrodu
}

// SetMeasureUnitID sets the "measure_unit_id" field.
func (wrodu *WmsReturnOrderDetailUpdate) SetMeasureUnitID(s string) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.SetMeasureUnitID(s)
	return wrodu
}

// SetNillableMeasureUnitID sets the "measure_unit_id" field if the given value is not nil.
func (wrodu *WmsReturnOrderDetailUpdate) SetNillableMeasureUnitID(s *string) *WmsReturnOrderDetailUpdate {
	if s != nil {
		wrodu.SetMeasureUnitID(*s)
	}
	return wrodu
}

// ClearMeasureUnitID clears the value of the "measure_unit_id" field.
func (wrodu *WmsReturnOrderDetailUpdate) ClearMeasureUnitID() *WmsReturnOrderDetailUpdate {
	wrodu.mutation.ClearMeasureUnitID()
	return wrodu
}

// SetNum sets the "num" field.
func (wrodu *WmsReturnOrderDetailUpdate) SetNum(u uint32) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.ResetNum()
	wrodu.mutation.SetNum(u)
	return wrodu
}

// SetNillableNum sets the "num" field if the given value is not nil.
func (wrodu *WmsReturnOrderDetailUpdate) SetNillableNum(u *uint32) *WmsReturnOrderDetailUpdate {
	if u != nil {
		wrodu.SetNum(*u)
	}
	return wrodu
}

// AddNum adds u to the "num" field.
func (wrodu *WmsReturnOrderDetailUpdate) AddNum(u int32) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.AddNum(u)
	return wrodu
}

// SetToRepositoryID sets the "to_repository_id" field.
func (wrodu *WmsReturnOrderDetailUpdate) SetToRepositoryID(s string) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.SetToRepositoryID(s)
	return wrodu
}

// SetNillableToRepositoryID sets the "to_repository_id" field if the given value is not nil.
func (wrodu *WmsReturnOrderDetailUpdate) SetNillableToRepositoryID(s *string) *WmsReturnOrderDetailUpdate {
	if s != nil {
		wrodu.SetToRepositoryID(*s)
	}
	return wrodu
}

// ClearToRepositoryID clears the value of the "to_repository_id" field.
func (wrodu *WmsReturnOrderDetailUpdate) ClearToRepositoryID() *WmsReturnOrderDetailUpdate {
	wrodu.mutation.ClearToRepositoryID()
	return wrodu
}

// SetToRepositoryAreaID sets the "to_repository_area_id" field.
func (wrodu *WmsReturnOrderDetailUpdate) SetToRepositoryAreaID(s string) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.SetToRepositoryAreaID(s)
	return wrodu
}

// SetNillableToRepositoryAreaID sets the "to_repository_area_id" field if the given value is not nil.
func (wrodu *WmsReturnOrderDetailUpdate) SetNillableToRepositoryAreaID(s *string) *WmsReturnOrderDetailUpdate {
	if s != nil {
		wrodu.SetToRepositoryAreaID(*s)
	}
	return wrodu
}

// ClearToRepositoryAreaID clears the value of the "to_repository_area_id" field.
func (wrodu *WmsReturnOrderDetailUpdate) ClearToRepositoryAreaID() *WmsReturnOrderDetailUpdate {
	wrodu.mutation.ClearToRepositoryAreaID()
	return wrodu
}

// SetToRepositoryPositionID sets the "to_repository_position_id" field.
func (wrodu *WmsReturnOrderDetailUpdate) SetToRepositoryPositionID(s string) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.SetToRepositoryPositionID(s)
	return wrodu
}

// SetNillableToRepositoryPositionID sets the "to_repository_position_id" field if the given value is not nil.
func (wrodu *WmsReturnOrderDetailUpdate) SetNillableToRepositoryPositionID(s *string) *WmsReturnOrderDetailUpdate {
	if s != nil {
		wrodu.SetToRepositoryPositionID(*s)
	}
	return wrodu
}

// ClearToRepositoryPositionID clears the value of the "to_repository_position_id" field.
func (wrodu *WmsReturnOrderDetailUpdate) ClearToRepositoryPositionID() *WmsReturnOrderDetailUpdate {
	wrodu.mutation.ClearToRepositoryPositionID()
	return wrodu
}

// SetReason sets the "reason" field.
func (wrodu *WmsReturnOrderDetailUpdate) SetReason(s string) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.SetReason(s)
	return wrodu
}

// SetNillableReason sets the "reason" field if the given value is not nil.
func (wrodu *WmsReturnOrderDetailUpdate) SetNillableReason(s *string) *WmsReturnOrderDetailUpdate {
	if s != nil {
		wrodu.SetReason(*s)
	}
	return wrodu
}

// ClearReason clears the value of the "reason" field.
func (wrodu *WmsReturnOrderDetailUpdate) ClearReason() *WmsReturnOrderDetailUpdate {
	wrodu.mutation.ClearReason()
	return wrodu
}

// SetReturnTime sets the "return_time" field.
func (wrodu *WmsReturnOrderDetailUpdate) SetReturnTime(t time.Time) *WmsReturnOrderDetailUpdate {
	wrodu.mutation.SetReturnTime(t)
	return wrodu
}

// SetNillableReturnTime sets the "return_time" field if the given value is not nil.
func (wrodu *WmsReturnOrderDetailUpdate) SetNillableReturnTime(t *time.Time) *WmsReturnOrderDetailUpdate {
	if t != nil {
		wrodu.SetReturnTime(*t)
	}
	return wrodu
}

// ClearReturnTime clears the value of the "return_time" field.
func (wrodu *WmsReturnOrderDetailUpdate) ClearReturnTime() *WmsReturnOrderDetailUpdate {
	wrodu.mutation.ClearReturnTime()
	return wrodu
}

// Mutation returns the WmsReturnOrderDetailMutation object of the builder.
func (wrodu *WmsReturnOrderDetailUpdate) Mutation() *WmsReturnOrderDetailMutation {
	return wrodu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (wrodu *WmsReturnOrderDetailUpdate) Save(ctx context.Context) (int, error) {
	wrodu.defaults()
	return withHooks(ctx, wrodu.sqlSave, wrodu.mutation, wrodu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (wrodu *WmsReturnOrderDetailUpdate) SaveX(ctx context.Context) int {
	affected, err := wrodu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (wrodu *WmsReturnOrderDetailUpdate) Exec(ctx context.Context) error {
	_, err := wrodu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wrodu *WmsReturnOrderDetailUpdate) ExecX(ctx context.Context) {
	if err := wrodu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wrodu *WmsReturnOrderDetailUpdate) defaults() {
	if _, ok := wrodu.mutation.UpdatedAt(); !ok {
		v := wmsreturnorderdetail.UpdateDefaultUpdatedAt()
		wrodu.mutation.SetUpdatedAt(v)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (wrodu *WmsReturnOrderDetailUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *WmsReturnOrderDetailUpdate {
	wrodu.modifiers = append(wrodu.modifiers, modifiers...)
	return wrodu
}

func (wrodu *WmsReturnOrderDetailUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(wmsreturnorderdetail.Table, wmsreturnorderdetail.Columns, sqlgraph.NewFieldSpec(wmsreturnorderdetail.FieldID, field.TypeString))
	if ps := wrodu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := wrodu.mutation.UpdatedAt(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := wrodu.mutation.CreatedBy(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldCreatedBy, field.TypeString, value)
	}
	if wrodu.mutation.CreatedByCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldCreatedBy, field.TypeString)
	}
	if value, ok := wrodu.mutation.UpdatedBy(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldUpdatedBy, field.TypeString, value)
	}
	if wrodu.mutation.UpdatedByCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldUpdatedBy, field.TypeString)
	}
	if value, ok := wrodu.mutation.Remark(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldRemark, field.TypeString, value)
	}
	if wrodu.mutation.RemarkCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldRemark, field.TypeString)
	}
	if value, ok := wrodu.mutation.OrderID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldOrderID, field.TypeString, value)
	}
	if wrodu.mutation.OrderIDCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldOrderID, field.TypeString)
	}
	if value, ok := wrodu.mutation.MaterialID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldMaterialID, field.TypeString, value)
	}
	if wrodu.mutation.MaterialIDCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldMaterialID, field.TypeString)
	}
	if value, ok := wrodu.mutation.MaterialName(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldMaterialName, field.TypeString, value)
	}
	if wrodu.mutation.MaterialNameCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldMaterialName, field.TypeString)
	}
	if value, ok := wrodu.mutation.Code(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldCode, field.TypeString, value)
	}
	if value, ok := wrodu.mutation.EquipmentID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldEquipmentID, field.TypeString, value)
	}
	if wrodu.mutation.EquipmentIDCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldEquipmentID, field.TypeString)
	}
	if value, ok := wrodu.mutation.EquipmentTypeID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldEquipmentTypeID, field.TypeString, value)
	}
	if wrodu.mutation.EquipmentTypeIDCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldEquipmentTypeID, field.TypeString)
	}
	if value, ok := wrodu.mutation.Feature(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldFeature, field.TypeJSON, value)
	}
	if wrodu.mutation.FeatureCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldFeature, field.TypeJSON)
	}
	if value, ok := wrodu.mutation.OwnerID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldOwnerID, field.TypeString, value)
	}
	if wrodu.mutation.OwnerIDCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldOwnerID, field.TypeString)
	}
	if value, ok := wrodu.mutation.ModelNo(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldModelNo, field.TypeString, value)
	}
	if value, ok := wrodu.mutation.MeasureUnitID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldMeasureUnitID, field.TypeString, value)
	}
	if wrodu.mutation.MeasureUnitIDCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldMeasureUnitID, field.TypeString)
	}
	if value, ok := wrodu.mutation.Num(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldNum, field.TypeUint32, value)
	}
	if value, ok := wrodu.mutation.AddedNum(); ok {
		_spec.AddField(wmsreturnorderdetail.FieldNum, field.TypeUint32, value)
	}
	if value, ok := wrodu.mutation.ToRepositoryID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldToRepositoryID, field.TypeString, value)
	}
	if wrodu.mutation.ToRepositoryIDCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldToRepositoryID, field.TypeString)
	}
	if value, ok := wrodu.mutation.ToRepositoryAreaID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldToRepositoryAreaID, field.TypeString, value)
	}
	if wrodu.mutation.ToRepositoryAreaIDCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldToRepositoryAreaID, field.TypeString)
	}
	if value, ok := wrodu.mutation.ToRepositoryPositionID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldToRepositoryPositionID, field.TypeString, value)
	}
	if wrodu.mutation.ToRepositoryPositionIDCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldToRepositoryPositionID, field.TypeString)
	}
	if value, ok := wrodu.mutation.Reason(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldReason, field.TypeString, value)
	}
	if wrodu.mutation.ReasonCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldReason, field.TypeString)
	}
	if value, ok := wrodu.mutation.ReturnTime(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldReturnTime, field.TypeTime, value)
	}
	if wrodu.mutation.ReturnTimeCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldReturnTime, field.TypeTime)
	}
	_spec.AddModifiers(wrodu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, wrodu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{wmsreturnorderdetail.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	wrodu.mutation.done = true
	return n, nil
}

// WmsReturnOrderDetailUpdateOne is the builder for updating a single WmsReturnOrderDetail entity.
type WmsReturnOrderDetailUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *WmsReturnOrderDetailMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdatedAt sets the "updated_at" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetUpdatedAt(t time.Time) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.SetUpdatedAt(t)
	return wroduo
}

// SetCreatedBy sets the "created_by" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetCreatedBy(s string) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.SetCreatedBy(s)
	return wroduo
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetNillableCreatedBy(s *string) *WmsReturnOrderDetailUpdateOne {
	if s != nil {
		wroduo.SetCreatedBy(*s)
	}
	return wroduo
}

// ClearCreatedBy clears the value of the "created_by" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) ClearCreatedBy() *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.ClearCreatedBy()
	return wroduo
}

// SetUpdatedBy sets the "updated_by" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetUpdatedBy(s string) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.SetUpdatedBy(s)
	return wroduo
}

// SetNillableUpdatedBy sets the "updated_by" field if the given value is not nil.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetNillableUpdatedBy(s *string) *WmsReturnOrderDetailUpdateOne {
	if s != nil {
		wroduo.SetUpdatedBy(*s)
	}
	return wroduo
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) ClearUpdatedBy() *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.ClearUpdatedBy()
	return wroduo
}

// SetRemark sets the "remark" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetRemark(s string) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.SetRemark(s)
	return wroduo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetNillableRemark(s *string) *WmsReturnOrderDetailUpdateOne {
	if s != nil {
		wroduo.SetRemark(*s)
	}
	return wroduo
}

// ClearRemark clears the value of the "remark" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) ClearRemark() *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.ClearRemark()
	return wroduo
}

// SetOrderID sets the "order_id" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetOrderID(s string) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.SetOrderID(s)
	return wroduo
}

// SetNillableOrderID sets the "order_id" field if the given value is not nil.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetNillableOrderID(s *string) *WmsReturnOrderDetailUpdateOne {
	if s != nil {
		wroduo.SetOrderID(*s)
	}
	return wroduo
}

// ClearOrderID clears the value of the "order_id" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) ClearOrderID() *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.ClearOrderID()
	return wroduo
}

// SetMaterialID sets the "material_id" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetMaterialID(s string) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.SetMaterialID(s)
	return wroduo
}

// SetNillableMaterialID sets the "material_id" field if the given value is not nil.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetNillableMaterialID(s *string) *WmsReturnOrderDetailUpdateOne {
	if s != nil {
		wroduo.SetMaterialID(*s)
	}
	return wroduo
}

// ClearMaterialID clears the value of the "material_id" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) ClearMaterialID() *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.ClearMaterialID()
	return wroduo
}

// SetMaterialName sets the "material_name" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetMaterialName(s string) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.SetMaterialName(s)
	return wroduo
}

// SetNillableMaterialName sets the "material_name" field if the given value is not nil.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetNillableMaterialName(s *string) *WmsReturnOrderDetailUpdateOne {
	if s != nil {
		wroduo.SetMaterialName(*s)
	}
	return wroduo
}

// ClearMaterialName clears the value of the "material_name" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) ClearMaterialName() *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.ClearMaterialName()
	return wroduo
}

// SetCode sets the "code" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetCode(s string) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.SetCode(s)
	return wroduo
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetNillableCode(s *string) *WmsReturnOrderDetailUpdateOne {
	if s != nil {
		wroduo.SetCode(*s)
	}
	return wroduo
}

// SetEquipmentID sets the "equipment_id" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetEquipmentID(s string) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.SetEquipmentID(s)
	return wroduo
}

// SetNillableEquipmentID sets the "equipment_id" field if the given value is not nil.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetNillableEquipmentID(s *string) *WmsReturnOrderDetailUpdateOne {
	if s != nil {
		wroduo.SetEquipmentID(*s)
	}
	return wroduo
}

// ClearEquipmentID clears the value of the "equipment_id" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) ClearEquipmentID() *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.ClearEquipmentID()
	return wroduo
}

// SetEquipmentTypeID sets the "equipment_type_id" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetEquipmentTypeID(s string) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.SetEquipmentTypeID(s)
	return wroduo
}

// SetNillableEquipmentTypeID sets the "equipment_type_id" field if the given value is not nil.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetNillableEquipmentTypeID(s *string) *WmsReturnOrderDetailUpdateOne {
	if s != nil {
		wroduo.SetEquipmentTypeID(*s)
	}
	return wroduo
}

// ClearEquipmentTypeID clears the value of the "equipment_type_id" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) ClearEquipmentTypeID() *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.ClearEquipmentTypeID()
	return wroduo
}

// SetFeature sets the "feature" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetFeature(m map[string]interface{}) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.SetFeature(m)
	return wroduo
}

// ClearFeature clears the value of the "feature" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) ClearFeature() *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.ClearFeature()
	return wroduo
}

// SetOwnerID sets the "owner_id" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetOwnerID(s string) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.SetOwnerID(s)
	return wroduo
}

// SetNillableOwnerID sets the "owner_id" field if the given value is not nil.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetNillableOwnerID(s *string) *WmsReturnOrderDetailUpdateOne {
	if s != nil {
		wroduo.SetOwnerID(*s)
	}
	return wroduo
}

// ClearOwnerID clears the value of the "owner_id" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) ClearOwnerID() *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.ClearOwnerID()
	return wroduo
}

// SetModelNo sets the "ModelNo" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetModelNo(s string) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.SetModelNo(s)
	return wroduo
}

// SetNillableModelNo sets the "ModelNo" field if the given value is not nil.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetNillableModelNo(s *string) *WmsReturnOrderDetailUpdateOne {
	if s != nil {
		wroduo.SetModelNo(*s)
	}
	return wroduo
}

// SetMeasureUnitID sets the "measure_unit_id" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetMeasureUnitID(s string) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.SetMeasureUnitID(s)
	return wroduo
}

// SetNillableMeasureUnitID sets the "measure_unit_id" field if the given value is not nil.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetNillableMeasureUnitID(s *string) *WmsReturnOrderDetailUpdateOne {
	if s != nil {
		wroduo.SetMeasureUnitID(*s)
	}
	return wroduo
}

// ClearMeasureUnitID clears the value of the "measure_unit_id" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) ClearMeasureUnitID() *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.ClearMeasureUnitID()
	return wroduo
}

// SetNum sets the "num" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetNum(u uint32) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.ResetNum()
	wroduo.mutation.SetNum(u)
	return wroduo
}

// SetNillableNum sets the "num" field if the given value is not nil.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetNillableNum(u *uint32) *WmsReturnOrderDetailUpdateOne {
	if u != nil {
		wroduo.SetNum(*u)
	}
	return wroduo
}

// AddNum adds u to the "num" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) AddNum(u int32) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.AddNum(u)
	return wroduo
}

// SetToRepositoryID sets the "to_repository_id" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetToRepositoryID(s string) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.SetToRepositoryID(s)
	return wroduo
}

// SetNillableToRepositoryID sets the "to_repository_id" field if the given value is not nil.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetNillableToRepositoryID(s *string) *WmsReturnOrderDetailUpdateOne {
	if s != nil {
		wroduo.SetToRepositoryID(*s)
	}
	return wroduo
}

// ClearToRepositoryID clears the value of the "to_repository_id" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) ClearToRepositoryID() *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.ClearToRepositoryID()
	return wroduo
}

// SetToRepositoryAreaID sets the "to_repository_area_id" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetToRepositoryAreaID(s string) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.SetToRepositoryAreaID(s)
	return wroduo
}

// SetNillableToRepositoryAreaID sets the "to_repository_area_id" field if the given value is not nil.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetNillableToRepositoryAreaID(s *string) *WmsReturnOrderDetailUpdateOne {
	if s != nil {
		wroduo.SetToRepositoryAreaID(*s)
	}
	return wroduo
}

// ClearToRepositoryAreaID clears the value of the "to_repository_area_id" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) ClearToRepositoryAreaID() *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.ClearToRepositoryAreaID()
	return wroduo
}

// SetToRepositoryPositionID sets the "to_repository_position_id" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetToRepositoryPositionID(s string) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.SetToRepositoryPositionID(s)
	return wroduo
}

// SetNillableToRepositoryPositionID sets the "to_repository_position_id" field if the given value is not nil.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetNillableToRepositoryPositionID(s *string) *WmsReturnOrderDetailUpdateOne {
	if s != nil {
		wroduo.SetToRepositoryPositionID(*s)
	}
	return wroduo
}

// ClearToRepositoryPositionID clears the value of the "to_repository_position_id" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) ClearToRepositoryPositionID() *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.ClearToRepositoryPositionID()
	return wroduo
}

// SetReason sets the "reason" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetReason(s string) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.SetReason(s)
	return wroduo
}

// SetNillableReason sets the "reason" field if the given value is not nil.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetNillableReason(s *string) *WmsReturnOrderDetailUpdateOne {
	if s != nil {
		wroduo.SetReason(*s)
	}
	return wroduo
}

// ClearReason clears the value of the "reason" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) ClearReason() *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.ClearReason()
	return wroduo
}

// SetReturnTime sets the "return_time" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetReturnTime(t time.Time) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.SetReturnTime(t)
	return wroduo
}

// SetNillableReturnTime sets the "return_time" field if the given value is not nil.
func (wroduo *WmsReturnOrderDetailUpdateOne) SetNillableReturnTime(t *time.Time) *WmsReturnOrderDetailUpdateOne {
	if t != nil {
		wroduo.SetReturnTime(*t)
	}
	return wroduo
}

// ClearReturnTime clears the value of the "return_time" field.
func (wroduo *WmsReturnOrderDetailUpdateOne) ClearReturnTime() *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.ClearReturnTime()
	return wroduo
}

// Mutation returns the WmsReturnOrderDetailMutation object of the builder.
func (wroduo *WmsReturnOrderDetailUpdateOne) Mutation() *WmsReturnOrderDetailMutation {
	return wroduo.mutation
}

// Where appends a list predicates to the WmsReturnOrderDetailUpdate builder.
func (wroduo *WmsReturnOrderDetailUpdateOne) Where(ps ...predicate.WmsReturnOrderDetail) *WmsReturnOrderDetailUpdateOne {
	wroduo.mutation.Where(ps...)
	return wroduo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (wroduo *WmsReturnOrderDetailUpdateOne) Select(field string, fields ...string) *WmsReturnOrderDetailUpdateOne {
	wroduo.fields = append([]string{field}, fields...)
	return wroduo
}

// Save executes the query and returns the updated WmsReturnOrderDetail entity.
func (wroduo *WmsReturnOrderDetailUpdateOne) Save(ctx context.Context) (*WmsReturnOrderDetail, error) {
	wroduo.defaults()
	return withHooks(ctx, wroduo.sqlSave, wroduo.mutation, wroduo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (wroduo *WmsReturnOrderDetailUpdateOne) SaveX(ctx context.Context) *WmsReturnOrderDetail {
	node, err := wroduo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (wroduo *WmsReturnOrderDetailUpdateOne) Exec(ctx context.Context) error {
	_, err := wroduo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wroduo *WmsReturnOrderDetailUpdateOne) ExecX(ctx context.Context) {
	if err := wroduo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wroduo *WmsReturnOrderDetailUpdateOne) defaults() {
	if _, ok := wroduo.mutation.UpdatedAt(); !ok {
		v := wmsreturnorderdetail.UpdateDefaultUpdatedAt()
		wroduo.mutation.SetUpdatedAt(v)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (wroduo *WmsReturnOrderDetailUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *WmsReturnOrderDetailUpdateOne {
	wroduo.modifiers = append(wroduo.modifiers, modifiers...)
	return wroduo
}

func (wroduo *WmsReturnOrderDetailUpdateOne) sqlSave(ctx context.Context) (_node *WmsReturnOrderDetail, err error) {
	_spec := sqlgraph.NewUpdateSpec(wmsreturnorderdetail.Table, wmsreturnorderdetail.Columns, sqlgraph.NewFieldSpec(wmsreturnorderdetail.FieldID, field.TypeString))
	id, ok := wroduo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "WmsReturnOrderDetail.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := wroduo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, wmsreturnorderdetail.FieldID)
		for _, f := range fields {
			if !wmsreturnorderdetail.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != wmsreturnorderdetail.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := wroduo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := wroduo.mutation.UpdatedAt(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := wroduo.mutation.CreatedBy(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldCreatedBy, field.TypeString, value)
	}
	if wroduo.mutation.CreatedByCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldCreatedBy, field.TypeString)
	}
	if value, ok := wroduo.mutation.UpdatedBy(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldUpdatedBy, field.TypeString, value)
	}
	if wroduo.mutation.UpdatedByCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldUpdatedBy, field.TypeString)
	}
	if value, ok := wroduo.mutation.Remark(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldRemark, field.TypeString, value)
	}
	if wroduo.mutation.RemarkCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldRemark, field.TypeString)
	}
	if value, ok := wroduo.mutation.OrderID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldOrderID, field.TypeString, value)
	}
	if wroduo.mutation.OrderIDCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldOrderID, field.TypeString)
	}
	if value, ok := wroduo.mutation.MaterialID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldMaterialID, field.TypeString, value)
	}
	if wroduo.mutation.MaterialIDCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldMaterialID, field.TypeString)
	}
	if value, ok := wroduo.mutation.MaterialName(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldMaterialName, field.TypeString, value)
	}
	if wroduo.mutation.MaterialNameCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldMaterialName, field.TypeString)
	}
	if value, ok := wroduo.mutation.Code(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldCode, field.TypeString, value)
	}
	if value, ok := wroduo.mutation.EquipmentID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldEquipmentID, field.TypeString, value)
	}
	if wroduo.mutation.EquipmentIDCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldEquipmentID, field.TypeString)
	}
	if value, ok := wroduo.mutation.EquipmentTypeID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldEquipmentTypeID, field.TypeString, value)
	}
	if wroduo.mutation.EquipmentTypeIDCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldEquipmentTypeID, field.TypeString)
	}
	if value, ok := wroduo.mutation.Feature(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldFeature, field.TypeJSON, value)
	}
	if wroduo.mutation.FeatureCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldFeature, field.TypeJSON)
	}
	if value, ok := wroduo.mutation.OwnerID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldOwnerID, field.TypeString, value)
	}
	if wroduo.mutation.OwnerIDCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldOwnerID, field.TypeString)
	}
	if value, ok := wroduo.mutation.ModelNo(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldModelNo, field.TypeString, value)
	}
	if value, ok := wroduo.mutation.MeasureUnitID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldMeasureUnitID, field.TypeString, value)
	}
	if wroduo.mutation.MeasureUnitIDCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldMeasureUnitID, field.TypeString)
	}
	if value, ok := wroduo.mutation.Num(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldNum, field.TypeUint32, value)
	}
	if value, ok := wroduo.mutation.AddedNum(); ok {
		_spec.AddField(wmsreturnorderdetail.FieldNum, field.TypeUint32, value)
	}
	if value, ok := wroduo.mutation.ToRepositoryID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldToRepositoryID, field.TypeString, value)
	}
	if wroduo.mutation.ToRepositoryIDCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldToRepositoryID, field.TypeString)
	}
	if value, ok := wroduo.mutation.ToRepositoryAreaID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldToRepositoryAreaID, field.TypeString, value)
	}
	if wroduo.mutation.ToRepositoryAreaIDCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldToRepositoryAreaID, field.TypeString)
	}
	if value, ok := wroduo.mutation.ToRepositoryPositionID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldToRepositoryPositionID, field.TypeString, value)
	}
	if wroduo.mutation.ToRepositoryPositionIDCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldToRepositoryPositionID, field.TypeString)
	}
	if value, ok := wroduo.mutation.Reason(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldReason, field.TypeString, value)
	}
	if wroduo.mutation.ReasonCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldReason, field.TypeString)
	}
	if value, ok := wroduo.mutation.ReturnTime(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldReturnTime, field.TypeTime, value)
	}
	if wroduo.mutation.ReturnTimeCleared() {
		_spec.ClearField(wmsreturnorderdetail.FieldReturnTime, field.TypeTime)
	}
	_spec.AddModifiers(wroduo.modifiers...)
	_node = &WmsReturnOrderDetail{config: wroduo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, wroduo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{wmsreturnorderdetail.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	wroduo.mutation.done = true
	return _node, nil
}
