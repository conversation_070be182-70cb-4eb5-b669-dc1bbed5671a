// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// AppApproveActivitiesColumns holds the columns for the "app_approve_activities" table.
	AppApproveActivitiesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "name", Type: field.TypeString, Unique: true, Size: 2147483647, Comment: "名称"},
		{Name: "module_id", Type: field.TypeString, Comment: "功能模块"},
		{Name: "description", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "描述"},
		{Name: "function", Type: field.TypeString, Unique: true, Comment: "方法名称"},
		{Name: "parameters", Type: field.TypeString, Size: 2147483647, Comment: "方法参数"},
		{Name: "result", Type: field.TypeString, Size: 2147483647, Comment: "方法返回值"},
	}
	// AppApproveActivitiesTable holds the schema information for the "app_approve_activities" table.
	AppApproveActivitiesTable = &schema.Table{
		Name:       "app_approve_activities",
		Comment:    "审批功能配置",
		Columns:    AppApproveActivitiesColumns,
		PrimaryKey: []*schema.Column{AppApproveActivitiesColumns[0]},
	}
	// AppApproveBasicsColumns holds the columns for the "app_approve_basics" table.
	AppApproveBasicsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "icon", Type: field.TypeString, Size: 2147483647, Comment: "图标"},
		{Name: "code", Type: field.TypeString, Unique: true, Comment: "流程编码"},
		{Name: "prefix", Type: field.TypeString, Nullable: true, Comment: "前缀"},
		{Name: "name", Type: field.TypeString, Comment: "名称"},
		{Name: "description", Type: field.TypeString, Nullable: true, Comment: "描述"},
		{Name: "visibility", Type: field.TypeEnum, Comment: "可见性", Enums: []string{"ALL", "CUSTOM"}, Default: "ALL"},
		{Name: "permitted_user", Type: field.TypeJSON, Nullable: true, Comment: "允许使用的用户"},
		{Name: "permitted_org", Type: field.TypeJSON, Nullable: true, Comment: "允许使用的组织"},
		{Name: "setting", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "审批设置"},
		{Name: "group_id", Type: field.TypeString, Comment: "分组Id"},
		{Name: "notify_type", Type: field.TypeJSON, Nullable: true, Comment: "通知方式"},
	}
	// AppApproveBasicsTable holds the schema information for the "app_approve_basics" table.
	AppApproveBasicsTable = &schema.Table{
		Name:       "app_approve_basics",
		Comment:    "审批流程基础信息",
		Columns:    AppApproveBasicsColumns,
		PrimaryKey: []*schema.Column{AppApproveBasicsColumns[0]},
	}
	// AppApproveGroupsColumns holds the columns for the "app_approve_groups" table.
	AppApproveGroupsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "name", Type: field.TypeString, Unique: true, Comment: "分组名称"},
		{Name: "description", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "描述"},
	}
	// AppApproveGroupsTable holds the schema information for the "app_approve_groups" table.
	AppApproveGroupsTable = &schema.Table{
		Name:       "app_approve_groups",
		Comment:    "审批流程表单配置",
		Columns:    AppApproveGroupsColumns,
		PrimaryKey: []*schema.Column{AppApproveGroupsColumns[0]},
	}
	// AppApproveRecordsColumns holds the columns for the "app_approve_records" table.
	AppApproveRecordsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "approve_code", Type: field.TypeString, Comment: "审批类型"},
		{Name: "workflow_id", Type: field.TypeString, Comment: "流程id"},
		{Name: "run_id", Type: field.TypeString, Comment: "运行id"},
		{Name: "status", Type: field.TypeEnum, Nullable: true, Comment: "状态", Enums: []string{"Pending", "Processing", "Approved", "Rejected", "Canceled", "Handle"}, Default: "Pending"},
		{Name: "approve_basic_id", Type: field.TypeString, Comment: "审批ID"},
	}
	// AppApproveRecordsTable holds the schema information for the "app_approve_records" table.
	AppApproveRecordsTable = &schema.Table{
		Name:       "app_approve_records",
		Comment:    "审批申请表",
		Columns:    AppApproveRecordsColumns,
		PrimaryKey: []*schema.Column{AppApproveRecordsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "app_approve_records_app_approve_basics_approve_record",
				Columns:    []*schema.Column{AppApproveRecordsColumns[9]},
				RefColumns: []*schema.Column{AppApproveBasicsColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
	}
	// AppApproveRecordLogsColumns holds the columns for the "app_approve_record_logs" table.
	AppApproveRecordLogsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "approve_basic_id", Type: field.TypeString, Comment: "审批ID"},
		{Name: "process_type", Type: field.TypeEnum, Comment: "执行类型", Enums: []string{"提交", "审批", "抄送", "办理"}},
		{Name: "process_result", Type: field.TypeEnum, Nullable: true, Comment: "执行结果", Enums: []string{"审批通过", "审批拒绝", "审批取消", "退回", "办理", "办理完成", "办理失败"}},
		{Name: "user_id", Type: field.TypeString, Comment: "用户Id"},
		{Name: "sign", Type: field.TypeString, Nullable: true, Comment: "签名"},
		{Name: "opinion", Type: field.TypeString, Nullable: true, Comment: "审批意见"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "approve_record_id", Type: field.TypeString, Comment: "审批记录ID"},
	}
	// AppApproveRecordLogsTable holds the schema information for the "app_approve_record_logs" table.
	AppApproveRecordLogsTable = &schema.Table{
		Name:       "app_approve_record_logs",
		Comment:    "审批记录日志表",
		Columns:    AppApproveRecordLogsColumns,
		PrimaryKey: []*schema.Column{AppApproveRecordLogsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "app_approve_record_logs_app_approve_records_logs",
				Columns:    []*schema.Column{AppApproveRecordLogsColumns[12]},
				RefColumns: []*schema.Column{AppApproveRecordsColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
	}
	// AppApproveRecordQueuesColumns holds the columns for the "app_approve_record_queues" table.
	AppApproveRecordQueuesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "approve_basic_id", Type: field.TypeString, Comment: "审批ID"},
		{Name: "step_id", Type: field.TypeString, Comment: "步骤ID"},
		{Name: "process_type", Type: field.TypeEnum, Comment: "执行类型", Enums: []string{"审批", "办理", "抄送"}},
		{Name: "user_id", Type: field.TypeString, Comment: "操作人"},
		{Name: "need_sign", Type: field.TypeBool, Comment: "需要手写签名", Default: false},
		{Name: "allow_rollback", Type: field.TypeBool, Comment: "允许撤回", Default: false},
		{Name: "need_opinion", Type: field.TypeBool, Comment: "是否必填审批意见", Default: false},
		{Name: "allow_remark", Type: field.TypeBool, Comment: "是否允许备注", Default: true},
		{Name: "status", Type: field.TypeEnum, Nullable: true, Comment: "状态", Enums: []string{"Pending", "Approved", "Rejected", "Canceled", "Handle"}, Default: "Pending"},
		{Name: "approve_record_id", Type: field.TypeString, Comment: "审批记录ID"},
	}
	// AppApproveRecordQueuesTable holds the schema information for the "app_approve_record_queues" table.
	AppApproveRecordQueuesTable = &schema.Table{
		Name:       "app_approve_record_queues",
		Comment:    "待操作审批记录表",
		Columns:    AppApproveRecordQueuesColumns,
		PrimaryKey: []*schema.Column{AppApproveRecordQueuesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "app_approve_record_queues_app_approve_records_queues",
				Columns:    []*schema.Column{AppApproveRecordQueuesColumns[14]},
				RefColumns: []*schema.Column{AppApproveRecordsColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "appapproverecordqueue_approve_basic_id_approve_record_id_step_id_user_id",
				Unique:  true,
				Columns: []*schema.Column{AppApproveRecordQueuesColumns[5], AppApproveRecordQueuesColumns[14], AppApproveRecordQueuesColumns[6], AppApproveRecordQueuesColumns[8]},
			},
		},
	}
	// AppApproveWorkflowsColumns holds the columns for the "app_approve_workflows" table.
	AppApproveWorkflowsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "approve_id", Type: field.TypeString, Comment: "审批id"},
		{Name: "workflow", Type: field.TypeString, Size: 2147483647, Comment: "流程图"},
	}
	// AppApproveWorkflowsTable holds the schema information for the "app_approve_workflows" table.
	AppApproveWorkflowsTable = &schema.Table{
		Name:       "app_approve_workflows",
		Comment:    "审批流程图配置",
		Columns:    AppApproveWorkflowsColumns,
		PrimaryKey: []*schema.Column{AppApproveWorkflowsColumns[0]},
	}
	// DemoBuildingsColumns holds the columns for the "demo_buildings" table.
	DemoBuildingsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "created_by", Type: field.TypeString, Nullable: true},
		{Name: "updated_by", Type: field.TypeString, Nullable: true},
		{Name: "team_id", Type: field.TypeString},
		{Name: "application_id", Type: field.TypeString},
		{Name: "project_id", Type: field.TypeString},
		{Name: "name", Type: field.TypeString},
	}
	// DemoBuildingsTable holds the schema information for the "demo_buildings" table.
	DemoBuildingsTable = &schema.Table{
		Name:       "demo_buildings",
		Columns:    DemoBuildingsColumns,
		PrimaryKey: []*schema.Column{DemoBuildingsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "demobuilding_team_id",
				Unique:  false,
				Columns: []*schema.Column{DemoBuildingsColumns[5]},
			},
			{
				Name:    "demobuilding_application_id",
				Unique:  false,
				Columns: []*schema.Column{DemoBuildingsColumns[6]},
			},
			{
				Name:    "demobuilding_project_id",
				Unique:  false,
				Columns: []*schema.Column{DemoBuildingsColumns[7]},
			},
		},
	}
	// SysApplicationsColumns holds the columns for the "sys_applications" table.
	SysApplicationsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "sort", Type: field.TypeInt32, Nullable: true, Comment: "排序", Default: 0},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "name", Type: field.TypeString, Comment: "应用名称"},
		{Name: "code", Type: field.TypeString, Unique: true, Comment: "应用标识"},
	}
	// SysApplicationsTable holds the schema information for the "sys_applications" table.
	SysApplicationsTable = &schema.Table{
		Name:       "sys_applications",
		Comment:    "应用产品表",
		Columns:    SysApplicationsColumns,
		PrimaryKey: []*schema.Column{SysApplicationsColumns[0]},
	}
	// SysApplicationModulesColumns holds the columns for the "sys_application_modules" table.
	SysApplicationModulesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "application_id", Type: field.TypeString, Comment: "AppID"},
		{Name: "module_id", Type: field.TypeString, Comment: "模块ID"},
	}
	// SysApplicationModulesTable holds the schema information for the "sys_application_modules" table.
	SysApplicationModulesTable = &schema.Table{
		Name:       "sys_application_modules",
		Comment:    "组成应用的功能模块表",
		Columns:    SysApplicationModulesColumns,
		PrimaryKey: []*schema.Column{SysApplicationModulesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "sys_application_modules_sys_applications_modules",
				Columns:    []*schema.Column{SysApplicationModulesColumns[5]},
				RefColumns: []*schema.Column{SysApplicationsColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "sys_application_modules_sys_modules_applications",
				Columns:    []*schema.Column{SysApplicationModulesColumns[6]},
				RefColumns: []*schema.Column{SysModulesColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "sysapplicationmodule_application_id_module_id",
				Unique:  true,
				Columns: []*schema.Column{SysApplicationModulesColumns[5], SysApplicationModulesColumns[6]},
			},
		},
	}
	// SysApplicationModuleResourcesColumns holds the columns for the "sys_application_module_resources" table.
	SysApplicationModuleResourcesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "module_id", Type: field.TypeString, Comment: "模块ID"},
		{Name: "application_id", Type: field.TypeString, Comment: "产品ID"},
		{Name: "resource_id", Type: field.TypeString, Comment: "资源ID"},
	}
	// SysApplicationModuleResourcesTable holds the schema information for the "sys_application_module_resources" table.
	SysApplicationModuleResourcesTable = &schema.Table{
		Name:       "sys_application_module_resources",
		Comment:    "组成应用的功能模块资源表",
		Columns:    SysApplicationModuleResourcesColumns,
		PrimaryKey: []*schema.Column{SysApplicationModuleResourcesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "sys_application_module_resources_sys_applications_resources",
				Columns:    []*schema.Column{SysApplicationModuleResourcesColumns[6]},
				RefColumns: []*schema.Column{SysApplicationsColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "sys_application_module_resources_sys_resources_applications",
				Columns:    []*schema.Column{SysApplicationModuleResourcesColumns[7]},
				RefColumns: []*schema.Column{SysResourcesColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "sysapplicationmoduleresource_application_id_module_id_resource_id",
				Unique:  true,
				Columns: []*schema.Column{SysApplicationModuleResourcesColumns[6], SysApplicationModuleResourcesColumns[5], SysApplicationModuleResourcesColumns[7]},
			},
		},
	}
	// SysAreasColumns holds the columns for the "sys_areas" table.
	SysAreasColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "code", Type: field.TypeString, Comment: "编号"},
		{Name: "name", Type: field.TypeString, Comment: "名称"},
		{Name: "province_code", Type: field.TypeString, Comment: "省份代码"},
		{Name: "city_code", Type: field.TypeString, Comment: "城市代码"},
	}
	// SysAreasTable holds the schema information for the "sys_areas" table.
	SysAreasTable = &schema.Table{
		Name:       "sys_areas",
		Comment:    "区县表",
		Columns:    SysAreasColumns,
		PrimaryKey: []*schema.Column{SysAreasColumns[0]},
	}
	// SysCitiesColumns holds the columns for the "sys_cities" table.
	SysCitiesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "code", Type: field.TypeString, Comment: "编号"},
		{Name: "name", Type: field.TypeString, Comment: "名称"},
		{Name: "province_code", Type: field.TypeString, Comment: "省份代码"},
	}
	// SysCitiesTable holds the schema information for the "sys_cities" table.
	SysCitiesTable = &schema.Table{
		Name:       "sys_cities",
		Comment:    "城市表",
		Columns:    SysCitiesColumns,
		PrimaryKey: []*schema.Column{SysCitiesColumns[0]},
	}
	// SysConfigsColumns holds the columns for the "sys_configs" table.
	SysConfigsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "key", Type: field.TypeString, Comment: "键"},
		{Name: "value", Type: field.TypeString, Comment: "值"},
	}
	// SysConfigsTable holds the schema information for the "sys_configs" table.
	SysConfigsTable = &schema.Table{
		Name:       "sys_configs",
		Comment:    "配置表",
		Columns:    SysConfigsColumns,
		PrimaryKey: []*schema.Column{SysConfigsColumns[0]},
	}
	// SysDictionariesColumns holds the columns for the "sys_dictionaries" table.
	SysDictionariesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "sort", Type: field.TypeInt32, Nullable: true, Comment: "排序", Default: 0},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "label", Type: field.TypeString, Nullable: true, Comment: "显示名称(中文)"},
		{Name: "name", Type: field.TypeString, Unique: true, Comment: "名称(英文)"},
	}
	// SysDictionariesTable holds the schema information for the "sys_dictionaries" table.
	SysDictionariesTable = &schema.Table{
		Name:       "sys_dictionaries",
		Comment:    "字典表",
		Columns:    SysDictionariesColumns,
		PrimaryKey: []*schema.Column{SysDictionariesColumns[0]},
	}
	// SysDictionaryDetailsColumns holds the columns for the "sys_dictionary_details" table.
	SysDictionaryDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "sort", Type: field.TypeInt32, Nullable: true, Comment: "排序", Default: 0},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "value", Type: field.TypeString, Comment: "值"},
		{Name: "label", Type: field.TypeString, Comment: "显示值"},
		{Name: "extend", Type: field.TypeString, Nullable: true, Comment: "扩展值"},
		{Name: "dictionary_id", Type: field.TypeString, Comment: "字典ID"},
	}
	// SysDictionaryDetailsTable holds the schema information for the "sys_dictionary_details" table.
	SysDictionaryDetailsTable = &schema.Table{
		Name:       "sys_dictionary_details",
		Comment:    "字典详情表",
		Columns:    SysDictionaryDetailsColumns,
		PrimaryKey: []*schema.Column{SysDictionaryDetailsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "sys_dictionary_details_sys_dictionaries_details",
				Columns:    []*schema.Column{SysDictionaryDetailsColumns[11]},
				RefColumns: []*schema.Column{SysDictionariesColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "sysdictionarydetail_dictionary_id_value",
				Unique:  true,
				Columns: []*schema.Column{SysDictionaryDetailsColumns[11], SysDictionaryDetailsColumns[8]},
			},
		},
	}
	// SysGamesColumns holds the columns for the "sys_games" table.
	SysGamesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "name", Type: field.TypeString, Comment: "名称"},
		{Name: "subtitle", Type: field.TypeString, Nullable: true, Comment: "副标题"},
	}
	// SysGamesTable holds the schema information for the "sys_games" table.
	SysGamesTable = &schema.Table{
		Name:       "sys_games",
		Comment:    "Game",
		Columns:    SysGamesColumns,
		PrimaryKey: []*schema.Column{SysGamesColumns[0]},
	}
	// SysLabelsColumns holds the columns for the "sys_labels" table.
	SysLabelsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "name", Type: field.TypeString, Comment: "标签名称"},
		{Name: "description", Type: field.TypeString, Nullable: true, Comment: "描述信息"},
		{Name: "category_id", Type: field.TypeString, Nullable: true, Comment: "标签分类"},
		{Name: "parent_id", Type: field.TypeString, Nullable: true, Comment: "父级ID"},
	}
	// SysLabelsTable holds the schema information for the "sys_labels" table.
	SysLabelsTable = &schema.Table{
		Name:       "sys_labels",
		Comment:    "标签表",
		Columns:    SysLabelsColumns,
		PrimaryKey: []*schema.Column{SysLabelsColumns[0]},
	}
	// SysLogsColumns holds the columns for the "sys_logs" table.
	SysLogsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "created_by", Type: field.TypeString, Nullable: true},
		{Name: "updated_by", Type: field.TypeString, Nullable: true},
		{Name: "status", Type: field.TypeInt32, Default: 0},
		{Name: "user_id", Type: field.TypeString, Nullable: true},
		{Name: "username", Type: field.TypeString, Nullable: true},
		{Name: "nickname", Type: field.TypeString, Nullable: true},
		{Name: "platform", Type: field.TypeString, Nullable: true},
		{Name: "ip", Type: field.TypeString, Nullable: true},
		{Name: "longitude", Type: field.TypeString, Nullable: true},
		{Name: "latitude", Type: field.TypeString, Nullable: true},
		{Name: "content", Type: field.TypeString, Nullable: true},
		{Name: "api", Type: field.TypeString, Nullable: true},
		{Name: "params", Type: field.TypeString, Nullable: true},
	}
	// SysLogsTable holds the schema information for the "sys_logs" table.
	SysLogsTable = &schema.Table{
		Name:       "sys_logs",
		Columns:    SysLogsColumns,
		PrimaryKey: []*schema.Column{SysLogsColumns[0]},
	}
	// SysMenusColumns holds the columns for the "sys_menus" table.
	SysMenusColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "project_id", Type: field.TypeString, Nullable: true, Comment: "项目ID"},
		{Name: "name", Type: field.TypeString, Comment: "菜单名称"},
		{Name: "type", Type: field.TypeEnum, Comment: "菜单类型", Enums: []string{"PC", "MOBILE"}, Default: "PC"},
		{Name: "icon", Type: field.TypeString, Nullable: true, Comment: "图标(中文)"},
		{Name: "position", Type: field.TypeEnum, Comment: "菜单位置", Enums: []string{"LEFT", "TOP"}, Default: "LEFT"},
		{Name: "path", Type: field.TypeString, Nullable: true, Comment: "路径"},
		{Name: "is_submenu", Type: field.TypeBool, Comment: "三级子菜单", Default: false},
		{Name: "is_link", Type: field.TypeBool, Comment: "是否外链", Default: false},
		{Name: "hide_children_in_menu", Type: field.TypeBool, Comment: "是否隐藏子菜单", Default: false},
		{Name: "hide_menu", Type: field.TypeBool, Comment: "是否隐藏菜单", Default: false},
		{Name: "sort", Type: field.TypeInt32, Comment: "排序", Default: 0},
		{Name: "parent_id", Type: field.TypeString, Nullable: true, Comment: "父级ID"},
		{Name: "sidebar_expansion_type", Type: field.TypeString, Nullable: true, Comment: "侧边栏展开方式"},
		{Name: "application_id", Type: field.TypeString, Comment: "应用ID"},
		{Name: "resource_id", Type: field.TypeString, Nullable: true, Comment: "资源ID"},
	}
	// SysMenusTable holds the schema information for the "sys_menus" table.
	SysMenusTable = &schema.Table{
		Name:       "sys_menus",
		Comment:    "应用菜单表",
		Columns:    SysMenusColumns,
		PrimaryKey: []*schema.Column{SysMenusColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "sys_menus_sys_applications_menus",
				Columns:    []*schema.Column{SysMenusColumns[18]},
				RefColumns: []*schema.Column{SysApplicationsColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "sys_menus_sys_resources_resource",
				Columns:    []*schema.Column{SysMenusColumns[19]},
				RefColumns: []*schema.Column{SysResourcesColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// SysMessageTemplatesColumns holds the columns for the "sys_message_templates" table.
	SysMessageTemplatesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "name", Type: field.TypeString, Comment: "模板名称"},
		{Name: "channel", Type: field.TypeEnum, Comment: "推送渠道", Enums: []string{"Wechat", "WorkWechat", "App", "Sms"}, Default: "Wechat"},
		{Name: "code", Type: field.TypeString, Comment: "模板编码", Default: ""},
		{Name: "content", Type: field.TypeString, Comment: "模板内容"},
	}
	// SysMessageTemplatesTable holds the schema information for the "sys_message_templates" table.
	SysMessageTemplatesTable = &schema.Table{
		Name:       "sys_message_templates",
		Comment:    "推送消息模板表",
		Columns:    SysMessageTemplatesColumns,
		PrimaryKey: []*schema.Column{SysMessageTemplatesColumns[0]},
	}
	// SysModulesColumns holds the columns for the "sys_modules" table.
	SysModulesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "sort", Type: field.TypeInt32, Nullable: true, Comment: "排序", Default: 0},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "name", Type: field.TypeString, Comment: "模块名称"},
		{Name: "code", Type: field.TypeString, Comment: "模块标识"},
	}
	// SysModulesTable holds the schema information for the "sys_modules" table.
	SysModulesTable = &schema.Table{
		Name:       "sys_modules",
		Comment:    "功能模块表",
		Columns:    SysModulesColumns,
		PrimaryKey: []*schema.Column{SysModulesColumns[0]},
	}
	// SysModuleResourcesColumns holds the columns for the "sys_module_resources" table.
	SysModuleResourcesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "module_id", Type: field.TypeString, Comment: "模块ID"},
		{Name: "resource_id", Type: field.TypeString, Comment: "资源ID"},
	}
	// SysModuleResourcesTable holds the schema information for the "sys_module_resources" table.
	SysModuleResourcesTable = &schema.Table{
		Name:       "sys_module_resources",
		Comment:    "功能模块资源表",
		Columns:    SysModuleResourcesColumns,
		PrimaryKey: []*schema.Column{SysModuleResourcesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "sys_module_resources_sys_modules_module_resources",
				Columns:    []*schema.Column{SysModuleResourcesColumns[5]},
				RefColumns: []*schema.Column{SysModulesColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "sys_module_resources_sys_resources_resource_modules",
				Columns:    []*schema.Column{SysModuleResourcesColumns[6]},
				RefColumns: []*schema.Column{SysResourcesColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "sysmoduleresource_module_id_resource_id",
				Unique:  true,
				Columns: []*schema.Column{SysModuleResourcesColumns[5], SysModuleResourcesColumns[6]},
			},
		},
	}
	// SysOrganizationColumns holds the columns for the "sys_organization" table.
	SysOrganizationColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "name", Type: field.TypeString, Comment: "部门名称"},
		{Name: "application_id", Type: field.TypeString, Comment: "应用ID"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "sort", Type: field.TypeInt32, Nullable: true, Comment: "排序"},
		{Name: "parent_id", Type: field.TypeString, Nullable: true, Comment: "父级ID"},
		{Name: "team_id", Type: field.TypeString, Comment: "团队ID"},
	}
	// SysOrganizationTable holds the schema information for the "sys_organization" table.
	SysOrganizationTable = &schema.Table{
		Name:       "sys_organization",
		Comment:    "用户组织表",
		Columns:    SysOrganizationColumns,
		PrimaryKey: []*schema.Column{SysOrganizationColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "sys_organization_sys_organization_parent",
				Columns:    []*schema.Column{SysOrganizationColumns[9]},
				RefColumns: []*schema.Column{SysOrganizationColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "sys_organization_sys_teams_organization",
				Columns:    []*schema.Column{SysOrganizationColumns[10]},
				RefColumns: []*schema.Column{SysTeamsColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
	}
	// SysPageCodesColumns holds the columns for the "sys_page_codes" table.
	SysPageCodesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "name", Type: field.TypeString, Unique: true, Comment: "名称"},
		{Name: "code", Type: field.TypeJSON, Comment: "代码"},
	}
	// SysPageCodesTable holds the schema information for the "sys_page_codes" table.
	SysPageCodesTable = &schema.Table{
		Name:       "sys_page_codes",
		Comment:    "页面代码",
		Columns:    SysPageCodesColumns,
		PrimaryKey: []*schema.Column{SysPageCodesColumns[0]},
	}
	// SysPageCodeHistoriesColumns holds the columns for the "sys_page_code_histories" table.
	SysPageCodeHistoriesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "name", Type: field.TypeString, Comment: "名称"},
		{Name: "code", Type: field.TypeJSON, Comment: "代码"},
	}
	// SysPageCodeHistoriesTable holds the schema information for the "sys_page_code_histories" table.
	SysPageCodeHistoriesTable = &schema.Table{
		Name:       "sys_page_code_histories",
		Comment:    "页面代码",
		Columns:    SysPageCodeHistoriesColumns,
		PrimaryKey: []*schema.Column{SysPageCodeHistoriesColumns[0]},
	}
	// SysProjectsColumns holds the columns for the "sys_projects" table.
	SysProjectsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "created_by", Type: field.TypeString, Nullable: true},
		{Name: "updated_by", Type: field.TypeString, Nullable: true},
		{Name: "sort", Type: field.TypeInt32, Nullable: true, Default: 0},
		{Name: "remark", Type: field.TypeString, Nullable: true},
		{Name: "name", Type: field.TypeString},
		{Name: "application_id", Type: field.TypeString},
		{Name: "team_id", Type: field.TypeString},
	}
	// SysProjectsTable holds the schema information for the "sys_projects" table.
	SysProjectsTable = &schema.Table{
		Name:       "sys_projects",
		Columns:    SysProjectsColumns,
		PrimaryKey: []*schema.Column{SysProjectsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "sys_projects_sys_applications_projects",
				Columns:    []*schema.Column{SysProjectsColumns[8]},
				RefColumns: []*schema.Column{SysApplicationsColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "sys_projects_sys_teams_projects",
				Columns:    []*schema.Column{SysProjectsColumns[9]},
				RefColumns: []*schema.Column{SysTeamsColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "sysproject_team_id_application_id_name",
				Unique:  true,
				Columns: []*schema.Column{SysProjectsColumns[9], SysProjectsColumns[8], SysProjectsColumns[7]},
			},
		},
	}
	// SysProvincesColumns holds the columns for the "sys_provinces" table.
	SysProvincesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "code", Type: field.TypeString, Comment: "编号"},
		{Name: "name", Type: field.TypeString, Comment: "名称"},
	}
	// SysProvincesTable holds the schema information for the "sys_provinces" table.
	SysProvincesTable = &schema.Table{
		Name:       "sys_provinces",
		Comment:    "省份表",
		Columns:    SysProvincesColumns,
		PrimaryKey: []*schema.Column{SysProvincesColumns[0]},
	}
	// SysResourcesColumns holds the columns for the "sys_resources" table.
	SysResourcesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "sort", Type: field.TypeInt32, Nullable: true, Comment: "排序", Default: 0},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "name", Type: field.TypeString, Comment: "资源名称"},
		{Name: "icon", Type: field.TypeString, Nullable: true, Comment: "图标(中文)"},
		{Name: "display_name", Type: field.TypeString, Nullable: true, Comment: "显示名称"},
		{Name: "code", Type: field.TypeString, Comment: "资源标识(中文)"},
		{Name: "type", Type: field.TypeEnum, Enums: []string{"FOLDER", "BUTTON", "API", "MENU"}},
		{Name: "depend_ids", Type: field.TypeJSON, Nullable: true, Comment: "依赖ID"},
		{Name: "method", Type: field.TypeEnum, Nullable: true, Comment: "请求方法(中文)", Enums: []string{"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "TRACE", "CONNECT"}},
		{Name: "path", Type: field.TypeString, Nullable: true, Comment: "路径(中文)"},
		{Name: "parent_id", Type: field.TypeString, Nullable: true, Comment: "父级ID"},
	}
	// SysResourcesTable holds the schema information for the "sys_resources" table.
	SysResourcesTable = &schema.Table{
		Name:       "sys_resources",
		Comment:    "资源表",
		Columns:    SysResourcesColumns,
		PrimaryKey: []*schema.Column{SysResourcesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "sys_resources_sys_resources_children",
				Columns:    []*schema.Column{SysResourcesColumns[16]},
				RefColumns: []*schema.Column{SysResourcesColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// SysRolesColumns holds the columns for the "sys_roles" table.
	SysRolesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "sort", Type: field.TypeInt32, Nullable: true, Comment: "排序", Default: 0},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "name", Type: field.TypeString, Comment: "角色名称"},
		{Name: "code", Type: field.TypeString, Nullable: true, Comment: "角色编码"},
		{Name: "role_resource_ids", Type: field.TypeString, Nullable: true, Comment: "角色资源ID集合"},
		{Name: "resource_scope", Type: field.TypeEnum, Comment: "数据范围", Enums: []string{"ALL", "SELF"}},
		{Name: "application_id", Type: field.TypeString, Comment: "应用ID"},
		{Name: "project_id", Type: field.TypeString, Nullable: true},
		{Name: "team_id", Type: field.TypeString, Comment: "团队ID"},
	}
	// SysRolesTable holds the schema information for the "sys_roles" table.
	SysRolesTable = &schema.Table{
		Name:       "sys_roles",
		Comment:    "角色表",
		Columns:    SysRolesColumns,
		PrimaryKey: []*schema.Column{SysRolesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "sys_roles_sys_applications_roles",
				Columns:    []*schema.Column{SysRolesColumns[11]},
				RefColumns: []*schema.Column{SysApplicationsColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "sys_roles_sys_projects_roles",
				Columns:    []*schema.Column{SysRolesColumns[12]},
				RefColumns: []*schema.Column{SysProjectsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "sys_roles_sys_teams_roles",
				Columns:    []*schema.Column{SysRolesColumns[13]},
				RefColumns: []*schema.Column{SysTeamsColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "sysrole_application_id_team_id_project_id_name",
				Unique:  true,
				Columns: []*schema.Column{SysRolesColumns[11], SysRolesColumns[13], SysRolesColumns[12], SysRolesColumns[7]},
			},
		},
	}
	// SysRoleMenusColumns holds the columns for the "sys_role_menus" table.
	SysRoleMenusColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "menu_id", Type: field.TypeString, Comment: "菜单ID"},
		{Name: "role_id", Type: field.TypeString, Comment: "角色ID"},
	}
	// SysRoleMenusTable holds the schema information for the "sys_role_menus" table.
	SysRoleMenusTable = &schema.Table{
		Name:       "sys_role_menus",
		Comment:    "角色菜单表",
		Columns:    SysRoleMenusColumns,
		PrimaryKey: []*schema.Column{SysRoleMenusColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "sys_role_menus_sys_menus_menu_roles",
				Columns:    []*schema.Column{SysRoleMenusColumns[5]},
				RefColumns: []*schema.Column{SysMenusColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "sys_role_menus_sys_roles_role_menus",
				Columns:    []*schema.Column{SysRoleMenusColumns[6]},
				RefColumns: []*schema.Column{SysRolesColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "sysrolemenu_role_id_menu_id",
				Unique:  true,
				Columns: []*schema.Column{SysRoleMenusColumns[6], SysRoleMenusColumns[5]},
			},
		},
	}
	// SysRoleResourcesColumns holds the columns for the "sys_role_resources" table.
	SysRoleResourcesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "resource_id", Type: field.TypeString, Comment: "资源ID"},
		{Name: "role_id", Type: field.TypeString, Comment: "角色ID"},
	}
	// SysRoleResourcesTable holds the schema information for the "sys_role_resources" table.
	SysRoleResourcesTable = &schema.Table{
		Name:       "sys_role_resources",
		Comment:    "角色资源表",
		Columns:    SysRoleResourcesColumns,
		PrimaryKey: []*schema.Column{SysRoleResourcesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "sys_role_resources_sys_resources_resource_roles",
				Columns:    []*schema.Column{SysRoleResourcesColumns[5]},
				RefColumns: []*schema.Column{SysResourcesColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "sys_role_resources_sys_roles_role_resources",
				Columns:    []*schema.Column{SysRoleResourcesColumns[6]},
				RefColumns: []*schema.Column{SysRolesColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "sysroleresource_role_id_resource_id",
				Unique:  true,
				Columns: []*schema.Column{SysRoleResourcesColumns[6], SysRoleResourcesColumns[5]},
			},
		},
	}
	// SysStreetsColumns holds the columns for the "sys_streets" table.
	SysStreetsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "code", Type: field.TypeString, Comment: "编号"},
		{Name: "name", Type: field.TypeString, Comment: "名称"},
		{Name: "province_code", Type: field.TypeString, Comment: "省份代码"},
		{Name: "city_code", Type: field.TypeString, Comment: "城市代码"},
		{Name: "area_code", Type: field.TypeString, Comment: "区县代码"},
	}
	// SysStreetsTable holds the schema information for the "sys_streets" table.
	SysStreetsTable = &schema.Table{
		Name:       "sys_streets",
		Comment:    "街道表",
		Columns:    SysStreetsColumns,
		PrimaryKey: []*schema.Column{SysStreetsColumns[0]},
	}
	// SysTeamsColumns holds the columns for the "sys_teams" table.
	SysTeamsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "name", Type: field.TypeString, Comment: "团队名称"},
		{Name: "description", Type: field.TypeString, Nullable: true, Comment: "团队描述"},
	}
	// SysTeamsTable holds the schema information for the "sys_teams" table.
	SysTeamsTable = &schema.Table{
		Name:       "sys_teams",
		Comment:    "团队表",
		Columns:    SysTeamsColumns,
		PrimaryKey: []*schema.Column{SysTeamsColumns[0]},
	}
	// SysTeamApplicationsColumns holds the columns for the "sys_team_applications" table.
	SysTeamApplicationsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "application_id", Type: field.TypeString, Comment: "产品ID"},
		{Name: "team_id", Type: field.TypeString, Comment: "团队ID"},
	}
	// SysTeamApplicationsTable holds the schema information for the "sys_team_applications" table.
	SysTeamApplicationsTable = &schema.Table{
		Name:       "sys_team_applications",
		Comment:    "团队产品表",
		Columns:    SysTeamApplicationsColumns,
		PrimaryKey: []*schema.Column{SysTeamApplicationsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "sys_team_applications_sys_applications_teams",
				Columns:    []*schema.Column{SysTeamApplicationsColumns[5]},
				RefColumns: []*schema.Column{SysApplicationsColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "sys_team_applications_sys_teams_applications",
				Columns:    []*schema.Column{SysTeamApplicationsColumns[6]},
				RefColumns: []*schema.Column{SysTeamsColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "systeamapplication_team_id_application_id",
				Unique:  true,
				Columns: []*schema.Column{SysTeamApplicationsColumns[6], SysTeamApplicationsColumns[5]},
			},
		},
	}
	// SysUploadsColumns holds the columns for the "sys_uploads" table.
	SysUploadsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "created_by", Type: field.TypeString, Nullable: true},
		{Name: "updated_by", Type: field.TypeString, Nullable: true},
		{Name: "filename", Type: field.TypeString},
		{Name: "fullname", Type: field.TypeString},
		{Name: "mime_type", Type: field.TypeString},
		{Name: "extension", Type: field.TypeString},
		{Name: "folder", Type: field.TypeString},
		{Name: "path", Type: field.TypeString},
		{Name: "size", Type: field.TypeInt64},
		{Name: "md5", Type: field.TypeString},
		{Name: "platform", Type: field.TypeString},
		{Name: "status", Type: field.TypeInt, Default: 1},
		{Name: "uploaded_size", Type: field.TypeInt64, Default: 0},
		{Name: "uploaded_at", Type: field.TypeTime, Nullable: true},
	}
	// SysUploadsTable holds the schema information for the "sys_uploads" table.
	SysUploadsTable = &schema.Table{
		Name:       "sys_uploads",
		Columns:    SysUploadsColumns,
		PrimaryKey: []*schema.Column{SysUploadsColumns[0]},
	}
	// SysUsersColumns holds the columns for the "sys_users" table.
	SysUsersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "created_by", Type: field.TypeString, Nullable: true},
		{Name: "updated_by", Type: field.TypeString, Nullable: true},
		{Name: "status", Type: field.TypeInt32, Default: 0},
		{Name: "username", Type: field.TypeString, Unique: true},
		{Name: "nickname", Type: field.TypeString, Nullable: true},
		{Name: "password", Type: field.TypeString, Nullable: true},
		{Name: "salt", Type: field.TypeString, Nullable: true},
		{Name: "parent_id", Type: field.TypeString, Nullable: true},
		{Name: "phone", Type: field.TypeString, Unique: true, Nullable: true},
		{Name: "avatar", Type: field.TypeString, Nullable: true},
		{Name: "gender", Type: field.TypeEnum, Enums: []string{"MALE", "FEMALE", "Unknown"}, Default: "Unknown"},
		{Name: "job_title", Type: field.TypeString, Default: ""},
		{Name: "allow_notify", Type: field.TypeBool, Default: true},
	}
	// SysUsersTable holds the schema information for the "sys_users" table.
	SysUsersTable = &schema.Table{
		Name:       "sys_users",
		Columns:    SysUsersColumns,
		PrimaryKey: []*schema.Column{SysUsersColumns[0]},
	}
	// SysUserAuthsColumns holds the columns for the "sys_user_auths" table.
	SysUserAuthsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "auth_type", Type: field.TypeEnum, Comment: "认证类型", Enums: []string{"password", "wechat", "work_wechat"}},
		{Name: "identifier", Type: field.TypeString, Comment: "标识符:如用户名、手机号、邮箱、微信openid、企业微信userid"},
		{Name: "credential", Type: field.TypeString, Comment: "凭证:如密码、微信access_token、企业微信access_token"},
		{Name: "user_id", Type: field.TypeString, Comment: "用户ID"},
	}
	// SysUserAuthsTable holds the schema information for the "sys_user_auths" table.
	SysUserAuthsTable = &schema.Table{
		Name:       "sys_user_auths",
		Comment:    "用户认证信息表",
		Columns:    SysUserAuthsColumns,
		PrimaryKey: []*schema.Column{SysUserAuthsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "sys_user_auths_sys_users_auths",
				Columns:    []*schema.Column{SysUserAuthsColumns[8]},
				RefColumns: []*schema.Column{SysUsersColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "sysuserauth_user_id_auth_type",
				Unique:  true,
				Columns: []*schema.Column{SysUserAuthsColumns[8], SysUserAuthsColumns[5]},
			},
		},
	}
	// SysUserLogsColumns holds the columns for the "sys_user_logs" table.
	SysUserLogsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "user_id", Type: field.TypeString, Nullable: true, Comment: "用户ID"},
		{Name: "username", Type: field.TypeString, Comment: "用户名"},
		{Name: "nickname", Type: field.TypeString, Nullable: true, Comment: "昵称"},
		{Name: "platform", Type: field.TypeString, Nullable: true, Comment: "平台"},
		{Name: "modules", Type: field.TypeString, Nullable: true, Comment: "模块"},
		{Name: "ip", Type: field.TypeString, Nullable: true, Comment: "IP"},
		{Name: "longitude", Type: field.TypeString, Nullable: true, Comment: "经度"},
		{Name: "latitude", Type: field.TypeString, Nullable: true, Comment: "纬度"},
		{Name: "behavior", Type: field.TypeString, Nullable: true, Comment: "行为: login"},
		{Name: "behavior_method", Type: field.TypeString, Nullable: true, Comment: "行为方式, 如登录的行为方式: 密码登录、企业微信登录"},
	}
	// SysUserLogsTable holds the schema information for the "sys_user_logs" table.
	SysUserLogsTable = &schema.Table{
		Name:       "sys_user_logs",
		Comment:    "用户日志",
		Columns:    SysUserLogsColumns,
		PrimaryKey: []*schema.Column{SysUserLogsColumns[0]},
	}
	// SysUserOrganizationColumns holds the columns for the "sys_user_organization" table.
	SysUserOrganizationColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "team_id", Type: field.TypeString, Comment: "团队id"},
		{Name: "organization_role", Type: field.TypeEnum, Comment: "用户在组织中的角色", Enums: []string{"ADMIN", "MEMBER"}, Default: "MEMBER"},
		{Name: "is_main", Type: field.TypeBool, Comment: "是否是主要组织", Default: false},
		{Name: "application_id", Type: field.TypeString, Comment: "应用ID"},
		{Name: "organization_id", Type: field.TypeString, Comment: "组织ID"},
		{Name: "user_id", Type: field.TypeString, Comment: "用户ID"},
	}
	// SysUserOrganizationTable holds the schema information for the "sys_user_organization" table.
	SysUserOrganizationTable = &schema.Table{
		Name:       "sys_user_organization",
		Comment:    "用户组织关联表",
		Columns:    SysUserOrganizationColumns,
		PrimaryKey: []*schema.Column{SysUserOrganizationColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "sys_user_organization_sys_applications_application_users",
				Columns:    []*schema.Column{SysUserOrganizationColumns[8]},
				RefColumns: []*schema.Column{SysApplicationsColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "sys_user_organization_sys_organization_users",
				Columns:    []*schema.Column{SysUserOrganizationColumns[9]},
				RefColumns: []*schema.Column{SysOrganizationColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "sys_user_organization_sys_users_organizations",
				Columns:    []*schema.Column{SysUserOrganizationColumns[10]},
				RefColumns: []*schema.Column{SysUsersColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "sysuserorganization_team_id_application_id_organization_id_user_id",
				Unique:  true,
				Columns: []*schema.Column{SysUserOrganizationColumns[5], SysUserOrganizationColumns[8], SysUserOrganizationColumns[9], SysUserOrganizationColumns[10]},
			},
		},
	}
	// SysUserRolesColumns holds the columns for the "sys_user_roles" table.
	SysUserRolesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "team_id", Type: field.TypeString, Comment: "团队id"},
		{Name: "application_id", Type: field.TypeString, Comment: "应用ID"},
		{Name: "role_id", Type: field.TypeString, Comment: "角色ID"},
		{Name: "user_id", Type: field.TypeString, Comment: "用户ID"},
	}
	// SysUserRolesTable holds the schema information for the "sys_user_roles" table.
	SysUserRolesTable = &schema.Table{
		Name:       "sys_user_roles",
		Comment:    "用户角色关联表",
		Columns:    SysUserRolesColumns,
		PrimaryKey: []*schema.Column{SysUserRolesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "sys_user_roles_sys_roles_role_users",
				Columns:    []*schema.Column{SysUserRolesColumns[7]},
				RefColumns: []*schema.Column{SysRolesColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "sys_user_roles_sys_users_user_roles",
				Columns:    []*schema.Column{SysUserRolesColumns[8]},
				RefColumns: []*schema.Column{SysUsersColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "sysuserrole_role_id_user_id_team_id_application_id",
				Unique:  true,
				Columns: []*schema.Column{SysUserRolesColumns[7], SysUserRolesColumns[8], SysUserRolesColumns[5], SysUserRolesColumns[6]},
			},
		},
	}
	// WmsAccessDoorLogsColumns holds the columns for the "wms_access_door_logs" table.
	WmsAccessDoorLogsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "behavior", Type: field.TypeString, Nullable: true, Comment: "行为"},
		{Name: "status", Type: field.TypeString, Nullable: true, Comment: "状态"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "仓库"},
		{Name: "material_id", Type: field.TypeString, Nullable: true, Comment: "物料"},
	}
	// WmsAccessDoorLogsTable holds the schema information for the "wms_access_door_logs" table.
	WmsAccessDoorLogsTable = &schema.Table{
		Name:       "wms_access_door_logs",
		Comment:    "通道门记录",
		Columns:    WmsAccessDoorLogsColumns,
		PrimaryKey: []*schema.Column{WmsAccessDoorLogsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_access_door_logs_wms_repositories_repository",
				Columns:    []*schema.Column{WmsAccessDoorLogsColumns[8]},
				RefColumns: []*schema.Column{WmsRepositoriesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_access_door_logs_wms_materials_material",
				Columns:    []*schema.Column{WmsAccessDoorLogsColumns[9]},
				RefColumns: []*schema.Column{WmsMaterialsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsApprovalTasksColumns holds the columns for the "wms_approval_tasks" table.
	WmsApprovalTasksColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "contract_urls", Type: field.TypeString, Nullable: true, Comment: "合同附件"},
		{Name: "invoice_urls", Type: field.TypeString, Nullable: true, Comment: "发票附件"},
		{Name: "audit_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "other_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "name", Type: field.TypeString, Comment: "审批名称"},
		{Name: "order_no", Type: field.TypeString, Comment: "审批单号"},
		{Name: "source", Type: field.TypeString, Nullable: true, Comment: "来源"},
		{Name: "source_id", Type: field.TypeString, Nullable: true, Comment: "来源id"},
		{Name: "from_repository_id", Type: field.TypeString, Nullable: true, Comment: "入库前仓库"},
		{Name: "to_repository_id", Type: field.TypeString, Nullable: true, Comment: "入库后仓库"},
		{Name: "workflow_basci_code", Type: field.TypeString, Nullable: true, Comment: "工作流编码"},
		{Name: "workflow_id", Type: field.TypeString, Nullable: true, Comment: "工作流ID"},
		{Name: "reason", Type: field.TypeString, Nullable: true, Comment: "申请原因"},
		{Name: "app_approve_record_task", Type: field.TypeString, Unique: true, Nullable: true},
		{Name: "fire_station_id", Type: field.TypeString, Nullable: true, Comment: "消防站"},
	}
	// WmsApprovalTasksTable holds the schema information for the "wms_approval_tasks" table.
	WmsApprovalTasksTable = &schema.Table{
		Name:       "wms_approval_tasks",
		Comment:    "审批任务",
		Columns:    WmsApprovalTasksColumns,
		PrimaryKey: []*schema.Column{WmsApprovalTasksColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_approval_tasks_app_approve_records_task",
				Columns:    []*schema.Column{WmsApprovalTasksColumns[20]},
				RefColumns: []*schema.Column{AppApproveRecordsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_approval_tasks_wms_fire_stations_fire_station",
				Columns:    []*schema.Column{WmsApprovalTasksColumns[21]},
				RefColumns: []*schema.Column{WmsFireStationsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsApprovalTaskDetailsColumns holds the columns for the "wms_approval_task_details" table.
	WmsApprovalTaskDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "equipment_name", Type: field.TypeString, Nullable: true, Comment: "设备名称"},
		{Name: "material_name", Type: field.TypeString, Nullable: true, Comment: "资产名称"},
		{Name: "code", Type: field.TypeString, Nullable: true, Comment: "编号"},
		{Name: "quantity", Type: field.TypeInt32, Nullable: true, Comment: "数量"},
		{Name: "reason", Type: field.TypeString, Nullable: true, Comment: "原因"},
		{Name: "operation_date", Type: field.TypeTime, Nullable: true, Comment: "操作日期"},
		{Name: "description", Type: field.TypeString, Nullable: true, Comment: "补充说明"},
		{Name: "images", Type: field.TypeString, Nullable: true, Comment: "图片"},
		{Name: "source", Type: field.TypeString, Nullable: true, Comment: "来源"},
		{Name: "source_id", Type: field.TypeString, Nullable: true, Comment: "来源id"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "仓库ID"},
		{Name: "feature", Type: field.TypeJSON, Nullable: true, Comment: "详细规格"},
		{Name: "operation_type", Type: field.TypeString, Nullable: true, Comment: "操作类型"},
		{Name: "approve_task_id", Type: field.TypeString, Nullable: true, Comment: "审批任务 ID"},
		{Name: "equipment_id", Type: field.TypeString, Nullable: true, Comment: "设备"},
		{Name: "material_id", Type: field.TypeString, Nullable: true, Comment: "资产 ID"},
	}
	// WmsApprovalTaskDetailsTable holds the schema information for the "wms_approval_task_details" table.
	WmsApprovalTaskDetailsTable = &schema.Table{
		Name:       "wms_approval_task_details",
		Comment:    "维修单明细",
		Columns:    WmsApprovalTaskDetailsColumns,
		PrimaryKey: []*schema.Column{WmsApprovalTaskDetailsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_approval_task_details_wms_approval_tasks_details",
				Columns:    []*schema.Column{WmsApprovalTaskDetailsColumns[20]},
				RefColumns: []*schema.Column{WmsApprovalTasksColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_approval_task_details_wms_equipments_equipment",
				Columns:    []*schema.Column{WmsApprovalTaskDetailsColumns[21]},
				RefColumns: []*schema.Column{WmsEquipmentsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_approval_task_details_wms_materials_material",
				Columns:    []*schema.Column{WmsApprovalTaskDetailsColumns[22]},
				RefColumns: []*schema.Column{WmsMaterialsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsApprovalTaskOperationsColumns holds the columns for the "wms_approval_task_operations" table.
	WmsApprovalTaskOperationsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "operation_time", Type: field.TypeTime, Comment: "操作时间"},
		{Name: "completion_time", Type: field.TypeTime, Nullable: true, Comment: "完成时间"},
		{Name: "description", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "说明"},
		{Name: "attachments", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "附件"},
		{Name: "wms_approval_task_detail_operations", Type: field.TypeString},
	}
	// WmsApprovalTaskOperationsTable holds the schema information for the "wms_approval_task_operations" table.
	WmsApprovalTaskOperationsTable = &schema.Table{
		Name:       "wms_approval_task_operations",
		Comment:    "审批任务操作表",
		Columns:    WmsApprovalTaskOperationsColumns,
		PrimaryKey: []*schema.Column{WmsApprovalTaskOperationsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_approval_task_operations_wms_approval_task_details_operations",
				Columns:    []*schema.Column{WmsApprovalTaskOperationsColumns[10]},
				RefColumns: []*schema.Column{WmsApprovalTaskDetailsColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
	}
	// WmsApprovalTaskRemarksColumns holds the columns for the "wms_approval_task_remarks" table.
	WmsApprovalTaskRemarksColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "approval_task_id", Type: field.TypeString, Comment: "审批任务"},
	}
	// WmsApprovalTaskRemarksTable holds the schema information for the "wms_approval_task_remarks" table.
	WmsApprovalTaskRemarksTable = &schema.Table{
		Name:       "wms_approval_task_remarks",
		Comment:    "审批任务补充说明",
		Columns:    WmsApprovalTaskRemarksColumns,
		PrimaryKey: []*schema.Column{WmsApprovalTaskRemarksColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_approval_task_remarks_wms_approval_tasks_remarks",
				Columns:    []*schema.Column{WmsApprovalTaskRemarksColumns[6]},
				RefColumns: []*schema.Column{WmsApprovalTasksColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
	}
	// WmsAuditPlansColumns holds the columns for the "wms_audit_plans" table.
	WmsAuditPlansColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "name", Type: field.TypeString, Comment: "计划名称"},
		{Name: "owner_id", Type: field.TypeString, Nullable: true, Comment: "盘点负责人"},
		{Name: "plan_start_time", Type: field.TypeTime, Nullable: true, Comment: "计划开始时间"},
		{Name: "plan_end_time", Type: field.TypeTime, Nullable: true, Comment: "计划结束时间"},
		{Name: "start_time", Type: field.TypeTime, Nullable: true, Comment: "开始时间"},
		{Name: "end_time", Type: field.TypeTime, Nullable: true, Comment: "结束时间"},
		{Name: "repository_ids", Type: field.TypeString, Nullable: true, Comment: "盘点仓库"},
		{Name: "equipment_type_ids", Type: field.TypeString, Nullable: true, Comment: "盘点装备分类"},
		{Name: "user_ids", Type: field.TypeString, Nullable: true, Comment: "盘点人员领用装备"},
		{Name: "organization_ids", Type: field.TypeString, Nullable: true, Comment: "盘点部门领用装备"},
	}
	// WmsAuditPlansTable holds the schema information for the "wms_audit_plans" table.
	WmsAuditPlansTable = &schema.Table{
		Name:       "wms_audit_plans",
		Comment:    "装备盘点计划表",
		Columns:    WmsAuditPlansColumns,
		PrimaryKey: []*schema.Column{WmsAuditPlansColumns[0]},
	}
	// WmsAuditPlanDetailsColumns holds the columns for the "wms_audit_plan_details" table.
	WmsAuditPlanDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "scope", Type: field.TypeString, Comment: "盘点范围"},
		{Name: "state", Type: field.TypeEnum, Comment: "使用状态", Enums: []string{"InUse", "InStock"}},
		{Name: "is_audit", Type: field.TypeBool, Comment: "是否已盘点", Default: false},
		{Name: "is_reader", Type: field.TypeBool, Comment: "是否来自RFID读取", Default: false},
		{Name: "source", Type: field.TypeString, Comment: "数据来源"},
		{Name: "material_type", Type: field.TypeString, Comment: "装备类型"},
		{Name: "code", Type: field.TypeString, Nullable: true, Comment: "编码"},
		{Name: "code_type", Type: field.TypeString, Comment: "编码类型"},
		{Name: "name", Type: field.TypeString, Comment: "装备名称"},
		{Name: "model_no", Type: field.TypeString, Comment: "规格型号"},
		{Name: "provider_id", Type: field.TypeString, Comment: "供应商"},
		{Name: "num", Type: field.TypeUint32, Comment: "数量", Default: 0},
		{Name: "price", Type: field.TypeUint64, Comment: "单价", Default: 0},
		{Name: "expire_time", Type: field.TypeTime, Nullable: true, Comment: "过期日期"},
		{Name: "equipment_status", Type: field.TypeInt32, Nullable: true, Comment: "装备状态", Default: 1},
		{Name: "feature", Type: field.TypeJSON, Nullable: true, Comment: "详细规格"},
		{Name: "audit_plan_id", Type: field.TypeString, Comment: "盘点计划ID"},
		{Name: "equipment_id", Type: field.TypeString, Nullable: true, Comment: "装备"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "仓库"},
		{Name: "repository_area_id", Type: field.TypeString, Nullable: true, Comment: "库区"},
		{Name: "repository_position_id", Type: field.TypeString, Nullable: true, Comment: "库位"},
		{Name: "equipment_type_id", Type: field.TypeString, Nullable: true, Comment: "装备分类"},
		{Name: "fire_station_id", Type: field.TypeString, Nullable: true, Comment: "消防站"},
		{Name: "measure_unit_id", Type: field.TypeString, Nullable: true, Comment: "计量单位"},
		{Name: "owner_id", Type: field.TypeString, Nullable: true, Comment: "持有人"},
		{Name: "material_id", Type: field.TypeString, Comment: "资产ID"},
	}
	// WmsAuditPlanDetailsTable holds the schema information for the "wms_audit_plan_details" table.
	WmsAuditPlanDetailsTable = &schema.Table{
		Name:       "wms_audit_plan_details",
		Comment:    "装备盘点计划明细表",
		Columns:    WmsAuditPlanDetailsColumns,
		PrimaryKey: []*schema.Column{WmsAuditPlanDetailsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_audit_plan_details_wms_audit_plans_details",
				Columns:    []*schema.Column{WmsAuditPlanDetailsColumns[23]},
				RefColumns: []*schema.Column{WmsAuditPlansColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "wms_audit_plan_details_wms_equipments_equipment",
				Columns:    []*schema.Column{WmsAuditPlanDetailsColumns[24]},
				RefColumns: []*schema.Column{WmsEquipmentsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_audit_plan_details_wms_repositories_repository",
				Columns:    []*schema.Column{WmsAuditPlanDetailsColumns[25]},
				RefColumns: []*schema.Column{WmsRepositoriesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_audit_plan_details_wms_repository_areas_repository_area",
				Columns:    []*schema.Column{WmsAuditPlanDetailsColumns[26]},
				RefColumns: []*schema.Column{WmsRepositoryAreasColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_audit_plan_details_wms_repository_positions_repository_position",
				Columns:    []*schema.Column{WmsAuditPlanDetailsColumns[27]},
				RefColumns: []*schema.Column{WmsRepositoryPositionsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_audit_plan_details_wms_equipment_types_equipment_type",
				Columns:    []*schema.Column{WmsAuditPlanDetailsColumns[28]},
				RefColumns: []*schema.Column{WmsEquipmentTypesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_audit_plan_details_wms_fire_stations_fire_station",
				Columns:    []*schema.Column{WmsAuditPlanDetailsColumns[29]},
				RefColumns: []*schema.Column{WmsFireStationsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_audit_plan_details_wms_measure_units_measure_unit",
				Columns:    []*schema.Column{WmsAuditPlanDetailsColumns[30]},
				RefColumns: []*schema.Column{WmsMeasureUnitsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_audit_plan_details_sys_users_owner",
				Columns:    []*schema.Column{WmsAuditPlanDetailsColumns[31]},
				RefColumns: []*schema.Column{SysUsersColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_audit_plan_details_wms_materials_audit_plan_details",
				Columns:    []*schema.Column{WmsAuditPlanDetailsColumns[32]},
				RefColumns: []*schema.Column{WmsMaterialsColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "wmsauditplandetail_audit_plan_id_material_id",
				Unique:  true,
				Columns: []*schema.Column{WmsAuditPlanDetailsColumns[23], WmsAuditPlanDetailsColumns[32]},
			},
		},
	}
	// WmsBorrowOrdersColumns holds the columns for the "wms_borrow_orders" table.
	WmsBorrowOrdersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "contract_urls", Type: field.TypeString, Nullable: true, Comment: "合同附件"},
		{Name: "invoice_urls", Type: field.TypeString, Nullable: true, Comment: "发票附件"},
		{Name: "audit_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "other_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "order_no", Type: field.TypeString, Comment: "借用单号"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "仓库"},
		{Name: "borrow_time", Type: field.TypeTime, Comment: "借用时间"},
		{Name: "expect_return_time", Type: field.TypeTime, Comment: "预期归还时间"},
		{Name: "borrower_id", Type: field.TypeString, Comment: "借用人"},
		{Name: "status", Type: field.TypeString, Comment: "借用状态"},
		{Name: "equipment_num", Type: field.TypeUint32, Nullable: true, Comment: "设备数量"},
		{Name: "reason", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "事由"},
	}
	// WmsBorrowOrdersTable holds the schema information for the "wms_borrow_orders" table.
	WmsBorrowOrdersTable = &schema.Table{
		Name:       "wms_borrow_orders",
		Comment:    "借用单",
		Columns:    WmsBorrowOrdersColumns,
		PrimaryKey: []*schema.Column{WmsBorrowOrdersColumns[0]},
	}
	// WmsBorrowOrderDetailsColumns holds the columns for the "wms_borrow_order_details" table.
	WmsBorrowOrderDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "order_id", Type: field.TypeString, Nullable: true, Comment: "借用单"},
		{Name: "equipment_type_id", Type: field.TypeString, Nullable: true, Comment: "设备类型"},
		{Name: "equipment_id", Type: field.TypeString, Nullable: true, Comment: "设备"},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "设备名称"},
		{Name: "model_no", Type: field.TypeString, Comment: "规格型号"},
		{Name: "measure_unit_id", Type: field.TypeString, Nullable: true, Comment: "计量单位"},
		{Name: "num", Type: field.TypeUint32, Comment: "数量"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "仓库"},
		{Name: "return_time", Type: field.TypeTime, Nullable: true, Comment: "归还时间"},
		{Name: "is_return", Type: field.TypeBool, Comment: "是否归还", Default: false},
		{Name: "feature", Type: field.TypeJSON, Nullable: true, Comment: "详细规格"},
	}
	// WmsBorrowOrderDetailsTable holds the schema information for the "wms_borrow_order_details" table.
	WmsBorrowOrderDetailsTable = &schema.Table{
		Name:       "wms_borrow_order_details",
		Comment:    "借用单明细",
		Columns:    WmsBorrowOrderDetailsColumns,
		PrimaryKey: []*schema.Column{WmsBorrowOrderDetailsColumns[0]},
	}
	// WmsCarsColumns holds the columns for the "wms_cars" table.
	WmsCarsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "license_plate", Type: field.TypeString, Nullable: true, Comment: "车牌号码"},
		{Name: "color", Type: field.TypeString, Comment: "车辆颜色", Default: "未知"},
		{Name: "vin", Type: field.TypeString, Nullable: true, Comment: "车架号"},
		{Name: "brand", Type: field.TypeString, Nullable: true, Comment: "车辆品牌"},
		{Name: "engine_number", Type: field.TypeString, Nullable: true, Comment: "发动机号"},
		{Name: "manufacture_date", Type: field.TypeTime, Nullable: true, Comment: "出厂日期"},
		{Name: "purchase_date", Type: field.TypeTime, Nullable: true, Comment: "采购日期"},
		{Name: "images", Type: field.TypeString, Nullable: true, Comment: "车辆图片"},
		{Name: "organization_id", Type: field.TypeString, Nullable: true, Comment: "部门 ID"},
		{Name: "wms_material_wms_car", Type: field.TypeString, Unique: true},
	}
	// WmsCarsTable holds the schema information for the "wms_cars" table.
	WmsCarsTable = &schema.Table{
		Name:       "wms_cars",
		Comment:    "车辆管理表",
		Columns:    WmsCarsColumns,
		PrimaryKey: []*schema.Column{WmsCarsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_cars_sys_organization_organization",
				Columns:    []*schema.Column{WmsCarsColumns[14]},
				RefColumns: []*schema.Column{SysOrganizationColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_cars_wms_materials_wms_car",
				Columns:    []*schema.Column{WmsCarsColumns[15]},
				RefColumns: []*schema.Column{WmsMaterialsColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
	}
	// WmsClaimOrdersColumns holds the columns for the "wms_claim_orders" table.
	WmsClaimOrdersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "contract_urls", Type: field.TypeString, Nullable: true, Comment: "合同附件"},
		{Name: "invoice_urls", Type: field.TypeString, Nullable: true, Comment: "发票附件"},
		{Name: "audit_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "other_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "order_no", Type: field.TypeString, Comment: "领用单号"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "仓库"},
		{Name: "claim_time", Type: field.TypeTime, Comment: "领用时间"},
		{Name: "claim_user_id", Type: field.TypeString, Comment: "领用人"},
		{Name: "status", Type: field.TypeString, Comment: "领用状态"},
		{Name: "equipment_num", Type: field.TypeUint32, Nullable: true, Comment: "设备数量"},
		{Name: "reason", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "事由"},
	}
	// WmsClaimOrdersTable holds the schema information for the "wms_claim_orders" table.
	WmsClaimOrdersTable = &schema.Table{
		Name:       "wms_claim_orders",
		Comment:    "借用单",
		Columns:    WmsClaimOrdersColumns,
		PrimaryKey: []*schema.Column{WmsClaimOrdersColumns[0]},
	}
	// WmsClaimOrderDetailsColumns holds the columns for the "wms_claim_order_details" table.
	WmsClaimOrderDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "order_id", Type: field.TypeString, Nullable: true, Comment: "领用单"},
		{Name: "equipment_type_id", Type: field.TypeString, Nullable: true, Comment: "设备类型"},
		{Name: "equipment_id", Type: field.TypeString, Nullable: true, Comment: "设备"},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "设备名称"},
		{Name: "model_no", Type: field.TypeString, Comment: "规格型号"},
		{Name: "measure_unit_id", Type: field.TypeString, Nullable: true, Comment: "计量单位"},
		{Name: "num", Type: field.TypeUint32, Comment: "数量"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "仓库"},
		{Name: "feature", Type: field.TypeJSON, Nullable: true, Comment: "详细规格"},
	}
	// WmsClaimOrderDetailsTable holds the schema information for the "wms_claim_order_details" table.
	WmsClaimOrderDetailsTable = &schema.Table{
		Name:       "wms_claim_order_details",
		Comment:    "领用单明细",
		Columns:    WmsClaimOrderDetailsColumns,
		PrimaryKey: []*schema.Column{WmsClaimOrderDetailsColumns[0]},
	}
	// WmsCompaniesColumns holds the columns for the "wms_companies" table.
	WmsCompaniesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "area_code", Type: field.TypeString, Nullable: true, Comment: "区域编码"},
		{Name: "area_name", Type: field.TypeString, Nullable: true, Comment: "区域名称"},
		{Name: "address", Type: field.TypeString, Nullable: true, Comment: "详细地址"},
		{Name: "name", Type: field.TypeString, Comment: "单位名称"},
		{Name: "alias", Type: field.TypeString, Nullable: true, Comment: "单位简称"},
		{Name: "unified_social_credit_code", Type: field.TypeString, Comment: "统一社会信用代码"},
		{Name: "legal_person", Type: field.TypeString, Comment: "法人"},
		{Name: "legal_person_phone", Type: field.TypeString, Comment: "法人电话"},
		{Name: "legal_person_idcard", Type: field.TypeString, Nullable: true, Comment: "法人身份证号"},
		{Name: "business_status", Type: field.TypeString, Comment: "经营状态"},
		{Name: "business_nature", Type: field.TypeString, Comment: "经营性质"},
		{Name: "business_description", Type: field.TypeString, Nullable: true, Comment: "经营描述"},
		{Name: "business_license", Type: field.TypeString, Comment: "营业执照"},
		{Name: "business_license_images", Type: field.TypeString, Comment: "营业执照图片"},
		{Name: "business_license_register_date", Type: field.TypeTime, Comment: "营业执照注册日期"},
		{Name: "business_license_register_province_code", Type: field.TypeString, Comment: "营业执照注册省份代码"},
		{Name: "business_license_register_city_code", Type: field.TypeString, Comment: "营业执照注册城市代码"},
		{Name: "business_license_register_area_code", Type: field.TypeString, Comment: "营业执照注册区域代码"},
		{Name: "business_license_register_street_code", Type: field.TypeString, Comment: "营业执照注册街道代码"},
		{Name: "business_license_register_address", Type: field.TypeString, Comment: "营业执照注册地址"},
		{Name: "supervision_level", Type: field.TypeString, Nullable: true, Comment: "监管级别"},
		{Name: "email", Type: field.TypeString, Nullable: true, Comment: "电子邮箱"},
		{Name: "gis_x", Type: field.TypeString, Nullable: true, Comment: "经度"},
		{Name: "gis_y", Type: field.TypeString, Nullable: true, Comment: "纬度"},
		{Name: "contact_person", Type: field.TypeString, Comment: "联系人"},
		{Name: "contact_person_phone", Type: field.TypeString, Comment: "联系人电话"},
		{Name: "contact_person_idcard", Type: field.TypeString, Comment: "联系人身份证号"},
		{Name: "other_image", Type: field.TypeString, Nullable: true, Comment: "其他图片"},
	}
	// WmsCompaniesTable holds the schema information for the "wms_companies" table.
	WmsCompaniesTable = &schema.Table{
		Name:       "wms_companies",
		Comment:    "公司表",
		Columns:    WmsCompaniesColumns,
		PrimaryKey: []*schema.Column{WmsCompaniesColumns[0]},
	}
	// WmsCompanyAddressesColumns holds the columns for the "wms_company_addresses" table.
	WmsCompanyAddressesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "area_code", Type: field.TypeString, Nullable: true, Comment: "区域编码"},
		{Name: "area_name", Type: field.TypeString, Nullable: true, Comment: "区域名称"},
		{Name: "address", Type: field.TypeString, Nullable: true, Comment: "详细地址"},
		{Name: "company_id", Type: field.TypeString, Nullable: true, Comment: "公司"},
	}
	// WmsCompanyAddressesTable holds the schema information for the "wms_company_addresses" table.
	WmsCompanyAddressesTable = &schema.Table{
		Name:       "wms_company_addresses",
		Comment:    "公司地址表",
		Columns:    WmsCompanyAddressesColumns,
		PrimaryKey: []*schema.Column{WmsCompanyAddressesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_company_addresses_wms_companies_addresses",
				Columns:    []*schema.Column{WmsCompanyAddressesColumns[8]},
				RefColumns: []*schema.Column{WmsCompaniesColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsDiscardMeetingsColumns holds the columns for the "wms_discard_meetings" table.
	WmsDiscardMeetingsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "contract_urls", Type: field.TypeString, Nullable: true, Comment: "合同附件"},
		{Name: "invoice_urls", Type: field.TypeString, Nullable: true, Comment: "发票附件"},
		{Name: "audit_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "other_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "order_no", Type: field.TypeString, Comment: "上会单号"},
		{Name: "meeting_time", Type: field.TypeTime, Comment: "上会时间"},
		{Name: "equipment_type_num", Type: field.TypeUint32, Nullable: true, Comment: "设备类型数量"},
		{Name: "equipment_num", Type: field.TypeUint32, Nullable: true, Comment: "设备数量"},
	}
	// WmsDiscardMeetingsTable holds the schema information for the "wms_discard_meetings" table.
	WmsDiscardMeetingsTable = &schema.Table{
		Name:       "wms_discard_meetings",
		Comment:    "固资上会",
		Columns:    WmsDiscardMeetingsColumns,
		PrimaryKey: []*schema.Column{WmsDiscardMeetingsColumns[0]},
	}
	// WmsDiscardMeetingDetailsColumns holds the columns for the "wms_discard_meeting_details" table.
	WmsDiscardMeetingDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "material_id", Type: field.TypeString, Nullable: true, Comment: "资产ID"},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "名称"},
		{Name: "code", Type: field.TypeString, Comment: "编码"},
		{Name: "model_no", Type: field.TypeString, Comment: "规格型号"},
		{Name: "num", Type: field.TypeUint32, Comment: "报废数量"},
		{Name: "price", Type: field.TypeUint64, Comment: "单价"},
		{Name: "reason", Type: field.TypeString, Comment: "报废原因"},
		{Name: "expired_time", Type: field.TypeTime, Comment: "过期时间"},
		{Name: "enter_repository_time", Type: field.TypeTime, Comment: "入库时间"},
		{Name: "source", Type: field.TypeString, Nullable: true, Comment: "来源"},
		{Name: "order_no", Type: field.TypeString, Nullable: true, Comment: "上会单号"},
		{Name: "feature", Type: field.TypeJSON, Nullable: true, Comment: "详细规格"},
		{Name: "discard_meeting_id", Type: field.TypeString, Nullable: true, Comment: "固资上会单"},
		{Name: "equipment_type_id", Type: field.TypeString, Nullable: true, Comment: "设备类型"},
		{Name: "equipment_id", Type: field.TypeString, Nullable: true, Comment: "设备"},
		{Name: "measure_unit_id", Type: field.TypeString, Nullable: true, Comment: "计量单位"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "所在仓库"},
		{Name: "repository_area_id", Type: field.TypeString, Nullable: true, Comment: "所在库区"},
		{Name: "repository_position_id", Type: field.TypeString, Nullable: true, Comment: "所在库位"},
	}
	// WmsDiscardMeetingDetailsTable holds the schema information for the "wms_discard_meeting_details" table.
	WmsDiscardMeetingDetailsTable = &schema.Table{
		Name:       "wms_discard_meeting_details",
		Comment:    "固资上会明细",
		Columns:    WmsDiscardMeetingDetailsColumns,
		PrimaryKey: []*schema.Column{WmsDiscardMeetingDetailsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_discard_meeting_details_wms_discard_meetings_details",
				Columns:    []*schema.Column{WmsDiscardMeetingDetailsColumns[18]},
				RefColumns: []*schema.Column{WmsDiscardMeetingsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_discard_meeting_details_wms_equipment_types_equipment_type",
				Columns:    []*schema.Column{WmsDiscardMeetingDetailsColumns[19]},
				RefColumns: []*schema.Column{WmsEquipmentTypesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_discard_meeting_details_wms_equipments_equipment",
				Columns:    []*schema.Column{WmsDiscardMeetingDetailsColumns[20]},
				RefColumns: []*schema.Column{WmsEquipmentsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_discard_meeting_details_wms_measure_units_measure_unit",
				Columns:    []*schema.Column{WmsDiscardMeetingDetailsColumns[21]},
				RefColumns: []*schema.Column{WmsMeasureUnitsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_discard_meeting_details_wms_repositories_repository",
				Columns:    []*schema.Column{WmsDiscardMeetingDetailsColumns[22]},
				RefColumns: []*schema.Column{WmsRepositoriesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_discard_meeting_details_wms_repository_areas_repository_area",
				Columns:    []*schema.Column{WmsDiscardMeetingDetailsColumns[23]},
				RefColumns: []*schema.Column{WmsRepositoryAreasColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_discard_meeting_details_wms_repository_positions_repository_position",
				Columns:    []*schema.Column{WmsDiscardMeetingDetailsColumns[24]},
				RefColumns: []*schema.Column{WmsRepositoryPositionsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsDiscardOrdersColumns holds the columns for the "wms_discard_orders" table.
	WmsDiscardOrdersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "contract_urls", Type: field.TypeString, Nullable: true, Comment: "合同附件"},
		{Name: "invoice_urls", Type: field.TypeString, Nullable: true, Comment: "发票附件"},
		{Name: "audit_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "other_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "order_no", Type: field.TypeString, Comment: "报废单号"},
		{Name: "status", Type: field.TypeString, Comment: "报废状态"},
		{Name: "equipment_num", Type: field.TypeUint32, Nullable: true, Comment: "设备数量"},
	}
	// WmsDiscardOrdersTable holds the schema information for the "wms_discard_orders" table.
	WmsDiscardOrdersTable = &schema.Table{
		Name:       "wms_discard_orders",
		Comment:    "报废单",
		Columns:    WmsDiscardOrdersColumns,
		PrimaryKey: []*schema.Column{WmsDiscardOrdersColumns[0]},
	}
	// WmsDiscardOrderDetailsColumns holds the columns for the "wms_discard_order_details" table.
	WmsDiscardOrderDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "order_id", Type: field.TypeString, Nullable: true, Comment: "报废单"},
		{Name: "material_id", Type: field.TypeString, Nullable: true, Comment: "物料"},
		{Name: "material_name", Type: field.TypeString, Nullable: true, Comment: "物料名称"},
		{Name: "code", Type: field.TypeString, Comment: "编码"},
		{Name: "equipment_id", Type: field.TypeString, Nullable: true, Comment: "装备"},
		{Name: "equipment_type_id", Type: field.TypeString, Nullable: true, Comment: "装备类型"},
		{Name: "feature", Type: field.TypeJSON, Nullable: true, Comment: "详细规格"},
		{Name: "owner_id", Type: field.TypeString, Nullable: true, Comment: "归属人"},
		{Name: "model_no", Type: field.TypeString, Comment: "规格型号"},
		{Name: "measure_unit_id", Type: field.TypeString, Nullable: true, Comment: "计量单位"},
		{Name: "num", Type: field.TypeUint32, Comment: "报废数量"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "仓库"},
		{Name: "repository_area_id", Type: field.TypeString, Nullable: true, Comment: "库区"},
		{Name: "repository_position_id", Type: field.TypeString, Nullable: true, Comment: "库位"},
		{Name: "reason", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "报废原因"},
		{Name: "discard_time", Type: field.TypeTime, Nullable: true, Comment: "报废时间"},
	}
	// WmsDiscardOrderDetailsTable holds the schema information for the "wms_discard_order_details" table.
	WmsDiscardOrderDetailsTable = &schema.Table{
		Name:       "wms_discard_order_details",
		Comment:    "报废单明细",
		Columns:    WmsDiscardOrderDetailsColumns,
		PrimaryKey: []*schema.Column{WmsDiscardOrderDetailsColumns[0]},
	}
	// WmsDiscardPlanOrdersColumns holds the columns for the "wms_discard_plan_orders" table.
	WmsDiscardPlanOrdersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "contract_urls", Type: field.TypeString, Nullable: true, Comment: "合同附件"},
		{Name: "invoice_urls", Type: field.TypeString, Nullable: true, Comment: "发票附件"},
		{Name: "audit_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "other_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "order_no", Type: field.TypeString, Comment: "报废计划单号"},
		{Name: "name", Type: field.TypeString, Comment: "计划名称"},
		{Name: "type", Type: field.TypeString, Comment: "报废类型"},
		{Name: "plan_time", Type: field.TypeTime, Comment: "计划报废时间"},
		{Name: "equipment_type_num", Type: field.TypeUint32, Nullable: true, Comment: "设备类型数量"},
		{Name: "equipment_num", Type: field.TypeUint32, Nullable: true, Comment: "设备数量"},
		{Name: "fire_station_id", Type: field.TypeString, Nullable: true, Comment: "消防站"},
	}
	// WmsDiscardPlanOrdersTable holds the schema information for the "wms_discard_plan_orders" table.
	WmsDiscardPlanOrdersTable = &schema.Table{
		Name:       "wms_discard_plan_orders",
		Comment:    "报废计划",
		Columns:    WmsDiscardPlanOrdersColumns,
		PrimaryKey: []*schema.Column{WmsDiscardPlanOrdersColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_discard_plan_orders_wms_fire_stations_fire_station",
				Columns:    []*schema.Column{WmsDiscardPlanOrdersColumns[16]},
				RefColumns: []*schema.Column{WmsFireStationsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsDiscardPlanOrderDetailsColumns holds the columns for the "wms_discard_plan_order_details" table.
	WmsDiscardPlanOrderDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "material_id", Type: field.TypeString, Nullable: true, Comment: "资产ID"},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "设备名称"},
		{Name: "code", Type: field.TypeString, Comment: "编码"},
		{Name: "model_no", Type: field.TypeString, Comment: "规格型号"},
		{Name: "num", Type: field.TypeUint32, Comment: "报废数量"},
		{Name: "price", Type: field.TypeUint64, Comment: "单价"},
		{Name: "reason", Type: field.TypeString, Comment: "报废原因"},
		{Name: "expired_time", Type: field.TypeTime, Comment: "过期时间"},
		{Name: "enter_repository_time", Type: field.TypeTime, Comment: "入库时间"},
		{Name: "source", Type: field.TypeString, Nullable: true, Comment: "来源"},
		{Name: "order_no", Type: field.TypeString, Comment: "报废单号"},
		{Name: "feature", Type: field.TypeJSON, Nullable: true, Comment: "详细规格"},
		{Name: "discard_plan_order_id", Type: field.TypeString, Nullable: true, Comment: "报废计划单"},
		{Name: "equipment_type_id", Type: field.TypeString, Nullable: true, Comment: "设备类型"},
		{Name: "equipment_id", Type: field.TypeString, Nullable: true, Comment: "设备"},
		{Name: "measure_unit_id", Type: field.TypeString, Nullable: true, Comment: "计量单位"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "所在仓库"},
		{Name: "repository_area_id", Type: field.TypeString, Nullable: true, Comment: "所在库区"},
		{Name: "repository_position_id", Type: field.TypeString, Nullable: true, Comment: "所在库位"},
	}
	// WmsDiscardPlanOrderDetailsTable holds the schema information for the "wms_discard_plan_order_details" table.
	WmsDiscardPlanOrderDetailsTable = &schema.Table{
		Name:       "wms_discard_plan_order_details",
		Comment:    "报废计划明细",
		Columns:    WmsDiscardPlanOrderDetailsColumns,
		PrimaryKey: []*schema.Column{WmsDiscardPlanOrderDetailsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_discard_plan_order_details_wms_discard_plan_orders_details",
				Columns:    []*schema.Column{WmsDiscardPlanOrderDetailsColumns[18]},
				RefColumns: []*schema.Column{WmsDiscardPlanOrdersColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_discard_plan_order_details_wms_equipment_types_equipment_type",
				Columns:    []*schema.Column{WmsDiscardPlanOrderDetailsColumns[19]},
				RefColumns: []*schema.Column{WmsEquipmentTypesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_discard_plan_order_details_wms_equipments_equipment",
				Columns:    []*schema.Column{WmsDiscardPlanOrderDetailsColumns[20]},
				RefColumns: []*schema.Column{WmsEquipmentsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_discard_plan_order_details_wms_measure_units_measure_unit",
				Columns:    []*schema.Column{WmsDiscardPlanOrderDetailsColumns[21]},
				RefColumns: []*schema.Column{WmsMeasureUnitsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_discard_plan_order_details_wms_repositories_repository",
				Columns:    []*schema.Column{WmsDiscardPlanOrderDetailsColumns[22]},
				RefColumns: []*schema.Column{WmsRepositoriesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_discard_plan_order_details_wms_repository_areas_repository_area",
				Columns:    []*schema.Column{WmsDiscardPlanOrderDetailsColumns[23]},
				RefColumns: []*schema.Column{WmsRepositoryAreasColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_discard_plan_order_details_wms_repository_positions_repository_position",
				Columns:    []*schema.Column{WmsDiscardPlanOrderDetailsColumns[24]},
				RefColumns: []*schema.Column{WmsRepositoryPositionsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsDocumentsColumns holds the columns for the "wms_documents" table.
	WmsDocumentsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "url", Type: field.TypeString, Comment: "文件地址"},
		{Name: "type", Type: field.TypeString, Comment: "单据类型"},
		{Name: "enter_way", Type: field.TypeString, Comment: "登记方式"},
		{Name: "source", Type: field.TypeString, Comment: "来源"},
		{Name: "source_type", Type: field.TypeString, Comment: "来源类型"},
		{Name: "source_id", Type: field.TypeString, Comment: "来源id"},
		{Name: "wms_approval_task_documents", Type: field.TypeString, Nullable: true},
		{Name: "wms_discard_meeting_documents", Type: field.TypeString, Nullable: true},
		{Name: "wms_discard_plan_order_documents", Type: field.TypeString, Nullable: true},
		{Name: "wms_enter_repository_order_documents", Type: field.TypeString, Nullable: true},
		{Name: "wms_maintain_plan_documents", Type: field.TypeString, Nullable: true},
		{Name: "wms_out_repository_order_documents", Type: field.TypeString, Nullable: true},
		{Name: "wms_purchase_order_documents", Type: field.TypeString, Nullable: true},
	}
	// WmsDocumentsTable holds the schema information for the "wms_documents" table.
	WmsDocumentsTable = &schema.Table{
		Name:       "wms_documents",
		Comment:    "单据",
		Columns:    WmsDocumentsColumns,
		PrimaryKey: []*schema.Column{WmsDocumentsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_documents_wms_approval_tasks_documents",
				Columns:    []*schema.Column{WmsDocumentsColumns[12]},
				RefColumns: []*schema.Column{WmsApprovalTasksColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_documents_wms_discard_meetings_documents",
				Columns:    []*schema.Column{WmsDocumentsColumns[13]},
				RefColumns: []*schema.Column{WmsDiscardMeetingsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_documents_wms_discard_plan_orders_documents",
				Columns:    []*schema.Column{WmsDocumentsColumns[14]},
				RefColumns: []*schema.Column{WmsDiscardPlanOrdersColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_documents_wms_enter_repository_orders_documents",
				Columns:    []*schema.Column{WmsDocumentsColumns[15]},
				RefColumns: []*schema.Column{WmsEnterRepositoryOrdersColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_documents_wms_maintain_plans_documents",
				Columns:    []*schema.Column{WmsDocumentsColumns[16]},
				RefColumns: []*schema.Column{WmsMaintainPlansColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_documents_wms_out_repository_orders_documents",
				Columns:    []*schema.Column{WmsDocumentsColumns[17]},
				RefColumns: []*schema.Column{WmsOutRepositoryOrdersColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_documents_wms_purchase_orders_documents",
				Columns:    []*schema.Column{WmsDocumentsColumns[18]},
				RefColumns: []*schema.Column{WmsPurchaseOrdersColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "wmsdocument_source_type_source_id_type_url",
				Unique:  true,
				Columns: []*schema.Column{WmsDocumentsColumns[10], WmsDocumentsColumns[11], WmsDocumentsColumns[7], WmsDocumentsColumns[6]},
			},
		},
	}
	// WmsEnterRepositoryOrdersColumns holds the columns for the "wms_enter_repository_orders" table.
	WmsEnterRepositoryOrdersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "contract_urls", Type: field.TypeString, Nullable: true, Comment: "合同附件"},
		{Name: "invoice_urls", Type: field.TypeString, Nullable: true, Comment: "发票附件"},
		{Name: "audit_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "other_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "order_no", Type: field.TypeString, Comment: "入库单号"},
		{Name: "type", Type: field.TypeString, Comment: "入库类型"},
		{Name: "enter_time", Type: field.TypeTime, Comment: "入库时间"},
		{Name: "relation_order_no", Type: field.TypeString, Nullable: true, Comment: "关联单号"},
		{Name: "status", Type: field.TypeString, Comment: "入库状态"},
		{Name: "equipment_num", Type: field.TypeUint32, Nullable: true, Comment: "设备数量"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "入库仓库"},
	}
	// WmsEnterRepositoryOrdersTable holds the schema information for the "wms_enter_repository_orders" table.
	WmsEnterRepositoryOrdersTable = &schema.Table{
		Name:       "wms_enter_repository_orders",
		Comment:    "入库单",
		Columns:    WmsEnterRepositoryOrdersColumns,
		PrimaryKey: []*schema.Column{WmsEnterRepositoryOrdersColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_enter_repository_orders_wms_repositories_repository",
				Columns:    []*schema.Column{WmsEnterRepositoryOrdersColumns[16]},
				RefColumns: []*schema.Column{WmsRepositoriesColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsEnterRepositoryOrderDetailsColumns holds the columns for the "wms_enter_repository_order_details" table.
	WmsEnterRepositoryOrderDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "设备名称"},
		{Name: "code", Type: field.TypeString, Nullable: true, Comment: "编码:RFID编码或二维码"},
		{Name: "code_type", Type: field.TypeString, Nullable: true, Comment: "编码类型"},
		{Name: "model_no", Type: field.TypeString, Comment: "规格型号"},
		{Name: "provider_id", Type: field.TypeString, Comment: "供应商"},
		{Name: "num", Type: field.TypeUint32, Comment: "数量"},
		{Name: "price", Type: field.TypeUint64, Comment: "单价"},
		{Name: "repository_area_id", Type: field.TypeString, Nullable: true, Comment: "入库库区"},
		{Name: "order_no", Type: field.TypeString, Nullable: true, Comment: "入库单号"},
		{Name: "expire_time", Type: field.TypeTime, Nullable: true, Comment: "过期日期"},
		{Name: "feature", Type: field.TypeJSON, Nullable: true, Comment: "详细规格"},
		{Name: "material_id", Type: field.TypeString, Nullable: true, Comment: "资产id"},
		{Name: "enter_repository_order_id", Type: field.TypeString, Nullable: true, Comment: "入库单"},
		{Name: "equipment_type_id", Type: field.TypeString, Nullable: true, Comment: "设备类型"},
		{Name: "equipment_id", Type: field.TypeString, Nullable: true, Comment: "设备"},
		{Name: "measure_unit_id", Type: field.TypeString, Nullable: true, Comment: "计量单位"},
		{Name: "repository_position_id", Type: field.TypeString, Nullable: true, Comment: "入库仓位"},
		{Name: "from_repository_id", Type: field.TypeString, Nullable: true, Comment: "入库前仓库"},
		{Name: "to_repository_id", Type: field.TypeString, Nullable: true, Comment: "入库后仓库"},
	}
	// WmsEnterRepositoryOrderDetailsTable holds the schema information for the "wms_enter_repository_order_details" table.
	WmsEnterRepositoryOrderDetailsTable = &schema.Table{
		Name:       "wms_enter_repository_order_details",
		Comment:    "入库单明细",
		Columns:    WmsEnterRepositoryOrderDetailsColumns,
		PrimaryKey: []*schema.Column{WmsEnterRepositoryOrderDetailsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_enter_repository_order_details_wms_enter_repository_orders_details",
				Columns:    []*schema.Column{WmsEnterRepositoryOrderDetailsColumns[18]},
				RefColumns: []*schema.Column{WmsEnterRepositoryOrdersColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_enter_repository_order_details_wms_equipment_types_equipment_type",
				Columns:    []*schema.Column{WmsEnterRepositoryOrderDetailsColumns[19]},
				RefColumns: []*schema.Column{WmsEquipmentTypesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_enter_repository_order_details_wms_equipments_equipment",
				Columns:    []*schema.Column{WmsEnterRepositoryOrderDetailsColumns[20]},
				RefColumns: []*schema.Column{WmsEquipmentsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_enter_repository_order_details_wms_measure_units_measure_unit",
				Columns:    []*schema.Column{WmsEnterRepositoryOrderDetailsColumns[21]},
				RefColumns: []*schema.Column{WmsMeasureUnitsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_enter_repository_order_details_wms_repository_positions_repository_position",
				Columns:    []*schema.Column{WmsEnterRepositoryOrderDetailsColumns[22]},
				RefColumns: []*schema.Column{WmsRepositoryPositionsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_enter_repository_order_details_wms_repositories_from_repository",
				Columns:    []*schema.Column{WmsEnterRepositoryOrderDetailsColumns[23]},
				RefColumns: []*schema.Column{WmsRepositoriesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_enter_repository_order_details_wms_repositories_to_repository",
				Columns:    []*schema.Column{WmsEnterRepositoryOrderDetailsColumns[24]},
				RefColumns: []*schema.Column{WmsRepositoriesColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "wmsenterrepositoryorderdetail_enter_repository_order_id_code",
				Unique:  true,
				Columns: []*schema.Column{WmsEnterRepositoryOrderDetailsColumns[18], WmsEnterRepositoryOrderDetailsColumns[7]},
			},
		},
	}
	// WmsEquipmentsColumns holds the columns for the "wms_equipments" table.
	WmsEquipmentsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "auth_type", Type: field.TypeEnum, Comment: "权限类型， any_user: 任何用户，custom: 自定义", Enums: []string{"any_user", "custom"}, Default: "any_user"},
		{Name: "auth_user_ids", Type: field.TypeString, Nullable: true, Comment: "自定义权限用户ID列表，逗号分隔"},
		{Name: "auth_organization_ids", Type: field.TypeString, Nullable: true, Comment: "自定义权限组织ID列表，逗号分隔"},
		{Name: "code", Type: field.TypeString, Nullable: true, Comment: "编码"},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "名称"},
		{Name: "model_no", Type: field.TypeString, Nullable: true, Comment: "规格型号"},
		{Name: "equipment_type_code", Type: field.TypeString, Nullable: true, Comment: "设备类型编码"},
		{Name: "equipment_type_name", Type: field.TypeString, Nullable: true, Comment: "设备类型名称"},
		{Name: "seller_name", Type: field.TypeString, Nullable: true, Comment: "装备销售企业名称"},
		{Name: "provider_country", Type: field.TypeString, Nullable: true, Comment: "装备生产国家"},
		{Name: "provider_province", Type: field.TypeString, Nullable: true, Comment: "装备生产省份"},
		{Name: "provider_city", Type: field.TypeString, Nullable: true, Comment: "装备生产城市"},
		{Name: "provider_area", Type: field.TypeString, Nullable: true, Comment: "装备生产区县"},
		{Name: "provider_name", Type: field.TypeString, Nullable: true, Comment: "装备生产企业名称"},
		{Name: "images", Type: field.TypeString, Nullable: true, Comment: "图片"},
		{Name: "type", Type: field.TypeString, Nullable: true, Comment: "设备类型"},
		{Name: "weight", Type: field.TypeString, Nullable: true, Comment: "重量"},
		{Name: "readme", Type: field.TypeString, Nullable: true, Comment: "使用说明"},
		{Name: "inventory_count", Type: field.TypeInt32, Nullable: true, Comment: "库存数量，动态计算"},
		{Name: "is_no_code", Type: field.TypeBool, Nullable: true, Comment: "是否有编码"},
		{Name: "is_one_material_one_code", Type: field.TypeBool, Comment: "是否一物一码", Default: true},
		{Name: "discard_method", Type: field.TypeInt32, Comment: "报废方式， 1 审核报废、 2 上会报废、 3 出库即报废", Default: 1},
		{Name: "equipment_type_id", Type: field.TypeString, Nullable: true, Comment: "设备分类"},
	}
	// WmsEquipmentsTable holds the schema information for the "wms_equipments" table.
	WmsEquipmentsTable = &schema.Table{
		Name:       "wms_equipments",
		Comment:    "设备表",
		Columns:    WmsEquipmentsColumns,
		PrimaryKey: []*schema.Column{WmsEquipmentsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_equipments_wms_equipment_types_equipments",
				Columns:    []*schema.Column{WmsEquipmentsColumns[29]},
				RefColumns: []*schema.Column{WmsEquipmentTypesColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsEquipmentDetailsColumns holds the columns for the "wms_equipment_details" table.
	WmsEquipmentDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "value", Type: field.TypeString, Nullable: true, Comment: "值"},
		{Name: "is_show_to_list", Type: field.TypeInt32, Comment: "是否显示到列表", Default: 0},
		{Name: "equipment_id", Type: field.TypeString, Nullable: true, Comment: "设备"},
		{Name: "equipment_type_id", Type: field.TypeString, Nullable: true, Comment: "设备类型"},
		{Name: "equipment_type_property_id", Type: field.TypeString, Nullable: true, Comment: "设备类型属性"},
		{Name: "equipment_type_property_group_id", Type: field.TypeString, Nullable: true, Comment: "设备类型属性组"},
	}
	// WmsEquipmentDetailsTable holds the schema information for the "wms_equipment_details" table.
	WmsEquipmentDetailsTable = &schema.Table{
		Name:       "wms_equipment_details",
		Comment:    "设备详情表",
		Columns:    WmsEquipmentDetailsColumns,
		PrimaryKey: []*schema.Column{WmsEquipmentDetailsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_equipment_details_wms_equipments_details",
				Columns:    []*schema.Column{WmsEquipmentDetailsColumns[7]},
				RefColumns: []*schema.Column{WmsEquipmentsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_equipment_details_wms_equipment_types_details",
				Columns:    []*schema.Column{WmsEquipmentDetailsColumns[8]},
				RefColumns: []*schema.Column{WmsEquipmentTypesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_equipment_details_wms_equipment_type_properties_details",
				Columns:    []*schema.Column{WmsEquipmentDetailsColumns[9]},
				RefColumns: []*schema.Column{WmsEquipmentTypePropertiesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_equipment_details_wms_equipment_type_property_groups_details",
				Columns:    []*schema.Column{WmsEquipmentDetailsColumns[10]},
				RefColumns: []*schema.Column{WmsEquipmentTypePropertyGroupsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsEquipmentTypesColumns holds the columns for the "wms_equipment_types" table.
	WmsEquipmentTypesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "sort", Type: field.TypeInt32, Nullable: true, Comment: "排序", Default: 0},
		{Name: "code", Type: field.TypeString, Comment: "类型编码"},
		{Name: "name", Type: field.TypeString, Comment: "类型名称"},
		{Name: "feature_description", Type: field.TypeString, Nullable: true, Comment: "用途或性能描述"},
		{Name: "measure_unit", Type: field.TypeString, Nullable: true, Comment: "计量单位"},
		{Name: "parent_id", Type: field.TypeString, Nullable: true, Comment: "上级类型"},
	}
	// WmsEquipmentTypesTable holds the schema information for the "wms_equipment_types" table.
	WmsEquipmentTypesTable = &schema.Table{
		Name:       "wms_equipment_types",
		Comment:    "装备类型表",
		Columns:    WmsEquipmentTypesColumns,
		PrimaryKey: []*schema.Column{WmsEquipmentTypesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_equipment_types_wms_equipment_types_children",
				Columns:    []*schema.Column{WmsEquipmentTypesColumns[11]},
				RefColumns: []*schema.Column{WmsEquipmentTypesColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "wmsequipmenttype_code",
				Unique:  true,
				Columns: []*schema.Column{WmsEquipmentTypesColumns[7]},
			},
		},
	}
	// WmsEquipmentTypePropertiesColumns holds the columns for the "wms_equipment_type_properties" table.
	WmsEquipmentTypePropertiesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "sort", Type: field.TypeInt32, Nullable: true, Comment: "排序", Default: 0},
		{Name: "name", Type: field.TypeString, Comment: "字段名称"},
		{Name: "field_type", Type: field.TypeString, Comment: "字段类型"},
		{Name: "is_required", Type: field.TypeString, Comment: "是否必填"},
		{Name: "property_type", Type: field.TypeString, Comment: "属性类型, custom: 自定义, common: 通用，通用属性每个装备类型都有，不可修改删除", Default: "custom"},
		{Name: "field_key", Type: field.TypeString, Nullable: true, Comment: "字段键, 只用于通用属性，用于关联设备表字段"},
		{Name: "measure_unit_id", Type: field.TypeString, Nullable: true, Comment: "计量单位"},
		{Name: "measure_unit_name", Type: field.TypeString, Nullable: true, Comment: "计量单位名称"},
		{Name: "group_name", Type: field.TypeString, Nullable: true, Comment: "属性组名称"},
		{Name: "equipment_type_id", Type: field.TypeString, Nullable: true, Comment: "装备类型ID，通用属性值为0"},
		{Name: "group_id", Type: field.TypeString, Nullable: true, Comment: "属性组ID"},
	}
	// WmsEquipmentTypePropertiesTable holds the schema information for the "wms_equipment_type_properties" table.
	WmsEquipmentTypePropertiesTable = &schema.Table{
		Name:       "wms_equipment_type_properties",
		Comment:    "装备类型属性表",
		Columns:    WmsEquipmentTypePropertiesColumns,
		PrimaryKey: []*schema.Column{WmsEquipmentTypePropertiesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_equipment_type_properties_wms_equipment_types_properties",
				Columns:    []*schema.Column{WmsEquipmentTypePropertiesColumns[15]},
				RefColumns: []*schema.Column{WmsEquipmentTypesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_equipment_type_properties_wms_equipment_type_property_groups_properties",
				Columns:    []*schema.Column{WmsEquipmentTypePropertiesColumns[16]},
				RefColumns: []*schema.Column{WmsEquipmentTypePropertyGroupsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsEquipmentTypePropertyGroupsColumns holds the columns for the "wms_equipment_type_property_groups" table.
	WmsEquipmentTypePropertyGroupsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "sort", Type: field.TypeInt32, Nullable: true, Comment: "排序", Default: 0},
		{Name: "name", Type: field.TypeString, Comment: "属性组名称"},
		{Name: "property_group_type", Type: field.TypeString, Comment: "属性组类型, custom: 自定义, common: 通用，通用属性组每个装备类型都有，不可修改删除", Default: "custom"},
		{Name: "equipment_type_id", Type: field.TypeString, Nullable: true, Comment: "装备类型ID, 通用属性组值为0"},
	}
	// WmsEquipmentTypePropertyGroupsTable holds the schema information for the "wms_equipment_type_property_groups" table.
	WmsEquipmentTypePropertyGroupsTable = &schema.Table{
		Name:       "wms_equipment_type_property_groups",
		Comment:    "装备类型属性表",
		Columns:    WmsEquipmentTypePropertyGroupsColumns,
		PrimaryKey: []*schema.Column{WmsEquipmentTypePropertyGroupsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_equipment_type_property_groups_wms_equipment_types_property_groups",
				Columns:    []*schema.Column{WmsEquipmentTypePropertyGroupsColumns[8]},
				RefColumns: []*schema.Column{WmsEquipmentTypesColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsEquipmentTypePropertyOptionsColumns holds the columns for the "wms_equipment_type_property_options" table.
	WmsEquipmentTypePropertyOptionsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "sort", Type: field.TypeInt32, Nullable: true, Comment: "排序", Default: 0},
		{Name: "code", Type: field.TypeString, Nullable: true, Comment: "编码"},
		{Name: "label", Type: field.TypeString, Nullable: true, Comment: "显示名称"},
		{Name: "equipment_type_property_id", Type: field.TypeString, Nullable: true, Comment: "设备类型属性"},
	}
	// WmsEquipmentTypePropertyOptionsTable holds the schema information for the "wms_equipment_type_property_options" table.
	WmsEquipmentTypePropertyOptionsTable = &schema.Table{
		Name:       "wms_equipment_type_property_options",
		Comment:    "设备属性选项表",
		Columns:    WmsEquipmentTypePropertyOptionsColumns,
		PrimaryKey: []*schema.Column{WmsEquipmentTypePropertyOptionsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_equipment_type_property_options_wms_equipment_type_properties_options",
				Columns:    []*schema.Column{WmsEquipmentTypePropertyOptionsColumns[8]},
				RefColumns: []*schema.Column{WmsEquipmentTypePropertiesColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "wmsequipmenttypepropertyoption_equipment_type_property_id_code",
				Unique:  true,
				Columns: []*schema.Column{WmsEquipmentTypePropertyOptionsColumns[8], WmsEquipmentTypePropertyOptionsColumns[6]},
			},
		},
	}
	// WmsEquipmentUserGroupsColumns holds the columns for the "wms_equipment_user_groups" table.
	WmsEquipmentUserGroupsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "user_group_id", Type: field.TypeString, Comment: "人员组"},
		{Name: "equipment_id", Type: field.TypeString, Nullable: true, Comment: "设备"},
	}
	// WmsEquipmentUserGroupsTable holds the schema information for the "wms_equipment_user_groups" table.
	WmsEquipmentUserGroupsTable = &schema.Table{
		Name:       "wms_equipment_user_groups",
		Comment:    "设备人员组表",
		Columns:    WmsEquipmentUserGroupsColumns,
		PrimaryKey: []*schema.Column{WmsEquipmentUserGroupsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_equipment_user_groups_wms_equipments_user_groups",
				Columns:    []*schema.Column{WmsEquipmentUserGroupsColumns[6]},
				RefColumns: []*schema.Column{WmsEquipmentsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsFireStationsColumns holds the columns for the "wms_fire_stations" table.
	WmsFireStationsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "area_code", Type: field.TypeString, Nullable: true, Comment: "区域编码"},
		{Name: "area_name", Type: field.TypeString, Nullable: true, Comment: "区域名称"},
		{Name: "address", Type: field.TypeString, Nullable: true, Comment: "详细地址"},
		{Name: "name", Type: field.TypeString, Unique: true, Comment: "消防站名称"},
		{Name: "person_count", Type: field.TypeInt32, Comment: "人数", Default: 0},
	}
	// WmsFireStationsTable holds the schema information for the "wms_fire_stations" table.
	WmsFireStationsTable = &schema.Table{
		Name:       "wms_fire_stations",
		Comment:    "消防站",
		Columns:    WmsFireStationsColumns,
		PrimaryKey: []*schema.Column{WmsFireStationsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "wmsfirestation_name",
				Unique:  true,
				Columns: []*schema.Column{WmsFireStationsColumns[10]},
			},
		},
	}
	// WmsGpSsColumns holds the columns for the "wms_gp_ss" table.
	WmsGpSsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "code", Type: field.TypeString, Unique: true, Comment: "gps 设备编码"},
		{Name: "online_status", Type: field.TypeString, Comment: "在线状态"},
		{Name: "name", Type: field.TypeString, Comment: "名称"},
		{Name: "sim_card", Type: field.TypeString, Nullable: true, Comment: "sim卡号"},
		{Name: "model_no", Type: field.TypeString, Nullable: true, Comment: "型号"},
		{Name: "wms_car_gps", Type: field.TypeString, Nullable: true},
	}
	// WmsGpSsTable holds the schema information for the "wms_gp_ss" table.
	WmsGpSsTable = &schema.Table{
		Name:       "wms_gp_ss",
		Comment:    "GPS管理表",
		Columns:    WmsGpSsColumns,
		PrimaryKey: []*schema.Column{WmsGpSsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_gp_ss_wms_cars_gps",
				Columns:    []*schema.Column{WmsGpSsColumns[11]},
				RefColumns: []*schema.Column{WmsCarsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsGpsRecordsColumns holds the columns for the "wms_gps_records" table.
	WmsGpsRecordsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "code", Type: field.TypeString, Comment: "设备编码"},
		{Name: "longitude", Type: field.TypeString, Comment: "经度"},
		{Name: "latitude", Type: field.TypeString, Comment: "纬度"},
		{Name: "altitude", Type: field.TypeString, Nullable: true, Comment: "海拔"},
		{Name: "speed", Type: field.TypeString, Nullable: true, Comment: "速度"},
		{Name: "direction", Type: field.TypeString, Nullable: true, Comment: "方向"},
		{Name: "gps_time", Type: field.TypeTime, Comment: "GPS时间"},
		{Name: "location", Type: field.TypeOther, Nullable: true, Comment: "空间点位", SchemaType: map[string]string{"postgres": "geometry(Point, 4326)"}},
	}
	// WmsGpsRecordsTable holds the schema information for the "wms_gps_records" table.
	WmsGpsRecordsTable = &schema.Table{
		Name:       "wms_gps_records",
		Comment:    "GPS定位记录",
		Columns:    WmsGpsRecordsColumns,
		PrimaryKey: []*schema.Column{WmsGpsRecordsColumns[0]},
	}
	// WmsLearningCoursesColumns holds the columns for the "wms_learning_courses" table.
	WmsLearningCoursesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "auth_type", Type: field.TypeEnum, Comment: "权限类型， any_user: 任何用户，custom: 自定义", Enums: []string{"any_user", "custom"}, Default: "any_user"},
		{Name: "auth_user_ids", Type: field.TypeString, Nullable: true, Comment: "自定义权限用户ID列表，逗号分隔"},
		{Name: "auth_organization_ids", Type: field.TypeString, Nullable: true, Comment: "自定义权限组织ID列表，逗号分隔"},
		{Name: "name", Type: field.TypeString, Unique: true, Comment: "课程名称"},
		{Name: "avator", Type: field.TypeString, Nullable: true, Comment: "封面"},
		{Name: "equipment_ids", Type: field.TypeString, Nullable: true, Comment: "关联装备"},
	}
	// WmsLearningCoursesTable holds the schema information for the "wms_learning_courses" table.
	WmsLearningCoursesTable = &schema.Table{
		Name:       "wms_learning_courses",
		Comment:    "课程表",
		Columns:    WmsLearningCoursesColumns,
		PrimaryKey: []*schema.Column{WmsLearningCoursesColumns[0]},
	}
	// WmsLearningCourseCoursewaresColumns holds the columns for the "wms_learning_course_coursewares" table.
	WmsLearningCourseCoursewaresColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "sort", Type: field.TypeInt32, Nullable: true, Comment: "排序", Default: 0},
		{Name: "course_id", Type: field.TypeString, Nullable: true, Comment: "课程ID"},
		{Name: "courseware_id", Type: field.TypeString, Nullable: true, Comment: "课件ID"},
	}
	// WmsLearningCourseCoursewaresTable holds the schema information for the "wms_learning_course_coursewares" table.
	WmsLearningCourseCoursewaresTable = &schema.Table{
		Name:       "wms_learning_course_coursewares",
		Comment:    "课程课件表",
		Columns:    WmsLearningCourseCoursewaresColumns,
		PrimaryKey: []*schema.Column{WmsLearningCourseCoursewaresColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_learning_course_coursewares_wms_learning_courses_course_coursewares",
				Columns:    []*schema.Column{WmsLearningCourseCoursewaresColumns[6]},
				RefColumns: []*schema.Column{WmsLearningCoursesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_learning_course_coursewares_wms_learning_coursewares_course_coursewares",
				Columns:    []*schema.Column{WmsLearningCourseCoursewaresColumns[7]},
				RefColumns: []*schema.Column{WmsLearningCoursewaresColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsLearningCourseLogsColumns holds the columns for the "wms_learning_course_logs" table.
	WmsLearningCourseLogsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "type", Type: field.TypeString, Nullable: true, Comment: "日志类型: view-访问"},
		{Name: "user_id", Type: field.TypeString, Nullable: true, Comment: "用户ID"},
		{Name: "learning_course_id", Type: field.TypeString, Nullable: true, Comment: "课程ID"},
	}
	// WmsLearningCourseLogsTable holds the schema information for the "wms_learning_course_logs" table.
	WmsLearningCourseLogsTable = &schema.Table{
		Name:       "wms_learning_course_logs",
		Comment:    "课程日志表",
		Columns:    WmsLearningCourseLogsColumns,
		PrimaryKey: []*schema.Column{WmsLearningCourseLogsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_learning_course_logs_sys_users_logs",
				Columns:    []*schema.Column{WmsLearningCourseLogsColumns[6]},
				RefColumns: []*schema.Column{SysUsersColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_learning_course_logs_wms_learning_courses_logs",
				Columns:    []*schema.Column{WmsLearningCourseLogsColumns[7]},
				RefColumns: []*schema.Column{WmsLearningCoursesColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "wmslearningcourselog_learning_course_id",
				Unique:  false,
				Columns: []*schema.Column{WmsLearningCourseLogsColumns[7]},
			},
			{
				Name:    "wmslearningcourselog_user_id",
				Unique:  false,
				Columns: []*schema.Column{WmsLearningCourseLogsColumns[6]},
			},
			{
				Name:    "wmslearningcourselog_type",
				Unique:  false,
				Columns: []*schema.Column{WmsLearningCourseLogsColumns[5]},
			},
		},
	}
	// WmsLearningCourseRecordsColumns holds the columns for the "wms_learning_course_records" table.
	WmsLearningCourseRecordsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "learning_plan_id", Type: field.TypeString, Comment: "学习计划id"},
		{Name: "process", Type: field.TypeUint32, Comment: "进度", Default: 0},
		{Name: "user_id", Type: field.TypeString, Comment: "用户id"},
		{Name: "learning_course_id", Type: field.TypeString, Comment: "课程id"},
	}
	// WmsLearningCourseRecordsTable holds the schema information for the "wms_learning_course_records" table.
	WmsLearningCourseRecordsTable = &schema.Table{
		Name:       "wms_learning_course_records",
		Comment:    "学习课程记录",
		Columns:    WmsLearningCourseRecordsColumns,
		PrimaryKey: []*schema.Column{WmsLearningCourseRecordsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_learning_course_records_sys_users_course_records",
				Columns:    []*schema.Column{WmsLearningCourseRecordsColumns[8]},
				RefColumns: []*schema.Column{SysUsersColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "wms_learning_course_records_wms_learning_courses_records",
				Columns:    []*schema.Column{WmsLearningCourseRecordsColumns[9]},
				RefColumns: []*schema.Column{WmsLearningCoursesColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "wmslearningcourserecord_user_id_learning_plan_id_learning_course_id",
				Unique:  true,
				Columns: []*schema.Column{WmsLearningCourseRecordsColumns[8], WmsLearningCourseRecordsColumns[6], WmsLearningCourseRecordsColumns[9]},
			},
		},
	}
	// WmsLearningCoursewaresColumns holds the columns for the "wms_learning_coursewares" table.
	WmsLearningCoursewaresColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "name", Type: field.TypeString, Unique: true, Comment: "课件名称"},
		{Name: "category", Type: field.TypeString, Comment: "课件分类"},
		{Name: "type", Type: field.TypeString, Comment: "课件类型"},
		{Name: "type_text_editor_content", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "图文内容"},
		{Name: "type_video_content", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "视频内容"},
		{Name: "type_file_content", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "文件内容"},
		{Name: "type_link_content", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "外链内容"},
		{Name: "type_link_open_way", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "外链打开方式"},
		{Name: "time_length", Type: field.TypeUint32, Comment: "时长，单位分钟", Default: 10},
	}
	// WmsLearningCoursewaresTable holds the schema information for the "wms_learning_coursewares" table.
	WmsLearningCoursewaresTable = &schema.Table{
		Name:       "wms_learning_coursewares",
		Comment:    "课件表",
		Columns:    WmsLearningCoursewaresColumns,
		PrimaryKey: []*schema.Column{WmsLearningCoursewaresColumns[0]},
	}
	// WmsLearningCoursewareRecordsColumns holds the columns for the "wms_learning_courseware_records" table.
	WmsLearningCoursewareRecordsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "learning_plan_id", Type: field.TypeString, Comment: "学习计划id"},
		{Name: "learning_course_id", Type: field.TypeString, Comment: "课程id"},
		{Name: "user_id", Type: field.TypeString, Comment: "用户id"},
		{Name: "process", Type: field.TypeUint32, Comment: "进度", Default: 0},
		{Name: "learning_courseware_id", Type: field.TypeString, Comment: "课件id"},
	}
	// WmsLearningCoursewareRecordsTable holds the schema information for the "wms_learning_courseware_records" table.
	WmsLearningCoursewareRecordsTable = &schema.Table{
		Name:       "wms_learning_courseware_records",
		Comment:    "学习课件记录",
		Columns:    WmsLearningCoursewareRecordsColumns,
		PrimaryKey: []*schema.Column{WmsLearningCoursewareRecordsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_learning_courseware_records_wms_learning_coursewares_records",
				Columns:    []*schema.Column{WmsLearningCoursewareRecordsColumns[10]},
				RefColumns: []*schema.Column{WmsLearningCoursewaresColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "wmslearningcoursewarerecord_user_id_learning_plan_id_learning_course_id_learning_courseware_id",
				Unique:  true,
				Columns: []*schema.Column{WmsLearningCoursewareRecordsColumns[8], WmsLearningCoursewareRecordsColumns[6], WmsLearningCoursewareRecordsColumns[7], WmsLearningCoursewareRecordsColumns[10]},
			},
		},
	}
	// WmsLearningPlansColumns holds the columns for the "wms_learning_plans" table.
	WmsLearningPlansColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "auth_type", Type: field.TypeEnum, Comment: "权限类型， any_user: 任何用户，custom: 自定义", Enums: []string{"any_user", "custom"}, Default: "any_user"},
		{Name: "auth_user_ids", Type: field.TypeString, Nullable: true, Comment: "自定义权限用户ID列表，逗号分隔"},
		{Name: "auth_organization_ids", Type: field.TypeString, Nullable: true, Comment: "自定义权限组织ID列表，逗号分隔"},
		{Name: "name", Type: field.TypeString, Unique: true, Comment: "计划名称"},
		{Name: "environment_type", Type: field.TypeString, Nullable: true, Comment: "场景分类"},
		{Name: "start_time", Type: field.TypeTime, Nullable: true, Comment: "开始时间"},
		{Name: "end_time", Type: field.TypeTime, Nullable: true, Comment: "结束时间"},
		{Name: "description", Type: field.TypeString, Nullable: true, Comment: "描述"},
		{Name: "notice_event_published", Type: field.TypeBool, Comment: "通知事件发布", Default: false},
		{Name: "notice_event_changed", Type: field.TypeBool, Comment: "通知事件变更", Default: false},
		{Name: "notice_event_before_stared", Type: field.TypeBool, Comment: "通知事件开始前", Default: false},
		{Name: "notice_event_before_minutes", Type: field.TypeInt32, Comment: "通知事件开始前分钟数", Default: 15},
		{Name: "course_ids", Type: field.TypeString, Nullable: true, Comment: "关联课程"},
	}
	// WmsLearningPlansTable holds the schema information for the "wms_learning_plans" table.
	WmsLearningPlansTable = &schema.Table{
		Name:       "wms_learning_plans",
		Comment:    "学习计划",
		Columns:    WmsLearningPlansColumns,
		PrimaryKey: []*schema.Column{WmsLearningPlansColumns[0]},
	}
	// WmsLearningPlanRecordsColumns holds the columns for the "wms_learning_plan_records" table.
	WmsLearningPlanRecordsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "user_id", Type: field.TypeString, Comment: "用户id"},
		{Name: "process", Type: field.TypeUint32, Comment: "进度", Default: 0},
		{Name: "learning_plan_id", Type: field.TypeString, Comment: "学习计划id"},
	}
	// WmsLearningPlanRecordsTable holds the schema information for the "wms_learning_plan_records" table.
	WmsLearningPlanRecordsTable = &schema.Table{
		Name:       "wms_learning_plan_records",
		Comment:    "学习计划记录",
		Columns:    WmsLearningPlanRecordsColumns,
		PrimaryKey: []*schema.Column{WmsLearningPlanRecordsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_learning_plan_records_wms_learning_plans_records",
				Columns:    []*schema.Column{WmsLearningPlanRecordsColumns[8]},
				RefColumns: []*schema.Column{WmsLearningPlansColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "wmslearningplanrecord_user_id_learning_plan_id",
				Unique:  true,
				Columns: []*schema.Column{WmsLearningPlanRecordsColumns[6], WmsLearningPlanRecordsColumns[8]},
			},
		},
	}
	// WmsMaintainOrdersColumns holds the columns for the "wms_maintain_orders" table.
	WmsMaintainOrdersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "contract_urls", Type: field.TypeString, Nullable: true, Comment: "合同附件"},
		{Name: "invoice_urls", Type: field.TypeString, Nullable: true, Comment: "发票附件"},
		{Name: "audit_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "other_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "order_no", Type: field.TypeString, Comment: "保养单号"},
		{Name: "equipment_num", Type: field.TypeUint32, Nullable: true, Comment: "装备数量"},
		{Name: "status", Type: field.TypeString, Nullable: true, Comment: "保养状态"},
		{Name: "maintain_time", Type: field.TypeTime, Nullable: true, Comment: "保养时间"},
	}
	// WmsMaintainOrdersTable holds the schema information for the "wms_maintain_orders" table.
	WmsMaintainOrdersTable = &schema.Table{
		Name:       "wms_maintain_orders",
		Comment:    "保养单",
		Columns:    WmsMaintainOrdersColumns,
		PrimaryKey: []*schema.Column{WmsMaintainOrdersColumns[0]},
	}
	// WmsMaintainOrderDetailsColumns holds the columns for the "wms_maintain_order_details" table.
	WmsMaintainOrderDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "order_id", Type: field.TypeString, Nullable: true, Comment: "保养单"},
		{Name: "material_id", Type: field.TypeString, Nullable: true, Comment: "物料"},
		{Name: "material_name", Type: field.TypeString, Nullable: true, Comment: "物料名称"},
		{Name: "code", Type: field.TypeString, Comment: "编码"},
		{Name: "equipment_id", Type: field.TypeString, Nullable: true, Comment: "装备"},
		{Name: "equipment_type_id", Type: field.TypeString, Nullable: true, Comment: "装备类型"},
		{Name: "feature", Type: field.TypeJSON, Nullable: true, Comment: "详细规格"},
		{Name: "owner_id", Type: field.TypeString, Nullable: true, Comment: "归属人"},
		{Name: "model_no", Type: field.TypeString, Comment: "规格型号"},
		{Name: "measure_unit_id", Type: field.TypeString, Nullable: true, Comment: "计量单位"},
		{Name: "num", Type: field.TypeUint32, Comment: "保养数量"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "仓库"},
		{Name: "repository_area_id", Type: field.TypeString, Nullable: true, Comment: "库区"},
		{Name: "repository_position_id", Type: field.TypeString, Nullable: true, Comment: "库位"},
		{Name: "reason", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "保养原因"},
		{Name: "maintain_time", Type: field.TypeTime, Nullable: true, Comment: "保养时间"},
	}
	// WmsMaintainOrderDetailsTable holds the schema information for the "wms_maintain_order_details" table.
	WmsMaintainOrderDetailsTable = &schema.Table{
		Name:       "wms_maintain_order_details",
		Comment:    "保养单明细",
		Columns:    WmsMaintainOrderDetailsColumns,
		PrimaryKey: []*schema.Column{WmsMaintainOrderDetailsColumns[0]},
	}
	// WmsMaintainPlansColumns holds the columns for the "wms_maintain_plans" table.
	WmsMaintainPlansColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "contract_urls", Type: field.TypeString, Nullable: true, Comment: "合同附件"},
		{Name: "invoice_urls", Type: field.TypeString, Nullable: true, Comment: "发票附件"},
		{Name: "audit_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "other_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "order_no", Type: field.TypeString, Comment: "单号"},
		{Name: "name", Type: field.TypeString, Comment: "计划名称"},
		{Name: "plan_time", Type: field.TypeTime, Comment: "计划保养时间"},
		{Name: "equipment_type_num", Type: field.TypeUint32, Nullable: true, Comment: "设备类型数量"},
		{Name: "equipment_num", Type: field.TypeUint32, Nullable: true, Comment: "设备数量"},
		{Name: "fire_station_id", Type: field.TypeString, Nullable: true, Comment: "消防站"},
	}
	// WmsMaintainPlansTable holds the schema information for the "wms_maintain_plans" table.
	WmsMaintainPlansTable = &schema.Table{
		Name:       "wms_maintain_plans",
		Comment:    "保养计划",
		Columns:    WmsMaintainPlansColumns,
		PrimaryKey: []*schema.Column{WmsMaintainPlansColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_maintain_plans_wms_fire_stations_fire_station",
				Columns:    []*schema.Column{WmsMaintainPlansColumns[15]},
				RefColumns: []*schema.Column{WmsFireStationsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsMaintainPlanDetailsColumns holds the columns for the "wms_maintain_plan_details" table.
	WmsMaintainPlanDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "material_id", Type: field.TypeString, Nullable: true, Comment: "资产ID"},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "设备名称"},
		{Name: "code", Type: field.TypeString, Comment: "编码"},
		{Name: "model_no", Type: field.TypeString, Comment: "规格型号"},
		{Name: "num", Type: field.TypeUint32, Comment: "保养数量"},
		{Name: "reason", Type: field.TypeString, Comment: "保养原因"},
		{Name: "enter_repository_time", Type: field.TypeTime, Comment: "入库时间"},
		{Name: "source", Type: field.TypeString, Nullable: true, Comment: "来源"},
		{Name: "order_no", Type: field.TypeString, Nullable: true, Comment: "单号"},
		{Name: "feature", Type: field.TypeJSON, Nullable: true, Comment: "详细规格"},
		{Name: "maintain_plan_id", Type: field.TypeString, Nullable: true, Comment: "保养计划单"},
		{Name: "equipment_type_id", Type: field.TypeString, Nullable: true, Comment: "设备类型"},
		{Name: "equipment_id", Type: field.TypeString, Nullable: true, Comment: "设备"},
		{Name: "measure_unit_id", Type: field.TypeString, Nullable: true, Comment: "计量单位"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "所在仓库"},
		{Name: "repository_area_id", Type: field.TypeString, Nullable: true, Comment: "所在库区"},
		{Name: "repository_position_id", Type: field.TypeString, Nullable: true, Comment: "所在库位"},
	}
	// WmsMaintainPlanDetailsTable holds the schema information for the "wms_maintain_plan_details" table.
	WmsMaintainPlanDetailsTable = &schema.Table{
		Name:       "wms_maintain_plan_details",
		Comment:    "保养计划明细",
		Columns:    WmsMaintainPlanDetailsColumns,
		PrimaryKey: []*schema.Column{WmsMaintainPlanDetailsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_maintain_plan_details_wms_maintain_plans_details",
				Columns:    []*schema.Column{WmsMaintainPlanDetailsColumns[16]},
				RefColumns: []*schema.Column{WmsMaintainPlansColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_maintain_plan_details_wms_equipment_types_equipment_type",
				Columns:    []*schema.Column{WmsMaintainPlanDetailsColumns[17]},
				RefColumns: []*schema.Column{WmsEquipmentTypesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_maintain_plan_details_wms_equipments_equipment",
				Columns:    []*schema.Column{WmsMaintainPlanDetailsColumns[18]},
				RefColumns: []*schema.Column{WmsEquipmentsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_maintain_plan_details_wms_measure_units_measure_unit",
				Columns:    []*schema.Column{WmsMaintainPlanDetailsColumns[19]},
				RefColumns: []*schema.Column{WmsMeasureUnitsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_maintain_plan_details_wms_repositories_repository",
				Columns:    []*schema.Column{WmsMaintainPlanDetailsColumns[20]},
				RefColumns: []*schema.Column{WmsRepositoriesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_maintain_plan_details_wms_repository_areas_repository_area",
				Columns:    []*schema.Column{WmsMaintainPlanDetailsColumns[21]},
				RefColumns: []*schema.Column{WmsRepositoryAreasColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_maintain_plan_details_wms_repository_positions_repository_position",
				Columns:    []*schema.Column{WmsMaintainPlanDetailsColumns[22]},
				RefColumns: []*schema.Column{WmsRepositoryPositionsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsMaterialsColumns holds the columns for the "wms_materials" table.
	WmsMaterialsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "material_type", Type: field.TypeString, Comment: "装备类型"},
		{Name: "key", Type: field.TypeString, Nullable: true, Comment: "唯一标识"},
		{Name: "code", Type: field.TypeString, Nullable: true, Comment: "编码"},
		{Name: "code_type", Type: field.TypeString, Comment: "编码类型"},
		{Name: "name", Type: field.TypeString, Comment: "装备名称"},
		{Name: "model_no", Type: field.TypeString, Comment: "规格型号"},
		{Name: "provider_id", Type: field.TypeString, Comment: "供应商"},
		{Name: "num", Type: field.TypeUint32, Comment: "数量", Default: 0},
		{Name: "price", Type: field.TypeUint64, Comment: "单价", Default: 0},
		{Name: "expire_time", Type: field.TypeTime, Nullable: true, Comment: "过期日期"},
		{Name: "equipment_status", Type: field.TypeInt32, Nullable: true, Comment: "装备状态", Default: 1},
		{Name: "is_approving", Type: field.TypeBool, Nullable: true, Comment: "是否审批中", Default: false},
		{Name: "original_status", Type: field.TypeInt32, Nullable: true, Comment: "原状态"},
		{Name: "is_one_material_one_code", Type: field.TypeBool, Comment: "是否一物一码", Default: true},
		{Name: "discard_method", Type: field.TypeInt32, Comment: "报废方式， 1 审核报废、 2 上会报废、 3 出库即报废", Default: 1},
		{Name: "feature", Type: field.TypeJSON, Nullable: true, Comment: "详细规格"},
		{Name: "original_id", Type: field.TypeString, Nullable: true, Comment: "原始ID"},
		{Name: "order_no", Type: field.TypeString, Nullable: true, Comment: "关联订单号"},
		{Name: "finance_system_no", Type: field.TypeString, Nullable: true, Comment: "财务系统编号"},
		{Name: "use_start_time", Type: field.TypeTime, Nullable: true, Comment: "使用开始时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "wms_car_contained_materials", Type: field.TypeString, Nullable: true},
		{Name: "equipment_id", Type: field.TypeString, Nullable: true, Comment: "装备"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "仓库"},
		{Name: "repository_area_id", Type: field.TypeString, Nullable: true, Comment: "库区"},
		{Name: "repository_position_id", Type: field.TypeString, Nullable: true, Comment: "库位"},
		{Name: "equipment_type_id", Type: field.TypeString, Nullable: true, Comment: "装备分类"},
		{Name: "fire_station_id", Type: field.TypeString, Nullable: true, Comment: "消防站"},
		{Name: "measure_unit_id", Type: field.TypeString, Nullable: true, Comment: "计量单位"},
		{Name: "owner_id", Type: field.TypeString, Nullable: true, Comment: "持有人"},
	}
	// WmsMaterialsTable holds the schema information for the "wms_materials" table.
	WmsMaterialsTable = &schema.Table{
		Name:       "wms_materials",
		Comment:    "物料",
		Columns:    WmsMaterialsColumns,
		PrimaryKey: []*schema.Column{WmsMaterialsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_materials_sys_users_materials",
				Columns:    []*schema.Column{WmsMaterialsColumns[26]},
				RefColumns: []*schema.Column{SysUsersColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_materials_wms_cars_contained_materials",
				Columns:    []*schema.Column{WmsMaterialsColumns[27]},
				RefColumns: []*schema.Column{WmsCarsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_materials_wms_equipments_materials",
				Columns:    []*schema.Column{WmsMaterialsColumns[28]},
				RefColumns: []*schema.Column{WmsEquipmentsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_materials_wms_repositories_repository",
				Columns:    []*schema.Column{WmsMaterialsColumns[29]},
				RefColumns: []*schema.Column{WmsRepositoriesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_materials_wms_repository_areas_repository_area",
				Columns:    []*schema.Column{WmsMaterialsColumns[30]},
				RefColumns: []*schema.Column{WmsRepositoryAreasColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_materials_wms_repository_positions_repository_position",
				Columns:    []*schema.Column{WmsMaterialsColumns[31]},
				RefColumns: []*schema.Column{WmsRepositoryPositionsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_materials_wms_equipment_types_equipment_type",
				Columns:    []*schema.Column{WmsMaterialsColumns[32]},
				RefColumns: []*schema.Column{WmsEquipmentTypesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_materials_wms_fire_stations_fire_station",
				Columns:    []*schema.Column{WmsMaterialsColumns[33]},
				RefColumns: []*schema.Column{WmsFireStationsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_materials_wms_measure_units_measure_unit",
				Columns:    []*schema.Column{WmsMaterialsColumns[34]},
				RefColumns: []*schema.Column{WmsMeasureUnitsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_materials_sys_users_owner",
				Columns:    []*schema.Column{WmsMaterialsColumns[35]},
				RefColumns: []*schema.Column{SysUsersColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "wmsmaterial_owner_id",
				Unique:  false,
				Columns: []*schema.Column{WmsMaterialsColumns[35]},
			},
			{
				Name:    "wmsmaterial_order_no",
				Unique:  false,
				Columns: []*schema.Column{WmsMaterialsColumns[23]},
			},
		},
	}
	// WmsMaterialLogsColumns holds the columns for the "wms_material_logs" table.
	WmsMaterialLogsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "code", Type: field.TypeString, Nullable: true, Comment: "编码"},
		{Name: "code_type", Type: field.TypeString, Comment: "编码类型"},
		{Name: "name", Type: field.TypeString, Comment: "装备名称"},
		{Name: "model_no", Type: field.TypeString, Comment: "规格型号"},
		{Name: "provider_id", Type: field.TypeString, Comment: "供应商"},
		{Name: "num", Type: field.TypeUint32, Comment: "数量", Default: 0},
		{Name: "price", Type: field.TypeUint64, Comment: "单价", Default: 0},
		{Name: "expire_time", Type: field.TypeTime, Nullable: true, Comment: "过期日期"},
		{Name: "feature", Type: field.TypeJSON, Nullable: true, Comment: "详细规格"},
		{Name: "behavior", Type: field.TypeString, Nullable: true, Comment: "操作行为"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "仓库"},
		{Name: "repository_area_id", Type: field.TypeString, Nullable: true, Comment: "库区"},
		{Name: "repository_position_id", Type: field.TypeString, Nullable: true, Comment: "库位"},
		{Name: "to_repository_id", Type: field.TypeString, Nullable: true, Comment: "调拨到的仓库"},
		{Name: "to_repository_area_id", Type: field.TypeString, Nullable: true, Comment: "调拨到的库区"},
		{Name: "to_repository_position_id", Type: field.TypeString, Nullable: true, Comment: "调拨到的库位"},
		{Name: "equipment_id", Type: field.TypeString, Nullable: true, Comment: "装备"},
		{Name: "equipment_type_id", Type: field.TypeString, Nullable: true, Comment: "装备分类"},
		{Name: "fire_station_id", Type: field.TypeString, Nullable: true, Comment: "消防站"},
		{Name: "measure_unit_id", Type: field.TypeString, Nullable: true, Comment: "计量单位"},
		{Name: "owner_id", Type: field.TypeString, Nullable: true, Comment: "持有人"},
	}
	// WmsMaterialLogsTable holds the schema information for the "wms_material_logs" table.
	WmsMaterialLogsTable = &schema.Table{
		Name:       "wms_material_logs",
		Comment:    "物料日志",
		Columns:    WmsMaterialLogsColumns,
		PrimaryKey: []*schema.Column{WmsMaterialLogsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_material_logs_wms_repositories_repository",
				Columns:    []*schema.Column{WmsMaterialLogsColumns[17]},
				RefColumns: []*schema.Column{WmsRepositoriesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_material_logs_wms_repository_areas_repository_area",
				Columns:    []*schema.Column{WmsMaterialLogsColumns[18]},
				RefColumns: []*schema.Column{WmsRepositoryAreasColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_material_logs_wms_repository_positions_repository_position",
				Columns:    []*schema.Column{WmsMaterialLogsColumns[19]},
				RefColumns: []*schema.Column{WmsRepositoryPositionsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_material_logs_wms_repositories_to_repository",
				Columns:    []*schema.Column{WmsMaterialLogsColumns[20]},
				RefColumns: []*schema.Column{WmsRepositoriesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_material_logs_wms_repository_areas_to_repository_area",
				Columns:    []*schema.Column{WmsMaterialLogsColumns[21]},
				RefColumns: []*schema.Column{WmsRepositoryAreasColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_material_logs_wms_repository_positions_to_repository_position",
				Columns:    []*schema.Column{WmsMaterialLogsColumns[22]},
				RefColumns: []*schema.Column{WmsRepositoryPositionsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_material_logs_wms_equipments_equipment",
				Columns:    []*schema.Column{WmsMaterialLogsColumns[23]},
				RefColumns: []*schema.Column{WmsEquipmentsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_material_logs_wms_equipment_types_equipment_type",
				Columns:    []*schema.Column{WmsMaterialLogsColumns[24]},
				RefColumns: []*schema.Column{WmsEquipmentTypesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_material_logs_wms_fire_stations_fire_station",
				Columns:    []*schema.Column{WmsMaterialLogsColumns[25]},
				RefColumns: []*schema.Column{WmsFireStationsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_material_logs_wms_measure_units_measure_unit",
				Columns:    []*schema.Column{WmsMaterialLogsColumns[26]},
				RefColumns: []*schema.Column{WmsMeasureUnitsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_material_logs_sys_users_owner",
				Columns:    []*schema.Column{WmsMaterialLogsColumns[27]},
				RefColumns: []*schema.Column{SysUsersColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "wmsmateriallog_owner_id",
				Unique:  false,
				Columns: []*schema.Column{WmsMaterialLogsColumns[27]},
			},
		},
	}
	// WmsMeasureUnitsColumns holds the columns for the "wms_measure_units" table.
	WmsMeasureUnitsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "name", Type: field.TypeString, Comment: "单位名称"},
		{Name: "symbol", Type: field.TypeString, Comment: "符号"},
	}
	// WmsMeasureUnitsTable holds the schema information for the "wms_measure_units" table.
	WmsMeasureUnitsTable = &schema.Table{
		Name:       "wms_measure_units",
		Comment:    "计量单位表",
		Columns:    WmsMeasureUnitsColumns,
		PrimaryKey: []*schema.Column{WmsMeasureUnitsColumns[0]},
	}
	// WmsOperateLogsColumns holds the columns for the "wms_operate_logs" table.
	WmsOperateLogsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "object_id", Type: field.TypeString, Comment: "操作对象"},
		{Name: "category", Type: field.TypeString, Comment: "类目"},
		{Name: "content", Type: field.TypeString, Comment: "日志内容"},
		{Name: "wms_approval_task_logs", Type: field.TypeString, Nullable: true},
		{Name: "wms_discard_meeting_logs", Type: field.TypeString, Nullable: true},
		{Name: "wms_discard_plan_order_logs", Type: field.TypeString, Nullable: true},
		{Name: "wms_enter_repository_order_logs", Type: field.TypeString, Nullable: true},
		{Name: "wms_maintain_plan_logs", Type: field.TypeString, Nullable: true},
		{Name: "wms_out_repository_order_logs", Type: field.TypeString, Nullable: true},
		{Name: "wms_purchase_order_logs", Type: field.TypeString, Nullable: true},
	}
	// WmsOperateLogsTable holds the schema information for the "wms_operate_logs" table.
	WmsOperateLogsTable = &schema.Table{
		Name:       "wms_operate_logs",
		Comment:    "操作日志",
		Columns:    WmsOperateLogsColumns,
		PrimaryKey: []*schema.Column{WmsOperateLogsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_operate_logs_wms_approval_tasks_logs",
				Columns:    []*schema.Column{WmsOperateLogsColumns[9]},
				RefColumns: []*schema.Column{WmsApprovalTasksColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_operate_logs_wms_discard_meetings_logs",
				Columns:    []*schema.Column{WmsOperateLogsColumns[10]},
				RefColumns: []*schema.Column{WmsDiscardMeetingsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_operate_logs_wms_discard_plan_orders_logs",
				Columns:    []*schema.Column{WmsOperateLogsColumns[11]},
				RefColumns: []*schema.Column{WmsDiscardPlanOrdersColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_operate_logs_wms_enter_repository_orders_logs",
				Columns:    []*schema.Column{WmsOperateLogsColumns[12]},
				RefColumns: []*schema.Column{WmsEnterRepositoryOrdersColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_operate_logs_wms_maintain_plans_logs",
				Columns:    []*schema.Column{WmsOperateLogsColumns[13]},
				RefColumns: []*schema.Column{WmsMaintainPlansColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_operate_logs_wms_out_repository_orders_logs",
				Columns:    []*schema.Column{WmsOperateLogsColumns[14]},
				RefColumns: []*schema.Column{WmsOutRepositoryOrdersColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_operate_logs_wms_purchase_orders_logs",
				Columns:    []*schema.Column{WmsOperateLogsColumns[15]},
				RefColumns: []*schema.Column{WmsPurchaseOrdersColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsOutRepositoryOrdersColumns holds the columns for the "wms_out_repository_orders" table.
	WmsOutRepositoryOrdersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "contract_urls", Type: field.TypeString, Nullable: true, Comment: "合同附件"},
		{Name: "invoice_urls", Type: field.TypeString, Nullable: true, Comment: "发票附件"},
		{Name: "audit_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "other_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "order_no", Type: field.TypeString, Comment: "出库单号"},
		{Name: "type", Type: field.TypeString, Comment: "出库类型"},
		{Name: "out_time", Type: field.TypeTime, Comment: "出库时间"},
		{Name: "relation_order_no", Type: field.TypeString, Comment: "关联采购单号"},
		{Name: "status", Type: field.TypeString, Comment: "出库状态"},
		{Name: "equipment_num", Type: field.TypeUint32, Nullable: true, Comment: "设备数量"},
		{Name: "receiver", Type: field.TypeString, Nullable: true, Comment: "领用人"},
		{Name: "is_confirm_receive", Type: field.TypeBool, Nullable: true, Comment: "是否确认取货", Default: false},
		{Name: "sign_image", Type: field.TypeString, Nullable: true, Comment: "签名的图片, 存的是base64"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "出库仓库"},
	}
	// WmsOutRepositoryOrdersTable holds the schema information for the "wms_out_repository_orders" table.
	WmsOutRepositoryOrdersTable = &schema.Table{
		Name:       "wms_out_repository_orders",
		Comment:    "出库单",
		Columns:    WmsOutRepositoryOrdersColumns,
		PrimaryKey: []*schema.Column{WmsOutRepositoryOrdersColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_out_repository_orders_wms_repositories_repository",
				Columns:    []*schema.Column{WmsOutRepositoryOrdersColumns[19]},
				RefColumns: []*schema.Column{WmsRepositoriesColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsOutRepositoryOrderDetailsColumns holds the columns for the "wms_out_repository_order_details" table.
	WmsOutRepositoryOrderDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "material_id", Type: field.TypeString, Nullable: true, Comment: "资产ID"},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "设备名称"},
		{Name: "code", Type: field.TypeString, Nullable: true, Comment: "编码:RFID编码或二维码"},
		{Name: "code_type", Type: field.TypeString, Nullable: true, Comment: "编码类型"},
		{Name: "model_no", Type: field.TypeString, Nullable: true, Comment: "规格型号"},
		{Name: "provider_id", Type: field.TypeString, Comment: "供应商"},
		{Name: "num", Type: field.TypeUint32, Comment: "数量"},
		{Name: "price", Type: field.TypeUint64, Comment: "单价"},
		{Name: "order_no", Type: field.TypeString, Nullable: true, Comment: "出库单号"},
		{Name: "feature", Type: field.TypeJSON, Nullable: true, Comment: "详细规格"},
		{Name: "out_repository_order_id", Type: field.TypeString, Nullable: true, Comment: "出库单"},
		{Name: "equipment_type_id", Type: field.TypeString, Nullable: true, Comment: "设备类型"},
		{Name: "equipment_id", Type: field.TypeString, Nullable: true, Comment: "设备"},
		{Name: "measure_unit_id", Type: field.TypeString, Nullable: true, Comment: "计量单位"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "出库前仓库"},
		{Name: "repository_area_id", Type: field.TypeString, Nullable: true, Comment: "出库前库区"},
		{Name: "repository_position_id", Type: field.TypeString, Nullable: true, Comment: "出库前库位"},
	}
	// WmsOutRepositoryOrderDetailsTable holds the schema information for the "wms_out_repository_order_details" table.
	WmsOutRepositoryOrderDetailsTable = &schema.Table{
		Name:       "wms_out_repository_order_details",
		Comment:    "出库单明细",
		Columns:    WmsOutRepositoryOrderDetailsColumns,
		PrimaryKey: []*schema.Column{WmsOutRepositoryOrderDetailsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_out_repository_order_details_wms_out_repository_orders_details",
				Columns:    []*schema.Column{WmsOutRepositoryOrderDetailsColumns[16]},
				RefColumns: []*schema.Column{WmsOutRepositoryOrdersColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_out_repository_order_details_wms_equipment_types_equipment_type",
				Columns:    []*schema.Column{WmsOutRepositoryOrderDetailsColumns[17]},
				RefColumns: []*schema.Column{WmsEquipmentTypesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_out_repository_order_details_wms_equipments_equipment",
				Columns:    []*schema.Column{WmsOutRepositoryOrderDetailsColumns[18]},
				RefColumns: []*schema.Column{WmsEquipmentsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_out_repository_order_details_wms_measure_units_measure_unit",
				Columns:    []*schema.Column{WmsOutRepositoryOrderDetailsColumns[19]},
				RefColumns: []*schema.Column{WmsMeasureUnitsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_out_repository_order_details_wms_repositories_repository",
				Columns:    []*schema.Column{WmsOutRepositoryOrderDetailsColumns[20]},
				RefColumns: []*schema.Column{WmsRepositoriesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_out_repository_order_details_wms_repository_areas_repository_area",
				Columns:    []*schema.Column{WmsOutRepositoryOrderDetailsColumns[21]},
				RefColumns: []*schema.Column{WmsRepositoryAreasColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_out_repository_order_details_wms_repository_positions_repository_position",
				Columns:    []*schema.Column{WmsOutRepositoryOrderDetailsColumns[22]},
				RefColumns: []*schema.Column{WmsRepositoryPositionsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "wmsoutrepositoryorderdetail_out_repository_order_id_code",
				Unique:  true,
				Columns: []*schema.Column{WmsOutRepositoryOrderDetailsColumns[16], WmsOutRepositoryOrderDetailsColumns[8]},
			},
		},
	}
	// WmsPurchaseOrdersColumns holds the columns for the "wms_purchase_orders" table.
	WmsPurchaseOrdersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "contract_urls", Type: field.TypeString, Nullable: true, Comment: "合同附件"},
		{Name: "invoice_urls", Type: field.TypeString, Nullable: true, Comment: "发票附件"},
		{Name: "audit_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "other_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "order_no", Type: field.TypeString, Comment: "采购单号"},
		{Name: "reason", Type: field.TypeString, Comment: "申请事由"},
		{Name: "hope_end_time", Type: field.TypeTime, Nullable: true, Comment: "期望交付时间"},
		{Name: "hope_arrival_time", Type: field.TypeTime, Nullable: true, Comment: "预计到货时间"},
		{Name: "arrival_time", Type: field.TypeTime, Nullable: true, Comment: "实际到货时间"},
		{Name: "equipment_type_num", Type: field.TypeUint32, Nullable: true, Comment: "采购品类数"},
		{Name: "equipment_num", Type: field.TypeUint32, Nullable: true, Comment: "采购设备数"},
		{Name: "total_price", Type: field.TypeUint64, Nullable: true, Comment: "采购总价"},
		{Name: "is_notify_repository_admin", Type: field.TypeInt32, Comment: "是否通知仓库管理员", Default: 0},
		{Name: "status", Type: field.TypeInt32, Comment: "采购状态", Default: 0},
		{Name: "workflow_id", Type: field.TypeString, Nullable: true, Comment: "工作流ID"},
		{Name: "purchaser_user_id", Type: field.TypeString, Nullable: true, Comment: "采购员"},
	}
	// WmsPurchaseOrdersTable holds the schema information for the "wms_purchase_orders" table.
	WmsPurchaseOrdersTable = &schema.Table{
		Name:       "wms_purchase_orders",
		Comment:    "采购单",
		Columns:    WmsPurchaseOrdersColumns,
		PrimaryKey: []*schema.Column{WmsPurchaseOrdersColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_purchase_orders_sys_users_purchase_orders",
				Columns:    []*schema.Column{WmsPurchaseOrdersColumns[21]},
				RefColumns: []*schema.Column{SysUsersColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsPurchaseOrderDetailsColumns holds the columns for the "wms_purchase_order_details" table.
	WmsPurchaseOrderDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "equipment_type_id", Type: field.TypeString, Nullable: true, Comment: "设备类型"},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "设备名称"},
		{Name: "model_no", Type: field.TypeString, Nullable: true, Comment: "规格型号"},
		{Name: "num", Type: field.TypeUint32, Comment: "数量"},
		{Name: "price", Type: field.TypeUint64, Nullable: true, Comment: "单价"},
		{Name: "total_price", Type: field.TypeUint64, Nullable: true, Comment: "总价"},
		{Name: "feature", Type: field.TypeJSON, Nullable: true, Comment: "详细规格"},
		{Name: "purchase_order_id", Type: field.TypeString, Nullable: true, Comment: "采购单ID"},
		{Name: "equipment_id", Type: field.TypeString, Nullable: true, Comment: "设备"},
		{Name: "measure_unit_id", Type: field.TypeString, Nullable: true, Comment: "计量单位"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "入库仓库"},
		{Name: "provider_id", Type: field.TypeString, Nullable: true, Comment: "供应商"},
	}
	// WmsPurchaseOrderDetailsTable holds the schema information for the "wms_purchase_order_details" table.
	WmsPurchaseOrderDetailsTable = &schema.Table{
		Name:       "wms_purchase_order_details",
		Comment:    "采购单明细",
		Columns:    WmsPurchaseOrderDetailsColumns,
		PrimaryKey: []*schema.Column{WmsPurchaseOrderDetailsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_purchase_order_details_wms_purchase_orders_details",
				Columns:    []*schema.Column{WmsPurchaseOrderDetailsColumns[13]},
				RefColumns: []*schema.Column{WmsPurchaseOrdersColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_purchase_order_details_wms_equipments_equipment",
				Columns:    []*schema.Column{WmsPurchaseOrderDetailsColumns[14]},
				RefColumns: []*schema.Column{WmsEquipmentsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_purchase_order_details_wms_measure_units_measure_unit",
				Columns:    []*schema.Column{WmsPurchaseOrderDetailsColumns[15]},
				RefColumns: []*schema.Column{WmsMeasureUnitsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_purchase_order_details_wms_repositories_repository",
				Columns:    []*schema.Column{WmsPurchaseOrderDetailsColumns[16]},
				RefColumns: []*schema.Column{WmsRepositoriesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_purchase_order_details_wms_companies_provider",
				Columns:    []*schema.Column{WmsPurchaseOrderDetailsColumns[17]},
				RefColumns: []*schema.Column{WmsCompaniesColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsRepairOrdersColumns holds the columns for the "wms_repair_orders" table.
	WmsRepairOrdersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "order_no", Type: field.TypeString, Comment: "维修单号"},
		{Name: "equipment_num", Type: field.TypeUint32, Nullable: true, Comment: "装备数量"},
		{Name: "status", Type: field.TypeString, Nullable: true, Comment: "维修状态"},
		{Name: "repair_time", Type: field.TypeTime, Nullable: true, Comment: "维修时间"},
	}
	// WmsRepairOrdersTable holds the schema information for the "wms_repair_orders" table.
	WmsRepairOrdersTable = &schema.Table{
		Name:       "wms_repair_orders",
		Comment:    "维修单",
		Columns:    WmsRepairOrdersColumns,
		PrimaryKey: []*schema.Column{WmsRepairOrdersColumns[0]},
	}
	// WmsRepairOrderDetailsColumns holds the columns for the "wms_repair_order_details" table.
	WmsRepairOrderDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "order_id", Type: field.TypeString, Nullable: true, Comment: "维修单"},
		{Name: "material_id", Type: field.TypeString, Nullable: true, Comment: "物料"},
		{Name: "material_name", Type: field.TypeString, Nullable: true, Comment: "物料名称"},
		{Name: "code", Type: field.TypeString, Comment: "编码"},
		{Name: "equipment_id", Type: field.TypeString, Nullable: true, Comment: "装备"},
		{Name: "equipment_type_id", Type: field.TypeString, Nullable: true, Comment: "装备类型"},
		{Name: "feature", Type: field.TypeJSON, Nullable: true, Comment: "详细规格"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "仓库"},
		{Name: "repository_area_id", Type: field.TypeString, Nullable: true, Comment: "库区"},
		{Name: "repository_position_id", Type: field.TypeString, Nullable: true, Comment: "库位"},
		{Name: "owner_id", Type: field.TypeString, Nullable: true, Comment: "归属人"},
		{Name: "model_no", Type: field.TypeString, Comment: "规格型号"},
		{Name: "measure_unit_id", Type: field.TypeString, Nullable: true, Comment: "计量单位"},
		{Name: "num", Type: field.TypeUint32, Comment: "维修数量"},
		{Name: "repair_type", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "维修类型"},
		{Name: "repair_reason", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "故障描述"},
		{Name: "image_urls", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "故障图片"},
		{Name: "repair_time", Type: field.TypeTime, Nullable: true, Comment: "维修时间"},
	}
	// WmsRepairOrderDetailsTable holds the schema information for the "wms_repair_order_details" table.
	WmsRepairOrderDetailsTable = &schema.Table{
		Name:       "wms_repair_order_details",
		Comment:    "维修单明细",
		Columns:    WmsRepairOrderDetailsColumns,
		PrimaryKey: []*schema.Column{WmsRepairOrderDetailsColumns[0]},
	}
	// WmsRepairSettlementOrdersColumns holds the columns for the "wms_repair_settlement_orders" table.
	WmsRepairSettlementOrdersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "repair_company", Type: field.TypeString, Nullable: true, Comment: "承修方"},
		{Name: "repair_company_address", Type: field.TypeString, Nullable: true, Comment: "承修方地址"},
		{Name: "repair_company_bank", Type: field.TypeString, Nullable: true, Comment: "承修方开户行"},
		{Name: "repair_company_bank_account", Type: field.TypeString, Nullable: true, Comment: "承修方银行账号"},
		{Name: "repair_company_phone", Type: field.TypeString, Nullable: true, Comment: "承修方电话"},
		{Name: "repair_company_email", Type: field.TypeString, Nullable: true, Comment: "承修方邮箱"},
		{Name: "repair_company_site", Type: field.TypeString, Nullable: true, Comment: "承修方网址"},
		{Name: "repair_company_fax", Type: field.TypeString, Nullable: true, Comment: "承修方传真"},
		{Name: "repaired_company", Type: field.TypeString, Nullable: true, Comment: "被修方"},
		{Name: "transport_person", Type: field.TypeString, Nullable: true, Comment: "送修人"},
		{Name: "transport_person_phone", Type: field.TypeString, Nullable: true, Comment: "送修人电话"},
		{Name: "execute_no", Type: field.TypeString, Nullable: true, Comment: "施工编号"},
		{Name: "enter_time", Type: field.TypeTime, Nullable: true, Comment: "进厂时间"},
		{Name: "leave_km", Type: field.TypeString, Nullable: true, Comment: "出厂里程"},
		{Name: "work_hours_standard", Type: field.TypeUint32, Nullable: true, Comment: "工时定额"},
		{Name: "vehicle_no", Type: field.TypeString, Nullable: true, Comment: "车牌号"},
		{Name: "brand_model", Type: field.TypeString, Nullable: true, Comment: "厂牌型号"},
		{Name: "leave_time", Type: field.TypeTime, Nullable: true, Comment: "出厂时间"},
		{Name: "execute_standard", Type: field.TypeUint32, Nullable: true, Comment: "执行标准"},
		{Name: "contract_no", Type: field.TypeString, Nullable: true, Comment: "合同号"},
		{Name: "qualified_no", Type: field.TypeString, Nullable: true, Comment: "合格证号"},
		{Name: "category", Type: field.TypeString, Nullable: true, Comment: "分类代号"},
		{Name: "settlement_price", Type: field.TypeUint64, Nullable: true, Comment: "结算价格"},
		{Name: "should_pay_price", Type: field.TypeUint64, Nullable: true, Comment: "应付价格"},
		{Name: "total_work_hours", Type: field.TypeUint32, Nullable: true, Comment: "总工时定额"},
		{Name: "total_work_price", Type: field.TypeUint64, Nullable: true, Comment: "总工时费"},
		{Name: "promise_using_days", Type: field.TypeString, Nullable: true, Comment: "承诺行驶天数"},
		{Name: "promise_using_km", Type: field.TypeString, Nullable: true, Comment: "承诺行驶公里数"},
		{Name: "is_old_confirm_and_recover", Type: field.TypeUint32, Nullable: true, Comment: "旧件已确认，并由托修方收回"},
		{Name: "is_old_confirm_and_giveup", Type: field.TypeUint32, Nullable: true, Comment: "旧件已确认，托修方声明放弃"},
		{Name: "is_none_old", Type: field.TypeUint32, Nullable: true, Comment: "是否无旧件"},
		{Name: "settlementer", Type: field.TypeString, Nullable: true, Comment: "结算人"},
		{Name: "settlement_time", Type: field.TypeTime, Nullable: true, Comment: "结算时间"},
		{Name: "customer", Type: field.TypeString, Nullable: true, Comment: "客户"},
		{Name: "file_url", Type: field.TypeString, Nullable: true, Comment: "文件"},
	}
	// WmsRepairSettlementOrdersTable holds the schema information for the "wms_repair_settlement_orders" table.
	WmsRepairSettlementOrdersTable = &schema.Table{
		Name:       "wms_repair_settlement_orders",
		Comment:    "维修结算单",
		Columns:    WmsRepairSettlementOrdersColumns,
		PrimaryKey: []*schema.Column{WmsRepairSettlementOrdersColumns[0]},
	}
	// WmsRepairSettlementOrderSettlementfeeDetailsColumns holds the columns for the "wms_repair_settlement_order_settlementfee_details" table.
	WmsRepairSettlementOrderSettlementfeeDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "order_id", Type: field.TypeString, Nullable: true, Comment: "维修结算单ID"},
		{Name: "no", Type: field.TypeString, Nullable: true, Comment: "序号"},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "名称"},
		{Name: "price", Type: field.TypeUint64, Nullable: true, Comment: "价格"},
	}
	// WmsRepairSettlementOrderSettlementfeeDetailsTable holds the schema information for the "wms_repair_settlement_order_settlementfee_details" table.
	WmsRepairSettlementOrderSettlementfeeDetailsTable = &schema.Table{
		Name:       "wms_repair_settlement_order_settlementfee_details",
		Comment:    "维修结算单结算价格详情",
		Columns:    WmsRepairSettlementOrderSettlementfeeDetailsColumns,
		PrimaryKey: []*schema.Column{WmsRepairSettlementOrderSettlementfeeDetailsColumns[0]},
	}
	// WmsRepairSettlementOrderWorkfeeDetailsColumns holds the columns for the "wms_repair_settlement_order_workfee_details" table.
	WmsRepairSettlementOrderWorkfeeDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "order_id", Type: field.TypeString, Nullable: true, Comment: "维修结算单ID"},
		{Name: "no", Type: field.TypeString, Nullable: true, Comment: "项目序号"},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "作业项目"},
		{Name: "hours", Type: field.TypeUint32, Nullable: true, Comment: "工时定额"},
		{Name: "price", Type: field.TypeUint64, Nullable: true, Comment: "工时单价"},
		{Name: "total", Type: field.TypeUint64, Nullable: true, Comment: "金额"},
	}
	// WmsRepairSettlementOrderWorkfeeDetailsTable holds the schema information for the "wms_repair_settlement_order_workfee_details" table.
	WmsRepairSettlementOrderWorkfeeDetailsTable = &schema.Table{
		Name:       "wms_repair_settlement_order_workfee_details",
		Comment:    "维修结算单工时费详情",
		Columns:    WmsRepairSettlementOrderWorkfeeDetailsColumns,
		PrimaryKey: []*schema.Column{WmsRepairSettlementOrderWorkfeeDetailsColumns[0]},
	}
	// WmsRepositoriesColumns holds the columns for the "wms_repositories" table.
	WmsRepositoriesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "area_code", Type: field.TypeString, Nullable: true, Comment: "区域编码"},
		{Name: "area_name", Type: field.TypeString, Nullable: true, Comment: "区域名称"},
		{Name: "address", Type: field.TypeString, Nullable: true, Comment: "详细地址"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "code", Type: field.TypeString, Comment: "仓库编码"},
		{Name: "name", Type: field.TypeString, Comment: "仓库名称"},
		{Name: "capacity", Type: field.TypeString, Comment: "仓库容量"},
		{Name: "gis_x", Type: field.TypeString, Nullable: true, Comment: "经度"},
		{Name: "gis_y", Type: field.TypeString, Nullable: true, Comment: "纬度"},
		{Name: "images", Type: field.TypeString, Nullable: true, Comment: "图片"},
		{Name: "wms_repository_screen_repository", Type: field.TypeString, Nullable: true},
	}
	// WmsRepositoriesTable holds the schema information for the "wms_repositories" table.
	WmsRepositoriesTable = &schema.Table{
		Name:       "wms_repositories",
		Comment:    "仓库表",
		Columns:    WmsRepositoriesColumns,
		PrimaryKey: []*schema.Column{WmsRepositoriesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_repositories_wms_repository_screens_repository",
				Columns:    []*schema.Column{WmsRepositoriesColumns[16]},
				RefColumns: []*schema.Column{WmsRepositoryScreensColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsRepositoryAdminsColumns holds the columns for the "wms_repository_admins" table.
	WmsRepositoryAdminsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "仓库"},
		{Name: "user_id", Type: field.TypeString, Nullable: true, Comment: "用户"},
	}
	// WmsRepositoryAdminsTable holds the schema information for the "wms_repository_admins" table.
	WmsRepositoryAdminsTable = &schema.Table{
		Name:       "wms_repository_admins",
		Comment:    "仓库管理员表",
		Columns:    WmsRepositoryAdminsColumns,
		PrimaryKey: []*schema.Column{WmsRepositoryAdminsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_repository_admins_wms_repositories_admins",
				Columns:    []*schema.Column{WmsRepositoryAdminsColumns[7]},
				RefColumns: []*schema.Column{WmsRepositoriesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_repository_admins_sys_users_user",
				Columns:    []*schema.Column{WmsRepositoryAdminsColumns[8]},
				RefColumns: []*schema.Column{SysUsersColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsRepositoryAreasColumns holds the columns for the "wms_repository_areas" table.
	WmsRepositoryAreasColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "code", Type: field.TypeString, Comment: "库区编码"},
		{Name: "name", Type: field.TypeString, Comment: "库区名称"},
		{Name: "area", Type: field.TypeString, Comment: "库区面积"},
		{Name: "images", Type: field.TypeJSON, Nullable: true, Comment: "图片"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "所属仓库"},
	}
	// WmsRepositoryAreasTable holds the schema information for the "wms_repository_areas" table.
	WmsRepositoryAreasTable = &schema.Table{
		Name:       "wms_repository_areas",
		Comment:    "仓库库区表",
		Columns:    WmsRepositoryAreasColumns,
		PrimaryKey: []*schema.Column{WmsRepositoryAreasColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_repository_areas_wms_repositories_areas",
				Columns:    []*schema.Column{WmsRepositoryAreasColumns[11]},
				RefColumns: []*schema.Column{WmsRepositoriesColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsRepositoryPositionsColumns holds the columns for the "wms_repository_positions" table.
	WmsRepositoryPositionsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "code", Type: field.TypeString, Comment: "库位编码"},
		{Name: "name", Type: field.TypeString, Comment: "库位名称"},
		{Name: "images", Type: field.TypeJSON, Nullable: true, Comment: "图片"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "所属仓库"},
		{Name: "repository_area_id", Type: field.TypeString, Nullable: true, Comment: "所属库区"},
	}
	// WmsRepositoryPositionsTable holds the schema information for the "wms_repository_positions" table.
	WmsRepositoryPositionsTable = &schema.Table{
		Name:       "wms_repository_positions",
		Comment:    "仓库库位表",
		Columns:    WmsRepositoryPositionsColumns,
		PrimaryKey: []*schema.Column{WmsRepositoryPositionsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_repository_positions_wms_repositories_positions",
				Columns:    []*schema.Column{WmsRepositoryPositionsColumns[10]},
				RefColumns: []*schema.Column{WmsRepositoriesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wms_repository_positions_wms_repository_areas_positions",
				Columns:    []*schema.Column{WmsRepositoryPositionsColumns[11]},
				RefColumns: []*schema.Column{WmsRepositoryAreasColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WmsRepositoryScreensColumns holds the columns for the "wms_repository_screens" table.
	WmsRepositoryScreensColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "no", Type: field.TypeString, Comment: "分屏编码"},
		{Name: "repository_id", Type: field.TypeString, Comment: "所属仓库"},
		{Name: "name", Type: field.TypeString, Comment: "分屏名称"},
		{Name: "equipment_type_carousel_interval", Type: field.TypeUint32, Nullable: true, Comment: "装备类型轮播时间", Default: 0},
		{Name: "equipment_type_sub_carousel_interval", Type: field.TypeUint32, Nullable: true, Comment: "装备类型子分类轮播时间", Default: 0},
		{Name: "material_carousel_interval", Type: field.TypeUint32, Nullable: true, Comment: "装备详情轮播时间", Default: 0},
	}
	// WmsRepositoryScreensTable holds the schema information for the "wms_repository_screens" table.
	WmsRepositoryScreensTable = &schema.Table{
		Name:       "wms_repository_screens",
		Comment:    "库区分屏",
		Columns:    WmsRepositoryScreensColumns,
		PrimaryKey: []*schema.Column{WmsRepositoryScreensColumns[0]},
	}
	// WmsRepositoryScreenRepositoryAreasColumns holds the columns for the "wms_repository_screen_repository_areas" table.
	WmsRepositoryScreenRepositoryAreasColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "repository_screen_id", Type: field.TypeString, Comment: "库区分屏ID"},
		{Name: "repository_area_id", Type: field.TypeString, Comment: "库区ID"},
	}
	// WmsRepositoryScreenRepositoryAreasTable holds the schema information for the "wms_repository_screen_repository_areas" table.
	WmsRepositoryScreenRepositoryAreasTable = &schema.Table{
		Name:       "wms_repository_screen_repository_areas",
		Comment:    "库区分屏库区",
		Columns:    WmsRepositoryScreenRepositoryAreasColumns,
		PrimaryKey: []*schema.Column{WmsRepositoryScreenRepositoryAreasColumns[0]},
	}
	// WmsReturnOrdersColumns holds the columns for the "wms_return_orders" table.
	WmsReturnOrdersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "contract_urls", Type: field.TypeString, Nullable: true, Comment: "合同附件"},
		{Name: "invoice_urls", Type: field.TypeString, Nullable: true, Comment: "发票附件"},
		{Name: "audit_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "other_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "order_no", Type: field.TypeString, Comment: "退还单号"},
		{Name: "repository_id", Type: field.TypeString, Comment: "仓库id"},
		{Name: "reason", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "退还原因"},
		{Name: "equipment_num", Type: field.TypeUint32, Nullable: true, Comment: "装备数量"},
		{Name: "status", Type: field.TypeString, Nullable: true, Comment: "退还状态"},
		{Name: "return_time", Type: field.TypeTime, Nullable: true, Comment: "退还时间"},
	}
	// WmsReturnOrdersTable holds the schema information for the "wms_return_orders" table.
	WmsReturnOrdersTable = &schema.Table{
		Name:       "wms_return_orders",
		Comment:    "退还单",
		Columns:    WmsReturnOrdersColumns,
		PrimaryKey: []*schema.Column{WmsReturnOrdersColumns[0]},
	}
	// WmsReturnOrderDetailsColumns holds the columns for the "wms_return_order_details" table.
	WmsReturnOrderDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "order_id", Type: field.TypeString, Nullable: true, Comment: "退还单"},
		{Name: "material_id", Type: field.TypeString, Nullable: true, Comment: "物料"},
		{Name: "material_name", Type: field.TypeString, Nullable: true, Comment: "物料名称"},
		{Name: "code", Type: field.TypeString, Comment: "编码"},
		{Name: "equipment_id", Type: field.TypeString, Nullable: true, Comment: "装备"},
		{Name: "equipment_type_id", Type: field.TypeString, Nullable: true, Comment: "装备类型"},
		{Name: "feature", Type: field.TypeJSON, Nullable: true, Comment: "详细规格"},
		{Name: "owner_id", Type: field.TypeString, Nullable: true, Comment: "归属人"},
		{Name: "model_no", Type: field.TypeString, Comment: "规格型号"},
		{Name: "measure_unit_id", Type: field.TypeString, Nullable: true, Comment: "计量单位"},
		{Name: "num", Type: field.TypeUint32, Comment: "退还数量"},
		{Name: "to_repository_id", Type: field.TypeString, Nullable: true, Comment: "仓库"},
		{Name: "to_repository_area_id", Type: field.TypeString, Nullable: true, Comment: "库区"},
		{Name: "to_repository_position_id", Type: field.TypeString, Nullable: true, Comment: "库位"},
		{Name: "reason", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "退还原因"},
		{Name: "return_time", Type: field.TypeTime, Nullable: true, Comment: "退还时间"},
	}
	// WmsReturnOrderDetailsTable holds the schema information for the "wms_return_order_details" table.
	WmsReturnOrderDetailsTable = &schema.Table{
		Name:       "wms_return_order_details",
		Comment:    "退还单明细",
		Columns:    WmsReturnOrderDetailsColumns,
		PrimaryKey: []*schema.Column{WmsReturnOrderDetailsColumns[0]},
	}
	// WmsRfidsColumns holds the columns for the "wms_rfids" table.
	WmsRfidsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "code", Type: field.TypeString, Unique: true, Comment: "RFID编码"},
		{Name: "is_used", Type: field.TypeBool, Comment: "是否已使用", Default: false},
		{Name: "locked_at", Type: field.TypeTime, Nullable: true, Comment: "锁定时间"},
	}
	// WmsRfidsTable holds the schema information for the "wms_rfids" table.
	WmsRfidsTable = &schema.Table{
		Name:       "wms_rfids",
		Comment:    "RFID编码池表",
		Columns:    WmsRfidsColumns,
		PrimaryKey: []*schema.Column{WmsRfidsColumns[0]},
	}
	// WmsRfidReadersColumns holds the columns for the "wms_rfid_readers" table.
	WmsRfidReadersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "sort", Type: field.TypeInt32, Nullable: true, Comment: "排序", Default: 0},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "type", Type: field.TypeEnum, Comment: "扫描器类型", Enums: []string{"Gun", "Door"}, Default: "Gun"},
		{Name: "code", Type: field.TypeString, Unique: true, Comment: "扫描器编码"},
		{Name: "name", Type: field.TypeString, Comment: "扫描器名称"},
		{Name: "description", Type: field.TypeString, Nullable: true, Comment: "描述"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "仓库"},
		{Name: "alarm_user_ids", Type: field.TypeString, Nullable: true, Comment: "报警用户"},
		{Name: "use_user_id", Type: field.TypeString, Nullable: true, Comment: "使用用户"},
	}
	// WmsRfidReadersTable holds the schema information for the "wms_rfid_readers" table.
	WmsRfidReadersTable = &schema.Table{
		Name:       "wms_rfid_readers",
		Comment:    "RFID扫描器表",
		Columns:    WmsRfidReadersColumns,
		PrimaryKey: []*schema.Column{WmsRfidReadersColumns[0]},
	}
	// WmsRfidReaderRecordsColumns holds the columns for the "wms_rfid_reader_records" table.
	WmsRfidReaderRecordsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "rfid_code", Type: field.TypeString, Comment: "RFID编码"},
		{Name: "direction", Type: field.TypeEnum, Comment: "方向", Enums: []string{"Inward", "Outward"}, Default: "Inward"},
		{Name: "reader_id", Type: field.TypeString, Comment: "扫描器ID"},
	}
	// WmsRfidReaderRecordsTable holds the schema information for the "wms_rfid_reader_records" table.
	WmsRfidReaderRecordsTable = &schema.Table{
		Name:       "wms_rfid_reader_records",
		Comment:    "RFID扫描记录表",
		Columns:    WmsRfidReaderRecordsColumns,
		PrimaryKey: []*schema.Column{WmsRfidReaderRecordsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_rfid_reader_records_wms_rfid_readers_records",
				Columns:    []*schema.Column{WmsRfidReaderRecordsColumns[8]},
				RefColumns: []*schema.Column{WmsRfidReadersColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
	}
	// WmsTransferOrdersColumns holds the columns for the "wms_transfer_orders" table.
	WmsTransferOrdersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "contract_urls", Type: field.TypeString, Nullable: true, Comment: "合同附件"},
		{Name: "invoice_urls", Type: field.TypeString, Nullable: true, Comment: "发票附件"},
		{Name: "audit_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "other_urls", Type: field.TypeString, Nullable: true, Comment: "会审凭证附件"},
		{Name: "order_no", Type: field.TypeString, Comment: "调拨单号"},
		{Name: "equipment_num", Type: field.TypeUint32, Nullable: true, Comment: "装备数量"},
		{Name: "from_repository_id", Type: field.TypeString, Nullable: true, Comment: "调出仓库"},
		{Name: "to_repository_id", Type: field.TypeString, Nullable: true, Comment: "调入仓库"},
		{Name: "status", Type: field.TypeString, Nullable: true, Comment: "调拨状态"},
		{Name: "transfer_time", Type: field.TypeTime, Nullable: true, Comment: "调拨时间"},
	}
	// WmsTransferOrdersTable holds the schema information for the "wms_transfer_orders" table.
	WmsTransferOrdersTable = &schema.Table{
		Name:       "wms_transfer_orders",
		Comment:    "调拨单",
		Columns:    WmsTransferOrdersColumns,
		PrimaryKey: []*schema.Column{WmsTransferOrdersColumns[0]},
	}
	// WmsTransferOrderDetailsColumns holds the columns for the "wms_transfer_order_details" table.
	WmsTransferOrderDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "order_id", Type: field.TypeString, Nullable: true, Comment: "调拨单"},
		{Name: "material_id", Type: field.TypeString, Nullable: true, Comment: "物料"},
		{Name: "material_name", Type: field.TypeString, Nullable: true, Comment: "物料名称"},
		{Name: "code", Type: field.TypeString, Comment: "编码"},
		{Name: "equipment_id", Type: field.TypeString, Nullable: true, Comment: "装备"},
		{Name: "equipment_type_id", Type: field.TypeString, Nullable: true, Comment: "装备类型"},
		{Name: "feature", Type: field.TypeJSON, Nullable: true, Comment: "详细规格"},
		{Name: "repository_id", Type: field.TypeString, Nullable: true, Comment: "仓库"},
		{Name: "repository_area_id", Type: field.TypeString, Nullable: true, Comment: "库区"},
		{Name: "repository_position_id", Type: field.TypeString, Nullable: true, Comment: "库位"},
		{Name: "owner_id", Type: field.TypeString, Nullable: true, Comment: "归属人"},
		{Name: "model_no", Type: field.TypeString, Comment: "规格型号"},
		{Name: "measure_unit_id", Type: field.TypeString, Nullable: true, Comment: "计量单位"},
		{Name: "num", Type: field.TypeUint32, Comment: "调拨数量"},
		{Name: "transfer_reason", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "调拨原因"},
		{Name: "transfer_time", Type: field.TypeTime, Nullable: true, Comment: "调拨时间"},
	}
	// WmsTransferOrderDetailsTable holds the schema information for the "wms_transfer_order_details" table.
	WmsTransferOrderDetailsTable = &schema.Table{
		Name:       "wms_transfer_order_details",
		Comment:    "调拨单明细",
		Columns:    WmsTransferOrderDetailsColumns,
		PrimaryKey: []*schema.Column{WmsTransferOrderDetailsColumns[0]},
	}
	// WmsVehicleRepairOrdersColumns holds the columns for the "wms_vehicle_repair_orders" table.
	WmsVehicleRepairOrdersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "repair_company", Type: field.TypeString, Nullable: true, Comment: "承修方"},
		{Name: "repair_company_address", Type: field.TypeString, Nullable: true, Comment: "承修方地址"},
		{Name: "repair_company_bank", Type: field.TypeString, Nullable: true, Comment: "承修方开户行"},
		{Name: "repair_company_bank_account", Type: field.TypeString, Nullable: true, Comment: "承修方银行账号"},
		{Name: "repair_company_phone", Type: field.TypeString, Nullable: true, Comment: "承修方电话"},
		{Name: "repair_company_email", Type: field.TypeString, Nullable: true, Comment: "承修方邮箱"},
		{Name: "repair_company_site", Type: field.TypeString, Nullable: true, Comment: "承修方网址"},
		{Name: "repair_company_fax", Type: field.TypeString, Nullable: true, Comment: "承修方传真"},
		{Name: "repaired_company", Type: field.TypeString, Nullable: true, Comment: "被修方"},
		{Name: "transport_person", Type: field.TypeString, Nullable: true, Comment: "送修人"},
		{Name: "transport_person_phone", Type: field.TypeString, Nullable: true, Comment: "送修人电话"},
		{Name: "execute_no", Type: field.TypeString, Nullable: true, Comment: "施工编号"},
		{Name: "enter_time", Type: field.TypeTime, Nullable: true, Comment: "进厂时间"},
		{Name: "leave_km", Type: field.TypeString, Nullable: true, Comment: "出厂里程"},
		{Name: "work_hours_standard", Type: field.TypeUint32, Nullable: true, Comment: "工时定额"},
		{Name: "vehicle_no", Type: field.TypeString, Nullable: true, Comment: "车牌号"},
		{Name: "brand_model", Type: field.TypeString, Nullable: true, Comment: "厂牌型号"},
		{Name: "leave_time", Type: field.TypeTime, Nullable: true, Comment: "出厂时间"},
		{Name: "execute_standard", Type: field.TypeUint32, Nullable: true, Comment: "执行标准"},
		{Name: "contract_no", Type: field.TypeString, Nullable: true, Comment: "合同号"},
		{Name: "qualified_no", Type: field.TypeString, Nullable: true, Comment: "合格证号"},
		{Name: "category", Type: field.TypeString, Nullable: true, Comment: "分类代号"},
		{Name: "settlement_price", Type: field.TypeUint64, Nullable: true, Comment: "结算价格"},
		{Name: "should_pay_price", Type: field.TypeUint64, Nullable: true, Comment: "应付价格"},
		{Name: "total_work_hours", Type: field.TypeUint32, Nullable: true, Comment: "总工时定额"},
		{Name: "total_work_price", Type: field.TypeUint64, Nullable: true, Comment: "总工时费"},
		{Name: "total_material_num", Type: field.TypeUint32, Nullable: true, Comment: "总材料数量"},
		{Name: "total_material_price", Type: field.TypeUint64, Nullable: true, Comment: "总材料费"},
		{Name: "promise_using_days", Type: field.TypeString, Nullable: true, Comment: "承诺行驶天数"},
		{Name: "promise_using_km", Type: field.TypeString, Nullable: true, Comment: "承诺行驶公里数"},
		{Name: "is_old_confirm_and_recover", Type: field.TypeUint32, Nullable: true, Comment: "旧件已确认，并由托修方收回"},
		{Name: "is_old_confirm_and_giveup", Type: field.TypeUint32, Nullable: true, Comment: "旧件已确认，托修方声明放弃"},
		{Name: "is_none_old", Type: field.TypeUint32, Nullable: true, Comment: "是否无旧件"},
		{Name: "settlementer", Type: field.TypeString, Nullable: true, Comment: "结算人"},
		{Name: "settlement_time", Type: field.TypeTime, Nullable: true, Comment: "结算时间"},
		{Name: "customer", Type: field.TypeString, Nullable: true, Comment: "客户"},
		{Name: "file_url", Type: field.TypeString, Nullable: true, Comment: "文件"},
	}
	// WmsVehicleRepairOrdersTable holds the schema information for the "wms_vehicle_repair_orders" table.
	WmsVehicleRepairOrdersTable = &schema.Table{
		Name:       "wms_vehicle_repair_orders",
		Comment:    "车辆维修保养单",
		Columns:    WmsVehicleRepairOrdersColumns,
		PrimaryKey: []*schema.Column{WmsVehicleRepairOrdersColumns[0]},
	}
	// WmsVehicleRepairOrderMaterialfeeDetailsColumns holds the columns for the "wms_vehicle_repair_order_materialfee_details" table.
	WmsVehicleRepairOrderMaterialfeeDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "order_id", Type: field.TypeString, Nullable: true, Comment: "车辆维修保养单ID"},
		{Name: "no", Type: field.TypeString, Nullable: true, Comment: "配件编号"},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "配件名称"},
		{Name: "measure_unit", Type: field.TypeString, Nullable: true, Comment: "计量单位"},
		{Name: "num", Type: field.TypeUint32, Nullable: true, Comment: "数量"},
		{Name: "price", Type: field.TypeUint64, Nullable: true, Comment: "单价"},
		{Name: "total", Type: field.TypeUint64, Nullable: true, Comment: "金额"},
		{Name: "type", Type: field.TypeString, Nullable: true, Comment: "配件类型"},
	}
	// WmsVehicleRepairOrderMaterialfeeDetailsTable holds the schema information for the "wms_vehicle_repair_order_materialfee_details" table.
	WmsVehicleRepairOrderMaterialfeeDetailsTable = &schema.Table{
		Name:       "wms_vehicle_repair_order_materialfee_details",
		Comment:    "车辆维修保养单材料费详情",
		Columns:    WmsVehicleRepairOrderMaterialfeeDetailsColumns,
		PrimaryKey: []*schema.Column{WmsVehicleRepairOrderMaterialfeeDetailsColumns[0]},
	}
	// WmsVehicleRepairOrderSettlementfeeDetailsColumns holds the columns for the "wms_vehicle_repair_order_settlementfee_details" table.
	WmsVehicleRepairOrderSettlementfeeDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "order_id", Type: field.TypeString, Nullable: true, Comment: "车辆维修保养单ID"},
		{Name: "no", Type: field.TypeString, Nullable: true, Comment: "序号"},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "名称"},
		{Name: "price", Type: field.TypeUint64, Nullable: true, Comment: "价格"},
	}
	// WmsVehicleRepairOrderSettlementfeeDetailsTable holds the schema information for the "wms_vehicle_repair_order_settlementfee_details" table.
	WmsVehicleRepairOrderSettlementfeeDetailsTable = &schema.Table{
		Name:       "wms_vehicle_repair_order_settlementfee_details",
		Comment:    "车辆维修保养单结算价格详情",
		Columns:    WmsVehicleRepairOrderSettlementfeeDetailsColumns,
		PrimaryKey: []*schema.Column{WmsVehicleRepairOrderSettlementfeeDetailsColumns[0]},
	}
	// WmsVehicleRepairOrderWorkfeeDetailsColumns holds the columns for the "wms_vehicle_repair_order_workfee_details" table.
	WmsVehicleRepairOrderWorkfeeDetailsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "order_id", Type: field.TypeString, Nullable: true, Comment: "车辆维修保养单ID"},
		{Name: "no", Type: field.TypeString, Nullable: true, Comment: "项目序号"},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "作业项目"},
		{Name: "hours", Type: field.TypeUint32, Nullable: true, Comment: "工时定额"},
		{Name: "price", Type: field.TypeUint64, Nullable: true, Comment: "工时单价"},
		{Name: "total", Type: field.TypeUint64, Nullable: true, Comment: "金额"},
	}
	// WmsVehicleRepairOrderWorkfeeDetailsTable holds the schema information for the "wms_vehicle_repair_order_workfee_details" table.
	WmsVehicleRepairOrderWorkfeeDetailsTable = &schema.Table{
		Name:       "wms_vehicle_repair_order_workfee_details",
		Comment:    "车辆维修保养单工时费详情",
		Columns:    WmsVehicleRepairOrderWorkfeeDetailsColumns,
		PrimaryKey: []*schema.Column{WmsVehicleRepairOrderWorkfeeDetailsColumns[0]},
	}
	// WorkWxApprovalMessagesColumns holds the columns for the "work_wx_approval_messages" table.
	WorkWxApprovalMessagesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注"},
		{Name: "status", Type: field.TypeInt32, Comment: "状态：0 禁用、 1启用", Default: 0},
		{Name: "to_user_name", Type: field.TypeString, Comment: "接收消息的企业微信CorpID"},
		{Name: "from_user_name", Type: field.TypeString, Comment: "发送消息的企业微信账号ID"},
		{Name: "create_time", Type: field.TypeInt64, Comment: "消息创建时间（Unix时间戳）"},
		{Name: "msg_type", Type: field.TypeString, Comment: "消息类型"},
		{Name: "event", Type: field.TypeString, Comment: "事件类型"},
		{Name: "agent_id", Type: field.TypeInt, Comment: "企业应用ID"},
		{Name: "third_no", Type: field.TypeString, Unique: true, Comment: "审批单编号"},
		{Name: "sp_type", Type: field.TypeString, Comment: "审批类型"},
		{Name: "open_sp_name", Type: field.TypeString, Comment: "审批模板名称"},
		{Name: "open_template_id", Type: field.TypeString, Comment: "审批模板ID"},
		{Name: "open_sp_status", Type: field.TypeInt, Comment: "审批状态：1-审批中；2-已通过；3-已驳回；4-已撤销；6-通过后撤销；7-已删除；10-已支付"},
		{Name: "apply_time", Type: field.TypeInt64, Comment: "提交审批时间（Unix时间戳）"},
		{Name: "apply_user_name", Type: field.TypeString, Comment: "提交审批人姓名"},
		{Name: "apply_user_id", Type: field.TypeString, Comment: "提交审批人UserID"},
		{Name: "apply_user_party", Type: field.TypeString, Comment: "提交审批人所在部门"},
		{Name: "apply_user_image", Type: field.TypeString, Comment: "提交审批人头像URL"},
		{Name: "approver_step", Type: field.TypeInt, Comment: "审批流程当前审批节点次序（从0开始）"},
	}
	// WorkWxApprovalMessagesTable holds the schema information for the "work_wx_approval_messages" table.
	WorkWxApprovalMessagesTable = &schema.Table{
		Name:       "work_wx_approval_messages",
		Comment:    "企业微信审批消息",
		Columns:    WorkWxApprovalMessagesColumns,
		PrimaryKey: []*schema.Column{WorkWxApprovalMessagesColumns[0]},
	}
	// WorkWxApprovalNodesColumns holds the columns for the "work_wx_approval_nodes" table.
	WorkWxApprovalNodesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "node_status", Type: field.TypeInt, Comment: "节点状态：1-审批中；2-已同意；3-已驳回；4-已转审"},
		{Name: "node_attr", Type: field.TypeInt, Comment: "节点类型：1-或签；2-会签；3-单签"},
		{Name: "node_type", Type: field.TypeInt, Comment: "审批节点类型：1-固定成员；2-标签；3-上级；4-上级的上级...以此类推"},
		{Name: "item_name", Type: field.TypeString, Comment: "审批人姓名"},
		{Name: "item_image", Type: field.TypeString, Comment: "审批人头像URL"},
		{Name: "item_user_id", Type: field.TypeString, Comment: "审批人UserID"},
		{Name: "item_status", Type: field.TypeInt, Comment: "审批状态：1-审批中；2-已同意；3-已驳回；4-已转审"},
		{Name: "item_speech", Type: field.TypeString, Nullable: true, Comment: "审批意见"},
		{Name: "item_op_time", Type: field.TypeInt64, Comment: "操作时间（Unix时间戳）"},
		{Name: "approval_message_id", Type: field.TypeString, Comment: "关联的审批消息ID"},
	}
	// WorkWxApprovalNodesTable holds the schema information for the "work_wx_approval_nodes" table.
	WorkWxApprovalNodesTable = &schema.Table{
		Name:       "work_wx_approval_nodes",
		Comment:    "企业微信审批节点",
		Columns:    WorkWxApprovalNodesColumns,
		PrimaryKey: []*schema.Column{WorkWxApprovalNodesColumns[0]},
	}
	// WorkWxNotifyNodesColumns holds the columns for the "work_wx_notify_nodes" table.
	WorkWxNotifyNodesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime, Comment: "创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "更新时间"},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Comment: "创建人"},
		{Name: "updated_by", Type: field.TypeString, Nullable: true, Comment: "更新人"},
		{Name: "item_name", Type: field.TypeString, Comment: "抄送人姓名"},
		{Name: "item_image", Type: field.TypeString, Comment: "抄送人头像URL"},
		{Name: "item_user_id", Type: field.TypeString, Comment: "抄送人UserID"},
		{Name: "approval_message_id", Type: field.TypeString, Comment: "关联的审批消息ID"},
	}
	// WorkWxNotifyNodesTable holds the schema information for the "work_wx_notify_nodes" table.
	WorkWxNotifyNodesTable = &schema.Table{
		Name:       "work_wx_notify_nodes",
		Comment:    "企业微信抄送节点",
		Columns:    WorkWxNotifyNodesColumns,
		PrimaryKey: []*schema.Column{WorkWxNotifyNodesColumns[0]},
	}
	// WmsAuditPlanUsersColumns holds the columns for the "wms_audit_plan_users" table.
	WmsAuditPlanUsersColumns = []*schema.Column{
		{Name: "wms_audit_plan_id", Type: field.TypeString},
		{Name: "sys_user_id", Type: field.TypeString},
	}
	// WmsAuditPlanUsersTable holds the schema information for the "wms_audit_plan_users" table.
	WmsAuditPlanUsersTable = &schema.Table{
		Name:       "wms_audit_plan_users",
		Columns:    WmsAuditPlanUsersColumns,
		PrimaryKey: []*schema.Column{WmsAuditPlanUsersColumns[0], WmsAuditPlanUsersColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_audit_plan_users_wms_audit_plan_id",
				Columns:    []*schema.Column{WmsAuditPlanUsersColumns[0]},
				RefColumns: []*schema.Column{WmsAuditPlansColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "wms_audit_plan_users_sys_user_id",
				Columns:    []*schema.Column{WmsAuditPlanUsersColumns[1]},
				RefColumns: []*schema.Column{SysUsersColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// WmsAuditPlanOrganizationsColumns holds the columns for the "wms_audit_plan_organizations" table.
	WmsAuditPlanOrganizationsColumns = []*schema.Column{
		{Name: "wms_audit_plan_id", Type: field.TypeString},
		{Name: "sys_organization_id", Type: field.TypeString},
	}
	// WmsAuditPlanOrganizationsTable holds the schema information for the "wms_audit_plan_organizations" table.
	WmsAuditPlanOrganizationsTable = &schema.Table{
		Name:       "wms_audit_plan_organizations",
		Columns:    WmsAuditPlanOrganizationsColumns,
		PrimaryKey: []*schema.Column{WmsAuditPlanOrganizationsColumns[0], WmsAuditPlanOrganizationsColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_audit_plan_organizations_wms_audit_plan_id",
				Columns:    []*schema.Column{WmsAuditPlanOrganizationsColumns[0]},
				RefColumns: []*schema.Column{WmsAuditPlansColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "wms_audit_plan_organizations_sys_organization_id",
				Columns:    []*schema.Column{WmsAuditPlanOrganizationsColumns[1]},
				RefColumns: []*schema.Column{SysOrganizationColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// WmsAuditPlanRepositoriesColumns holds the columns for the "wms_audit_plan_repositories" table.
	WmsAuditPlanRepositoriesColumns = []*schema.Column{
		{Name: "wms_audit_plan_id", Type: field.TypeString},
		{Name: "wms_repository_id", Type: field.TypeString},
	}
	// WmsAuditPlanRepositoriesTable holds the schema information for the "wms_audit_plan_repositories" table.
	WmsAuditPlanRepositoriesTable = &schema.Table{
		Name:       "wms_audit_plan_repositories",
		Columns:    WmsAuditPlanRepositoriesColumns,
		PrimaryKey: []*schema.Column{WmsAuditPlanRepositoriesColumns[0], WmsAuditPlanRepositoriesColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_audit_plan_repositories_wms_audit_plan_id",
				Columns:    []*schema.Column{WmsAuditPlanRepositoriesColumns[0]},
				RefColumns: []*schema.Column{WmsAuditPlansColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "wms_audit_plan_repositories_wms_repository_id",
				Columns:    []*schema.Column{WmsAuditPlanRepositoriesColumns[1]},
				RefColumns: []*schema.Column{WmsRepositoriesColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// WmsAuditPlanEquipmentTypesColumns holds the columns for the "wms_audit_plan_equipmentTypes" table.
	WmsAuditPlanEquipmentTypesColumns = []*schema.Column{
		{Name: "wms_audit_plan_id", Type: field.TypeString},
		{Name: "wms_equipment_type_id", Type: field.TypeString},
	}
	// WmsAuditPlanEquipmentTypesTable holds the schema information for the "wms_audit_plan_equipmentTypes" table.
	WmsAuditPlanEquipmentTypesTable = &schema.Table{
		Name:       "wms_audit_plan_equipmentTypes",
		Columns:    WmsAuditPlanEquipmentTypesColumns,
		PrimaryKey: []*schema.Column{WmsAuditPlanEquipmentTypesColumns[0], WmsAuditPlanEquipmentTypesColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_audit_plan_equipmentTypes_wms_audit_plan_id",
				Columns:    []*schema.Column{WmsAuditPlanEquipmentTypesColumns[0]},
				RefColumns: []*schema.Column{WmsAuditPlansColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "wms_audit_plan_equipmentTypes_wms_equipment_type_id",
				Columns:    []*schema.Column{WmsAuditPlanEquipmentTypesColumns[1]},
				RefColumns: []*schema.Column{WmsEquipmentTypesColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// WmsCompanyLabelsColumns holds the columns for the "wms_company_labels" table.
	WmsCompanyLabelsColumns = []*schema.Column{
		{Name: "wms_company_id", Type: field.TypeString},
		{Name: "sys_label_id", Type: field.TypeString},
	}
	// WmsCompanyLabelsTable holds the schema information for the "wms_company_labels" table.
	WmsCompanyLabelsTable = &schema.Table{
		Name:       "wms_company_labels",
		Columns:    WmsCompanyLabelsColumns,
		PrimaryKey: []*schema.Column{WmsCompanyLabelsColumns[0], WmsCompanyLabelsColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_company_labels_wms_company_id",
				Columns:    []*schema.Column{WmsCompanyLabelsColumns[0]},
				RefColumns: []*schema.Column{WmsCompaniesColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "wms_company_labels_sys_label_id",
				Columns:    []*schema.Column{WmsCompanyLabelsColumns[1]},
				RefColumns: []*schema.Column{SysLabelsColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// WmsEquipmentUsersColumns holds the columns for the "wms_equipment_users" table.
	WmsEquipmentUsersColumns = []*schema.Column{
		{Name: "wms_equipment_id", Type: field.TypeString},
		{Name: "sys_user_id", Type: field.TypeString},
	}
	// WmsEquipmentUsersTable holds the schema information for the "wms_equipment_users" table.
	WmsEquipmentUsersTable = &schema.Table{
		Name:       "wms_equipment_users",
		Columns:    WmsEquipmentUsersColumns,
		PrimaryKey: []*schema.Column{WmsEquipmentUsersColumns[0], WmsEquipmentUsersColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_equipment_users_wms_equipment_id",
				Columns:    []*schema.Column{WmsEquipmentUsersColumns[0]},
				RefColumns: []*schema.Column{WmsEquipmentsColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "wms_equipment_users_sys_user_id",
				Columns:    []*schema.Column{WmsEquipmentUsersColumns[1]},
				RefColumns: []*schema.Column{SysUsersColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// WmsEquipmentOrganizationsColumns holds the columns for the "wms_equipment_organizations" table.
	WmsEquipmentOrganizationsColumns = []*schema.Column{
		{Name: "wms_equipment_id", Type: field.TypeString},
		{Name: "sys_organization_id", Type: field.TypeString},
	}
	// WmsEquipmentOrganizationsTable holds the schema information for the "wms_equipment_organizations" table.
	WmsEquipmentOrganizationsTable = &schema.Table{
		Name:       "wms_equipment_organizations",
		Columns:    WmsEquipmentOrganizationsColumns,
		PrimaryKey: []*schema.Column{WmsEquipmentOrganizationsColumns[0], WmsEquipmentOrganizationsColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_equipment_organizations_wms_equipment_id",
				Columns:    []*schema.Column{WmsEquipmentOrganizationsColumns[0]},
				RefColumns: []*schema.Column{WmsEquipmentsColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "wms_equipment_organizations_sys_organization_id",
				Columns:    []*schema.Column{WmsEquipmentOrganizationsColumns[1]},
				RefColumns: []*schema.Column{SysOrganizationColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// WmsLearningCourseLabelsColumns holds the columns for the "wms_learning_course_labels" table.
	WmsLearningCourseLabelsColumns = []*schema.Column{
		{Name: "wms_learning_course_id", Type: field.TypeString},
		{Name: "sys_label_id", Type: field.TypeString},
	}
	// WmsLearningCourseLabelsTable holds the schema information for the "wms_learning_course_labels" table.
	WmsLearningCourseLabelsTable = &schema.Table{
		Name:       "wms_learning_course_labels",
		Columns:    WmsLearningCourseLabelsColumns,
		PrimaryKey: []*schema.Column{WmsLearningCourseLabelsColumns[0], WmsLearningCourseLabelsColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_learning_course_labels_wms_learning_course_id",
				Columns:    []*schema.Column{WmsLearningCourseLabelsColumns[0]},
				RefColumns: []*schema.Column{WmsLearningCoursesColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "wms_learning_course_labels_sys_label_id",
				Columns:    []*schema.Column{WmsLearningCourseLabelsColumns[1]},
				RefColumns: []*schema.Column{SysLabelsColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// WmsLearningCourseEquipmentsColumns holds the columns for the "wms_learning_course_equipments" table.
	WmsLearningCourseEquipmentsColumns = []*schema.Column{
		{Name: "wms_learning_course_id", Type: field.TypeString},
		{Name: "wms_equipment_id", Type: field.TypeString},
	}
	// WmsLearningCourseEquipmentsTable holds the schema information for the "wms_learning_course_equipments" table.
	WmsLearningCourseEquipmentsTable = &schema.Table{
		Name:       "wms_learning_course_equipments",
		Columns:    WmsLearningCourseEquipmentsColumns,
		PrimaryKey: []*schema.Column{WmsLearningCourseEquipmentsColumns[0], WmsLearningCourseEquipmentsColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_learning_course_equipments_wms_learning_course_id",
				Columns:    []*schema.Column{WmsLearningCourseEquipmentsColumns[0]},
				RefColumns: []*schema.Column{WmsLearningCoursesColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "wms_learning_course_equipments_wms_equipment_id",
				Columns:    []*schema.Column{WmsLearningCourseEquipmentsColumns[1]},
				RefColumns: []*schema.Column{WmsEquipmentsColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// WmsLearningCourseUsersColumns holds the columns for the "wms_learning_course_users" table.
	WmsLearningCourseUsersColumns = []*schema.Column{
		{Name: "wms_learning_course_id", Type: field.TypeString},
		{Name: "sys_user_id", Type: field.TypeString},
	}
	// WmsLearningCourseUsersTable holds the schema information for the "wms_learning_course_users" table.
	WmsLearningCourseUsersTable = &schema.Table{
		Name:       "wms_learning_course_users",
		Columns:    WmsLearningCourseUsersColumns,
		PrimaryKey: []*schema.Column{WmsLearningCourseUsersColumns[0], WmsLearningCourseUsersColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_learning_course_users_wms_learning_course_id",
				Columns:    []*schema.Column{WmsLearningCourseUsersColumns[0]},
				RefColumns: []*schema.Column{WmsLearningCoursesColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "wms_learning_course_users_sys_user_id",
				Columns:    []*schema.Column{WmsLearningCourseUsersColumns[1]},
				RefColumns: []*schema.Column{SysUsersColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// WmsLearningCourseOrganizationsColumns holds the columns for the "wms_learning_course_organizations" table.
	WmsLearningCourseOrganizationsColumns = []*schema.Column{
		{Name: "wms_learning_course_id", Type: field.TypeString},
		{Name: "sys_organization_id", Type: field.TypeString},
	}
	// WmsLearningCourseOrganizationsTable holds the schema information for the "wms_learning_course_organizations" table.
	WmsLearningCourseOrganizationsTable = &schema.Table{
		Name:       "wms_learning_course_organizations",
		Columns:    WmsLearningCourseOrganizationsColumns,
		PrimaryKey: []*schema.Column{WmsLearningCourseOrganizationsColumns[0], WmsLearningCourseOrganizationsColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_learning_course_organizations_wms_learning_course_id",
				Columns:    []*schema.Column{WmsLearningCourseOrganizationsColumns[0]},
				RefColumns: []*schema.Column{WmsLearningCoursesColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "wms_learning_course_organizations_sys_organization_id",
				Columns:    []*schema.Column{WmsLearningCourseOrganizationsColumns[1]},
				RefColumns: []*schema.Column{SysOrganizationColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// WmsLearningCoursewareLabelsColumns holds the columns for the "wms_learning_courseware_labels" table.
	WmsLearningCoursewareLabelsColumns = []*schema.Column{
		{Name: "wms_learning_courseware_id", Type: field.TypeString},
		{Name: "sys_label_id", Type: field.TypeString},
	}
	// WmsLearningCoursewareLabelsTable holds the schema information for the "wms_learning_courseware_labels" table.
	WmsLearningCoursewareLabelsTable = &schema.Table{
		Name:       "wms_learning_courseware_labels",
		Columns:    WmsLearningCoursewareLabelsColumns,
		PrimaryKey: []*schema.Column{WmsLearningCoursewareLabelsColumns[0], WmsLearningCoursewareLabelsColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_learning_courseware_labels_wms_learning_courseware_id",
				Columns:    []*schema.Column{WmsLearningCoursewareLabelsColumns[0]},
				RefColumns: []*schema.Column{WmsLearningCoursewaresColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "wms_learning_courseware_labels_sys_label_id",
				Columns:    []*schema.Column{WmsLearningCoursewareLabelsColumns[1]},
				RefColumns: []*schema.Column{SysLabelsColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// WmsLearningPlanUsersColumns holds the columns for the "wms_learning_plan_users" table.
	WmsLearningPlanUsersColumns = []*schema.Column{
		{Name: "wms_learning_plan_id", Type: field.TypeString},
		{Name: "sys_user_id", Type: field.TypeString},
	}
	// WmsLearningPlanUsersTable holds the schema information for the "wms_learning_plan_users" table.
	WmsLearningPlanUsersTable = &schema.Table{
		Name:       "wms_learning_plan_users",
		Columns:    WmsLearningPlanUsersColumns,
		PrimaryKey: []*schema.Column{WmsLearningPlanUsersColumns[0], WmsLearningPlanUsersColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_learning_plan_users_wms_learning_plan_id",
				Columns:    []*schema.Column{WmsLearningPlanUsersColumns[0]},
				RefColumns: []*schema.Column{WmsLearningPlansColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "wms_learning_plan_users_sys_user_id",
				Columns:    []*schema.Column{WmsLearningPlanUsersColumns[1]},
				RefColumns: []*schema.Column{SysUsersColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// WmsLearningPlanOrganizationsColumns holds the columns for the "wms_learning_plan_organizations" table.
	WmsLearningPlanOrganizationsColumns = []*schema.Column{
		{Name: "wms_learning_plan_id", Type: field.TypeString},
		{Name: "sys_organization_id", Type: field.TypeString},
	}
	// WmsLearningPlanOrganizationsTable holds the schema information for the "wms_learning_plan_organizations" table.
	WmsLearningPlanOrganizationsTable = &schema.Table{
		Name:       "wms_learning_plan_organizations",
		Columns:    WmsLearningPlanOrganizationsColumns,
		PrimaryKey: []*schema.Column{WmsLearningPlanOrganizationsColumns[0], WmsLearningPlanOrganizationsColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_learning_plan_organizations_wms_learning_plan_id",
				Columns:    []*schema.Column{WmsLearningPlanOrganizationsColumns[0]},
				RefColumns: []*schema.Column{WmsLearningPlansColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "wms_learning_plan_organizations_sys_organization_id",
				Columns:    []*schema.Column{WmsLearningPlanOrganizationsColumns[1]},
				RefColumns: []*schema.Column{SysOrganizationColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// WmsLearningPlanCoursesColumns holds the columns for the "wms_learning_plan_courses" table.
	WmsLearningPlanCoursesColumns = []*schema.Column{
		{Name: "wms_learning_plan_id", Type: field.TypeString},
		{Name: "wms_learning_course_id", Type: field.TypeString},
	}
	// WmsLearningPlanCoursesTable holds the schema information for the "wms_learning_plan_courses" table.
	WmsLearningPlanCoursesTable = &schema.Table{
		Name:       "wms_learning_plan_courses",
		Columns:    WmsLearningPlanCoursesColumns,
		PrimaryKey: []*schema.Column{WmsLearningPlanCoursesColumns[0], WmsLearningPlanCoursesColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_learning_plan_courses_wms_learning_plan_id",
				Columns:    []*schema.Column{WmsLearningPlanCoursesColumns[0]},
				RefColumns: []*schema.Column{WmsLearningPlansColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "wms_learning_plan_courses_wms_learning_course_id",
				Columns:    []*schema.Column{WmsLearningPlanCoursesColumns[1]},
				RefColumns: []*schema.Column{WmsLearningCoursesColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// WmsMeasureUnitLabelsColumns holds the columns for the "wms_measure_unit_labels" table.
	WmsMeasureUnitLabelsColumns = []*schema.Column{
		{Name: "wms_measure_unit_id", Type: field.TypeString},
		{Name: "sys_label_id", Type: field.TypeString},
	}
	// WmsMeasureUnitLabelsTable holds the schema information for the "wms_measure_unit_labels" table.
	WmsMeasureUnitLabelsTable = &schema.Table{
		Name:       "wms_measure_unit_labels",
		Columns:    WmsMeasureUnitLabelsColumns,
		PrimaryKey: []*schema.Column{WmsMeasureUnitLabelsColumns[0], WmsMeasureUnitLabelsColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_measure_unit_labels_wms_measure_unit_id",
				Columns:    []*schema.Column{WmsMeasureUnitLabelsColumns[0]},
				RefColumns: []*schema.Column{WmsMeasureUnitsColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "wms_measure_unit_labels_sys_label_id",
				Columns:    []*schema.Column{WmsMeasureUnitLabelsColumns[1]},
				RefColumns: []*schema.Column{SysLabelsColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// WmsRepositoryOrganizationsColumns holds the columns for the "wms_repository_organizations" table.
	WmsRepositoryOrganizationsColumns = []*schema.Column{
		{Name: "wms_repository_id", Type: field.TypeString},
		{Name: "sys_organization_id", Type: field.TypeString},
	}
	// WmsRepositoryOrganizationsTable holds the schema information for the "wms_repository_organizations" table.
	WmsRepositoryOrganizationsTable = &schema.Table{
		Name:       "wms_repository_organizations",
		Columns:    WmsRepositoryOrganizationsColumns,
		PrimaryKey: []*schema.Column{WmsRepositoryOrganizationsColumns[0], WmsRepositoryOrganizationsColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wms_repository_organizations_wms_repository_id",
				Columns:    []*schema.Column{WmsRepositoryOrganizationsColumns[0]},
				RefColumns: []*schema.Column{WmsRepositoriesColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "wms_repository_organizations_sys_organization_id",
				Columns:    []*schema.Column{WmsRepositoryOrganizationsColumns[1]},
				RefColumns: []*schema.Column{SysOrganizationColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		AppApproveActivitiesTable,
		AppApproveBasicsTable,
		AppApproveGroupsTable,
		AppApproveRecordsTable,
		AppApproveRecordLogsTable,
		AppApproveRecordQueuesTable,
		AppApproveWorkflowsTable,
		DemoBuildingsTable,
		SysApplicationsTable,
		SysApplicationModulesTable,
		SysApplicationModuleResourcesTable,
		SysAreasTable,
		SysCitiesTable,
		SysConfigsTable,
		SysDictionariesTable,
		SysDictionaryDetailsTable,
		SysGamesTable,
		SysLabelsTable,
		SysLogsTable,
		SysMenusTable,
		SysMessageTemplatesTable,
		SysModulesTable,
		SysModuleResourcesTable,
		SysOrganizationTable,
		SysPageCodesTable,
		SysPageCodeHistoriesTable,
		SysProjectsTable,
		SysProvincesTable,
		SysResourcesTable,
		SysRolesTable,
		SysRoleMenusTable,
		SysRoleResourcesTable,
		SysStreetsTable,
		SysTeamsTable,
		SysTeamApplicationsTable,
		SysUploadsTable,
		SysUsersTable,
		SysUserAuthsTable,
		SysUserLogsTable,
		SysUserOrganizationTable,
		SysUserRolesTable,
		WmsAccessDoorLogsTable,
		WmsApprovalTasksTable,
		WmsApprovalTaskDetailsTable,
		WmsApprovalTaskOperationsTable,
		WmsApprovalTaskRemarksTable,
		WmsAuditPlansTable,
		WmsAuditPlanDetailsTable,
		WmsBorrowOrdersTable,
		WmsBorrowOrderDetailsTable,
		WmsCarsTable,
		WmsClaimOrdersTable,
		WmsClaimOrderDetailsTable,
		WmsCompaniesTable,
		WmsCompanyAddressesTable,
		WmsDiscardMeetingsTable,
		WmsDiscardMeetingDetailsTable,
		WmsDiscardOrdersTable,
		WmsDiscardOrderDetailsTable,
		WmsDiscardPlanOrdersTable,
		WmsDiscardPlanOrderDetailsTable,
		WmsDocumentsTable,
		WmsEnterRepositoryOrdersTable,
		WmsEnterRepositoryOrderDetailsTable,
		WmsEquipmentsTable,
		WmsEquipmentDetailsTable,
		WmsEquipmentTypesTable,
		WmsEquipmentTypePropertiesTable,
		WmsEquipmentTypePropertyGroupsTable,
		WmsEquipmentTypePropertyOptionsTable,
		WmsEquipmentUserGroupsTable,
		WmsFireStationsTable,
		WmsGpSsTable,
		WmsGpsRecordsTable,
		WmsLearningCoursesTable,
		WmsLearningCourseCoursewaresTable,
		WmsLearningCourseLogsTable,
		WmsLearningCourseRecordsTable,
		WmsLearningCoursewaresTable,
		WmsLearningCoursewareRecordsTable,
		WmsLearningPlansTable,
		WmsLearningPlanRecordsTable,
		WmsMaintainOrdersTable,
		WmsMaintainOrderDetailsTable,
		WmsMaintainPlansTable,
		WmsMaintainPlanDetailsTable,
		WmsMaterialsTable,
		WmsMaterialLogsTable,
		WmsMeasureUnitsTable,
		WmsOperateLogsTable,
		WmsOutRepositoryOrdersTable,
		WmsOutRepositoryOrderDetailsTable,
		WmsPurchaseOrdersTable,
		WmsPurchaseOrderDetailsTable,
		WmsRepairOrdersTable,
		WmsRepairOrderDetailsTable,
		WmsRepairSettlementOrdersTable,
		WmsRepairSettlementOrderSettlementfeeDetailsTable,
		WmsRepairSettlementOrderWorkfeeDetailsTable,
		WmsRepositoriesTable,
		WmsRepositoryAdminsTable,
		WmsRepositoryAreasTable,
		WmsRepositoryPositionsTable,
		WmsRepositoryScreensTable,
		WmsRepositoryScreenRepositoryAreasTable,
		WmsReturnOrdersTable,
		WmsReturnOrderDetailsTable,
		WmsRfidsTable,
		WmsRfidReadersTable,
		WmsRfidReaderRecordsTable,
		WmsTransferOrdersTable,
		WmsTransferOrderDetailsTable,
		WmsVehicleRepairOrdersTable,
		WmsVehicleRepairOrderMaterialfeeDetailsTable,
		WmsVehicleRepairOrderSettlementfeeDetailsTable,
		WmsVehicleRepairOrderWorkfeeDetailsTable,
		WorkWxApprovalMessagesTable,
		WorkWxApprovalNodesTable,
		WorkWxNotifyNodesTable,
		WmsAuditPlanUsersTable,
		WmsAuditPlanOrganizationsTable,
		WmsAuditPlanRepositoriesTable,
		WmsAuditPlanEquipmentTypesTable,
		WmsCompanyLabelsTable,
		WmsEquipmentUsersTable,
		WmsEquipmentOrganizationsTable,
		WmsLearningCourseLabelsTable,
		WmsLearningCourseEquipmentsTable,
		WmsLearningCourseUsersTable,
		WmsLearningCourseOrganizationsTable,
		WmsLearningCoursewareLabelsTable,
		WmsLearningPlanUsersTable,
		WmsLearningPlanOrganizationsTable,
		WmsLearningPlanCoursesTable,
		WmsMeasureUnitLabelsTable,
		WmsRepositoryOrganizationsTable,
	}
)

func init() {
	AppApproveRecordsTable.ForeignKeys[0].RefTable = AppApproveBasicsTable
	AppApproveRecordLogsTable.ForeignKeys[0].RefTable = AppApproveRecordsTable
	AppApproveRecordQueuesTable.ForeignKeys[0].RefTable = AppApproveRecordsTable
	SysApplicationModulesTable.ForeignKeys[0].RefTable = SysApplicationsTable
	SysApplicationModulesTable.ForeignKeys[1].RefTable = SysModulesTable
	SysApplicationModuleResourcesTable.ForeignKeys[0].RefTable = SysApplicationsTable
	SysApplicationModuleResourcesTable.ForeignKeys[1].RefTable = SysResourcesTable
	SysDictionaryDetailsTable.ForeignKeys[0].RefTable = SysDictionariesTable
	SysMenusTable.ForeignKeys[0].RefTable = SysApplicationsTable
	SysMenusTable.ForeignKeys[1].RefTable = SysResourcesTable
	SysModuleResourcesTable.ForeignKeys[0].RefTable = SysModulesTable
	SysModuleResourcesTable.ForeignKeys[1].RefTable = SysResourcesTable
	SysOrganizationTable.ForeignKeys[0].RefTable = SysOrganizationTable
	SysOrganizationTable.ForeignKeys[1].RefTable = SysTeamsTable
	SysOrganizationTable.Annotation = &entsql.Annotation{
		Table: "sys_organization",
	}
	SysProjectsTable.ForeignKeys[0].RefTable = SysApplicationsTable
	SysProjectsTable.ForeignKeys[1].RefTable = SysTeamsTable
	SysResourcesTable.ForeignKeys[0].RefTable = SysResourcesTable
	SysRolesTable.ForeignKeys[0].RefTable = SysApplicationsTable
	SysRolesTable.ForeignKeys[1].RefTable = SysProjectsTable
	SysRolesTable.ForeignKeys[2].RefTable = SysTeamsTable
	SysRoleMenusTable.ForeignKeys[0].RefTable = SysMenusTable
	SysRoleMenusTable.ForeignKeys[1].RefTable = SysRolesTable
	SysRoleResourcesTable.ForeignKeys[0].RefTable = SysResourcesTable
	SysRoleResourcesTable.ForeignKeys[1].RefTable = SysRolesTable
	SysTeamApplicationsTable.ForeignKeys[0].RefTable = SysApplicationsTable
	SysTeamApplicationsTable.ForeignKeys[1].RefTable = SysTeamsTable
	SysUserAuthsTable.ForeignKeys[0].RefTable = SysUsersTable
	SysUserOrganizationTable.ForeignKeys[0].RefTable = SysApplicationsTable
	SysUserOrganizationTable.ForeignKeys[1].RefTable = SysOrganizationTable
	SysUserOrganizationTable.ForeignKeys[2].RefTable = SysUsersTable
	SysUserOrganizationTable.Annotation = &entsql.Annotation{
		Table: "sys_user_organization",
	}
	SysUserRolesTable.ForeignKeys[0].RefTable = SysRolesTable
	SysUserRolesTable.ForeignKeys[1].RefTable = SysUsersTable
	WmsAccessDoorLogsTable.ForeignKeys[0].RefTable = WmsRepositoriesTable
	WmsAccessDoorLogsTable.ForeignKeys[1].RefTable = WmsMaterialsTable
	WmsApprovalTasksTable.ForeignKeys[0].RefTable = AppApproveRecordsTable
	WmsApprovalTasksTable.ForeignKeys[1].RefTable = WmsFireStationsTable
	WmsApprovalTaskDetailsTable.ForeignKeys[0].RefTable = WmsApprovalTasksTable
	WmsApprovalTaskDetailsTable.ForeignKeys[1].RefTable = WmsEquipmentsTable
	WmsApprovalTaskDetailsTable.ForeignKeys[2].RefTable = WmsMaterialsTable
	WmsApprovalTaskOperationsTable.ForeignKeys[0].RefTable = WmsApprovalTaskDetailsTable
	WmsApprovalTaskRemarksTable.ForeignKeys[0].RefTable = WmsApprovalTasksTable
	WmsAuditPlanDetailsTable.ForeignKeys[0].RefTable = WmsAuditPlansTable
	WmsAuditPlanDetailsTable.ForeignKeys[1].RefTable = WmsEquipmentsTable
	WmsAuditPlanDetailsTable.ForeignKeys[2].RefTable = WmsRepositoriesTable
	WmsAuditPlanDetailsTable.ForeignKeys[3].RefTable = WmsRepositoryAreasTable
	WmsAuditPlanDetailsTable.ForeignKeys[4].RefTable = WmsRepositoryPositionsTable
	WmsAuditPlanDetailsTable.ForeignKeys[5].RefTable = WmsEquipmentTypesTable
	WmsAuditPlanDetailsTable.ForeignKeys[6].RefTable = WmsFireStationsTable
	WmsAuditPlanDetailsTable.ForeignKeys[7].RefTable = WmsMeasureUnitsTable
	WmsAuditPlanDetailsTable.ForeignKeys[8].RefTable = SysUsersTable
	WmsAuditPlanDetailsTable.ForeignKeys[9].RefTable = WmsMaterialsTable
	WmsCarsTable.ForeignKeys[0].RefTable = SysOrganizationTable
	WmsCarsTable.ForeignKeys[1].RefTable = WmsMaterialsTable
	WmsCompanyAddressesTable.ForeignKeys[0].RefTable = WmsCompaniesTable
	WmsDiscardMeetingDetailsTable.ForeignKeys[0].RefTable = WmsDiscardMeetingsTable
	WmsDiscardMeetingDetailsTable.ForeignKeys[1].RefTable = WmsEquipmentTypesTable
	WmsDiscardMeetingDetailsTable.ForeignKeys[2].RefTable = WmsEquipmentsTable
	WmsDiscardMeetingDetailsTable.ForeignKeys[3].RefTable = WmsMeasureUnitsTable
	WmsDiscardMeetingDetailsTable.ForeignKeys[4].RefTable = WmsRepositoriesTable
	WmsDiscardMeetingDetailsTable.ForeignKeys[5].RefTable = WmsRepositoryAreasTable
	WmsDiscardMeetingDetailsTable.ForeignKeys[6].RefTable = WmsRepositoryPositionsTable
	WmsDiscardPlanOrdersTable.ForeignKeys[0].RefTable = WmsFireStationsTable
	WmsDiscardPlanOrderDetailsTable.ForeignKeys[0].RefTable = WmsDiscardPlanOrdersTable
	WmsDiscardPlanOrderDetailsTable.ForeignKeys[1].RefTable = WmsEquipmentTypesTable
	WmsDiscardPlanOrderDetailsTable.ForeignKeys[2].RefTable = WmsEquipmentsTable
	WmsDiscardPlanOrderDetailsTable.ForeignKeys[3].RefTable = WmsMeasureUnitsTable
	WmsDiscardPlanOrderDetailsTable.ForeignKeys[4].RefTable = WmsRepositoriesTable
	WmsDiscardPlanOrderDetailsTable.ForeignKeys[5].RefTable = WmsRepositoryAreasTable
	WmsDiscardPlanOrderDetailsTable.ForeignKeys[6].RefTable = WmsRepositoryPositionsTable
	WmsDocumentsTable.ForeignKeys[0].RefTable = WmsApprovalTasksTable
	WmsDocumentsTable.ForeignKeys[1].RefTable = WmsDiscardMeetingsTable
	WmsDocumentsTable.ForeignKeys[2].RefTable = WmsDiscardPlanOrdersTable
	WmsDocumentsTable.ForeignKeys[3].RefTable = WmsEnterRepositoryOrdersTable
	WmsDocumentsTable.ForeignKeys[4].RefTable = WmsMaintainPlansTable
	WmsDocumentsTable.ForeignKeys[5].RefTable = WmsOutRepositoryOrdersTable
	WmsDocumentsTable.ForeignKeys[6].RefTable = WmsPurchaseOrdersTable
	WmsEnterRepositoryOrdersTable.ForeignKeys[0].RefTable = WmsRepositoriesTable
	WmsEnterRepositoryOrderDetailsTable.ForeignKeys[0].RefTable = WmsEnterRepositoryOrdersTable
	WmsEnterRepositoryOrderDetailsTable.ForeignKeys[1].RefTable = WmsEquipmentTypesTable
	WmsEnterRepositoryOrderDetailsTable.ForeignKeys[2].RefTable = WmsEquipmentsTable
	WmsEnterRepositoryOrderDetailsTable.ForeignKeys[3].RefTable = WmsMeasureUnitsTable
	WmsEnterRepositoryOrderDetailsTable.ForeignKeys[4].RefTable = WmsRepositoryPositionsTable
	WmsEnterRepositoryOrderDetailsTable.ForeignKeys[5].RefTable = WmsRepositoriesTable
	WmsEnterRepositoryOrderDetailsTable.ForeignKeys[6].RefTable = WmsRepositoriesTable
	WmsEquipmentsTable.ForeignKeys[0].RefTable = WmsEquipmentTypesTable
	WmsEquipmentDetailsTable.ForeignKeys[0].RefTable = WmsEquipmentsTable
	WmsEquipmentDetailsTable.ForeignKeys[1].RefTable = WmsEquipmentTypesTable
	WmsEquipmentDetailsTable.ForeignKeys[2].RefTable = WmsEquipmentTypePropertiesTable
	WmsEquipmentDetailsTable.ForeignKeys[3].RefTable = WmsEquipmentTypePropertyGroupsTable
	WmsEquipmentTypesTable.ForeignKeys[0].RefTable = WmsEquipmentTypesTable
	WmsEquipmentTypePropertiesTable.ForeignKeys[0].RefTable = WmsEquipmentTypesTable
	WmsEquipmentTypePropertiesTable.ForeignKeys[1].RefTable = WmsEquipmentTypePropertyGroupsTable
	WmsEquipmentTypePropertyGroupsTable.ForeignKeys[0].RefTable = WmsEquipmentTypesTable
	WmsEquipmentTypePropertyOptionsTable.ForeignKeys[0].RefTable = WmsEquipmentTypePropertiesTable
	WmsEquipmentUserGroupsTable.ForeignKeys[0].RefTable = WmsEquipmentsTable
	WmsGpSsTable.ForeignKeys[0].RefTable = WmsCarsTable
	WmsLearningCourseCoursewaresTable.ForeignKeys[0].RefTable = WmsLearningCoursesTable
	WmsLearningCourseCoursewaresTable.ForeignKeys[1].RefTable = WmsLearningCoursewaresTable
	WmsLearningCourseLogsTable.ForeignKeys[0].RefTable = SysUsersTable
	WmsLearningCourseLogsTable.ForeignKeys[1].RefTable = WmsLearningCoursesTable
	WmsLearningCourseRecordsTable.ForeignKeys[0].RefTable = SysUsersTable
	WmsLearningCourseRecordsTable.ForeignKeys[1].RefTable = WmsLearningCoursesTable
	WmsLearningCoursewareRecordsTable.ForeignKeys[0].RefTable = WmsLearningCoursewaresTable
	WmsLearningPlanRecordsTable.ForeignKeys[0].RefTable = WmsLearningPlansTable
	WmsMaintainPlansTable.ForeignKeys[0].RefTable = WmsFireStationsTable
	WmsMaintainPlanDetailsTable.ForeignKeys[0].RefTable = WmsMaintainPlansTable
	WmsMaintainPlanDetailsTable.ForeignKeys[1].RefTable = WmsEquipmentTypesTable
	WmsMaintainPlanDetailsTable.ForeignKeys[2].RefTable = WmsEquipmentsTable
	WmsMaintainPlanDetailsTable.ForeignKeys[3].RefTable = WmsMeasureUnitsTable
	WmsMaintainPlanDetailsTable.ForeignKeys[4].RefTable = WmsRepositoriesTable
	WmsMaintainPlanDetailsTable.ForeignKeys[5].RefTable = WmsRepositoryAreasTable
	WmsMaintainPlanDetailsTable.ForeignKeys[6].RefTable = WmsRepositoryPositionsTable
	WmsMaterialsTable.ForeignKeys[0].RefTable = SysUsersTable
	WmsMaterialsTable.ForeignKeys[1].RefTable = WmsCarsTable
	WmsMaterialsTable.ForeignKeys[2].RefTable = WmsEquipmentsTable
	WmsMaterialsTable.ForeignKeys[3].RefTable = WmsRepositoriesTable
	WmsMaterialsTable.ForeignKeys[4].RefTable = WmsRepositoryAreasTable
	WmsMaterialsTable.ForeignKeys[5].RefTable = WmsRepositoryPositionsTable
	WmsMaterialsTable.ForeignKeys[6].RefTable = WmsEquipmentTypesTable
	WmsMaterialsTable.ForeignKeys[7].RefTable = WmsFireStationsTable
	WmsMaterialsTable.ForeignKeys[8].RefTable = WmsMeasureUnitsTable
	WmsMaterialsTable.ForeignKeys[9].RefTable = SysUsersTable
	WmsMaterialLogsTable.ForeignKeys[0].RefTable = WmsRepositoriesTable
	WmsMaterialLogsTable.ForeignKeys[1].RefTable = WmsRepositoryAreasTable
	WmsMaterialLogsTable.ForeignKeys[2].RefTable = WmsRepositoryPositionsTable
	WmsMaterialLogsTable.ForeignKeys[3].RefTable = WmsRepositoriesTable
	WmsMaterialLogsTable.ForeignKeys[4].RefTable = WmsRepositoryAreasTable
	WmsMaterialLogsTable.ForeignKeys[5].RefTable = WmsRepositoryPositionsTable
	WmsMaterialLogsTable.ForeignKeys[6].RefTable = WmsEquipmentsTable
	WmsMaterialLogsTable.ForeignKeys[7].RefTable = WmsEquipmentTypesTable
	WmsMaterialLogsTable.ForeignKeys[8].RefTable = WmsFireStationsTable
	WmsMaterialLogsTable.ForeignKeys[9].RefTable = WmsMeasureUnitsTable
	WmsMaterialLogsTable.ForeignKeys[10].RefTable = SysUsersTable
	WmsOperateLogsTable.ForeignKeys[0].RefTable = WmsApprovalTasksTable
	WmsOperateLogsTable.ForeignKeys[1].RefTable = WmsDiscardMeetingsTable
	WmsOperateLogsTable.ForeignKeys[2].RefTable = WmsDiscardPlanOrdersTable
	WmsOperateLogsTable.ForeignKeys[3].RefTable = WmsEnterRepositoryOrdersTable
	WmsOperateLogsTable.ForeignKeys[4].RefTable = WmsMaintainPlansTable
	WmsOperateLogsTable.ForeignKeys[5].RefTable = WmsOutRepositoryOrdersTable
	WmsOperateLogsTable.ForeignKeys[6].RefTable = WmsPurchaseOrdersTable
	WmsOutRepositoryOrdersTable.ForeignKeys[0].RefTable = WmsRepositoriesTable
	WmsOutRepositoryOrderDetailsTable.ForeignKeys[0].RefTable = WmsOutRepositoryOrdersTable
	WmsOutRepositoryOrderDetailsTable.ForeignKeys[1].RefTable = WmsEquipmentTypesTable
	WmsOutRepositoryOrderDetailsTable.ForeignKeys[2].RefTable = WmsEquipmentsTable
	WmsOutRepositoryOrderDetailsTable.ForeignKeys[3].RefTable = WmsMeasureUnitsTable
	WmsOutRepositoryOrderDetailsTable.ForeignKeys[4].RefTable = WmsRepositoriesTable
	WmsOutRepositoryOrderDetailsTable.ForeignKeys[5].RefTable = WmsRepositoryAreasTable
	WmsOutRepositoryOrderDetailsTable.ForeignKeys[6].RefTable = WmsRepositoryPositionsTable
	WmsPurchaseOrdersTable.ForeignKeys[0].RefTable = SysUsersTable
	WmsPurchaseOrderDetailsTable.ForeignKeys[0].RefTable = WmsPurchaseOrdersTable
	WmsPurchaseOrderDetailsTable.ForeignKeys[1].RefTable = WmsEquipmentsTable
	WmsPurchaseOrderDetailsTable.ForeignKeys[2].RefTable = WmsMeasureUnitsTable
	WmsPurchaseOrderDetailsTable.ForeignKeys[3].RefTable = WmsRepositoriesTable
	WmsPurchaseOrderDetailsTable.ForeignKeys[4].RefTable = WmsCompaniesTable
	WmsRepositoriesTable.ForeignKeys[0].RefTable = WmsRepositoryScreensTable
	WmsRepositoryAdminsTable.ForeignKeys[0].RefTable = WmsRepositoriesTable
	WmsRepositoryAdminsTable.ForeignKeys[1].RefTable = SysUsersTable
	WmsRepositoryAreasTable.ForeignKeys[0].RefTable = WmsRepositoriesTable
	WmsRepositoryPositionsTable.ForeignKeys[0].RefTable = WmsRepositoriesTable
	WmsRepositoryPositionsTable.ForeignKeys[1].RefTable = WmsRepositoryAreasTable
	WmsRfidReaderRecordsTable.ForeignKeys[0].RefTable = WmsRfidReadersTable
	WmsAuditPlanUsersTable.ForeignKeys[0].RefTable = WmsAuditPlansTable
	WmsAuditPlanUsersTable.ForeignKeys[1].RefTable = SysUsersTable
	WmsAuditPlanOrganizationsTable.ForeignKeys[0].RefTable = WmsAuditPlansTable
	WmsAuditPlanOrganizationsTable.ForeignKeys[1].RefTable = SysOrganizationTable
	WmsAuditPlanRepositoriesTable.ForeignKeys[0].RefTable = WmsAuditPlansTable
	WmsAuditPlanRepositoriesTable.ForeignKeys[1].RefTable = WmsRepositoriesTable
	WmsAuditPlanEquipmentTypesTable.ForeignKeys[0].RefTable = WmsAuditPlansTable
	WmsAuditPlanEquipmentTypesTable.ForeignKeys[1].RefTable = WmsEquipmentTypesTable
	WmsCompanyLabelsTable.ForeignKeys[0].RefTable = WmsCompaniesTable
	WmsCompanyLabelsTable.ForeignKeys[1].RefTable = SysLabelsTable
	WmsEquipmentUsersTable.ForeignKeys[0].RefTable = WmsEquipmentsTable
	WmsEquipmentUsersTable.ForeignKeys[1].RefTable = SysUsersTable
	WmsEquipmentOrganizationsTable.ForeignKeys[0].RefTable = WmsEquipmentsTable
	WmsEquipmentOrganizationsTable.ForeignKeys[1].RefTable = SysOrganizationTable
	WmsLearningCourseLabelsTable.ForeignKeys[0].RefTable = WmsLearningCoursesTable
	WmsLearningCourseLabelsTable.ForeignKeys[1].RefTable = SysLabelsTable
	WmsLearningCourseEquipmentsTable.ForeignKeys[0].RefTable = WmsLearningCoursesTable
	WmsLearningCourseEquipmentsTable.ForeignKeys[1].RefTable = WmsEquipmentsTable
	WmsLearningCourseUsersTable.ForeignKeys[0].RefTable = WmsLearningCoursesTable
	WmsLearningCourseUsersTable.ForeignKeys[1].RefTable = SysUsersTable
	WmsLearningCourseOrganizationsTable.ForeignKeys[0].RefTable = WmsLearningCoursesTable
	WmsLearningCourseOrganizationsTable.ForeignKeys[1].RefTable = SysOrganizationTable
	WmsLearningCoursewareLabelsTable.ForeignKeys[0].RefTable = WmsLearningCoursewaresTable
	WmsLearningCoursewareLabelsTable.ForeignKeys[1].RefTable = SysLabelsTable
	WmsLearningPlanUsersTable.ForeignKeys[0].RefTable = WmsLearningPlansTable
	WmsLearningPlanUsersTable.ForeignKeys[1].RefTable = SysUsersTable
	WmsLearningPlanOrganizationsTable.ForeignKeys[0].RefTable = WmsLearningPlansTable
	WmsLearningPlanOrganizationsTable.ForeignKeys[1].RefTable = SysOrganizationTable
	WmsLearningPlanCoursesTable.ForeignKeys[0].RefTable = WmsLearningPlansTable
	WmsLearningPlanCoursesTable.ForeignKeys[1].RefTable = WmsLearningCoursesTable
	WmsMeasureUnitLabelsTable.ForeignKeys[0].RefTable = WmsMeasureUnitsTable
	WmsMeasureUnitLabelsTable.ForeignKeys[1].RefTable = SysLabelsTable
	WmsRepositoryOrganizationsTable.ForeignKeys[0].RefTable = WmsRepositoriesTable
	WmsRepositoryOrganizationsTable.ForeignKeys[1].RefTable = SysOrganizationTable
}
