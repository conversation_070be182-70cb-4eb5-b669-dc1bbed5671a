// Code generated by ent, DO NOT EDIT.

package wmsborroworder

import (
	"kratos-mono-demo/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContainsFold(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldUpdatedAt, v))
}

// CreatedBy applies equality check predicate on the "created_by" field. It's identical to CreatedByEQ.
func CreatedBy(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldCreatedBy, v))
}

// UpdatedBy applies equality check predicate on the "updated_by" field. It's identical to UpdatedByEQ.
func UpdatedBy(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldUpdatedBy, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldRemark, v))
}

// ContractUrls applies equality check predicate on the "contract_urls" field. It's identical to ContractUrlsEQ.
func ContractUrls(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldContractUrls, v))
}

// InvoiceUrls applies equality check predicate on the "invoice_urls" field. It's identical to InvoiceUrlsEQ.
func InvoiceUrls(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldInvoiceUrls, v))
}

// AuditUrls applies equality check predicate on the "audit_urls" field. It's identical to AuditUrlsEQ.
func AuditUrls(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldAuditUrls, v))
}

// OtherUrls applies equality check predicate on the "other_urls" field. It's identical to OtherUrlsEQ.
func OtherUrls(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldOtherUrls, v))
}

// OrderNo applies equality check predicate on the "order_no" field. It's identical to OrderNoEQ.
func OrderNo(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldOrderNo, v))
}

// RepositoryID applies equality check predicate on the "repository_id" field. It's identical to RepositoryIDEQ.
func RepositoryID(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldRepositoryID, v))
}

// BorrowTime applies equality check predicate on the "borrow_time" field. It's identical to BorrowTimeEQ.
func BorrowTime(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldBorrowTime, v))
}

// ExpectReturnTime applies equality check predicate on the "expect_return_time" field. It's identical to ExpectReturnTimeEQ.
func ExpectReturnTime(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldExpectReturnTime, v))
}

// BorrowerID applies equality check predicate on the "borrower_id" field. It's identical to BorrowerIDEQ.
func BorrowerID(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldBorrowerID, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldStatus, v))
}

// EquipmentNum applies equality check predicate on the "equipment_num" field. It's identical to EquipmentNumEQ.
func EquipmentNum(v uint32) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldEquipmentNum, v))
}

// Reason applies equality check predicate on the "reason" field. It's identical to ReasonEQ.
func Reason(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldReason, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLTE(FieldUpdatedAt, v))
}

// CreatedByEQ applies the EQ predicate on the "created_by" field.
func CreatedByEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldCreatedBy, v))
}

// CreatedByNEQ applies the NEQ predicate on the "created_by" field.
func CreatedByNEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNEQ(FieldCreatedBy, v))
}

// CreatedByIn applies the In predicate on the "created_by" field.
func CreatedByIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIn(FieldCreatedBy, vs...))
}

// CreatedByNotIn applies the NotIn predicate on the "created_by" field.
func CreatedByNotIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotIn(FieldCreatedBy, vs...))
}

// CreatedByGT applies the GT predicate on the "created_by" field.
func CreatedByGT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGT(FieldCreatedBy, v))
}

// CreatedByGTE applies the GTE predicate on the "created_by" field.
func CreatedByGTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGTE(FieldCreatedBy, v))
}

// CreatedByLT applies the LT predicate on the "created_by" field.
func CreatedByLT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLT(FieldCreatedBy, v))
}

// CreatedByLTE applies the LTE predicate on the "created_by" field.
func CreatedByLTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLTE(FieldCreatedBy, v))
}

// CreatedByContains applies the Contains predicate on the "created_by" field.
func CreatedByContains(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContains(FieldCreatedBy, v))
}

// CreatedByHasPrefix applies the HasPrefix predicate on the "created_by" field.
func CreatedByHasPrefix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasPrefix(FieldCreatedBy, v))
}

// CreatedByHasSuffix applies the HasSuffix predicate on the "created_by" field.
func CreatedByHasSuffix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasSuffix(FieldCreatedBy, v))
}

// CreatedByIsNil applies the IsNil predicate on the "created_by" field.
func CreatedByIsNil() predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIsNull(FieldCreatedBy))
}

// CreatedByNotNil applies the NotNil predicate on the "created_by" field.
func CreatedByNotNil() predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotNull(FieldCreatedBy))
}

// CreatedByEqualFold applies the EqualFold predicate on the "created_by" field.
func CreatedByEqualFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEqualFold(FieldCreatedBy, v))
}

// CreatedByContainsFold applies the ContainsFold predicate on the "created_by" field.
func CreatedByContainsFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContainsFold(FieldCreatedBy, v))
}

// UpdatedByEQ applies the EQ predicate on the "updated_by" field.
func UpdatedByEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldUpdatedBy, v))
}

// UpdatedByNEQ applies the NEQ predicate on the "updated_by" field.
func UpdatedByNEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNEQ(FieldUpdatedBy, v))
}

// UpdatedByIn applies the In predicate on the "updated_by" field.
func UpdatedByIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIn(FieldUpdatedBy, vs...))
}

// UpdatedByNotIn applies the NotIn predicate on the "updated_by" field.
func UpdatedByNotIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotIn(FieldUpdatedBy, vs...))
}

// UpdatedByGT applies the GT predicate on the "updated_by" field.
func UpdatedByGT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGT(FieldUpdatedBy, v))
}

// UpdatedByGTE applies the GTE predicate on the "updated_by" field.
func UpdatedByGTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGTE(FieldUpdatedBy, v))
}

// UpdatedByLT applies the LT predicate on the "updated_by" field.
func UpdatedByLT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLT(FieldUpdatedBy, v))
}

// UpdatedByLTE applies the LTE predicate on the "updated_by" field.
func UpdatedByLTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLTE(FieldUpdatedBy, v))
}

// UpdatedByContains applies the Contains predicate on the "updated_by" field.
func UpdatedByContains(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContains(FieldUpdatedBy, v))
}

// UpdatedByHasPrefix applies the HasPrefix predicate on the "updated_by" field.
func UpdatedByHasPrefix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasPrefix(FieldUpdatedBy, v))
}

// UpdatedByHasSuffix applies the HasSuffix predicate on the "updated_by" field.
func UpdatedByHasSuffix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasSuffix(FieldUpdatedBy, v))
}

// UpdatedByIsNil applies the IsNil predicate on the "updated_by" field.
func UpdatedByIsNil() predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIsNull(FieldUpdatedBy))
}

// UpdatedByNotNil applies the NotNil predicate on the "updated_by" field.
func UpdatedByNotNil() predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotNull(FieldUpdatedBy))
}

// UpdatedByEqualFold applies the EqualFold predicate on the "updated_by" field.
func UpdatedByEqualFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEqualFold(FieldUpdatedBy, v))
}

// UpdatedByContainsFold applies the ContainsFold predicate on the "updated_by" field.
func UpdatedByContainsFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContainsFold(FieldUpdatedBy, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContainsFold(FieldRemark, v))
}

// ContractUrlsEQ applies the EQ predicate on the "contract_urls" field.
func ContractUrlsEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldContractUrls, v))
}

// ContractUrlsNEQ applies the NEQ predicate on the "contract_urls" field.
func ContractUrlsNEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNEQ(FieldContractUrls, v))
}

// ContractUrlsIn applies the In predicate on the "contract_urls" field.
func ContractUrlsIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIn(FieldContractUrls, vs...))
}

// ContractUrlsNotIn applies the NotIn predicate on the "contract_urls" field.
func ContractUrlsNotIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotIn(FieldContractUrls, vs...))
}

// ContractUrlsGT applies the GT predicate on the "contract_urls" field.
func ContractUrlsGT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGT(FieldContractUrls, v))
}

// ContractUrlsGTE applies the GTE predicate on the "contract_urls" field.
func ContractUrlsGTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGTE(FieldContractUrls, v))
}

// ContractUrlsLT applies the LT predicate on the "contract_urls" field.
func ContractUrlsLT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLT(FieldContractUrls, v))
}

// ContractUrlsLTE applies the LTE predicate on the "contract_urls" field.
func ContractUrlsLTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLTE(FieldContractUrls, v))
}

// ContractUrlsContains applies the Contains predicate on the "contract_urls" field.
func ContractUrlsContains(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContains(FieldContractUrls, v))
}

// ContractUrlsHasPrefix applies the HasPrefix predicate on the "contract_urls" field.
func ContractUrlsHasPrefix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasPrefix(FieldContractUrls, v))
}

// ContractUrlsHasSuffix applies the HasSuffix predicate on the "contract_urls" field.
func ContractUrlsHasSuffix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasSuffix(FieldContractUrls, v))
}

// ContractUrlsIsNil applies the IsNil predicate on the "contract_urls" field.
func ContractUrlsIsNil() predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIsNull(FieldContractUrls))
}

// ContractUrlsNotNil applies the NotNil predicate on the "contract_urls" field.
func ContractUrlsNotNil() predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotNull(FieldContractUrls))
}

// ContractUrlsEqualFold applies the EqualFold predicate on the "contract_urls" field.
func ContractUrlsEqualFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEqualFold(FieldContractUrls, v))
}

// ContractUrlsContainsFold applies the ContainsFold predicate on the "contract_urls" field.
func ContractUrlsContainsFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContainsFold(FieldContractUrls, v))
}

// InvoiceUrlsEQ applies the EQ predicate on the "invoice_urls" field.
func InvoiceUrlsEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldInvoiceUrls, v))
}

// InvoiceUrlsNEQ applies the NEQ predicate on the "invoice_urls" field.
func InvoiceUrlsNEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNEQ(FieldInvoiceUrls, v))
}

// InvoiceUrlsIn applies the In predicate on the "invoice_urls" field.
func InvoiceUrlsIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIn(FieldInvoiceUrls, vs...))
}

// InvoiceUrlsNotIn applies the NotIn predicate on the "invoice_urls" field.
func InvoiceUrlsNotIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotIn(FieldInvoiceUrls, vs...))
}

// InvoiceUrlsGT applies the GT predicate on the "invoice_urls" field.
func InvoiceUrlsGT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGT(FieldInvoiceUrls, v))
}

// InvoiceUrlsGTE applies the GTE predicate on the "invoice_urls" field.
func InvoiceUrlsGTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGTE(FieldInvoiceUrls, v))
}

// InvoiceUrlsLT applies the LT predicate on the "invoice_urls" field.
func InvoiceUrlsLT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLT(FieldInvoiceUrls, v))
}

// InvoiceUrlsLTE applies the LTE predicate on the "invoice_urls" field.
func InvoiceUrlsLTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLTE(FieldInvoiceUrls, v))
}

// InvoiceUrlsContains applies the Contains predicate on the "invoice_urls" field.
func InvoiceUrlsContains(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContains(FieldInvoiceUrls, v))
}

// InvoiceUrlsHasPrefix applies the HasPrefix predicate on the "invoice_urls" field.
func InvoiceUrlsHasPrefix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasPrefix(FieldInvoiceUrls, v))
}

// InvoiceUrlsHasSuffix applies the HasSuffix predicate on the "invoice_urls" field.
func InvoiceUrlsHasSuffix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasSuffix(FieldInvoiceUrls, v))
}

// InvoiceUrlsIsNil applies the IsNil predicate on the "invoice_urls" field.
func InvoiceUrlsIsNil() predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIsNull(FieldInvoiceUrls))
}

// InvoiceUrlsNotNil applies the NotNil predicate on the "invoice_urls" field.
func InvoiceUrlsNotNil() predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotNull(FieldInvoiceUrls))
}

// InvoiceUrlsEqualFold applies the EqualFold predicate on the "invoice_urls" field.
func InvoiceUrlsEqualFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEqualFold(FieldInvoiceUrls, v))
}

// InvoiceUrlsContainsFold applies the ContainsFold predicate on the "invoice_urls" field.
func InvoiceUrlsContainsFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContainsFold(FieldInvoiceUrls, v))
}

// AuditUrlsEQ applies the EQ predicate on the "audit_urls" field.
func AuditUrlsEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldAuditUrls, v))
}

// AuditUrlsNEQ applies the NEQ predicate on the "audit_urls" field.
func AuditUrlsNEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNEQ(FieldAuditUrls, v))
}

// AuditUrlsIn applies the In predicate on the "audit_urls" field.
func AuditUrlsIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIn(FieldAuditUrls, vs...))
}

// AuditUrlsNotIn applies the NotIn predicate on the "audit_urls" field.
func AuditUrlsNotIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotIn(FieldAuditUrls, vs...))
}

// AuditUrlsGT applies the GT predicate on the "audit_urls" field.
func AuditUrlsGT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGT(FieldAuditUrls, v))
}

// AuditUrlsGTE applies the GTE predicate on the "audit_urls" field.
func AuditUrlsGTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGTE(FieldAuditUrls, v))
}

// AuditUrlsLT applies the LT predicate on the "audit_urls" field.
func AuditUrlsLT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLT(FieldAuditUrls, v))
}

// AuditUrlsLTE applies the LTE predicate on the "audit_urls" field.
func AuditUrlsLTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLTE(FieldAuditUrls, v))
}

// AuditUrlsContains applies the Contains predicate on the "audit_urls" field.
func AuditUrlsContains(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContains(FieldAuditUrls, v))
}

// AuditUrlsHasPrefix applies the HasPrefix predicate on the "audit_urls" field.
func AuditUrlsHasPrefix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasPrefix(FieldAuditUrls, v))
}

// AuditUrlsHasSuffix applies the HasSuffix predicate on the "audit_urls" field.
func AuditUrlsHasSuffix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasSuffix(FieldAuditUrls, v))
}

// AuditUrlsIsNil applies the IsNil predicate on the "audit_urls" field.
func AuditUrlsIsNil() predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIsNull(FieldAuditUrls))
}

// AuditUrlsNotNil applies the NotNil predicate on the "audit_urls" field.
func AuditUrlsNotNil() predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotNull(FieldAuditUrls))
}

// AuditUrlsEqualFold applies the EqualFold predicate on the "audit_urls" field.
func AuditUrlsEqualFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEqualFold(FieldAuditUrls, v))
}

// AuditUrlsContainsFold applies the ContainsFold predicate on the "audit_urls" field.
func AuditUrlsContainsFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContainsFold(FieldAuditUrls, v))
}

// OtherUrlsEQ applies the EQ predicate on the "other_urls" field.
func OtherUrlsEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldOtherUrls, v))
}

// OtherUrlsNEQ applies the NEQ predicate on the "other_urls" field.
func OtherUrlsNEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNEQ(FieldOtherUrls, v))
}

// OtherUrlsIn applies the In predicate on the "other_urls" field.
func OtherUrlsIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIn(FieldOtherUrls, vs...))
}

// OtherUrlsNotIn applies the NotIn predicate on the "other_urls" field.
func OtherUrlsNotIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotIn(FieldOtherUrls, vs...))
}

// OtherUrlsGT applies the GT predicate on the "other_urls" field.
func OtherUrlsGT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGT(FieldOtherUrls, v))
}

// OtherUrlsGTE applies the GTE predicate on the "other_urls" field.
func OtherUrlsGTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGTE(FieldOtherUrls, v))
}

// OtherUrlsLT applies the LT predicate on the "other_urls" field.
func OtherUrlsLT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLT(FieldOtherUrls, v))
}

// OtherUrlsLTE applies the LTE predicate on the "other_urls" field.
func OtherUrlsLTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLTE(FieldOtherUrls, v))
}

// OtherUrlsContains applies the Contains predicate on the "other_urls" field.
func OtherUrlsContains(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContains(FieldOtherUrls, v))
}

// OtherUrlsHasPrefix applies the HasPrefix predicate on the "other_urls" field.
func OtherUrlsHasPrefix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasPrefix(FieldOtherUrls, v))
}

// OtherUrlsHasSuffix applies the HasSuffix predicate on the "other_urls" field.
func OtherUrlsHasSuffix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasSuffix(FieldOtherUrls, v))
}

// OtherUrlsIsNil applies the IsNil predicate on the "other_urls" field.
func OtherUrlsIsNil() predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIsNull(FieldOtherUrls))
}

// OtherUrlsNotNil applies the NotNil predicate on the "other_urls" field.
func OtherUrlsNotNil() predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotNull(FieldOtherUrls))
}

// OtherUrlsEqualFold applies the EqualFold predicate on the "other_urls" field.
func OtherUrlsEqualFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEqualFold(FieldOtherUrls, v))
}

// OtherUrlsContainsFold applies the ContainsFold predicate on the "other_urls" field.
func OtherUrlsContainsFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContainsFold(FieldOtherUrls, v))
}

// OrderNoEQ applies the EQ predicate on the "order_no" field.
func OrderNoEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldOrderNo, v))
}

// OrderNoNEQ applies the NEQ predicate on the "order_no" field.
func OrderNoNEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNEQ(FieldOrderNo, v))
}

// OrderNoIn applies the In predicate on the "order_no" field.
func OrderNoIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIn(FieldOrderNo, vs...))
}

// OrderNoNotIn applies the NotIn predicate on the "order_no" field.
func OrderNoNotIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotIn(FieldOrderNo, vs...))
}

// OrderNoGT applies the GT predicate on the "order_no" field.
func OrderNoGT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGT(FieldOrderNo, v))
}

// OrderNoGTE applies the GTE predicate on the "order_no" field.
func OrderNoGTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGTE(FieldOrderNo, v))
}

// OrderNoLT applies the LT predicate on the "order_no" field.
func OrderNoLT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLT(FieldOrderNo, v))
}

// OrderNoLTE applies the LTE predicate on the "order_no" field.
func OrderNoLTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLTE(FieldOrderNo, v))
}

// OrderNoContains applies the Contains predicate on the "order_no" field.
func OrderNoContains(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContains(FieldOrderNo, v))
}

// OrderNoHasPrefix applies the HasPrefix predicate on the "order_no" field.
func OrderNoHasPrefix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasPrefix(FieldOrderNo, v))
}

// OrderNoHasSuffix applies the HasSuffix predicate on the "order_no" field.
func OrderNoHasSuffix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasSuffix(FieldOrderNo, v))
}

// OrderNoEqualFold applies the EqualFold predicate on the "order_no" field.
func OrderNoEqualFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEqualFold(FieldOrderNo, v))
}

// OrderNoContainsFold applies the ContainsFold predicate on the "order_no" field.
func OrderNoContainsFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContainsFold(FieldOrderNo, v))
}

// RepositoryIDEQ applies the EQ predicate on the "repository_id" field.
func RepositoryIDEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldRepositoryID, v))
}

// RepositoryIDNEQ applies the NEQ predicate on the "repository_id" field.
func RepositoryIDNEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNEQ(FieldRepositoryID, v))
}

// RepositoryIDIn applies the In predicate on the "repository_id" field.
func RepositoryIDIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIn(FieldRepositoryID, vs...))
}

// RepositoryIDNotIn applies the NotIn predicate on the "repository_id" field.
func RepositoryIDNotIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotIn(FieldRepositoryID, vs...))
}

// RepositoryIDGT applies the GT predicate on the "repository_id" field.
func RepositoryIDGT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGT(FieldRepositoryID, v))
}

// RepositoryIDGTE applies the GTE predicate on the "repository_id" field.
func RepositoryIDGTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGTE(FieldRepositoryID, v))
}

// RepositoryIDLT applies the LT predicate on the "repository_id" field.
func RepositoryIDLT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLT(FieldRepositoryID, v))
}

// RepositoryIDLTE applies the LTE predicate on the "repository_id" field.
func RepositoryIDLTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLTE(FieldRepositoryID, v))
}

// RepositoryIDContains applies the Contains predicate on the "repository_id" field.
func RepositoryIDContains(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContains(FieldRepositoryID, v))
}

// RepositoryIDHasPrefix applies the HasPrefix predicate on the "repository_id" field.
func RepositoryIDHasPrefix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasPrefix(FieldRepositoryID, v))
}

// RepositoryIDHasSuffix applies the HasSuffix predicate on the "repository_id" field.
func RepositoryIDHasSuffix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasSuffix(FieldRepositoryID, v))
}

// RepositoryIDIsNil applies the IsNil predicate on the "repository_id" field.
func RepositoryIDIsNil() predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIsNull(FieldRepositoryID))
}

// RepositoryIDNotNil applies the NotNil predicate on the "repository_id" field.
func RepositoryIDNotNil() predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotNull(FieldRepositoryID))
}

// RepositoryIDEqualFold applies the EqualFold predicate on the "repository_id" field.
func RepositoryIDEqualFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEqualFold(FieldRepositoryID, v))
}

// RepositoryIDContainsFold applies the ContainsFold predicate on the "repository_id" field.
func RepositoryIDContainsFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContainsFold(FieldRepositoryID, v))
}

// BorrowTimeEQ applies the EQ predicate on the "borrow_time" field.
func BorrowTimeEQ(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldBorrowTime, v))
}

// BorrowTimeNEQ applies the NEQ predicate on the "borrow_time" field.
func BorrowTimeNEQ(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNEQ(FieldBorrowTime, v))
}

// BorrowTimeIn applies the In predicate on the "borrow_time" field.
func BorrowTimeIn(vs ...time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIn(FieldBorrowTime, vs...))
}

// BorrowTimeNotIn applies the NotIn predicate on the "borrow_time" field.
func BorrowTimeNotIn(vs ...time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotIn(FieldBorrowTime, vs...))
}

// BorrowTimeGT applies the GT predicate on the "borrow_time" field.
func BorrowTimeGT(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGT(FieldBorrowTime, v))
}

// BorrowTimeGTE applies the GTE predicate on the "borrow_time" field.
func BorrowTimeGTE(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGTE(FieldBorrowTime, v))
}

// BorrowTimeLT applies the LT predicate on the "borrow_time" field.
func BorrowTimeLT(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLT(FieldBorrowTime, v))
}

// BorrowTimeLTE applies the LTE predicate on the "borrow_time" field.
func BorrowTimeLTE(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLTE(FieldBorrowTime, v))
}

// ExpectReturnTimeEQ applies the EQ predicate on the "expect_return_time" field.
func ExpectReturnTimeEQ(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldExpectReturnTime, v))
}

// ExpectReturnTimeNEQ applies the NEQ predicate on the "expect_return_time" field.
func ExpectReturnTimeNEQ(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNEQ(FieldExpectReturnTime, v))
}

// ExpectReturnTimeIn applies the In predicate on the "expect_return_time" field.
func ExpectReturnTimeIn(vs ...time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIn(FieldExpectReturnTime, vs...))
}

// ExpectReturnTimeNotIn applies the NotIn predicate on the "expect_return_time" field.
func ExpectReturnTimeNotIn(vs ...time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotIn(FieldExpectReturnTime, vs...))
}

// ExpectReturnTimeGT applies the GT predicate on the "expect_return_time" field.
func ExpectReturnTimeGT(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGT(FieldExpectReturnTime, v))
}

// ExpectReturnTimeGTE applies the GTE predicate on the "expect_return_time" field.
func ExpectReturnTimeGTE(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGTE(FieldExpectReturnTime, v))
}

// ExpectReturnTimeLT applies the LT predicate on the "expect_return_time" field.
func ExpectReturnTimeLT(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLT(FieldExpectReturnTime, v))
}

// ExpectReturnTimeLTE applies the LTE predicate on the "expect_return_time" field.
func ExpectReturnTimeLTE(v time.Time) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLTE(FieldExpectReturnTime, v))
}

// BorrowerIDEQ applies the EQ predicate on the "borrower_id" field.
func BorrowerIDEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldBorrowerID, v))
}

// BorrowerIDNEQ applies the NEQ predicate on the "borrower_id" field.
func BorrowerIDNEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNEQ(FieldBorrowerID, v))
}

// BorrowerIDIn applies the In predicate on the "borrower_id" field.
func BorrowerIDIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIn(FieldBorrowerID, vs...))
}

// BorrowerIDNotIn applies the NotIn predicate on the "borrower_id" field.
func BorrowerIDNotIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotIn(FieldBorrowerID, vs...))
}

// BorrowerIDGT applies the GT predicate on the "borrower_id" field.
func BorrowerIDGT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGT(FieldBorrowerID, v))
}

// BorrowerIDGTE applies the GTE predicate on the "borrower_id" field.
func BorrowerIDGTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGTE(FieldBorrowerID, v))
}

// BorrowerIDLT applies the LT predicate on the "borrower_id" field.
func BorrowerIDLT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLT(FieldBorrowerID, v))
}

// BorrowerIDLTE applies the LTE predicate on the "borrower_id" field.
func BorrowerIDLTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLTE(FieldBorrowerID, v))
}

// BorrowerIDContains applies the Contains predicate on the "borrower_id" field.
func BorrowerIDContains(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContains(FieldBorrowerID, v))
}

// BorrowerIDHasPrefix applies the HasPrefix predicate on the "borrower_id" field.
func BorrowerIDHasPrefix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasPrefix(FieldBorrowerID, v))
}

// BorrowerIDHasSuffix applies the HasSuffix predicate on the "borrower_id" field.
func BorrowerIDHasSuffix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasSuffix(FieldBorrowerID, v))
}

// BorrowerIDEqualFold applies the EqualFold predicate on the "borrower_id" field.
func BorrowerIDEqualFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEqualFold(FieldBorrowerID, v))
}

// BorrowerIDContainsFold applies the ContainsFold predicate on the "borrower_id" field.
func BorrowerIDContainsFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContainsFold(FieldBorrowerID, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLTE(FieldStatus, v))
}

// StatusContains applies the Contains predicate on the "status" field.
func StatusContains(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContains(FieldStatus, v))
}

// StatusHasPrefix applies the HasPrefix predicate on the "status" field.
func StatusHasPrefix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasPrefix(FieldStatus, v))
}

// StatusHasSuffix applies the HasSuffix predicate on the "status" field.
func StatusHasSuffix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasSuffix(FieldStatus, v))
}

// StatusEqualFold applies the EqualFold predicate on the "status" field.
func StatusEqualFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEqualFold(FieldStatus, v))
}

// StatusContainsFold applies the ContainsFold predicate on the "status" field.
func StatusContainsFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContainsFold(FieldStatus, v))
}

// EquipmentNumEQ applies the EQ predicate on the "equipment_num" field.
func EquipmentNumEQ(v uint32) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldEquipmentNum, v))
}

// EquipmentNumNEQ applies the NEQ predicate on the "equipment_num" field.
func EquipmentNumNEQ(v uint32) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNEQ(FieldEquipmentNum, v))
}

// EquipmentNumIn applies the In predicate on the "equipment_num" field.
func EquipmentNumIn(vs ...uint32) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIn(FieldEquipmentNum, vs...))
}

// EquipmentNumNotIn applies the NotIn predicate on the "equipment_num" field.
func EquipmentNumNotIn(vs ...uint32) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotIn(FieldEquipmentNum, vs...))
}

// EquipmentNumGT applies the GT predicate on the "equipment_num" field.
func EquipmentNumGT(v uint32) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGT(FieldEquipmentNum, v))
}

// EquipmentNumGTE applies the GTE predicate on the "equipment_num" field.
func EquipmentNumGTE(v uint32) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGTE(FieldEquipmentNum, v))
}

// EquipmentNumLT applies the LT predicate on the "equipment_num" field.
func EquipmentNumLT(v uint32) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLT(FieldEquipmentNum, v))
}

// EquipmentNumLTE applies the LTE predicate on the "equipment_num" field.
func EquipmentNumLTE(v uint32) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLTE(FieldEquipmentNum, v))
}

// EquipmentNumIsNil applies the IsNil predicate on the "equipment_num" field.
func EquipmentNumIsNil() predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIsNull(FieldEquipmentNum))
}

// EquipmentNumNotNil applies the NotNil predicate on the "equipment_num" field.
func EquipmentNumNotNil() predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotNull(FieldEquipmentNum))
}

// ReasonEQ applies the EQ predicate on the "reason" field.
func ReasonEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEQ(FieldReason, v))
}

// ReasonNEQ applies the NEQ predicate on the "reason" field.
func ReasonNEQ(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNEQ(FieldReason, v))
}

// ReasonIn applies the In predicate on the "reason" field.
func ReasonIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIn(FieldReason, vs...))
}

// ReasonNotIn applies the NotIn predicate on the "reason" field.
func ReasonNotIn(vs ...string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotIn(FieldReason, vs...))
}

// ReasonGT applies the GT predicate on the "reason" field.
func ReasonGT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGT(FieldReason, v))
}

// ReasonGTE applies the GTE predicate on the "reason" field.
func ReasonGTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldGTE(FieldReason, v))
}

// ReasonLT applies the LT predicate on the "reason" field.
func ReasonLT(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLT(FieldReason, v))
}

// ReasonLTE applies the LTE predicate on the "reason" field.
func ReasonLTE(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldLTE(FieldReason, v))
}

// ReasonContains applies the Contains predicate on the "reason" field.
func ReasonContains(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContains(FieldReason, v))
}

// ReasonHasPrefix applies the HasPrefix predicate on the "reason" field.
func ReasonHasPrefix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasPrefix(FieldReason, v))
}

// ReasonHasSuffix applies the HasSuffix predicate on the "reason" field.
func ReasonHasSuffix(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldHasSuffix(FieldReason, v))
}

// ReasonIsNil applies the IsNil predicate on the "reason" field.
func ReasonIsNil() predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldIsNull(FieldReason))
}

// ReasonNotNil applies the NotNil predicate on the "reason" field.
func ReasonNotNil() predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldNotNull(FieldReason))
}

// ReasonEqualFold applies the EqualFold predicate on the "reason" field.
func ReasonEqualFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldEqualFold(FieldReason, v))
}

// ReasonContainsFold applies the ContainsFold predicate on the "reason" field.
func ReasonContainsFold(v string) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.FieldContainsFold(FieldReason, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.WmsBorrowOrder) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.WmsBorrowOrder) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.WmsBorrowOrder) predicate.WmsBorrowOrder {
	return predicate.WmsBorrowOrder(sql.NotPredicates(p))
}
