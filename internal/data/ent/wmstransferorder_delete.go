// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"kratos-mono-demo/internal/data/ent/predicate"
	"kratos-mono-demo/internal/data/ent/wmstransferorder"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WmsTransferOrderDelete is the builder for deleting a WmsTransferOrder entity.
type WmsTransferOrderDelete struct {
	config
	hooks    []Hook
	mutation *WmsTransferOrderMutation
}

// Where appends a list predicates to the WmsTransferOrderDelete builder.
func (wtod *WmsTransferOrderDelete) Where(ps ...predicate.WmsTransferOrder) *WmsTransferOrderDelete {
	wtod.mutation.Where(ps...)
	return wtod
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (wtod *WmsTransferOrderDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, wtod.sqlExec, wtod.mutation, wtod.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (wtod *WmsTransferOrderDelete) ExecX(ctx context.Context) int {
	n, err := wtod.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (wtod *WmsTransferOrderDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(wmstransferorder.Table, sqlgraph.NewFieldSpec(wmstransferorder.FieldID, field.TypeString))
	if ps := wtod.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, wtod.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	wtod.mutation.done = true
	return affected, err
}

// WmsTransferOrderDeleteOne is the builder for deleting a single WmsTransferOrder entity.
type WmsTransferOrderDeleteOne struct {
	wtod *WmsTransferOrderDelete
}

// Where appends a list predicates to the WmsTransferOrderDelete builder.
func (wtodo *WmsTransferOrderDeleteOne) Where(ps ...predicate.WmsTransferOrder) *WmsTransferOrderDeleteOne {
	wtodo.wtod.mutation.Where(ps...)
	return wtodo
}

// Exec executes the deletion query.
func (wtodo *WmsTransferOrderDeleteOne) Exec(ctx context.Context) error {
	n, err := wtodo.wtod.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{wmstransferorder.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (wtodo *WmsTransferOrderDeleteOne) ExecX(ctx context.Context) {
	if err := wtodo.Exec(ctx); err != nil {
		panic(err)
	}
}
