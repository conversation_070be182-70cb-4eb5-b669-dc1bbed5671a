// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-mono-demo/internal/data/ent/wmstransferorderdetail"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WmsTransferOrderDetailCreate is the builder for creating a WmsTransferOrderDetail entity.
type WmsTransferOrderDetailCreate struct {
	config
	mutation *WmsTransferOrderDetailMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (wtodc *WmsTransferOrderDetailCreate) SetCreatedAt(t time.Time) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetCreatedAt(t)
	return wtodc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (wtodc *WmsTransferOrderDetailCreate) SetNillableCreatedAt(t *time.Time) *WmsTransferOrderDetailCreate {
	if t != nil {
		wtodc.SetCreatedAt(*t)
	}
	return wtodc
}

// SetUpdatedAt sets the "updated_at" field.
func (wtodc *WmsTransferOrderDetailCreate) SetUpdatedAt(t time.Time) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetUpdatedAt(t)
	return wtodc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (wtodc *WmsTransferOrderDetailCreate) SetNillableUpdatedAt(t *time.Time) *WmsTransferOrderDetailCreate {
	if t != nil {
		wtodc.SetUpdatedAt(*t)
	}
	return wtodc
}

// SetCreatedBy sets the "created_by" field.
func (wtodc *WmsTransferOrderDetailCreate) SetCreatedBy(s string) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetCreatedBy(s)
	return wtodc
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (wtodc *WmsTransferOrderDetailCreate) SetNillableCreatedBy(s *string) *WmsTransferOrderDetailCreate {
	if s != nil {
		wtodc.SetCreatedBy(*s)
	}
	return wtodc
}

// SetUpdatedBy sets the "updated_by" field.
func (wtodc *WmsTransferOrderDetailCreate) SetUpdatedBy(s string) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetUpdatedBy(s)
	return wtodc
}

// SetNillableUpdatedBy sets the "updated_by" field if the given value is not nil.
func (wtodc *WmsTransferOrderDetailCreate) SetNillableUpdatedBy(s *string) *WmsTransferOrderDetailCreate {
	if s != nil {
		wtodc.SetUpdatedBy(*s)
	}
	return wtodc
}

// SetRemark sets the "remark" field.
func (wtodc *WmsTransferOrderDetailCreate) SetRemark(s string) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetRemark(s)
	return wtodc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (wtodc *WmsTransferOrderDetailCreate) SetNillableRemark(s *string) *WmsTransferOrderDetailCreate {
	if s != nil {
		wtodc.SetRemark(*s)
	}
	return wtodc
}

// SetOrderID sets the "order_id" field.
func (wtodc *WmsTransferOrderDetailCreate) SetOrderID(s string) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetOrderID(s)
	return wtodc
}

// SetNillableOrderID sets the "order_id" field if the given value is not nil.
func (wtodc *WmsTransferOrderDetailCreate) SetNillableOrderID(s *string) *WmsTransferOrderDetailCreate {
	if s != nil {
		wtodc.SetOrderID(*s)
	}
	return wtodc
}

// SetMaterialID sets the "material_id" field.
func (wtodc *WmsTransferOrderDetailCreate) SetMaterialID(s string) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetMaterialID(s)
	return wtodc
}

// SetNillableMaterialID sets the "material_id" field if the given value is not nil.
func (wtodc *WmsTransferOrderDetailCreate) SetNillableMaterialID(s *string) *WmsTransferOrderDetailCreate {
	if s != nil {
		wtodc.SetMaterialID(*s)
	}
	return wtodc
}

// SetMaterialName sets the "material_name" field.
func (wtodc *WmsTransferOrderDetailCreate) SetMaterialName(s string) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetMaterialName(s)
	return wtodc
}

// SetNillableMaterialName sets the "material_name" field if the given value is not nil.
func (wtodc *WmsTransferOrderDetailCreate) SetNillableMaterialName(s *string) *WmsTransferOrderDetailCreate {
	if s != nil {
		wtodc.SetMaterialName(*s)
	}
	return wtodc
}

// SetCode sets the "code" field.
func (wtodc *WmsTransferOrderDetailCreate) SetCode(s string) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetCode(s)
	return wtodc
}

// SetEquipmentID sets the "equipment_id" field.
func (wtodc *WmsTransferOrderDetailCreate) SetEquipmentID(s string) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetEquipmentID(s)
	return wtodc
}

// SetNillableEquipmentID sets the "equipment_id" field if the given value is not nil.
func (wtodc *WmsTransferOrderDetailCreate) SetNillableEquipmentID(s *string) *WmsTransferOrderDetailCreate {
	if s != nil {
		wtodc.SetEquipmentID(*s)
	}
	return wtodc
}

// SetEquipmentTypeID sets the "equipment_type_id" field.
func (wtodc *WmsTransferOrderDetailCreate) SetEquipmentTypeID(s string) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetEquipmentTypeID(s)
	return wtodc
}

// SetNillableEquipmentTypeID sets the "equipment_type_id" field if the given value is not nil.
func (wtodc *WmsTransferOrderDetailCreate) SetNillableEquipmentTypeID(s *string) *WmsTransferOrderDetailCreate {
	if s != nil {
		wtodc.SetEquipmentTypeID(*s)
	}
	return wtodc
}

// SetFeature sets the "feature" field.
func (wtodc *WmsTransferOrderDetailCreate) SetFeature(m map[string]interface{}) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetFeature(m)
	return wtodc
}

// SetRepositoryID sets the "repository_id" field.
func (wtodc *WmsTransferOrderDetailCreate) SetRepositoryID(s string) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetRepositoryID(s)
	return wtodc
}

// SetNillableRepositoryID sets the "repository_id" field if the given value is not nil.
func (wtodc *WmsTransferOrderDetailCreate) SetNillableRepositoryID(s *string) *WmsTransferOrderDetailCreate {
	if s != nil {
		wtodc.SetRepositoryID(*s)
	}
	return wtodc
}

// SetRepositoryAreaID sets the "repository_area_id" field.
func (wtodc *WmsTransferOrderDetailCreate) SetRepositoryAreaID(s string) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetRepositoryAreaID(s)
	return wtodc
}

// SetNillableRepositoryAreaID sets the "repository_area_id" field if the given value is not nil.
func (wtodc *WmsTransferOrderDetailCreate) SetNillableRepositoryAreaID(s *string) *WmsTransferOrderDetailCreate {
	if s != nil {
		wtodc.SetRepositoryAreaID(*s)
	}
	return wtodc
}

// SetRepositoryPositionID sets the "repository_position_id" field.
func (wtodc *WmsTransferOrderDetailCreate) SetRepositoryPositionID(s string) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetRepositoryPositionID(s)
	return wtodc
}

// SetNillableRepositoryPositionID sets the "repository_position_id" field if the given value is not nil.
func (wtodc *WmsTransferOrderDetailCreate) SetNillableRepositoryPositionID(s *string) *WmsTransferOrderDetailCreate {
	if s != nil {
		wtodc.SetRepositoryPositionID(*s)
	}
	return wtodc
}

// SetOwnerID sets the "owner_id" field.
func (wtodc *WmsTransferOrderDetailCreate) SetOwnerID(s string) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetOwnerID(s)
	return wtodc
}

// SetNillableOwnerID sets the "owner_id" field if the given value is not nil.
func (wtodc *WmsTransferOrderDetailCreate) SetNillableOwnerID(s *string) *WmsTransferOrderDetailCreate {
	if s != nil {
		wtodc.SetOwnerID(*s)
	}
	return wtodc
}

// SetModelNo sets the "ModelNo" field.
func (wtodc *WmsTransferOrderDetailCreate) SetModelNo(s string) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetModelNo(s)
	return wtodc
}

// SetMeasureUnitID sets the "measure_unit_id" field.
func (wtodc *WmsTransferOrderDetailCreate) SetMeasureUnitID(s string) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetMeasureUnitID(s)
	return wtodc
}

// SetNillableMeasureUnitID sets the "measure_unit_id" field if the given value is not nil.
func (wtodc *WmsTransferOrderDetailCreate) SetNillableMeasureUnitID(s *string) *WmsTransferOrderDetailCreate {
	if s != nil {
		wtodc.SetMeasureUnitID(*s)
	}
	return wtodc
}

// SetNum sets the "num" field.
func (wtodc *WmsTransferOrderDetailCreate) SetNum(u uint32) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetNum(u)
	return wtodc
}

// SetTransferReason sets the "transfer_reason" field.
func (wtodc *WmsTransferOrderDetailCreate) SetTransferReason(s string) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetTransferReason(s)
	return wtodc
}

// SetNillableTransferReason sets the "transfer_reason" field if the given value is not nil.
func (wtodc *WmsTransferOrderDetailCreate) SetNillableTransferReason(s *string) *WmsTransferOrderDetailCreate {
	if s != nil {
		wtodc.SetTransferReason(*s)
	}
	return wtodc
}

// SetTransferTime sets the "transfer_time" field.
func (wtodc *WmsTransferOrderDetailCreate) SetTransferTime(t time.Time) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetTransferTime(t)
	return wtodc
}

// SetNillableTransferTime sets the "transfer_time" field if the given value is not nil.
func (wtodc *WmsTransferOrderDetailCreate) SetNillableTransferTime(t *time.Time) *WmsTransferOrderDetailCreate {
	if t != nil {
		wtodc.SetTransferTime(*t)
	}
	return wtodc
}

// SetID sets the "id" field.
func (wtodc *WmsTransferOrderDetailCreate) SetID(s string) *WmsTransferOrderDetailCreate {
	wtodc.mutation.SetID(s)
	return wtodc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (wtodc *WmsTransferOrderDetailCreate) SetNillableID(s *string) *WmsTransferOrderDetailCreate {
	if s != nil {
		wtodc.SetID(*s)
	}
	return wtodc
}

// Mutation returns the WmsTransferOrderDetailMutation object of the builder.
func (wtodc *WmsTransferOrderDetailCreate) Mutation() *WmsTransferOrderDetailMutation {
	return wtodc.mutation
}

// Save creates the WmsTransferOrderDetail in the database.
func (wtodc *WmsTransferOrderDetailCreate) Save(ctx context.Context) (*WmsTransferOrderDetail, error) {
	wtodc.defaults()
	return withHooks(ctx, wtodc.sqlSave, wtodc.mutation, wtodc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (wtodc *WmsTransferOrderDetailCreate) SaveX(ctx context.Context) *WmsTransferOrderDetail {
	v, err := wtodc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (wtodc *WmsTransferOrderDetailCreate) Exec(ctx context.Context) error {
	_, err := wtodc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wtodc *WmsTransferOrderDetailCreate) ExecX(ctx context.Context) {
	if err := wtodc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wtodc *WmsTransferOrderDetailCreate) defaults() {
	if _, ok := wtodc.mutation.CreatedAt(); !ok {
		v := wmstransferorderdetail.DefaultCreatedAt
		wtodc.mutation.SetCreatedAt(v)
	}
	if _, ok := wtodc.mutation.UpdatedAt(); !ok {
		v := wmstransferorderdetail.DefaultUpdatedAt()
		wtodc.mutation.SetUpdatedAt(v)
	}
	if _, ok := wtodc.mutation.Feature(); !ok {
		v := wmstransferorderdetail.DefaultFeature
		wtodc.mutation.SetFeature(v)
	}
	if _, ok := wtodc.mutation.ID(); !ok {
		v := wmstransferorderdetail.DefaultID()
		wtodc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (wtodc *WmsTransferOrderDetailCreate) check() error {
	if _, ok := wtodc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "WmsTransferOrderDetail.created_at"`)}
	}
	if _, ok := wtodc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "WmsTransferOrderDetail.updated_at"`)}
	}
	if _, ok := wtodc.mutation.Code(); !ok {
		return &ValidationError{Name: "code", err: errors.New(`ent: missing required field "WmsTransferOrderDetail.code"`)}
	}
	if _, ok := wtodc.mutation.ModelNo(); !ok {
		return &ValidationError{Name: "ModelNo", err: errors.New(`ent: missing required field "WmsTransferOrderDetail.ModelNo"`)}
	}
	if _, ok := wtodc.mutation.Num(); !ok {
		return &ValidationError{Name: "num", err: errors.New(`ent: missing required field "WmsTransferOrderDetail.num"`)}
	}
	return nil
}

func (wtodc *WmsTransferOrderDetailCreate) sqlSave(ctx context.Context) (*WmsTransferOrderDetail, error) {
	if err := wtodc.check(); err != nil {
		return nil, err
	}
	_node, _spec := wtodc.createSpec()
	if err := sqlgraph.CreateNode(ctx, wtodc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected WmsTransferOrderDetail.ID type: %T", _spec.ID.Value)
		}
	}
	wtodc.mutation.id = &_node.ID
	wtodc.mutation.done = true
	return _node, nil
}

func (wtodc *WmsTransferOrderDetailCreate) createSpec() (*WmsTransferOrderDetail, *sqlgraph.CreateSpec) {
	var (
		_node = &WmsTransferOrderDetail{config: wtodc.config}
		_spec = sqlgraph.NewCreateSpec(wmstransferorderdetail.Table, sqlgraph.NewFieldSpec(wmstransferorderdetail.FieldID, field.TypeString))
	)
	_spec.OnConflict = wtodc.conflict
	if id, ok := wtodc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := wtodc.mutation.CreatedAt(); ok {
		_spec.SetField(wmstransferorderdetail.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := wtodc.mutation.UpdatedAt(); ok {
		_spec.SetField(wmstransferorderdetail.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := wtodc.mutation.CreatedBy(); ok {
		_spec.SetField(wmstransferorderdetail.FieldCreatedBy, field.TypeString, value)
		_node.CreatedBy = value
	}
	if value, ok := wtodc.mutation.UpdatedBy(); ok {
		_spec.SetField(wmstransferorderdetail.FieldUpdatedBy, field.TypeString, value)
		_node.UpdatedBy = &value
	}
	if value, ok := wtodc.mutation.Remark(); ok {
		_spec.SetField(wmstransferorderdetail.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := wtodc.mutation.OrderID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldOrderID, field.TypeString, value)
		_node.OrderID = value
	}
	if value, ok := wtodc.mutation.MaterialID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldMaterialID, field.TypeString, value)
		_node.MaterialID = value
	}
	if value, ok := wtodc.mutation.MaterialName(); ok {
		_spec.SetField(wmstransferorderdetail.FieldMaterialName, field.TypeString, value)
		_node.MaterialName = value
	}
	if value, ok := wtodc.mutation.Code(); ok {
		_spec.SetField(wmstransferorderdetail.FieldCode, field.TypeString, value)
		_node.Code = value
	}
	if value, ok := wtodc.mutation.EquipmentID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldEquipmentID, field.TypeString, value)
		_node.EquipmentID = value
	}
	if value, ok := wtodc.mutation.EquipmentTypeID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldEquipmentTypeID, field.TypeString, value)
		_node.EquipmentTypeID = value
	}
	if value, ok := wtodc.mutation.Feature(); ok {
		_spec.SetField(wmstransferorderdetail.FieldFeature, field.TypeJSON, value)
		_node.Feature = value
	}
	if value, ok := wtodc.mutation.RepositoryID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldRepositoryID, field.TypeString, value)
		_node.RepositoryID = &value
	}
	if value, ok := wtodc.mutation.RepositoryAreaID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldRepositoryAreaID, field.TypeString, value)
		_node.RepositoryAreaID = &value
	}
	if value, ok := wtodc.mutation.RepositoryPositionID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldRepositoryPositionID, field.TypeString, value)
		_node.RepositoryPositionID = &value
	}
	if value, ok := wtodc.mutation.OwnerID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldOwnerID, field.TypeString, value)
		_node.OwnerID = &value
	}
	if value, ok := wtodc.mutation.ModelNo(); ok {
		_spec.SetField(wmstransferorderdetail.FieldModelNo, field.TypeString, value)
		_node.ModelNo = value
	}
	if value, ok := wtodc.mutation.MeasureUnitID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldMeasureUnitID, field.TypeString, value)
		_node.MeasureUnitID = value
	}
	if value, ok := wtodc.mutation.Num(); ok {
		_spec.SetField(wmstransferorderdetail.FieldNum, field.TypeUint32, value)
		_node.Num = value
	}
	if value, ok := wtodc.mutation.TransferReason(); ok {
		_spec.SetField(wmstransferorderdetail.FieldTransferReason, field.TypeString, value)
		_node.TransferReason = &value
	}
	if value, ok := wtodc.mutation.TransferTime(); ok {
		_spec.SetField(wmstransferorderdetail.FieldTransferTime, field.TypeTime, value)
		_node.TransferTime = &value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.WmsTransferOrderDetail.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.WmsTransferOrderDetailUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (wtodc *WmsTransferOrderDetailCreate) OnConflict(opts ...sql.ConflictOption) *WmsTransferOrderDetailUpsertOne {
	wtodc.conflict = opts
	return &WmsTransferOrderDetailUpsertOne{
		create: wtodc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.WmsTransferOrderDetail.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (wtodc *WmsTransferOrderDetailCreate) OnConflictColumns(columns ...string) *WmsTransferOrderDetailUpsertOne {
	wtodc.conflict = append(wtodc.conflict, sql.ConflictColumns(columns...))
	return &WmsTransferOrderDetailUpsertOne{
		create: wtodc,
	}
}

type (
	// WmsTransferOrderDetailUpsertOne is the builder for "upsert"-ing
	//  one WmsTransferOrderDetail node.
	WmsTransferOrderDetailUpsertOne struct {
		create *WmsTransferOrderDetailCreate
	}

	// WmsTransferOrderDetailUpsert is the "OnConflict" setter.
	WmsTransferOrderDetailUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *WmsTransferOrderDetailUpsert) SetUpdatedAt(v time.Time) *WmsTransferOrderDetailUpsert {
	u.Set(wmstransferorderdetail.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsert) UpdateUpdatedAt() *WmsTransferOrderDetailUpsert {
	u.SetExcluded(wmstransferorderdetail.FieldUpdatedAt)
	return u
}

// SetCreatedBy sets the "created_by" field.
func (u *WmsTransferOrderDetailUpsert) SetCreatedBy(v string) *WmsTransferOrderDetailUpsert {
	u.Set(wmstransferorderdetail.FieldCreatedBy, v)
	return u
}

// UpdateCreatedBy sets the "created_by" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsert) UpdateCreatedBy() *WmsTransferOrderDetailUpsert {
	u.SetExcluded(wmstransferorderdetail.FieldCreatedBy)
	return u
}

// ClearCreatedBy clears the value of the "created_by" field.
func (u *WmsTransferOrderDetailUpsert) ClearCreatedBy() *WmsTransferOrderDetailUpsert {
	u.SetNull(wmstransferorderdetail.FieldCreatedBy)
	return u
}

// SetUpdatedBy sets the "updated_by" field.
func (u *WmsTransferOrderDetailUpsert) SetUpdatedBy(v string) *WmsTransferOrderDetailUpsert {
	u.Set(wmstransferorderdetail.FieldUpdatedBy, v)
	return u
}

// UpdateUpdatedBy sets the "updated_by" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsert) UpdateUpdatedBy() *WmsTransferOrderDetailUpsert {
	u.SetExcluded(wmstransferorderdetail.FieldUpdatedBy)
	return u
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (u *WmsTransferOrderDetailUpsert) ClearUpdatedBy() *WmsTransferOrderDetailUpsert {
	u.SetNull(wmstransferorderdetail.FieldUpdatedBy)
	return u
}

// SetRemark sets the "remark" field.
func (u *WmsTransferOrderDetailUpsert) SetRemark(v string) *WmsTransferOrderDetailUpsert {
	u.Set(wmstransferorderdetail.FieldRemark, v)
	return u
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsert) UpdateRemark() *WmsTransferOrderDetailUpsert {
	u.SetExcluded(wmstransferorderdetail.FieldRemark)
	return u
}

// ClearRemark clears the value of the "remark" field.
func (u *WmsTransferOrderDetailUpsert) ClearRemark() *WmsTransferOrderDetailUpsert {
	u.SetNull(wmstransferorderdetail.FieldRemark)
	return u
}

// SetOrderID sets the "order_id" field.
func (u *WmsTransferOrderDetailUpsert) SetOrderID(v string) *WmsTransferOrderDetailUpsert {
	u.Set(wmstransferorderdetail.FieldOrderID, v)
	return u
}

// UpdateOrderID sets the "order_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsert) UpdateOrderID() *WmsTransferOrderDetailUpsert {
	u.SetExcluded(wmstransferorderdetail.FieldOrderID)
	return u
}

// ClearOrderID clears the value of the "order_id" field.
func (u *WmsTransferOrderDetailUpsert) ClearOrderID() *WmsTransferOrderDetailUpsert {
	u.SetNull(wmstransferorderdetail.FieldOrderID)
	return u
}

// SetMaterialID sets the "material_id" field.
func (u *WmsTransferOrderDetailUpsert) SetMaterialID(v string) *WmsTransferOrderDetailUpsert {
	u.Set(wmstransferorderdetail.FieldMaterialID, v)
	return u
}

// UpdateMaterialID sets the "material_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsert) UpdateMaterialID() *WmsTransferOrderDetailUpsert {
	u.SetExcluded(wmstransferorderdetail.FieldMaterialID)
	return u
}

// ClearMaterialID clears the value of the "material_id" field.
func (u *WmsTransferOrderDetailUpsert) ClearMaterialID() *WmsTransferOrderDetailUpsert {
	u.SetNull(wmstransferorderdetail.FieldMaterialID)
	return u
}

// SetMaterialName sets the "material_name" field.
func (u *WmsTransferOrderDetailUpsert) SetMaterialName(v string) *WmsTransferOrderDetailUpsert {
	u.Set(wmstransferorderdetail.FieldMaterialName, v)
	return u
}

// UpdateMaterialName sets the "material_name" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsert) UpdateMaterialName() *WmsTransferOrderDetailUpsert {
	u.SetExcluded(wmstransferorderdetail.FieldMaterialName)
	return u
}

// ClearMaterialName clears the value of the "material_name" field.
func (u *WmsTransferOrderDetailUpsert) ClearMaterialName() *WmsTransferOrderDetailUpsert {
	u.SetNull(wmstransferorderdetail.FieldMaterialName)
	return u
}

// SetCode sets the "code" field.
func (u *WmsTransferOrderDetailUpsert) SetCode(v string) *WmsTransferOrderDetailUpsert {
	u.Set(wmstransferorderdetail.FieldCode, v)
	return u
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsert) UpdateCode() *WmsTransferOrderDetailUpsert {
	u.SetExcluded(wmstransferorderdetail.FieldCode)
	return u
}

// SetEquipmentID sets the "equipment_id" field.
func (u *WmsTransferOrderDetailUpsert) SetEquipmentID(v string) *WmsTransferOrderDetailUpsert {
	u.Set(wmstransferorderdetail.FieldEquipmentID, v)
	return u
}

// UpdateEquipmentID sets the "equipment_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsert) UpdateEquipmentID() *WmsTransferOrderDetailUpsert {
	u.SetExcluded(wmstransferorderdetail.FieldEquipmentID)
	return u
}

// ClearEquipmentID clears the value of the "equipment_id" field.
func (u *WmsTransferOrderDetailUpsert) ClearEquipmentID() *WmsTransferOrderDetailUpsert {
	u.SetNull(wmstransferorderdetail.FieldEquipmentID)
	return u
}

// SetEquipmentTypeID sets the "equipment_type_id" field.
func (u *WmsTransferOrderDetailUpsert) SetEquipmentTypeID(v string) *WmsTransferOrderDetailUpsert {
	u.Set(wmstransferorderdetail.FieldEquipmentTypeID, v)
	return u
}

// UpdateEquipmentTypeID sets the "equipment_type_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsert) UpdateEquipmentTypeID() *WmsTransferOrderDetailUpsert {
	u.SetExcluded(wmstransferorderdetail.FieldEquipmentTypeID)
	return u
}

// ClearEquipmentTypeID clears the value of the "equipment_type_id" field.
func (u *WmsTransferOrderDetailUpsert) ClearEquipmentTypeID() *WmsTransferOrderDetailUpsert {
	u.SetNull(wmstransferorderdetail.FieldEquipmentTypeID)
	return u
}

// SetFeature sets the "feature" field.
func (u *WmsTransferOrderDetailUpsert) SetFeature(v map[string]interface{}) *WmsTransferOrderDetailUpsert {
	u.Set(wmstransferorderdetail.FieldFeature, v)
	return u
}

// UpdateFeature sets the "feature" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsert) UpdateFeature() *WmsTransferOrderDetailUpsert {
	u.SetExcluded(wmstransferorderdetail.FieldFeature)
	return u
}

// ClearFeature clears the value of the "feature" field.
func (u *WmsTransferOrderDetailUpsert) ClearFeature() *WmsTransferOrderDetailUpsert {
	u.SetNull(wmstransferorderdetail.FieldFeature)
	return u
}

// SetRepositoryID sets the "repository_id" field.
func (u *WmsTransferOrderDetailUpsert) SetRepositoryID(v string) *WmsTransferOrderDetailUpsert {
	u.Set(wmstransferorderdetail.FieldRepositoryID, v)
	return u
}

// UpdateRepositoryID sets the "repository_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsert) UpdateRepositoryID() *WmsTransferOrderDetailUpsert {
	u.SetExcluded(wmstransferorderdetail.FieldRepositoryID)
	return u
}

// ClearRepositoryID clears the value of the "repository_id" field.
func (u *WmsTransferOrderDetailUpsert) ClearRepositoryID() *WmsTransferOrderDetailUpsert {
	u.SetNull(wmstransferorderdetail.FieldRepositoryID)
	return u
}

// SetRepositoryAreaID sets the "repository_area_id" field.
func (u *WmsTransferOrderDetailUpsert) SetRepositoryAreaID(v string) *WmsTransferOrderDetailUpsert {
	u.Set(wmstransferorderdetail.FieldRepositoryAreaID, v)
	return u
}

// UpdateRepositoryAreaID sets the "repository_area_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsert) UpdateRepositoryAreaID() *WmsTransferOrderDetailUpsert {
	u.SetExcluded(wmstransferorderdetail.FieldRepositoryAreaID)
	return u
}

// ClearRepositoryAreaID clears the value of the "repository_area_id" field.
func (u *WmsTransferOrderDetailUpsert) ClearRepositoryAreaID() *WmsTransferOrderDetailUpsert {
	u.SetNull(wmstransferorderdetail.FieldRepositoryAreaID)
	return u
}

// SetRepositoryPositionID sets the "repository_position_id" field.
func (u *WmsTransferOrderDetailUpsert) SetRepositoryPositionID(v string) *WmsTransferOrderDetailUpsert {
	u.Set(wmstransferorderdetail.FieldRepositoryPositionID, v)
	return u
}

// UpdateRepositoryPositionID sets the "repository_position_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsert) UpdateRepositoryPositionID() *WmsTransferOrderDetailUpsert {
	u.SetExcluded(wmstransferorderdetail.FieldRepositoryPositionID)
	return u
}

// ClearRepositoryPositionID clears the value of the "repository_position_id" field.
func (u *WmsTransferOrderDetailUpsert) ClearRepositoryPositionID() *WmsTransferOrderDetailUpsert {
	u.SetNull(wmstransferorderdetail.FieldRepositoryPositionID)
	return u
}

// SetOwnerID sets the "owner_id" field.
func (u *WmsTransferOrderDetailUpsert) SetOwnerID(v string) *WmsTransferOrderDetailUpsert {
	u.Set(wmstransferorderdetail.FieldOwnerID, v)
	return u
}

// UpdateOwnerID sets the "owner_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsert) UpdateOwnerID() *WmsTransferOrderDetailUpsert {
	u.SetExcluded(wmstransferorderdetail.FieldOwnerID)
	return u
}

// ClearOwnerID clears the value of the "owner_id" field.
func (u *WmsTransferOrderDetailUpsert) ClearOwnerID() *WmsTransferOrderDetailUpsert {
	u.SetNull(wmstransferorderdetail.FieldOwnerID)
	return u
}

// SetModelNo sets the "ModelNo" field.
func (u *WmsTransferOrderDetailUpsert) SetModelNo(v string) *WmsTransferOrderDetailUpsert {
	u.Set(wmstransferorderdetail.FieldModelNo, v)
	return u
}

// UpdateModelNo sets the "ModelNo" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsert) UpdateModelNo() *WmsTransferOrderDetailUpsert {
	u.SetExcluded(wmstransferorderdetail.FieldModelNo)
	return u
}

// SetMeasureUnitID sets the "measure_unit_id" field.
func (u *WmsTransferOrderDetailUpsert) SetMeasureUnitID(v string) *WmsTransferOrderDetailUpsert {
	u.Set(wmstransferorderdetail.FieldMeasureUnitID, v)
	return u
}

// UpdateMeasureUnitID sets the "measure_unit_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsert) UpdateMeasureUnitID() *WmsTransferOrderDetailUpsert {
	u.SetExcluded(wmstransferorderdetail.FieldMeasureUnitID)
	return u
}

// ClearMeasureUnitID clears the value of the "measure_unit_id" field.
func (u *WmsTransferOrderDetailUpsert) ClearMeasureUnitID() *WmsTransferOrderDetailUpsert {
	u.SetNull(wmstransferorderdetail.FieldMeasureUnitID)
	return u
}

// SetNum sets the "num" field.
func (u *WmsTransferOrderDetailUpsert) SetNum(v uint32) *WmsTransferOrderDetailUpsert {
	u.Set(wmstransferorderdetail.FieldNum, v)
	return u
}

// UpdateNum sets the "num" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsert) UpdateNum() *WmsTransferOrderDetailUpsert {
	u.SetExcluded(wmstransferorderdetail.FieldNum)
	return u
}

// AddNum adds v to the "num" field.
func (u *WmsTransferOrderDetailUpsert) AddNum(v uint32) *WmsTransferOrderDetailUpsert {
	u.Add(wmstransferorderdetail.FieldNum, v)
	return u
}

// SetTransferReason sets the "transfer_reason" field.
func (u *WmsTransferOrderDetailUpsert) SetTransferReason(v string) *WmsTransferOrderDetailUpsert {
	u.Set(wmstransferorderdetail.FieldTransferReason, v)
	return u
}

// UpdateTransferReason sets the "transfer_reason" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsert) UpdateTransferReason() *WmsTransferOrderDetailUpsert {
	u.SetExcluded(wmstransferorderdetail.FieldTransferReason)
	return u
}

// ClearTransferReason clears the value of the "transfer_reason" field.
func (u *WmsTransferOrderDetailUpsert) ClearTransferReason() *WmsTransferOrderDetailUpsert {
	u.SetNull(wmstransferorderdetail.FieldTransferReason)
	return u
}

// SetTransferTime sets the "transfer_time" field.
func (u *WmsTransferOrderDetailUpsert) SetTransferTime(v time.Time) *WmsTransferOrderDetailUpsert {
	u.Set(wmstransferorderdetail.FieldTransferTime, v)
	return u
}

// UpdateTransferTime sets the "transfer_time" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsert) UpdateTransferTime() *WmsTransferOrderDetailUpsert {
	u.SetExcluded(wmstransferorderdetail.FieldTransferTime)
	return u
}

// ClearTransferTime clears the value of the "transfer_time" field.
func (u *WmsTransferOrderDetailUpsert) ClearTransferTime() *WmsTransferOrderDetailUpsert {
	u.SetNull(wmstransferorderdetail.FieldTransferTime)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.WmsTransferOrderDetail.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(wmstransferorderdetail.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *WmsTransferOrderDetailUpsertOne) UpdateNewValues() *WmsTransferOrderDetailUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(wmstransferorderdetail.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(wmstransferorderdetail.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.WmsTransferOrderDetail.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *WmsTransferOrderDetailUpsertOne) Ignore() *WmsTransferOrderDetailUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *WmsTransferOrderDetailUpsertOne) DoNothing() *WmsTransferOrderDetailUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the WmsTransferOrderDetailCreate.OnConflict
// documentation for more info.
func (u *WmsTransferOrderDetailUpsertOne) Update(set func(*WmsTransferOrderDetailUpsert)) *WmsTransferOrderDetailUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&WmsTransferOrderDetailUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *WmsTransferOrderDetailUpsertOne) SetUpdatedAt(v time.Time) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertOne) UpdateUpdatedAt() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetCreatedBy sets the "created_by" field.
func (u *WmsTransferOrderDetailUpsertOne) SetCreatedBy(v string) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetCreatedBy(v)
	})
}

// UpdateCreatedBy sets the "created_by" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertOne) UpdateCreatedBy() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateCreatedBy()
	})
}

// ClearCreatedBy clears the value of the "created_by" field.
func (u *WmsTransferOrderDetailUpsertOne) ClearCreatedBy() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearCreatedBy()
	})
}

// SetUpdatedBy sets the "updated_by" field.
func (u *WmsTransferOrderDetailUpsertOne) SetUpdatedBy(v string) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetUpdatedBy(v)
	})
}

// UpdateUpdatedBy sets the "updated_by" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertOne) UpdateUpdatedBy() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateUpdatedBy()
	})
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (u *WmsTransferOrderDetailUpsertOne) ClearUpdatedBy() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearUpdatedBy()
	})
}

// SetRemark sets the "remark" field.
func (u *WmsTransferOrderDetailUpsertOne) SetRemark(v string) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertOne) UpdateRemark() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *WmsTransferOrderDetailUpsertOne) ClearRemark() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearRemark()
	})
}

// SetOrderID sets the "order_id" field.
func (u *WmsTransferOrderDetailUpsertOne) SetOrderID(v string) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetOrderID(v)
	})
}

// UpdateOrderID sets the "order_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertOne) UpdateOrderID() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateOrderID()
	})
}

// ClearOrderID clears the value of the "order_id" field.
func (u *WmsTransferOrderDetailUpsertOne) ClearOrderID() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearOrderID()
	})
}

// SetMaterialID sets the "material_id" field.
func (u *WmsTransferOrderDetailUpsertOne) SetMaterialID(v string) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetMaterialID(v)
	})
}

// UpdateMaterialID sets the "material_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertOne) UpdateMaterialID() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateMaterialID()
	})
}

// ClearMaterialID clears the value of the "material_id" field.
func (u *WmsTransferOrderDetailUpsertOne) ClearMaterialID() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearMaterialID()
	})
}

// SetMaterialName sets the "material_name" field.
func (u *WmsTransferOrderDetailUpsertOne) SetMaterialName(v string) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetMaterialName(v)
	})
}

// UpdateMaterialName sets the "material_name" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertOne) UpdateMaterialName() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateMaterialName()
	})
}

// ClearMaterialName clears the value of the "material_name" field.
func (u *WmsTransferOrderDetailUpsertOne) ClearMaterialName() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearMaterialName()
	})
}

// SetCode sets the "code" field.
func (u *WmsTransferOrderDetailUpsertOne) SetCode(v string) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertOne) UpdateCode() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateCode()
	})
}

// SetEquipmentID sets the "equipment_id" field.
func (u *WmsTransferOrderDetailUpsertOne) SetEquipmentID(v string) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetEquipmentID(v)
	})
}

// UpdateEquipmentID sets the "equipment_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertOne) UpdateEquipmentID() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateEquipmentID()
	})
}

// ClearEquipmentID clears the value of the "equipment_id" field.
func (u *WmsTransferOrderDetailUpsertOne) ClearEquipmentID() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearEquipmentID()
	})
}

// SetEquipmentTypeID sets the "equipment_type_id" field.
func (u *WmsTransferOrderDetailUpsertOne) SetEquipmentTypeID(v string) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetEquipmentTypeID(v)
	})
}

// UpdateEquipmentTypeID sets the "equipment_type_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertOne) UpdateEquipmentTypeID() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateEquipmentTypeID()
	})
}

// ClearEquipmentTypeID clears the value of the "equipment_type_id" field.
func (u *WmsTransferOrderDetailUpsertOne) ClearEquipmentTypeID() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearEquipmentTypeID()
	})
}

// SetFeature sets the "feature" field.
func (u *WmsTransferOrderDetailUpsertOne) SetFeature(v map[string]interface{}) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetFeature(v)
	})
}

// UpdateFeature sets the "feature" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertOne) UpdateFeature() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateFeature()
	})
}

// ClearFeature clears the value of the "feature" field.
func (u *WmsTransferOrderDetailUpsertOne) ClearFeature() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearFeature()
	})
}

// SetRepositoryID sets the "repository_id" field.
func (u *WmsTransferOrderDetailUpsertOne) SetRepositoryID(v string) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetRepositoryID(v)
	})
}

// UpdateRepositoryID sets the "repository_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertOne) UpdateRepositoryID() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateRepositoryID()
	})
}

// ClearRepositoryID clears the value of the "repository_id" field.
func (u *WmsTransferOrderDetailUpsertOne) ClearRepositoryID() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearRepositoryID()
	})
}

// SetRepositoryAreaID sets the "repository_area_id" field.
func (u *WmsTransferOrderDetailUpsertOne) SetRepositoryAreaID(v string) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetRepositoryAreaID(v)
	})
}

// UpdateRepositoryAreaID sets the "repository_area_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertOne) UpdateRepositoryAreaID() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateRepositoryAreaID()
	})
}

// ClearRepositoryAreaID clears the value of the "repository_area_id" field.
func (u *WmsTransferOrderDetailUpsertOne) ClearRepositoryAreaID() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearRepositoryAreaID()
	})
}

// SetRepositoryPositionID sets the "repository_position_id" field.
func (u *WmsTransferOrderDetailUpsertOne) SetRepositoryPositionID(v string) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetRepositoryPositionID(v)
	})
}

// UpdateRepositoryPositionID sets the "repository_position_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertOne) UpdateRepositoryPositionID() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateRepositoryPositionID()
	})
}

// ClearRepositoryPositionID clears the value of the "repository_position_id" field.
func (u *WmsTransferOrderDetailUpsertOne) ClearRepositoryPositionID() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearRepositoryPositionID()
	})
}

// SetOwnerID sets the "owner_id" field.
func (u *WmsTransferOrderDetailUpsertOne) SetOwnerID(v string) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetOwnerID(v)
	})
}

// UpdateOwnerID sets the "owner_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertOne) UpdateOwnerID() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateOwnerID()
	})
}

// ClearOwnerID clears the value of the "owner_id" field.
func (u *WmsTransferOrderDetailUpsertOne) ClearOwnerID() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearOwnerID()
	})
}

// SetModelNo sets the "ModelNo" field.
func (u *WmsTransferOrderDetailUpsertOne) SetModelNo(v string) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetModelNo(v)
	})
}

// UpdateModelNo sets the "ModelNo" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertOne) UpdateModelNo() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateModelNo()
	})
}

// SetMeasureUnitID sets the "measure_unit_id" field.
func (u *WmsTransferOrderDetailUpsertOne) SetMeasureUnitID(v string) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetMeasureUnitID(v)
	})
}

// UpdateMeasureUnitID sets the "measure_unit_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertOne) UpdateMeasureUnitID() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateMeasureUnitID()
	})
}

// ClearMeasureUnitID clears the value of the "measure_unit_id" field.
func (u *WmsTransferOrderDetailUpsertOne) ClearMeasureUnitID() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearMeasureUnitID()
	})
}

// SetNum sets the "num" field.
func (u *WmsTransferOrderDetailUpsertOne) SetNum(v uint32) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetNum(v)
	})
}

// AddNum adds v to the "num" field.
func (u *WmsTransferOrderDetailUpsertOne) AddNum(v uint32) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.AddNum(v)
	})
}

// UpdateNum sets the "num" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertOne) UpdateNum() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateNum()
	})
}

// SetTransferReason sets the "transfer_reason" field.
func (u *WmsTransferOrderDetailUpsertOne) SetTransferReason(v string) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetTransferReason(v)
	})
}

// UpdateTransferReason sets the "transfer_reason" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertOne) UpdateTransferReason() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateTransferReason()
	})
}

// ClearTransferReason clears the value of the "transfer_reason" field.
func (u *WmsTransferOrderDetailUpsertOne) ClearTransferReason() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearTransferReason()
	})
}

// SetTransferTime sets the "transfer_time" field.
func (u *WmsTransferOrderDetailUpsertOne) SetTransferTime(v time.Time) *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetTransferTime(v)
	})
}

// UpdateTransferTime sets the "transfer_time" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertOne) UpdateTransferTime() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateTransferTime()
	})
}

// ClearTransferTime clears the value of the "transfer_time" field.
func (u *WmsTransferOrderDetailUpsertOne) ClearTransferTime() *WmsTransferOrderDetailUpsertOne {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearTransferTime()
	})
}

// Exec executes the query.
func (u *WmsTransferOrderDetailUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for WmsTransferOrderDetailCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *WmsTransferOrderDetailUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *WmsTransferOrderDetailUpsertOne) ID(ctx context.Context) (id string, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: WmsTransferOrderDetailUpsertOne.ID is not supported by MySQL driver. Use WmsTransferOrderDetailUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *WmsTransferOrderDetailUpsertOne) IDX(ctx context.Context) string {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// WmsTransferOrderDetailCreateBulk is the builder for creating many WmsTransferOrderDetail entities in bulk.
type WmsTransferOrderDetailCreateBulk struct {
	config
	err      error
	builders []*WmsTransferOrderDetailCreate
	conflict []sql.ConflictOption
}

// Save creates the WmsTransferOrderDetail entities in the database.
func (wtodcb *WmsTransferOrderDetailCreateBulk) Save(ctx context.Context) ([]*WmsTransferOrderDetail, error) {
	if wtodcb.err != nil {
		return nil, wtodcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(wtodcb.builders))
	nodes := make([]*WmsTransferOrderDetail, len(wtodcb.builders))
	mutators := make([]Mutator, len(wtodcb.builders))
	for i := range wtodcb.builders {
		func(i int, root context.Context) {
			builder := wtodcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*WmsTransferOrderDetailMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, wtodcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = wtodcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, wtodcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, wtodcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (wtodcb *WmsTransferOrderDetailCreateBulk) SaveX(ctx context.Context) []*WmsTransferOrderDetail {
	v, err := wtodcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (wtodcb *WmsTransferOrderDetailCreateBulk) Exec(ctx context.Context) error {
	_, err := wtodcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wtodcb *WmsTransferOrderDetailCreateBulk) ExecX(ctx context.Context) {
	if err := wtodcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.WmsTransferOrderDetail.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.WmsTransferOrderDetailUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (wtodcb *WmsTransferOrderDetailCreateBulk) OnConflict(opts ...sql.ConflictOption) *WmsTransferOrderDetailUpsertBulk {
	wtodcb.conflict = opts
	return &WmsTransferOrderDetailUpsertBulk{
		create: wtodcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.WmsTransferOrderDetail.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (wtodcb *WmsTransferOrderDetailCreateBulk) OnConflictColumns(columns ...string) *WmsTransferOrderDetailUpsertBulk {
	wtodcb.conflict = append(wtodcb.conflict, sql.ConflictColumns(columns...))
	return &WmsTransferOrderDetailUpsertBulk{
		create: wtodcb,
	}
}

// WmsTransferOrderDetailUpsertBulk is the builder for "upsert"-ing
// a bulk of WmsTransferOrderDetail nodes.
type WmsTransferOrderDetailUpsertBulk struct {
	create *WmsTransferOrderDetailCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.WmsTransferOrderDetail.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(wmstransferorderdetail.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *WmsTransferOrderDetailUpsertBulk) UpdateNewValues() *WmsTransferOrderDetailUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(wmstransferorderdetail.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(wmstransferorderdetail.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.WmsTransferOrderDetail.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *WmsTransferOrderDetailUpsertBulk) Ignore() *WmsTransferOrderDetailUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *WmsTransferOrderDetailUpsertBulk) DoNothing() *WmsTransferOrderDetailUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the WmsTransferOrderDetailCreateBulk.OnConflict
// documentation for more info.
func (u *WmsTransferOrderDetailUpsertBulk) Update(set func(*WmsTransferOrderDetailUpsert)) *WmsTransferOrderDetailUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&WmsTransferOrderDetailUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *WmsTransferOrderDetailUpsertBulk) SetUpdatedAt(v time.Time) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertBulk) UpdateUpdatedAt() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetCreatedBy sets the "created_by" field.
func (u *WmsTransferOrderDetailUpsertBulk) SetCreatedBy(v string) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetCreatedBy(v)
	})
}

// UpdateCreatedBy sets the "created_by" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertBulk) UpdateCreatedBy() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateCreatedBy()
	})
}

// ClearCreatedBy clears the value of the "created_by" field.
func (u *WmsTransferOrderDetailUpsertBulk) ClearCreatedBy() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearCreatedBy()
	})
}

// SetUpdatedBy sets the "updated_by" field.
func (u *WmsTransferOrderDetailUpsertBulk) SetUpdatedBy(v string) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetUpdatedBy(v)
	})
}

// UpdateUpdatedBy sets the "updated_by" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertBulk) UpdateUpdatedBy() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateUpdatedBy()
	})
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (u *WmsTransferOrderDetailUpsertBulk) ClearUpdatedBy() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearUpdatedBy()
	})
}

// SetRemark sets the "remark" field.
func (u *WmsTransferOrderDetailUpsertBulk) SetRemark(v string) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertBulk) UpdateRemark() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *WmsTransferOrderDetailUpsertBulk) ClearRemark() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearRemark()
	})
}

// SetOrderID sets the "order_id" field.
func (u *WmsTransferOrderDetailUpsertBulk) SetOrderID(v string) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetOrderID(v)
	})
}

// UpdateOrderID sets the "order_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertBulk) UpdateOrderID() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateOrderID()
	})
}

// ClearOrderID clears the value of the "order_id" field.
func (u *WmsTransferOrderDetailUpsertBulk) ClearOrderID() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearOrderID()
	})
}

// SetMaterialID sets the "material_id" field.
func (u *WmsTransferOrderDetailUpsertBulk) SetMaterialID(v string) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetMaterialID(v)
	})
}

// UpdateMaterialID sets the "material_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertBulk) UpdateMaterialID() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateMaterialID()
	})
}

// ClearMaterialID clears the value of the "material_id" field.
func (u *WmsTransferOrderDetailUpsertBulk) ClearMaterialID() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearMaterialID()
	})
}

// SetMaterialName sets the "material_name" field.
func (u *WmsTransferOrderDetailUpsertBulk) SetMaterialName(v string) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetMaterialName(v)
	})
}

// UpdateMaterialName sets the "material_name" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertBulk) UpdateMaterialName() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateMaterialName()
	})
}

// ClearMaterialName clears the value of the "material_name" field.
func (u *WmsTransferOrderDetailUpsertBulk) ClearMaterialName() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearMaterialName()
	})
}

// SetCode sets the "code" field.
func (u *WmsTransferOrderDetailUpsertBulk) SetCode(v string) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertBulk) UpdateCode() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateCode()
	})
}

// SetEquipmentID sets the "equipment_id" field.
func (u *WmsTransferOrderDetailUpsertBulk) SetEquipmentID(v string) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetEquipmentID(v)
	})
}

// UpdateEquipmentID sets the "equipment_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertBulk) UpdateEquipmentID() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateEquipmentID()
	})
}

// ClearEquipmentID clears the value of the "equipment_id" field.
func (u *WmsTransferOrderDetailUpsertBulk) ClearEquipmentID() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearEquipmentID()
	})
}

// SetEquipmentTypeID sets the "equipment_type_id" field.
func (u *WmsTransferOrderDetailUpsertBulk) SetEquipmentTypeID(v string) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetEquipmentTypeID(v)
	})
}

// UpdateEquipmentTypeID sets the "equipment_type_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertBulk) UpdateEquipmentTypeID() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateEquipmentTypeID()
	})
}

// ClearEquipmentTypeID clears the value of the "equipment_type_id" field.
func (u *WmsTransferOrderDetailUpsertBulk) ClearEquipmentTypeID() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearEquipmentTypeID()
	})
}

// SetFeature sets the "feature" field.
func (u *WmsTransferOrderDetailUpsertBulk) SetFeature(v map[string]interface{}) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetFeature(v)
	})
}

// UpdateFeature sets the "feature" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertBulk) UpdateFeature() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateFeature()
	})
}

// ClearFeature clears the value of the "feature" field.
func (u *WmsTransferOrderDetailUpsertBulk) ClearFeature() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearFeature()
	})
}

// SetRepositoryID sets the "repository_id" field.
func (u *WmsTransferOrderDetailUpsertBulk) SetRepositoryID(v string) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetRepositoryID(v)
	})
}

// UpdateRepositoryID sets the "repository_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertBulk) UpdateRepositoryID() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateRepositoryID()
	})
}

// ClearRepositoryID clears the value of the "repository_id" field.
func (u *WmsTransferOrderDetailUpsertBulk) ClearRepositoryID() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearRepositoryID()
	})
}

// SetRepositoryAreaID sets the "repository_area_id" field.
func (u *WmsTransferOrderDetailUpsertBulk) SetRepositoryAreaID(v string) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetRepositoryAreaID(v)
	})
}

// UpdateRepositoryAreaID sets the "repository_area_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertBulk) UpdateRepositoryAreaID() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateRepositoryAreaID()
	})
}

// ClearRepositoryAreaID clears the value of the "repository_area_id" field.
func (u *WmsTransferOrderDetailUpsertBulk) ClearRepositoryAreaID() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearRepositoryAreaID()
	})
}

// SetRepositoryPositionID sets the "repository_position_id" field.
func (u *WmsTransferOrderDetailUpsertBulk) SetRepositoryPositionID(v string) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetRepositoryPositionID(v)
	})
}

// UpdateRepositoryPositionID sets the "repository_position_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertBulk) UpdateRepositoryPositionID() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateRepositoryPositionID()
	})
}

// ClearRepositoryPositionID clears the value of the "repository_position_id" field.
func (u *WmsTransferOrderDetailUpsertBulk) ClearRepositoryPositionID() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearRepositoryPositionID()
	})
}

// SetOwnerID sets the "owner_id" field.
func (u *WmsTransferOrderDetailUpsertBulk) SetOwnerID(v string) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetOwnerID(v)
	})
}

// UpdateOwnerID sets the "owner_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertBulk) UpdateOwnerID() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateOwnerID()
	})
}

// ClearOwnerID clears the value of the "owner_id" field.
func (u *WmsTransferOrderDetailUpsertBulk) ClearOwnerID() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearOwnerID()
	})
}

// SetModelNo sets the "ModelNo" field.
func (u *WmsTransferOrderDetailUpsertBulk) SetModelNo(v string) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetModelNo(v)
	})
}

// UpdateModelNo sets the "ModelNo" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertBulk) UpdateModelNo() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateModelNo()
	})
}

// SetMeasureUnitID sets the "measure_unit_id" field.
func (u *WmsTransferOrderDetailUpsertBulk) SetMeasureUnitID(v string) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetMeasureUnitID(v)
	})
}

// UpdateMeasureUnitID sets the "measure_unit_id" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertBulk) UpdateMeasureUnitID() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateMeasureUnitID()
	})
}

// ClearMeasureUnitID clears the value of the "measure_unit_id" field.
func (u *WmsTransferOrderDetailUpsertBulk) ClearMeasureUnitID() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearMeasureUnitID()
	})
}

// SetNum sets the "num" field.
func (u *WmsTransferOrderDetailUpsertBulk) SetNum(v uint32) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetNum(v)
	})
}

// AddNum adds v to the "num" field.
func (u *WmsTransferOrderDetailUpsertBulk) AddNum(v uint32) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.AddNum(v)
	})
}

// UpdateNum sets the "num" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertBulk) UpdateNum() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateNum()
	})
}

// SetTransferReason sets the "transfer_reason" field.
func (u *WmsTransferOrderDetailUpsertBulk) SetTransferReason(v string) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetTransferReason(v)
	})
}

// UpdateTransferReason sets the "transfer_reason" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertBulk) UpdateTransferReason() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateTransferReason()
	})
}

// ClearTransferReason clears the value of the "transfer_reason" field.
func (u *WmsTransferOrderDetailUpsertBulk) ClearTransferReason() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearTransferReason()
	})
}

// SetTransferTime sets the "transfer_time" field.
func (u *WmsTransferOrderDetailUpsertBulk) SetTransferTime(v time.Time) *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.SetTransferTime(v)
	})
}

// UpdateTransferTime sets the "transfer_time" field to the value that was provided on create.
func (u *WmsTransferOrderDetailUpsertBulk) UpdateTransferTime() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.UpdateTransferTime()
	})
}

// ClearTransferTime clears the value of the "transfer_time" field.
func (u *WmsTransferOrderDetailUpsertBulk) ClearTransferTime() *WmsTransferOrderDetailUpsertBulk {
	return u.Update(func(s *WmsTransferOrderDetailUpsert) {
		s.ClearTransferTime()
	})
}

// Exec executes the query.
func (u *WmsTransferOrderDetailUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the WmsTransferOrderDetailCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for WmsTransferOrderDetailCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *WmsTransferOrderDetailUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
