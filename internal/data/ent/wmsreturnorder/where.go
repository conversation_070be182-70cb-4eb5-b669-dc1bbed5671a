// Code generated by ent, DO NOT EDIT.

package wmsreturnorder

import (
	"kratos-mono-demo/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContainsFold(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldUpdatedAt, v))
}

// CreatedBy applies equality check predicate on the "created_by" field. It's identical to CreatedByEQ.
func CreatedBy(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldCreatedBy, v))
}

// UpdatedBy applies equality check predicate on the "updated_by" field. It's identical to UpdatedByEQ.
func UpdatedBy(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldUpdatedBy, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldRemark, v))
}

// ContractUrls applies equality check predicate on the "contract_urls" field. It's identical to ContractUrlsEQ.
func ContractUrls(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldContractUrls, v))
}

// InvoiceUrls applies equality check predicate on the "invoice_urls" field. It's identical to InvoiceUrlsEQ.
func InvoiceUrls(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldInvoiceUrls, v))
}

// AuditUrls applies equality check predicate on the "audit_urls" field. It's identical to AuditUrlsEQ.
func AuditUrls(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldAuditUrls, v))
}

// OtherUrls applies equality check predicate on the "other_urls" field. It's identical to OtherUrlsEQ.
func OtherUrls(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldOtherUrls, v))
}

// OrderNo applies equality check predicate on the "order_no" field. It's identical to OrderNoEQ.
func OrderNo(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldOrderNo, v))
}

// RepositoryID applies equality check predicate on the "repository_id" field. It's identical to RepositoryIDEQ.
func RepositoryID(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldRepositoryID, v))
}

// Reason applies equality check predicate on the "reason" field. It's identical to ReasonEQ.
func Reason(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldReason, v))
}

// EquipmentNum applies equality check predicate on the "equipment_num" field. It's identical to EquipmentNumEQ.
func EquipmentNum(v uint32) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldEquipmentNum, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldStatus, v))
}

// ReturnTime applies equality check predicate on the "return_time" field. It's identical to ReturnTimeEQ.
func ReturnTime(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldReturnTime, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLTE(FieldUpdatedAt, v))
}

// CreatedByEQ applies the EQ predicate on the "created_by" field.
func CreatedByEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldCreatedBy, v))
}

// CreatedByNEQ applies the NEQ predicate on the "created_by" field.
func CreatedByNEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNEQ(FieldCreatedBy, v))
}

// CreatedByIn applies the In predicate on the "created_by" field.
func CreatedByIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIn(FieldCreatedBy, vs...))
}

// CreatedByNotIn applies the NotIn predicate on the "created_by" field.
func CreatedByNotIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotIn(FieldCreatedBy, vs...))
}

// CreatedByGT applies the GT predicate on the "created_by" field.
func CreatedByGT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGT(FieldCreatedBy, v))
}

// CreatedByGTE applies the GTE predicate on the "created_by" field.
func CreatedByGTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGTE(FieldCreatedBy, v))
}

// CreatedByLT applies the LT predicate on the "created_by" field.
func CreatedByLT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLT(FieldCreatedBy, v))
}

// CreatedByLTE applies the LTE predicate on the "created_by" field.
func CreatedByLTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLTE(FieldCreatedBy, v))
}

// CreatedByContains applies the Contains predicate on the "created_by" field.
func CreatedByContains(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContains(FieldCreatedBy, v))
}

// CreatedByHasPrefix applies the HasPrefix predicate on the "created_by" field.
func CreatedByHasPrefix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasPrefix(FieldCreatedBy, v))
}

// CreatedByHasSuffix applies the HasSuffix predicate on the "created_by" field.
func CreatedByHasSuffix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasSuffix(FieldCreatedBy, v))
}

// CreatedByIsNil applies the IsNil predicate on the "created_by" field.
func CreatedByIsNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIsNull(FieldCreatedBy))
}

// CreatedByNotNil applies the NotNil predicate on the "created_by" field.
func CreatedByNotNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotNull(FieldCreatedBy))
}

// CreatedByEqualFold applies the EqualFold predicate on the "created_by" field.
func CreatedByEqualFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEqualFold(FieldCreatedBy, v))
}

// CreatedByContainsFold applies the ContainsFold predicate on the "created_by" field.
func CreatedByContainsFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContainsFold(FieldCreatedBy, v))
}

// UpdatedByEQ applies the EQ predicate on the "updated_by" field.
func UpdatedByEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldUpdatedBy, v))
}

// UpdatedByNEQ applies the NEQ predicate on the "updated_by" field.
func UpdatedByNEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNEQ(FieldUpdatedBy, v))
}

// UpdatedByIn applies the In predicate on the "updated_by" field.
func UpdatedByIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIn(FieldUpdatedBy, vs...))
}

// UpdatedByNotIn applies the NotIn predicate on the "updated_by" field.
func UpdatedByNotIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotIn(FieldUpdatedBy, vs...))
}

// UpdatedByGT applies the GT predicate on the "updated_by" field.
func UpdatedByGT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGT(FieldUpdatedBy, v))
}

// UpdatedByGTE applies the GTE predicate on the "updated_by" field.
func UpdatedByGTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGTE(FieldUpdatedBy, v))
}

// UpdatedByLT applies the LT predicate on the "updated_by" field.
func UpdatedByLT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLT(FieldUpdatedBy, v))
}

// UpdatedByLTE applies the LTE predicate on the "updated_by" field.
func UpdatedByLTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLTE(FieldUpdatedBy, v))
}

// UpdatedByContains applies the Contains predicate on the "updated_by" field.
func UpdatedByContains(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContains(FieldUpdatedBy, v))
}

// UpdatedByHasPrefix applies the HasPrefix predicate on the "updated_by" field.
func UpdatedByHasPrefix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasPrefix(FieldUpdatedBy, v))
}

// UpdatedByHasSuffix applies the HasSuffix predicate on the "updated_by" field.
func UpdatedByHasSuffix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasSuffix(FieldUpdatedBy, v))
}

// UpdatedByIsNil applies the IsNil predicate on the "updated_by" field.
func UpdatedByIsNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIsNull(FieldUpdatedBy))
}

// UpdatedByNotNil applies the NotNil predicate on the "updated_by" field.
func UpdatedByNotNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotNull(FieldUpdatedBy))
}

// UpdatedByEqualFold applies the EqualFold predicate on the "updated_by" field.
func UpdatedByEqualFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEqualFold(FieldUpdatedBy, v))
}

// UpdatedByContainsFold applies the ContainsFold predicate on the "updated_by" field.
func UpdatedByContainsFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContainsFold(FieldUpdatedBy, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContainsFold(FieldRemark, v))
}

// ContractUrlsEQ applies the EQ predicate on the "contract_urls" field.
func ContractUrlsEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldContractUrls, v))
}

// ContractUrlsNEQ applies the NEQ predicate on the "contract_urls" field.
func ContractUrlsNEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNEQ(FieldContractUrls, v))
}

// ContractUrlsIn applies the In predicate on the "contract_urls" field.
func ContractUrlsIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIn(FieldContractUrls, vs...))
}

// ContractUrlsNotIn applies the NotIn predicate on the "contract_urls" field.
func ContractUrlsNotIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotIn(FieldContractUrls, vs...))
}

// ContractUrlsGT applies the GT predicate on the "contract_urls" field.
func ContractUrlsGT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGT(FieldContractUrls, v))
}

// ContractUrlsGTE applies the GTE predicate on the "contract_urls" field.
func ContractUrlsGTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGTE(FieldContractUrls, v))
}

// ContractUrlsLT applies the LT predicate on the "contract_urls" field.
func ContractUrlsLT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLT(FieldContractUrls, v))
}

// ContractUrlsLTE applies the LTE predicate on the "contract_urls" field.
func ContractUrlsLTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLTE(FieldContractUrls, v))
}

// ContractUrlsContains applies the Contains predicate on the "contract_urls" field.
func ContractUrlsContains(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContains(FieldContractUrls, v))
}

// ContractUrlsHasPrefix applies the HasPrefix predicate on the "contract_urls" field.
func ContractUrlsHasPrefix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasPrefix(FieldContractUrls, v))
}

// ContractUrlsHasSuffix applies the HasSuffix predicate on the "contract_urls" field.
func ContractUrlsHasSuffix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasSuffix(FieldContractUrls, v))
}

// ContractUrlsIsNil applies the IsNil predicate on the "contract_urls" field.
func ContractUrlsIsNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIsNull(FieldContractUrls))
}

// ContractUrlsNotNil applies the NotNil predicate on the "contract_urls" field.
func ContractUrlsNotNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotNull(FieldContractUrls))
}

// ContractUrlsEqualFold applies the EqualFold predicate on the "contract_urls" field.
func ContractUrlsEqualFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEqualFold(FieldContractUrls, v))
}

// ContractUrlsContainsFold applies the ContainsFold predicate on the "contract_urls" field.
func ContractUrlsContainsFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContainsFold(FieldContractUrls, v))
}

// InvoiceUrlsEQ applies the EQ predicate on the "invoice_urls" field.
func InvoiceUrlsEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldInvoiceUrls, v))
}

// InvoiceUrlsNEQ applies the NEQ predicate on the "invoice_urls" field.
func InvoiceUrlsNEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNEQ(FieldInvoiceUrls, v))
}

// InvoiceUrlsIn applies the In predicate on the "invoice_urls" field.
func InvoiceUrlsIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIn(FieldInvoiceUrls, vs...))
}

// InvoiceUrlsNotIn applies the NotIn predicate on the "invoice_urls" field.
func InvoiceUrlsNotIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotIn(FieldInvoiceUrls, vs...))
}

// InvoiceUrlsGT applies the GT predicate on the "invoice_urls" field.
func InvoiceUrlsGT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGT(FieldInvoiceUrls, v))
}

// InvoiceUrlsGTE applies the GTE predicate on the "invoice_urls" field.
func InvoiceUrlsGTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGTE(FieldInvoiceUrls, v))
}

// InvoiceUrlsLT applies the LT predicate on the "invoice_urls" field.
func InvoiceUrlsLT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLT(FieldInvoiceUrls, v))
}

// InvoiceUrlsLTE applies the LTE predicate on the "invoice_urls" field.
func InvoiceUrlsLTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLTE(FieldInvoiceUrls, v))
}

// InvoiceUrlsContains applies the Contains predicate on the "invoice_urls" field.
func InvoiceUrlsContains(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContains(FieldInvoiceUrls, v))
}

// InvoiceUrlsHasPrefix applies the HasPrefix predicate on the "invoice_urls" field.
func InvoiceUrlsHasPrefix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasPrefix(FieldInvoiceUrls, v))
}

// InvoiceUrlsHasSuffix applies the HasSuffix predicate on the "invoice_urls" field.
func InvoiceUrlsHasSuffix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasSuffix(FieldInvoiceUrls, v))
}

// InvoiceUrlsIsNil applies the IsNil predicate on the "invoice_urls" field.
func InvoiceUrlsIsNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIsNull(FieldInvoiceUrls))
}

// InvoiceUrlsNotNil applies the NotNil predicate on the "invoice_urls" field.
func InvoiceUrlsNotNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotNull(FieldInvoiceUrls))
}

// InvoiceUrlsEqualFold applies the EqualFold predicate on the "invoice_urls" field.
func InvoiceUrlsEqualFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEqualFold(FieldInvoiceUrls, v))
}

// InvoiceUrlsContainsFold applies the ContainsFold predicate on the "invoice_urls" field.
func InvoiceUrlsContainsFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContainsFold(FieldInvoiceUrls, v))
}

// AuditUrlsEQ applies the EQ predicate on the "audit_urls" field.
func AuditUrlsEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldAuditUrls, v))
}

// AuditUrlsNEQ applies the NEQ predicate on the "audit_urls" field.
func AuditUrlsNEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNEQ(FieldAuditUrls, v))
}

// AuditUrlsIn applies the In predicate on the "audit_urls" field.
func AuditUrlsIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIn(FieldAuditUrls, vs...))
}

// AuditUrlsNotIn applies the NotIn predicate on the "audit_urls" field.
func AuditUrlsNotIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotIn(FieldAuditUrls, vs...))
}

// AuditUrlsGT applies the GT predicate on the "audit_urls" field.
func AuditUrlsGT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGT(FieldAuditUrls, v))
}

// AuditUrlsGTE applies the GTE predicate on the "audit_urls" field.
func AuditUrlsGTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGTE(FieldAuditUrls, v))
}

// AuditUrlsLT applies the LT predicate on the "audit_urls" field.
func AuditUrlsLT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLT(FieldAuditUrls, v))
}

// AuditUrlsLTE applies the LTE predicate on the "audit_urls" field.
func AuditUrlsLTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLTE(FieldAuditUrls, v))
}

// AuditUrlsContains applies the Contains predicate on the "audit_urls" field.
func AuditUrlsContains(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContains(FieldAuditUrls, v))
}

// AuditUrlsHasPrefix applies the HasPrefix predicate on the "audit_urls" field.
func AuditUrlsHasPrefix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasPrefix(FieldAuditUrls, v))
}

// AuditUrlsHasSuffix applies the HasSuffix predicate on the "audit_urls" field.
func AuditUrlsHasSuffix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasSuffix(FieldAuditUrls, v))
}

// AuditUrlsIsNil applies the IsNil predicate on the "audit_urls" field.
func AuditUrlsIsNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIsNull(FieldAuditUrls))
}

// AuditUrlsNotNil applies the NotNil predicate on the "audit_urls" field.
func AuditUrlsNotNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotNull(FieldAuditUrls))
}

// AuditUrlsEqualFold applies the EqualFold predicate on the "audit_urls" field.
func AuditUrlsEqualFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEqualFold(FieldAuditUrls, v))
}

// AuditUrlsContainsFold applies the ContainsFold predicate on the "audit_urls" field.
func AuditUrlsContainsFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContainsFold(FieldAuditUrls, v))
}

// OtherUrlsEQ applies the EQ predicate on the "other_urls" field.
func OtherUrlsEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldOtherUrls, v))
}

// OtherUrlsNEQ applies the NEQ predicate on the "other_urls" field.
func OtherUrlsNEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNEQ(FieldOtherUrls, v))
}

// OtherUrlsIn applies the In predicate on the "other_urls" field.
func OtherUrlsIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIn(FieldOtherUrls, vs...))
}

// OtherUrlsNotIn applies the NotIn predicate on the "other_urls" field.
func OtherUrlsNotIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotIn(FieldOtherUrls, vs...))
}

// OtherUrlsGT applies the GT predicate on the "other_urls" field.
func OtherUrlsGT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGT(FieldOtherUrls, v))
}

// OtherUrlsGTE applies the GTE predicate on the "other_urls" field.
func OtherUrlsGTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGTE(FieldOtherUrls, v))
}

// OtherUrlsLT applies the LT predicate on the "other_urls" field.
func OtherUrlsLT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLT(FieldOtherUrls, v))
}

// OtherUrlsLTE applies the LTE predicate on the "other_urls" field.
func OtherUrlsLTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLTE(FieldOtherUrls, v))
}

// OtherUrlsContains applies the Contains predicate on the "other_urls" field.
func OtherUrlsContains(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContains(FieldOtherUrls, v))
}

// OtherUrlsHasPrefix applies the HasPrefix predicate on the "other_urls" field.
func OtherUrlsHasPrefix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasPrefix(FieldOtherUrls, v))
}

// OtherUrlsHasSuffix applies the HasSuffix predicate on the "other_urls" field.
func OtherUrlsHasSuffix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasSuffix(FieldOtherUrls, v))
}

// OtherUrlsIsNil applies the IsNil predicate on the "other_urls" field.
func OtherUrlsIsNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIsNull(FieldOtherUrls))
}

// OtherUrlsNotNil applies the NotNil predicate on the "other_urls" field.
func OtherUrlsNotNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotNull(FieldOtherUrls))
}

// OtherUrlsEqualFold applies the EqualFold predicate on the "other_urls" field.
func OtherUrlsEqualFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEqualFold(FieldOtherUrls, v))
}

// OtherUrlsContainsFold applies the ContainsFold predicate on the "other_urls" field.
func OtherUrlsContainsFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContainsFold(FieldOtherUrls, v))
}

// OrderNoEQ applies the EQ predicate on the "order_no" field.
func OrderNoEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldOrderNo, v))
}

// OrderNoNEQ applies the NEQ predicate on the "order_no" field.
func OrderNoNEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNEQ(FieldOrderNo, v))
}

// OrderNoIn applies the In predicate on the "order_no" field.
func OrderNoIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIn(FieldOrderNo, vs...))
}

// OrderNoNotIn applies the NotIn predicate on the "order_no" field.
func OrderNoNotIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotIn(FieldOrderNo, vs...))
}

// OrderNoGT applies the GT predicate on the "order_no" field.
func OrderNoGT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGT(FieldOrderNo, v))
}

// OrderNoGTE applies the GTE predicate on the "order_no" field.
func OrderNoGTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGTE(FieldOrderNo, v))
}

// OrderNoLT applies the LT predicate on the "order_no" field.
func OrderNoLT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLT(FieldOrderNo, v))
}

// OrderNoLTE applies the LTE predicate on the "order_no" field.
func OrderNoLTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLTE(FieldOrderNo, v))
}

// OrderNoContains applies the Contains predicate on the "order_no" field.
func OrderNoContains(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContains(FieldOrderNo, v))
}

// OrderNoHasPrefix applies the HasPrefix predicate on the "order_no" field.
func OrderNoHasPrefix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasPrefix(FieldOrderNo, v))
}

// OrderNoHasSuffix applies the HasSuffix predicate on the "order_no" field.
func OrderNoHasSuffix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasSuffix(FieldOrderNo, v))
}

// OrderNoEqualFold applies the EqualFold predicate on the "order_no" field.
func OrderNoEqualFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEqualFold(FieldOrderNo, v))
}

// OrderNoContainsFold applies the ContainsFold predicate on the "order_no" field.
func OrderNoContainsFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContainsFold(FieldOrderNo, v))
}

// RepositoryIDEQ applies the EQ predicate on the "repository_id" field.
func RepositoryIDEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldRepositoryID, v))
}

// RepositoryIDNEQ applies the NEQ predicate on the "repository_id" field.
func RepositoryIDNEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNEQ(FieldRepositoryID, v))
}

// RepositoryIDIn applies the In predicate on the "repository_id" field.
func RepositoryIDIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIn(FieldRepositoryID, vs...))
}

// RepositoryIDNotIn applies the NotIn predicate on the "repository_id" field.
func RepositoryIDNotIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotIn(FieldRepositoryID, vs...))
}

// RepositoryIDGT applies the GT predicate on the "repository_id" field.
func RepositoryIDGT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGT(FieldRepositoryID, v))
}

// RepositoryIDGTE applies the GTE predicate on the "repository_id" field.
func RepositoryIDGTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGTE(FieldRepositoryID, v))
}

// RepositoryIDLT applies the LT predicate on the "repository_id" field.
func RepositoryIDLT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLT(FieldRepositoryID, v))
}

// RepositoryIDLTE applies the LTE predicate on the "repository_id" field.
func RepositoryIDLTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLTE(FieldRepositoryID, v))
}

// RepositoryIDContains applies the Contains predicate on the "repository_id" field.
func RepositoryIDContains(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContains(FieldRepositoryID, v))
}

// RepositoryIDHasPrefix applies the HasPrefix predicate on the "repository_id" field.
func RepositoryIDHasPrefix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasPrefix(FieldRepositoryID, v))
}

// RepositoryIDHasSuffix applies the HasSuffix predicate on the "repository_id" field.
func RepositoryIDHasSuffix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasSuffix(FieldRepositoryID, v))
}

// RepositoryIDEqualFold applies the EqualFold predicate on the "repository_id" field.
func RepositoryIDEqualFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEqualFold(FieldRepositoryID, v))
}

// RepositoryIDContainsFold applies the ContainsFold predicate on the "repository_id" field.
func RepositoryIDContainsFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContainsFold(FieldRepositoryID, v))
}

// ReasonEQ applies the EQ predicate on the "reason" field.
func ReasonEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldReason, v))
}

// ReasonNEQ applies the NEQ predicate on the "reason" field.
func ReasonNEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNEQ(FieldReason, v))
}

// ReasonIn applies the In predicate on the "reason" field.
func ReasonIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIn(FieldReason, vs...))
}

// ReasonNotIn applies the NotIn predicate on the "reason" field.
func ReasonNotIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotIn(FieldReason, vs...))
}

// ReasonGT applies the GT predicate on the "reason" field.
func ReasonGT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGT(FieldReason, v))
}

// ReasonGTE applies the GTE predicate on the "reason" field.
func ReasonGTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGTE(FieldReason, v))
}

// ReasonLT applies the LT predicate on the "reason" field.
func ReasonLT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLT(FieldReason, v))
}

// ReasonLTE applies the LTE predicate on the "reason" field.
func ReasonLTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLTE(FieldReason, v))
}

// ReasonContains applies the Contains predicate on the "reason" field.
func ReasonContains(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContains(FieldReason, v))
}

// ReasonHasPrefix applies the HasPrefix predicate on the "reason" field.
func ReasonHasPrefix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasPrefix(FieldReason, v))
}

// ReasonHasSuffix applies the HasSuffix predicate on the "reason" field.
func ReasonHasSuffix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasSuffix(FieldReason, v))
}

// ReasonIsNil applies the IsNil predicate on the "reason" field.
func ReasonIsNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIsNull(FieldReason))
}

// ReasonNotNil applies the NotNil predicate on the "reason" field.
func ReasonNotNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotNull(FieldReason))
}

// ReasonEqualFold applies the EqualFold predicate on the "reason" field.
func ReasonEqualFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEqualFold(FieldReason, v))
}

// ReasonContainsFold applies the ContainsFold predicate on the "reason" field.
func ReasonContainsFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContainsFold(FieldReason, v))
}

// EquipmentNumEQ applies the EQ predicate on the "equipment_num" field.
func EquipmentNumEQ(v uint32) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldEquipmentNum, v))
}

// EquipmentNumNEQ applies the NEQ predicate on the "equipment_num" field.
func EquipmentNumNEQ(v uint32) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNEQ(FieldEquipmentNum, v))
}

// EquipmentNumIn applies the In predicate on the "equipment_num" field.
func EquipmentNumIn(vs ...uint32) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIn(FieldEquipmentNum, vs...))
}

// EquipmentNumNotIn applies the NotIn predicate on the "equipment_num" field.
func EquipmentNumNotIn(vs ...uint32) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotIn(FieldEquipmentNum, vs...))
}

// EquipmentNumGT applies the GT predicate on the "equipment_num" field.
func EquipmentNumGT(v uint32) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGT(FieldEquipmentNum, v))
}

// EquipmentNumGTE applies the GTE predicate on the "equipment_num" field.
func EquipmentNumGTE(v uint32) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGTE(FieldEquipmentNum, v))
}

// EquipmentNumLT applies the LT predicate on the "equipment_num" field.
func EquipmentNumLT(v uint32) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLT(FieldEquipmentNum, v))
}

// EquipmentNumLTE applies the LTE predicate on the "equipment_num" field.
func EquipmentNumLTE(v uint32) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLTE(FieldEquipmentNum, v))
}

// EquipmentNumIsNil applies the IsNil predicate on the "equipment_num" field.
func EquipmentNumIsNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIsNull(FieldEquipmentNum))
}

// EquipmentNumNotNil applies the NotNil predicate on the "equipment_num" field.
func EquipmentNumNotNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotNull(FieldEquipmentNum))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLTE(FieldStatus, v))
}

// StatusContains applies the Contains predicate on the "status" field.
func StatusContains(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContains(FieldStatus, v))
}

// StatusHasPrefix applies the HasPrefix predicate on the "status" field.
func StatusHasPrefix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasPrefix(FieldStatus, v))
}

// StatusHasSuffix applies the HasSuffix predicate on the "status" field.
func StatusHasSuffix(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldHasSuffix(FieldStatus, v))
}

// StatusIsNil applies the IsNil predicate on the "status" field.
func StatusIsNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIsNull(FieldStatus))
}

// StatusNotNil applies the NotNil predicate on the "status" field.
func StatusNotNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotNull(FieldStatus))
}

// StatusEqualFold applies the EqualFold predicate on the "status" field.
func StatusEqualFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEqualFold(FieldStatus, v))
}

// StatusContainsFold applies the ContainsFold predicate on the "status" field.
func StatusContainsFold(v string) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldContainsFold(FieldStatus, v))
}

// ReturnTimeEQ applies the EQ predicate on the "return_time" field.
func ReturnTimeEQ(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldEQ(FieldReturnTime, v))
}

// ReturnTimeNEQ applies the NEQ predicate on the "return_time" field.
func ReturnTimeNEQ(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNEQ(FieldReturnTime, v))
}

// ReturnTimeIn applies the In predicate on the "return_time" field.
func ReturnTimeIn(vs ...time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIn(FieldReturnTime, vs...))
}

// ReturnTimeNotIn applies the NotIn predicate on the "return_time" field.
func ReturnTimeNotIn(vs ...time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotIn(FieldReturnTime, vs...))
}

// ReturnTimeGT applies the GT predicate on the "return_time" field.
func ReturnTimeGT(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGT(FieldReturnTime, v))
}

// ReturnTimeGTE applies the GTE predicate on the "return_time" field.
func ReturnTimeGTE(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldGTE(FieldReturnTime, v))
}

// ReturnTimeLT applies the LT predicate on the "return_time" field.
func ReturnTimeLT(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLT(FieldReturnTime, v))
}

// ReturnTimeLTE applies the LTE predicate on the "return_time" field.
func ReturnTimeLTE(v time.Time) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldLTE(FieldReturnTime, v))
}

// ReturnTimeIsNil applies the IsNil predicate on the "return_time" field.
func ReturnTimeIsNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldIsNull(FieldReturnTime))
}

// ReturnTimeNotNil applies the NotNil predicate on the "return_time" field.
func ReturnTimeNotNil() predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.FieldNotNull(FieldReturnTime))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.WmsReturnOrder) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.WmsReturnOrder) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.WmsReturnOrder) predicate.WmsReturnOrder {
	return predicate.WmsReturnOrder(sql.NotPredicates(p))
}
