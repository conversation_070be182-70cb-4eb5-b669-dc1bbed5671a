// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-mono-demo/internal/data/ent/predicate"
	"kratos-mono-demo/internal/data/ent/wmstransferorderdetail"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WmsTransferOrderDetailUpdate is the builder for updating WmsTransferOrderDetail entities.
type WmsTransferOrderDetailUpdate struct {
	config
	hooks     []Hook
	mutation  *WmsTransferOrderDetailMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the WmsTransferOrderDetailUpdate builder.
func (wtodu *WmsTransferOrderDetailUpdate) Where(ps ...predicate.WmsTransferOrderDetail) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.Where(ps...)
	return wtodu
}

// SetUpdatedAt sets the "updated_at" field.
func (wtodu *WmsTransferOrderDetailUpdate) SetUpdatedAt(t time.Time) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.SetUpdatedAt(t)
	return wtodu
}

// SetCreatedBy sets the "created_by" field.
func (wtodu *WmsTransferOrderDetailUpdate) SetCreatedBy(s string) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.SetCreatedBy(s)
	return wtodu
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (wtodu *WmsTransferOrderDetailUpdate) SetNillableCreatedBy(s *string) *WmsTransferOrderDetailUpdate {
	if s != nil {
		wtodu.SetCreatedBy(*s)
	}
	return wtodu
}

// ClearCreatedBy clears the value of the "created_by" field.
func (wtodu *WmsTransferOrderDetailUpdate) ClearCreatedBy() *WmsTransferOrderDetailUpdate {
	wtodu.mutation.ClearCreatedBy()
	return wtodu
}

// SetUpdatedBy sets the "updated_by" field.
func (wtodu *WmsTransferOrderDetailUpdate) SetUpdatedBy(s string) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.SetUpdatedBy(s)
	return wtodu
}

// SetNillableUpdatedBy sets the "updated_by" field if the given value is not nil.
func (wtodu *WmsTransferOrderDetailUpdate) SetNillableUpdatedBy(s *string) *WmsTransferOrderDetailUpdate {
	if s != nil {
		wtodu.SetUpdatedBy(*s)
	}
	return wtodu
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (wtodu *WmsTransferOrderDetailUpdate) ClearUpdatedBy() *WmsTransferOrderDetailUpdate {
	wtodu.mutation.ClearUpdatedBy()
	return wtodu
}

// SetRemark sets the "remark" field.
func (wtodu *WmsTransferOrderDetailUpdate) SetRemark(s string) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.SetRemark(s)
	return wtodu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (wtodu *WmsTransferOrderDetailUpdate) SetNillableRemark(s *string) *WmsTransferOrderDetailUpdate {
	if s != nil {
		wtodu.SetRemark(*s)
	}
	return wtodu
}

// ClearRemark clears the value of the "remark" field.
func (wtodu *WmsTransferOrderDetailUpdate) ClearRemark() *WmsTransferOrderDetailUpdate {
	wtodu.mutation.ClearRemark()
	return wtodu
}

// SetOrderID sets the "order_id" field.
func (wtodu *WmsTransferOrderDetailUpdate) SetOrderID(s string) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.SetOrderID(s)
	return wtodu
}

// SetNillableOrderID sets the "order_id" field if the given value is not nil.
func (wtodu *WmsTransferOrderDetailUpdate) SetNillableOrderID(s *string) *WmsTransferOrderDetailUpdate {
	if s != nil {
		wtodu.SetOrderID(*s)
	}
	return wtodu
}

// ClearOrderID clears the value of the "order_id" field.
func (wtodu *WmsTransferOrderDetailUpdate) ClearOrderID() *WmsTransferOrderDetailUpdate {
	wtodu.mutation.ClearOrderID()
	return wtodu
}

// SetMaterialID sets the "material_id" field.
func (wtodu *WmsTransferOrderDetailUpdate) SetMaterialID(s string) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.SetMaterialID(s)
	return wtodu
}

// SetNillableMaterialID sets the "material_id" field if the given value is not nil.
func (wtodu *WmsTransferOrderDetailUpdate) SetNillableMaterialID(s *string) *WmsTransferOrderDetailUpdate {
	if s != nil {
		wtodu.SetMaterialID(*s)
	}
	return wtodu
}

// ClearMaterialID clears the value of the "material_id" field.
func (wtodu *WmsTransferOrderDetailUpdate) ClearMaterialID() *WmsTransferOrderDetailUpdate {
	wtodu.mutation.ClearMaterialID()
	return wtodu
}

// SetMaterialName sets the "material_name" field.
func (wtodu *WmsTransferOrderDetailUpdate) SetMaterialName(s string) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.SetMaterialName(s)
	return wtodu
}

// SetNillableMaterialName sets the "material_name" field if the given value is not nil.
func (wtodu *WmsTransferOrderDetailUpdate) SetNillableMaterialName(s *string) *WmsTransferOrderDetailUpdate {
	if s != nil {
		wtodu.SetMaterialName(*s)
	}
	return wtodu
}

// ClearMaterialName clears the value of the "material_name" field.
func (wtodu *WmsTransferOrderDetailUpdate) ClearMaterialName() *WmsTransferOrderDetailUpdate {
	wtodu.mutation.ClearMaterialName()
	return wtodu
}

// SetCode sets the "code" field.
func (wtodu *WmsTransferOrderDetailUpdate) SetCode(s string) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.SetCode(s)
	return wtodu
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (wtodu *WmsTransferOrderDetailUpdate) SetNillableCode(s *string) *WmsTransferOrderDetailUpdate {
	if s != nil {
		wtodu.SetCode(*s)
	}
	return wtodu
}

// SetEquipmentID sets the "equipment_id" field.
func (wtodu *WmsTransferOrderDetailUpdate) SetEquipmentID(s string) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.SetEquipmentID(s)
	return wtodu
}

// SetNillableEquipmentID sets the "equipment_id" field if the given value is not nil.
func (wtodu *WmsTransferOrderDetailUpdate) SetNillableEquipmentID(s *string) *WmsTransferOrderDetailUpdate {
	if s != nil {
		wtodu.SetEquipmentID(*s)
	}
	return wtodu
}

// ClearEquipmentID clears the value of the "equipment_id" field.
func (wtodu *WmsTransferOrderDetailUpdate) ClearEquipmentID() *WmsTransferOrderDetailUpdate {
	wtodu.mutation.ClearEquipmentID()
	return wtodu
}

// SetEquipmentTypeID sets the "equipment_type_id" field.
func (wtodu *WmsTransferOrderDetailUpdate) SetEquipmentTypeID(s string) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.SetEquipmentTypeID(s)
	return wtodu
}

// SetNillableEquipmentTypeID sets the "equipment_type_id" field if the given value is not nil.
func (wtodu *WmsTransferOrderDetailUpdate) SetNillableEquipmentTypeID(s *string) *WmsTransferOrderDetailUpdate {
	if s != nil {
		wtodu.SetEquipmentTypeID(*s)
	}
	return wtodu
}

// ClearEquipmentTypeID clears the value of the "equipment_type_id" field.
func (wtodu *WmsTransferOrderDetailUpdate) ClearEquipmentTypeID() *WmsTransferOrderDetailUpdate {
	wtodu.mutation.ClearEquipmentTypeID()
	return wtodu
}

// SetFeature sets the "feature" field.
func (wtodu *WmsTransferOrderDetailUpdate) SetFeature(m map[string]interface{}) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.SetFeature(m)
	return wtodu
}

// ClearFeature clears the value of the "feature" field.
func (wtodu *WmsTransferOrderDetailUpdate) ClearFeature() *WmsTransferOrderDetailUpdate {
	wtodu.mutation.ClearFeature()
	return wtodu
}

// SetRepositoryID sets the "repository_id" field.
func (wtodu *WmsTransferOrderDetailUpdate) SetRepositoryID(s string) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.SetRepositoryID(s)
	return wtodu
}

// SetNillableRepositoryID sets the "repository_id" field if the given value is not nil.
func (wtodu *WmsTransferOrderDetailUpdate) SetNillableRepositoryID(s *string) *WmsTransferOrderDetailUpdate {
	if s != nil {
		wtodu.SetRepositoryID(*s)
	}
	return wtodu
}

// ClearRepositoryID clears the value of the "repository_id" field.
func (wtodu *WmsTransferOrderDetailUpdate) ClearRepositoryID() *WmsTransferOrderDetailUpdate {
	wtodu.mutation.ClearRepositoryID()
	return wtodu
}

// SetRepositoryAreaID sets the "repository_area_id" field.
func (wtodu *WmsTransferOrderDetailUpdate) SetRepositoryAreaID(s string) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.SetRepositoryAreaID(s)
	return wtodu
}

// SetNillableRepositoryAreaID sets the "repository_area_id" field if the given value is not nil.
func (wtodu *WmsTransferOrderDetailUpdate) SetNillableRepositoryAreaID(s *string) *WmsTransferOrderDetailUpdate {
	if s != nil {
		wtodu.SetRepositoryAreaID(*s)
	}
	return wtodu
}

// ClearRepositoryAreaID clears the value of the "repository_area_id" field.
func (wtodu *WmsTransferOrderDetailUpdate) ClearRepositoryAreaID() *WmsTransferOrderDetailUpdate {
	wtodu.mutation.ClearRepositoryAreaID()
	return wtodu
}

// SetRepositoryPositionID sets the "repository_position_id" field.
func (wtodu *WmsTransferOrderDetailUpdate) SetRepositoryPositionID(s string) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.SetRepositoryPositionID(s)
	return wtodu
}

// SetNillableRepositoryPositionID sets the "repository_position_id" field if the given value is not nil.
func (wtodu *WmsTransferOrderDetailUpdate) SetNillableRepositoryPositionID(s *string) *WmsTransferOrderDetailUpdate {
	if s != nil {
		wtodu.SetRepositoryPositionID(*s)
	}
	return wtodu
}

// ClearRepositoryPositionID clears the value of the "repository_position_id" field.
func (wtodu *WmsTransferOrderDetailUpdate) ClearRepositoryPositionID() *WmsTransferOrderDetailUpdate {
	wtodu.mutation.ClearRepositoryPositionID()
	return wtodu
}

// SetOwnerID sets the "owner_id" field.
func (wtodu *WmsTransferOrderDetailUpdate) SetOwnerID(s string) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.SetOwnerID(s)
	return wtodu
}

// SetNillableOwnerID sets the "owner_id" field if the given value is not nil.
func (wtodu *WmsTransferOrderDetailUpdate) SetNillableOwnerID(s *string) *WmsTransferOrderDetailUpdate {
	if s != nil {
		wtodu.SetOwnerID(*s)
	}
	return wtodu
}

// ClearOwnerID clears the value of the "owner_id" field.
func (wtodu *WmsTransferOrderDetailUpdate) ClearOwnerID() *WmsTransferOrderDetailUpdate {
	wtodu.mutation.ClearOwnerID()
	return wtodu
}

// SetModelNo sets the "ModelNo" field.
func (wtodu *WmsTransferOrderDetailUpdate) SetModelNo(s string) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.SetModelNo(s)
	return wtodu
}

// SetNillableModelNo sets the "ModelNo" field if the given value is not nil.
func (wtodu *WmsTransferOrderDetailUpdate) SetNillableModelNo(s *string) *WmsTransferOrderDetailUpdate {
	if s != nil {
		wtodu.SetModelNo(*s)
	}
	return wtodu
}

// SetMeasureUnitID sets the "measure_unit_id" field.
func (wtodu *WmsTransferOrderDetailUpdate) SetMeasureUnitID(s string) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.SetMeasureUnitID(s)
	return wtodu
}

// SetNillableMeasureUnitID sets the "measure_unit_id" field if the given value is not nil.
func (wtodu *WmsTransferOrderDetailUpdate) SetNillableMeasureUnitID(s *string) *WmsTransferOrderDetailUpdate {
	if s != nil {
		wtodu.SetMeasureUnitID(*s)
	}
	return wtodu
}

// ClearMeasureUnitID clears the value of the "measure_unit_id" field.
func (wtodu *WmsTransferOrderDetailUpdate) ClearMeasureUnitID() *WmsTransferOrderDetailUpdate {
	wtodu.mutation.ClearMeasureUnitID()
	return wtodu
}

// SetNum sets the "num" field.
func (wtodu *WmsTransferOrderDetailUpdate) SetNum(u uint32) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.ResetNum()
	wtodu.mutation.SetNum(u)
	return wtodu
}

// SetNillableNum sets the "num" field if the given value is not nil.
func (wtodu *WmsTransferOrderDetailUpdate) SetNillableNum(u *uint32) *WmsTransferOrderDetailUpdate {
	if u != nil {
		wtodu.SetNum(*u)
	}
	return wtodu
}

// AddNum adds u to the "num" field.
func (wtodu *WmsTransferOrderDetailUpdate) AddNum(u int32) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.AddNum(u)
	return wtodu
}

// SetTransferReason sets the "transfer_reason" field.
func (wtodu *WmsTransferOrderDetailUpdate) SetTransferReason(s string) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.SetTransferReason(s)
	return wtodu
}

// SetNillableTransferReason sets the "transfer_reason" field if the given value is not nil.
func (wtodu *WmsTransferOrderDetailUpdate) SetNillableTransferReason(s *string) *WmsTransferOrderDetailUpdate {
	if s != nil {
		wtodu.SetTransferReason(*s)
	}
	return wtodu
}

// ClearTransferReason clears the value of the "transfer_reason" field.
func (wtodu *WmsTransferOrderDetailUpdate) ClearTransferReason() *WmsTransferOrderDetailUpdate {
	wtodu.mutation.ClearTransferReason()
	return wtodu
}

// SetTransferTime sets the "transfer_time" field.
func (wtodu *WmsTransferOrderDetailUpdate) SetTransferTime(t time.Time) *WmsTransferOrderDetailUpdate {
	wtodu.mutation.SetTransferTime(t)
	return wtodu
}

// SetNillableTransferTime sets the "transfer_time" field if the given value is not nil.
func (wtodu *WmsTransferOrderDetailUpdate) SetNillableTransferTime(t *time.Time) *WmsTransferOrderDetailUpdate {
	if t != nil {
		wtodu.SetTransferTime(*t)
	}
	return wtodu
}

// ClearTransferTime clears the value of the "transfer_time" field.
func (wtodu *WmsTransferOrderDetailUpdate) ClearTransferTime() *WmsTransferOrderDetailUpdate {
	wtodu.mutation.ClearTransferTime()
	return wtodu
}

// Mutation returns the WmsTransferOrderDetailMutation object of the builder.
func (wtodu *WmsTransferOrderDetailUpdate) Mutation() *WmsTransferOrderDetailMutation {
	return wtodu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (wtodu *WmsTransferOrderDetailUpdate) Save(ctx context.Context) (int, error) {
	wtodu.defaults()
	return withHooks(ctx, wtodu.sqlSave, wtodu.mutation, wtodu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (wtodu *WmsTransferOrderDetailUpdate) SaveX(ctx context.Context) int {
	affected, err := wtodu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (wtodu *WmsTransferOrderDetailUpdate) Exec(ctx context.Context) error {
	_, err := wtodu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wtodu *WmsTransferOrderDetailUpdate) ExecX(ctx context.Context) {
	if err := wtodu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wtodu *WmsTransferOrderDetailUpdate) defaults() {
	if _, ok := wtodu.mutation.UpdatedAt(); !ok {
		v := wmstransferorderdetail.UpdateDefaultUpdatedAt()
		wtodu.mutation.SetUpdatedAt(v)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (wtodu *WmsTransferOrderDetailUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *WmsTransferOrderDetailUpdate {
	wtodu.modifiers = append(wtodu.modifiers, modifiers...)
	return wtodu
}

func (wtodu *WmsTransferOrderDetailUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(wmstransferorderdetail.Table, wmstransferorderdetail.Columns, sqlgraph.NewFieldSpec(wmstransferorderdetail.FieldID, field.TypeString))
	if ps := wtodu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := wtodu.mutation.UpdatedAt(); ok {
		_spec.SetField(wmstransferorderdetail.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := wtodu.mutation.CreatedBy(); ok {
		_spec.SetField(wmstransferorderdetail.FieldCreatedBy, field.TypeString, value)
	}
	if wtodu.mutation.CreatedByCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldCreatedBy, field.TypeString)
	}
	if value, ok := wtodu.mutation.UpdatedBy(); ok {
		_spec.SetField(wmstransferorderdetail.FieldUpdatedBy, field.TypeString, value)
	}
	if wtodu.mutation.UpdatedByCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldUpdatedBy, field.TypeString)
	}
	if value, ok := wtodu.mutation.Remark(); ok {
		_spec.SetField(wmstransferorderdetail.FieldRemark, field.TypeString, value)
	}
	if wtodu.mutation.RemarkCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldRemark, field.TypeString)
	}
	if value, ok := wtodu.mutation.OrderID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldOrderID, field.TypeString, value)
	}
	if wtodu.mutation.OrderIDCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldOrderID, field.TypeString)
	}
	if value, ok := wtodu.mutation.MaterialID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldMaterialID, field.TypeString, value)
	}
	if wtodu.mutation.MaterialIDCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldMaterialID, field.TypeString)
	}
	if value, ok := wtodu.mutation.MaterialName(); ok {
		_spec.SetField(wmstransferorderdetail.FieldMaterialName, field.TypeString, value)
	}
	if wtodu.mutation.MaterialNameCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldMaterialName, field.TypeString)
	}
	if value, ok := wtodu.mutation.Code(); ok {
		_spec.SetField(wmstransferorderdetail.FieldCode, field.TypeString, value)
	}
	if value, ok := wtodu.mutation.EquipmentID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldEquipmentID, field.TypeString, value)
	}
	if wtodu.mutation.EquipmentIDCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldEquipmentID, field.TypeString)
	}
	if value, ok := wtodu.mutation.EquipmentTypeID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldEquipmentTypeID, field.TypeString, value)
	}
	if wtodu.mutation.EquipmentTypeIDCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldEquipmentTypeID, field.TypeString)
	}
	if value, ok := wtodu.mutation.Feature(); ok {
		_spec.SetField(wmstransferorderdetail.FieldFeature, field.TypeJSON, value)
	}
	if wtodu.mutation.FeatureCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldFeature, field.TypeJSON)
	}
	if value, ok := wtodu.mutation.RepositoryID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldRepositoryID, field.TypeString, value)
	}
	if wtodu.mutation.RepositoryIDCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldRepositoryID, field.TypeString)
	}
	if value, ok := wtodu.mutation.RepositoryAreaID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldRepositoryAreaID, field.TypeString, value)
	}
	if wtodu.mutation.RepositoryAreaIDCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldRepositoryAreaID, field.TypeString)
	}
	if value, ok := wtodu.mutation.RepositoryPositionID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldRepositoryPositionID, field.TypeString, value)
	}
	if wtodu.mutation.RepositoryPositionIDCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldRepositoryPositionID, field.TypeString)
	}
	if value, ok := wtodu.mutation.OwnerID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldOwnerID, field.TypeString, value)
	}
	if wtodu.mutation.OwnerIDCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldOwnerID, field.TypeString)
	}
	if value, ok := wtodu.mutation.ModelNo(); ok {
		_spec.SetField(wmstransferorderdetail.FieldModelNo, field.TypeString, value)
	}
	if value, ok := wtodu.mutation.MeasureUnitID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldMeasureUnitID, field.TypeString, value)
	}
	if wtodu.mutation.MeasureUnitIDCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldMeasureUnitID, field.TypeString)
	}
	if value, ok := wtodu.mutation.Num(); ok {
		_spec.SetField(wmstransferorderdetail.FieldNum, field.TypeUint32, value)
	}
	if value, ok := wtodu.mutation.AddedNum(); ok {
		_spec.AddField(wmstransferorderdetail.FieldNum, field.TypeUint32, value)
	}
	if value, ok := wtodu.mutation.TransferReason(); ok {
		_spec.SetField(wmstransferorderdetail.FieldTransferReason, field.TypeString, value)
	}
	if wtodu.mutation.TransferReasonCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldTransferReason, field.TypeString)
	}
	if value, ok := wtodu.mutation.TransferTime(); ok {
		_spec.SetField(wmstransferorderdetail.FieldTransferTime, field.TypeTime, value)
	}
	if wtodu.mutation.TransferTimeCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldTransferTime, field.TypeTime)
	}
	_spec.AddModifiers(wtodu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, wtodu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{wmstransferorderdetail.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	wtodu.mutation.done = true
	return n, nil
}

// WmsTransferOrderDetailUpdateOne is the builder for updating a single WmsTransferOrderDetail entity.
type WmsTransferOrderDetailUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *WmsTransferOrderDetailMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdatedAt sets the "updated_at" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetUpdatedAt(t time.Time) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.SetUpdatedAt(t)
	return wtoduo
}

// SetCreatedBy sets the "created_by" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetCreatedBy(s string) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.SetCreatedBy(s)
	return wtoduo
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetNillableCreatedBy(s *string) *WmsTransferOrderDetailUpdateOne {
	if s != nil {
		wtoduo.SetCreatedBy(*s)
	}
	return wtoduo
}

// ClearCreatedBy clears the value of the "created_by" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) ClearCreatedBy() *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.ClearCreatedBy()
	return wtoduo
}

// SetUpdatedBy sets the "updated_by" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetUpdatedBy(s string) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.SetUpdatedBy(s)
	return wtoduo
}

// SetNillableUpdatedBy sets the "updated_by" field if the given value is not nil.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetNillableUpdatedBy(s *string) *WmsTransferOrderDetailUpdateOne {
	if s != nil {
		wtoduo.SetUpdatedBy(*s)
	}
	return wtoduo
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) ClearUpdatedBy() *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.ClearUpdatedBy()
	return wtoduo
}

// SetRemark sets the "remark" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetRemark(s string) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.SetRemark(s)
	return wtoduo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetNillableRemark(s *string) *WmsTransferOrderDetailUpdateOne {
	if s != nil {
		wtoduo.SetRemark(*s)
	}
	return wtoduo
}

// ClearRemark clears the value of the "remark" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) ClearRemark() *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.ClearRemark()
	return wtoduo
}

// SetOrderID sets the "order_id" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetOrderID(s string) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.SetOrderID(s)
	return wtoduo
}

// SetNillableOrderID sets the "order_id" field if the given value is not nil.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetNillableOrderID(s *string) *WmsTransferOrderDetailUpdateOne {
	if s != nil {
		wtoduo.SetOrderID(*s)
	}
	return wtoduo
}

// ClearOrderID clears the value of the "order_id" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) ClearOrderID() *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.ClearOrderID()
	return wtoduo
}

// SetMaterialID sets the "material_id" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetMaterialID(s string) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.SetMaterialID(s)
	return wtoduo
}

// SetNillableMaterialID sets the "material_id" field if the given value is not nil.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetNillableMaterialID(s *string) *WmsTransferOrderDetailUpdateOne {
	if s != nil {
		wtoduo.SetMaterialID(*s)
	}
	return wtoduo
}

// ClearMaterialID clears the value of the "material_id" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) ClearMaterialID() *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.ClearMaterialID()
	return wtoduo
}

// SetMaterialName sets the "material_name" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetMaterialName(s string) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.SetMaterialName(s)
	return wtoduo
}

// SetNillableMaterialName sets the "material_name" field if the given value is not nil.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetNillableMaterialName(s *string) *WmsTransferOrderDetailUpdateOne {
	if s != nil {
		wtoduo.SetMaterialName(*s)
	}
	return wtoduo
}

// ClearMaterialName clears the value of the "material_name" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) ClearMaterialName() *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.ClearMaterialName()
	return wtoduo
}

// SetCode sets the "code" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetCode(s string) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.SetCode(s)
	return wtoduo
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetNillableCode(s *string) *WmsTransferOrderDetailUpdateOne {
	if s != nil {
		wtoduo.SetCode(*s)
	}
	return wtoduo
}

// SetEquipmentID sets the "equipment_id" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetEquipmentID(s string) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.SetEquipmentID(s)
	return wtoduo
}

// SetNillableEquipmentID sets the "equipment_id" field if the given value is not nil.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetNillableEquipmentID(s *string) *WmsTransferOrderDetailUpdateOne {
	if s != nil {
		wtoduo.SetEquipmentID(*s)
	}
	return wtoduo
}

// ClearEquipmentID clears the value of the "equipment_id" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) ClearEquipmentID() *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.ClearEquipmentID()
	return wtoduo
}

// SetEquipmentTypeID sets the "equipment_type_id" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetEquipmentTypeID(s string) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.SetEquipmentTypeID(s)
	return wtoduo
}

// SetNillableEquipmentTypeID sets the "equipment_type_id" field if the given value is not nil.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetNillableEquipmentTypeID(s *string) *WmsTransferOrderDetailUpdateOne {
	if s != nil {
		wtoduo.SetEquipmentTypeID(*s)
	}
	return wtoduo
}

// ClearEquipmentTypeID clears the value of the "equipment_type_id" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) ClearEquipmentTypeID() *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.ClearEquipmentTypeID()
	return wtoduo
}

// SetFeature sets the "feature" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetFeature(m map[string]interface{}) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.SetFeature(m)
	return wtoduo
}

// ClearFeature clears the value of the "feature" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) ClearFeature() *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.ClearFeature()
	return wtoduo
}

// SetRepositoryID sets the "repository_id" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetRepositoryID(s string) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.SetRepositoryID(s)
	return wtoduo
}

// SetNillableRepositoryID sets the "repository_id" field if the given value is not nil.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetNillableRepositoryID(s *string) *WmsTransferOrderDetailUpdateOne {
	if s != nil {
		wtoduo.SetRepositoryID(*s)
	}
	return wtoduo
}

// ClearRepositoryID clears the value of the "repository_id" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) ClearRepositoryID() *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.ClearRepositoryID()
	return wtoduo
}

// SetRepositoryAreaID sets the "repository_area_id" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetRepositoryAreaID(s string) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.SetRepositoryAreaID(s)
	return wtoduo
}

// SetNillableRepositoryAreaID sets the "repository_area_id" field if the given value is not nil.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetNillableRepositoryAreaID(s *string) *WmsTransferOrderDetailUpdateOne {
	if s != nil {
		wtoduo.SetRepositoryAreaID(*s)
	}
	return wtoduo
}

// ClearRepositoryAreaID clears the value of the "repository_area_id" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) ClearRepositoryAreaID() *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.ClearRepositoryAreaID()
	return wtoduo
}

// SetRepositoryPositionID sets the "repository_position_id" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetRepositoryPositionID(s string) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.SetRepositoryPositionID(s)
	return wtoduo
}

// SetNillableRepositoryPositionID sets the "repository_position_id" field if the given value is not nil.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetNillableRepositoryPositionID(s *string) *WmsTransferOrderDetailUpdateOne {
	if s != nil {
		wtoduo.SetRepositoryPositionID(*s)
	}
	return wtoduo
}

// ClearRepositoryPositionID clears the value of the "repository_position_id" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) ClearRepositoryPositionID() *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.ClearRepositoryPositionID()
	return wtoduo
}

// SetOwnerID sets the "owner_id" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetOwnerID(s string) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.SetOwnerID(s)
	return wtoduo
}

// SetNillableOwnerID sets the "owner_id" field if the given value is not nil.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetNillableOwnerID(s *string) *WmsTransferOrderDetailUpdateOne {
	if s != nil {
		wtoduo.SetOwnerID(*s)
	}
	return wtoduo
}

// ClearOwnerID clears the value of the "owner_id" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) ClearOwnerID() *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.ClearOwnerID()
	return wtoduo
}

// SetModelNo sets the "ModelNo" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetModelNo(s string) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.SetModelNo(s)
	return wtoduo
}

// SetNillableModelNo sets the "ModelNo" field if the given value is not nil.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetNillableModelNo(s *string) *WmsTransferOrderDetailUpdateOne {
	if s != nil {
		wtoduo.SetModelNo(*s)
	}
	return wtoduo
}

// SetMeasureUnitID sets the "measure_unit_id" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetMeasureUnitID(s string) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.SetMeasureUnitID(s)
	return wtoduo
}

// SetNillableMeasureUnitID sets the "measure_unit_id" field if the given value is not nil.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetNillableMeasureUnitID(s *string) *WmsTransferOrderDetailUpdateOne {
	if s != nil {
		wtoduo.SetMeasureUnitID(*s)
	}
	return wtoduo
}

// ClearMeasureUnitID clears the value of the "measure_unit_id" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) ClearMeasureUnitID() *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.ClearMeasureUnitID()
	return wtoduo
}

// SetNum sets the "num" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetNum(u uint32) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.ResetNum()
	wtoduo.mutation.SetNum(u)
	return wtoduo
}

// SetNillableNum sets the "num" field if the given value is not nil.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetNillableNum(u *uint32) *WmsTransferOrderDetailUpdateOne {
	if u != nil {
		wtoduo.SetNum(*u)
	}
	return wtoduo
}

// AddNum adds u to the "num" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) AddNum(u int32) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.AddNum(u)
	return wtoduo
}

// SetTransferReason sets the "transfer_reason" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetTransferReason(s string) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.SetTransferReason(s)
	return wtoduo
}

// SetNillableTransferReason sets the "transfer_reason" field if the given value is not nil.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetNillableTransferReason(s *string) *WmsTransferOrderDetailUpdateOne {
	if s != nil {
		wtoduo.SetTransferReason(*s)
	}
	return wtoduo
}

// ClearTransferReason clears the value of the "transfer_reason" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) ClearTransferReason() *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.ClearTransferReason()
	return wtoduo
}

// SetTransferTime sets the "transfer_time" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetTransferTime(t time.Time) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.SetTransferTime(t)
	return wtoduo
}

// SetNillableTransferTime sets the "transfer_time" field if the given value is not nil.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SetNillableTransferTime(t *time.Time) *WmsTransferOrderDetailUpdateOne {
	if t != nil {
		wtoduo.SetTransferTime(*t)
	}
	return wtoduo
}

// ClearTransferTime clears the value of the "transfer_time" field.
func (wtoduo *WmsTransferOrderDetailUpdateOne) ClearTransferTime() *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.ClearTransferTime()
	return wtoduo
}

// Mutation returns the WmsTransferOrderDetailMutation object of the builder.
func (wtoduo *WmsTransferOrderDetailUpdateOne) Mutation() *WmsTransferOrderDetailMutation {
	return wtoduo.mutation
}

// Where appends a list predicates to the WmsTransferOrderDetailUpdate builder.
func (wtoduo *WmsTransferOrderDetailUpdateOne) Where(ps ...predicate.WmsTransferOrderDetail) *WmsTransferOrderDetailUpdateOne {
	wtoduo.mutation.Where(ps...)
	return wtoduo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (wtoduo *WmsTransferOrderDetailUpdateOne) Select(field string, fields ...string) *WmsTransferOrderDetailUpdateOne {
	wtoduo.fields = append([]string{field}, fields...)
	return wtoduo
}

// Save executes the query and returns the updated WmsTransferOrderDetail entity.
func (wtoduo *WmsTransferOrderDetailUpdateOne) Save(ctx context.Context) (*WmsTransferOrderDetail, error) {
	wtoduo.defaults()
	return withHooks(ctx, wtoduo.sqlSave, wtoduo.mutation, wtoduo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (wtoduo *WmsTransferOrderDetailUpdateOne) SaveX(ctx context.Context) *WmsTransferOrderDetail {
	node, err := wtoduo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (wtoduo *WmsTransferOrderDetailUpdateOne) Exec(ctx context.Context) error {
	_, err := wtoduo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wtoduo *WmsTransferOrderDetailUpdateOne) ExecX(ctx context.Context) {
	if err := wtoduo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wtoduo *WmsTransferOrderDetailUpdateOne) defaults() {
	if _, ok := wtoduo.mutation.UpdatedAt(); !ok {
		v := wmstransferorderdetail.UpdateDefaultUpdatedAt()
		wtoduo.mutation.SetUpdatedAt(v)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (wtoduo *WmsTransferOrderDetailUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *WmsTransferOrderDetailUpdateOne {
	wtoduo.modifiers = append(wtoduo.modifiers, modifiers...)
	return wtoduo
}

func (wtoduo *WmsTransferOrderDetailUpdateOne) sqlSave(ctx context.Context) (_node *WmsTransferOrderDetail, err error) {
	_spec := sqlgraph.NewUpdateSpec(wmstransferorderdetail.Table, wmstransferorderdetail.Columns, sqlgraph.NewFieldSpec(wmstransferorderdetail.FieldID, field.TypeString))
	id, ok := wtoduo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "WmsTransferOrderDetail.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := wtoduo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, wmstransferorderdetail.FieldID)
		for _, f := range fields {
			if !wmstransferorderdetail.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != wmstransferorderdetail.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := wtoduo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := wtoduo.mutation.UpdatedAt(); ok {
		_spec.SetField(wmstransferorderdetail.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := wtoduo.mutation.CreatedBy(); ok {
		_spec.SetField(wmstransferorderdetail.FieldCreatedBy, field.TypeString, value)
	}
	if wtoduo.mutation.CreatedByCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldCreatedBy, field.TypeString)
	}
	if value, ok := wtoduo.mutation.UpdatedBy(); ok {
		_spec.SetField(wmstransferorderdetail.FieldUpdatedBy, field.TypeString, value)
	}
	if wtoduo.mutation.UpdatedByCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldUpdatedBy, field.TypeString)
	}
	if value, ok := wtoduo.mutation.Remark(); ok {
		_spec.SetField(wmstransferorderdetail.FieldRemark, field.TypeString, value)
	}
	if wtoduo.mutation.RemarkCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldRemark, field.TypeString)
	}
	if value, ok := wtoduo.mutation.OrderID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldOrderID, field.TypeString, value)
	}
	if wtoduo.mutation.OrderIDCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldOrderID, field.TypeString)
	}
	if value, ok := wtoduo.mutation.MaterialID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldMaterialID, field.TypeString, value)
	}
	if wtoduo.mutation.MaterialIDCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldMaterialID, field.TypeString)
	}
	if value, ok := wtoduo.mutation.MaterialName(); ok {
		_spec.SetField(wmstransferorderdetail.FieldMaterialName, field.TypeString, value)
	}
	if wtoduo.mutation.MaterialNameCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldMaterialName, field.TypeString)
	}
	if value, ok := wtoduo.mutation.Code(); ok {
		_spec.SetField(wmstransferorderdetail.FieldCode, field.TypeString, value)
	}
	if value, ok := wtoduo.mutation.EquipmentID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldEquipmentID, field.TypeString, value)
	}
	if wtoduo.mutation.EquipmentIDCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldEquipmentID, field.TypeString)
	}
	if value, ok := wtoduo.mutation.EquipmentTypeID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldEquipmentTypeID, field.TypeString, value)
	}
	if wtoduo.mutation.EquipmentTypeIDCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldEquipmentTypeID, field.TypeString)
	}
	if value, ok := wtoduo.mutation.Feature(); ok {
		_spec.SetField(wmstransferorderdetail.FieldFeature, field.TypeJSON, value)
	}
	if wtoduo.mutation.FeatureCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldFeature, field.TypeJSON)
	}
	if value, ok := wtoduo.mutation.RepositoryID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldRepositoryID, field.TypeString, value)
	}
	if wtoduo.mutation.RepositoryIDCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldRepositoryID, field.TypeString)
	}
	if value, ok := wtoduo.mutation.RepositoryAreaID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldRepositoryAreaID, field.TypeString, value)
	}
	if wtoduo.mutation.RepositoryAreaIDCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldRepositoryAreaID, field.TypeString)
	}
	if value, ok := wtoduo.mutation.RepositoryPositionID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldRepositoryPositionID, field.TypeString, value)
	}
	if wtoduo.mutation.RepositoryPositionIDCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldRepositoryPositionID, field.TypeString)
	}
	if value, ok := wtoduo.mutation.OwnerID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldOwnerID, field.TypeString, value)
	}
	if wtoduo.mutation.OwnerIDCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldOwnerID, field.TypeString)
	}
	if value, ok := wtoduo.mutation.ModelNo(); ok {
		_spec.SetField(wmstransferorderdetail.FieldModelNo, field.TypeString, value)
	}
	if value, ok := wtoduo.mutation.MeasureUnitID(); ok {
		_spec.SetField(wmstransferorderdetail.FieldMeasureUnitID, field.TypeString, value)
	}
	if wtoduo.mutation.MeasureUnitIDCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldMeasureUnitID, field.TypeString)
	}
	if value, ok := wtoduo.mutation.Num(); ok {
		_spec.SetField(wmstransferorderdetail.FieldNum, field.TypeUint32, value)
	}
	if value, ok := wtoduo.mutation.AddedNum(); ok {
		_spec.AddField(wmstransferorderdetail.FieldNum, field.TypeUint32, value)
	}
	if value, ok := wtoduo.mutation.TransferReason(); ok {
		_spec.SetField(wmstransferorderdetail.FieldTransferReason, field.TypeString, value)
	}
	if wtoduo.mutation.TransferReasonCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldTransferReason, field.TypeString)
	}
	if value, ok := wtoduo.mutation.TransferTime(); ok {
		_spec.SetField(wmstransferorderdetail.FieldTransferTime, field.TypeTime, value)
	}
	if wtoduo.mutation.TransferTimeCleared() {
		_spec.ClearField(wmstransferorderdetail.FieldTransferTime, field.TypeTime)
	}
	_spec.AddModifiers(wtoduo.modifiers...)
	_node = &WmsTransferOrderDetail{config: wtoduo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, wtoduo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{wmstransferorderdetail.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	wtoduo.mutation.done = true
	return _node, nil
}
