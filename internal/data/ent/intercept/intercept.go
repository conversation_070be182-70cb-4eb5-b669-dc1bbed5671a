// Code generated by ent, DO NOT EDIT.

package intercept

import (
	"context"
	"fmt"

	"kratos-mono-demo/internal/data/ent"
	"kratos-mono-demo/internal/data/ent/appapproveactivity"
	"kratos-mono-demo/internal/data/ent/appapprovebasic"
	"kratos-mono-demo/internal/data/ent/appapprovegroup"
	"kratos-mono-demo/internal/data/ent/appapproverecord"
	"kratos-mono-demo/internal/data/ent/appapproverecordlog"
	"kratos-mono-demo/internal/data/ent/appapproverecordqueue"
	"kratos-mono-demo/internal/data/ent/appapproveworkflow"
	"kratos-mono-demo/internal/data/ent/demobuilding"
	"kratos-mono-demo/internal/data/ent/predicate"
	"kratos-mono-demo/internal/data/ent/sysapplication"
	"kratos-mono-demo/internal/data/ent/sysapplicationmodule"
	"kratos-mono-demo/internal/data/ent/sysapplicationmoduleresource"
	"kratos-mono-demo/internal/data/ent/sysarea"
	"kratos-mono-demo/internal/data/ent/syscity"
	"kratos-mono-demo/internal/data/ent/sysconfig"
	"kratos-mono-demo/internal/data/ent/sysdictionary"
	"kratos-mono-demo/internal/data/ent/sysdictionarydetail"
	"kratos-mono-demo/internal/data/ent/sysgame"
	"kratos-mono-demo/internal/data/ent/syslabel"
	"kratos-mono-demo/internal/data/ent/syslog"
	"kratos-mono-demo/internal/data/ent/sysmenu"
	"kratos-mono-demo/internal/data/ent/sysmessagetemplate"
	"kratos-mono-demo/internal/data/ent/sysmodule"
	"kratos-mono-demo/internal/data/ent/sysmoduleresource"
	"kratos-mono-demo/internal/data/ent/sysorganization"
	"kratos-mono-demo/internal/data/ent/syspagecode"
	"kratos-mono-demo/internal/data/ent/syspagecodehistory"
	"kratos-mono-demo/internal/data/ent/sysproject"
	"kratos-mono-demo/internal/data/ent/sysprovince"
	"kratos-mono-demo/internal/data/ent/sysresource"
	"kratos-mono-demo/internal/data/ent/sysrole"
	"kratos-mono-demo/internal/data/ent/sysrolemenu"
	"kratos-mono-demo/internal/data/ent/sysroleresource"
	"kratos-mono-demo/internal/data/ent/sysstreet"
	"kratos-mono-demo/internal/data/ent/systeam"
	"kratos-mono-demo/internal/data/ent/systeamapplication"
	"kratos-mono-demo/internal/data/ent/sysupload"
	"kratos-mono-demo/internal/data/ent/sysuser"
	"kratos-mono-demo/internal/data/ent/sysuserauth"
	"kratos-mono-demo/internal/data/ent/sysuserlog"
	"kratos-mono-demo/internal/data/ent/sysuserorganization"
	"kratos-mono-demo/internal/data/ent/sysuserrole"
	"kratos-mono-demo/internal/data/ent/wmsaccessdoorlog"
	"kratos-mono-demo/internal/data/ent/wmsapprovaltask"
	"kratos-mono-demo/internal/data/ent/wmsapprovaltaskdetail"
	"kratos-mono-demo/internal/data/ent/wmsapprovaltaskoperation"
	"kratos-mono-demo/internal/data/ent/wmsapprovaltaskremark"
	"kratos-mono-demo/internal/data/ent/wmsauditplan"
	"kratos-mono-demo/internal/data/ent/wmsauditplandetail"
	"kratos-mono-demo/internal/data/ent/wmsborroworder"
	"kratos-mono-demo/internal/data/ent/wmsborroworderdetail"
	"kratos-mono-demo/internal/data/ent/wmscar"
	"kratos-mono-demo/internal/data/ent/wmsclaimorder"
	"kratos-mono-demo/internal/data/ent/wmsclaimorderdetail"
	"kratos-mono-demo/internal/data/ent/wmscompany"
	"kratos-mono-demo/internal/data/ent/wmscompanyaddress"
	"kratos-mono-demo/internal/data/ent/wmsdiscardmeeting"
	"kratos-mono-demo/internal/data/ent/wmsdiscardmeetingdetail"
	"kratos-mono-demo/internal/data/ent/wmsdiscardorder"
	"kratos-mono-demo/internal/data/ent/wmsdiscardorderdetail"
	"kratos-mono-demo/internal/data/ent/wmsdiscardplanorder"
	"kratos-mono-demo/internal/data/ent/wmsdiscardplanorderdetail"
	"kratos-mono-demo/internal/data/ent/wmsdocument"
	"kratos-mono-demo/internal/data/ent/wmsenterrepositoryorder"
	"kratos-mono-demo/internal/data/ent/wmsenterrepositoryorderdetail"
	"kratos-mono-demo/internal/data/ent/wmsequipment"
	"kratos-mono-demo/internal/data/ent/wmsequipmentdetail"
	"kratos-mono-demo/internal/data/ent/wmsequipmenttype"
	"kratos-mono-demo/internal/data/ent/wmsequipmenttypeproperty"
	"kratos-mono-demo/internal/data/ent/wmsequipmenttypepropertygroup"
	"kratos-mono-demo/internal/data/ent/wmsequipmenttypepropertyoption"
	"kratos-mono-demo/internal/data/ent/wmsequipmentusergroup"
	"kratos-mono-demo/internal/data/ent/wmsfirestation"
	"kratos-mono-demo/internal/data/ent/wmsgps"
	"kratos-mono-demo/internal/data/ent/wmsgpsrecord"
	"kratos-mono-demo/internal/data/ent/wmslearningcourse"
	"kratos-mono-demo/internal/data/ent/wmslearningcoursecourseware"
	"kratos-mono-demo/internal/data/ent/wmslearningcourselog"
	"kratos-mono-demo/internal/data/ent/wmslearningcourserecord"
	"kratos-mono-demo/internal/data/ent/wmslearningcourseware"
	"kratos-mono-demo/internal/data/ent/wmslearningcoursewarerecord"
	"kratos-mono-demo/internal/data/ent/wmslearningplan"
	"kratos-mono-demo/internal/data/ent/wmslearningplanrecord"
	"kratos-mono-demo/internal/data/ent/wmsmaintainorder"
	"kratos-mono-demo/internal/data/ent/wmsmaintainorderdetail"
	"kratos-mono-demo/internal/data/ent/wmsmaintainplan"
	"kratos-mono-demo/internal/data/ent/wmsmaintainplandetail"
	"kratos-mono-demo/internal/data/ent/wmsmaterial"
	"kratos-mono-demo/internal/data/ent/wmsmateriallog"
	"kratos-mono-demo/internal/data/ent/wmsmeasureunit"
	"kratos-mono-demo/internal/data/ent/wmsoperatelog"
	"kratos-mono-demo/internal/data/ent/wmsoutrepositoryorder"
	"kratos-mono-demo/internal/data/ent/wmsoutrepositoryorderdetail"
	"kratos-mono-demo/internal/data/ent/wmspurchaseorder"
	"kratos-mono-demo/internal/data/ent/wmspurchaseorderdetail"
	"kratos-mono-demo/internal/data/ent/wmsrepairorder"
	"kratos-mono-demo/internal/data/ent/wmsrepairorderdetail"
	"kratos-mono-demo/internal/data/ent/wmsrepairsettlementorder"
	"kratos-mono-demo/internal/data/ent/wmsrepairsettlementordersettlementfeedetail"
	"kratos-mono-demo/internal/data/ent/wmsrepairsettlementorderworkfeedetail"
	"kratos-mono-demo/internal/data/ent/wmsrepository"
	"kratos-mono-demo/internal/data/ent/wmsrepositoryadmin"
	"kratos-mono-demo/internal/data/ent/wmsrepositoryarea"
	"kratos-mono-demo/internal/data/ent/wmsrepositoryposition"
	"kratos-mono-demo/internal/data/ent/wmsrepositoryscreen"
	"kratos-mono-demo/internal/data/ent/wmsrepositoryscreenrepositoryarea"
	"kratos-mono-demo/internal/data/ent/wmsreturnorder"
	"kratos-mono-demo/internal/data/ent/wmsreturnorderdetail"
	"kratos-mono-demo/internal/data/ent/wmsrfid"
	"kratos-mono-demo/internal/data/ent/wmsrfidreader"
	"kratos-mono-demo/internal/data/ent/wmsrfidreaderrecord"
	"kratos-mono-demo/internal/data/ent/wmstransferorder"
	"kratos-mono-demo/internal/data/ent/wmstransferorderdetail"
	"kratos-mono-demo/internal/data/ent/wmsvehiclerepairorder"
	"kratos-mono-demo/internal/data/ent/wmsvehiclerepairordermaterialfeedetail"
	"kratos-mono-demo/internal/data/ent/wmsvehiclerepairordersettlementfeedetail"
	"kratos-mono-demo/internal/data/ent/wmsvehiclerepairorderworkfeedetail"
	"kratos-mono-demo/internal/data/ent/workwxapprovalmessage"
	"kratos-mono-demo/internal/data/ent/workwxapprovalnode"
	"kratos-mono-demo/internal/data/ent/workwxnotifynode"

	"entgo.io/ent/dialect/sql"
)

// The Query interface represents an operation that queries a graph.
// By using this interface, users can write generic code that manipulates
// query builders of different types.
type Query interface {
	// Type returns the string representation of the query type.
	Type() string
	// Limit the number of records to be returned by this query.
	Limit(int)
	// Offset to start from.
	Offset(int)
	// Unique configures the query builder to filter duplicate records.
	Unique(bool)
	// Order specifies how the records should be ordered.
	Order(...func(*sql.Selector))
	// WhereP appends storage-level predicates to the query builder. Using this method, users
	// can use type-assertion to append predicates that do not depend on any generated package.
	WhereP(...func(*sql.Selector))
}

// The Func type is an adapter that allows ordinary functions to be used as interceptors.
// Unlike traversal functions, interceptors are skipped during graph traversals. Note that the
// implementation of Func is different from the one defined in entgo.io/ent.InterceptFunc.
type Func func(context.Context, Query) error

// Intercept calls f(ctx, q) and then applied the next Querier.
func (f Func) Intercept(next ent.Querier) ent.Querier {
	return ent.QuerierFunc(func(ctx context.Context, q ent.Query) (ent.Value, error) {
		query, err := NewQuery(q)
		if err != nil {
			return nil, err
		}
		if err := f(ctx, query); err != nil {
			return nil, err
		}
		return next.Query(ctx, q)
	})
}

// The TraverseFunc type is an adapter to allow the use of ordinary function as Traverser.
// If f is a function with the appropriate signature, TraverseFunc(f) is a Traverser that calls f.
type TraverseFunc func(context.Context, Query) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseFunc) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseFunc) Traverse(ctx context.Context, q ent.Query) error {
	query, err := NewQuery(q)
	if err != nil {
		return err
	}
	return f(ctx, query)
}

// The AppApproveActivityFunc type is an adapter to allow the use of ordinary function as a Querier.
type AppApproveActivityFunc func(context.Context, *ent.AppApproveActivityQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AppApproveActivityFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AppApproveActivityQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AppApproveActivityQuery", q)
}

// The TraverseAppApproveActivity type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAppApproveActivity func(context.Context, *ent.AppApproveActivityQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAppApproveActivity) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAppApproveActivity) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AppApproveActivityQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AppApproveActivityQuery", q)
}

// The AppApproveBasicFunc type is an adapter to allow the use of ordinary function as a Querier.
type AppApproveBasicFunc func(context.Context, *ent.AppApproveBasicQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AppApproveBasicFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AppApproveBasicQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AppApproveBasicQuery", q)
}

// The TraverseAppApproveBasic type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAppApproveBasic func(context.Context, *ent.AppApproveBasicQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAppApproveBasic) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAppApproveBasic) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AppApproveBasicQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AppApproveBasicQuery", q)
}

// The AppApproveGroupFunc type is an adapter to allow the use of ordinary function as a Querier.
type AppApproveGroupFunc func(context.Context, *ent.AppApproveGroupQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AppApproveGroupFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AppApproveGroupQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AppApproveGroupQuery", q)
}

// The TraverseAppApproveGroup type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAppApproveGroup func(context.Context, *ent.AppApproveGroupQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAppApproveGroup) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAppApproveGroup) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AppApproveGroupQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AppApproveGroupQuery", q)
}

// The AppApproveRecordFunc type is an adapter to allow the use of ordinary function as a Querier.
type AppApproveRecordFunc func(context.Context, *ent.AppApproveRecordQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AppApproveRecordFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AppApproveRecordQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AppApproveRecordQuery", q)
}

// The TraverseAppApproveRecord type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAppApproveRecord func(context.Context, *ent.AppApproveRecordQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAppApproveRecord) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAppApproveRecord) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AppApproveRecordQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AppApproveRecordQuery", q)
}

// The AppApproveRecordLogFunc type is an adapter to allow the use of ordinary function as a Querier.
type AppApproveRecordLogFunc func(context.Context, *ent.AppApproveRecordLogQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AppApproveRecordLogFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AppApproveRecordLogQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AppApproveRecordLogQuery", q)
}

// The TraverseAppApproveRecordLog type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAppApproveRecordLog func(context.Context, *ent.AppApproveRecordLogQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAppApproveRecordLog) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAppApproveRecordLog) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AppApproveRecordLogQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AppApproveRecordLogQuery", q)
}

// The AppApproveRecordQueueFunc type is an adapter to allow the use of ordinary function as a Querier.
type AppApproveRecordQueueFunc func(context.Context, *ent.AppApproveRecordQueueQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AppApproveRecordQueueFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AppApproveRecordQueueQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AppApproveRecordQueueQuery", q)
}

// The TraverseAppApproveRecordQueue type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAppApproveRecordQueue func(context.Context, *ent.AppApproveRecordQueueQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAppApproveRecordQueue) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAppApproveRecordQueue) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AppApproveRecordQueueQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AppApproveRecordQueueQuery", q)
}

// The AppApproveWorkflowFunc type is an adapter to allow the use of ordinary function as a Querier.
type AppApproveWorkflowFunc func(context.Context, *ent.AppApproveWorkflowQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AppApproveWorkflowFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AppApproveWorkflowQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AppApproveWorkflowQuery", q)
}

// The TraverseAppApproveWorkflow type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAppApproveWorkflow func(context.Context, *ent.AppApproveWorkflowQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAppApproveWorkflow) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAppApproveWorkflow) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AppApproveWorkflowQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AppApproveWorkflowQuery", q)
}

// The DemoBuildingFunc type is an adapter to allow the use of ordinary function as a Querier.
type DemoBuildingFunc func(context.Context, *ent.DemoBuildingQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f DemoBuildingFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.DemoBuildingQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.DemoBuildingQuery", q)
}

// The TraverseDemoBuilding type is an adapter to allow the use of ordinary function as Traverser.
type TraverseDemoBuilding func(context.Context, *ent.DemoBuildingQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseDemoBuilding) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseDemoBuilding) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DemoBuildingQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.DemoBuildingQuery", q)
}

// The SysApplicationFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysApplicationFunc func(context.Context, *ent.SysApplicationQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysApplicationFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysApplicationQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysApplicationQuery", q)
}

// The TraverseSysApplication type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysApplication func(context.Context, *ent.SysApplicationQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysApplication) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysApplication) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysApplicationQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysApplicationQuery", q)
}

// The SysApplicationModuleFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysApplicationModuleFunc func(context.Context, *ent.SysApplicationModuleQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysApplicationModuleFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysApplicationModuleQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysApplicationModuleQuery", q)
}

// The TraverseSysApplicationModule type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysApplicationModule func(context.Context, *ent.SysApplicationModuleQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysApplicationModule) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysApplicationModule) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysApplicationModuleQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysApplicationModuleQuery", q)
}

// The SysApplicationModuleResourceFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysApplicationModuleResourceFunc func(context.Context, *ent.SysApplicationModuleResourceQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysApplicationModuleResourceFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysApplicationModuleResourceQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysApplicationModuleResourceQuery", q)
}

// The TraverseSysApplicationModuleResource type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysApplicationModuleResource func(context.Context, *ent.SysApplicationModuleResourceQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysApplicationModuleResource) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysApplicationModuleResource) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysApplicationModuleResourceQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysApplicationModuleResourceQuery", q)
}

// The SysAreaFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysAreaFunc func(context.Context, *ent.SysAreaQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysAreaFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysAreaQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysAreaQuery", q)
}

// The TraverseSysArea type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysArea func(context.Context, *ent.SysAreaQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysArea) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysArea) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysAreaQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysAreaQuery", q)
}

// The SysCityFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysCityFunc func(context.Context, *ent.SysCityQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysCityFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysCityQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysCityQuery", q)
}

// The TraverseSysCity type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysCity func(context.Context, *ent.SysCityQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysCity) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysCity) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysCityQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysCityQuery", q)
}

// The SysConfigFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysConfigFunc func(context.Context, *ent.SysConfigQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysConfigFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysConfigQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysConfigQuery", q)
}

// The TraverseSysConfig type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysConfig func(context.Context, *ent.SysConfigQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysConfig) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysConfig) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysConfigQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysConfigQuery", q)
}

// The SysDictionaryFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysDictionaryFunc func(context.Context, *ent.SysDictionaryQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysDictionaryFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysDictionaryQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysDictionaryQuery", q)
}

// The TraverseSysDictionary type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysDictionary func(context.Context, *ent.SysDictionaryQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysDictionary) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysDictionary) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysDictionaryQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysDictionaryQuery", q)
}

// The SysDictionaryDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysDictionaryDetailFunc func(context.Context, *ent.SysDictionaryDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysDictionaryDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysDictionaryDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysDictionaryDetailQuery", q)
}

// The TraverseSysDictionaryDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysDictionaryDetail func(context.Context, *ent.SysDictionaryDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysDictionaryDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysDictionaryDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysDictionaryDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysDictionaryDetailQuery", q)
}

// The SysGameFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysGameFunc func(context.Context, *ent.SysGameQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysGameFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysGameQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysGameQuery", q)
}

// The TraverseSysGame type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysGame func(context.Context, *ent.SysGameQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysGame) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysGame) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysGameQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysGameQuery", q)
}

// The SysLabelFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysLabelFunc func(context.Context, *ent.SysLabelQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysLabelFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysLabelQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysLabelQuery", q)
}

// The TraverseSysLabel type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysLabel func(context.Context, *ent.SysLabelQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysLabel) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysLabel) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysLabelQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysLabelQuery", q)
}

// The SysLogFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysLogFunc func(context.Context, *ent.SysLogQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysLogFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysLogQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysLogQuery", q)
}

// The TraverseSysLog type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysLog func(context.Context, *ent.SysLogQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysLog) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysLog) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysLogQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysLogQuery", q)
}

// The SysMenuFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysMenuFunc func(context.Context, *ent.SysMenuQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysMenuFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysMenuQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysMenuQuery", q)
}

// The TraverseSysMenu type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysMenu func(context.Context, *ent.SysMenuQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysMenu) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysMenu) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysMenuQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysMenuQuery", q)
}

// The SysMessageTemplateFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysMessageTemplateFunc func(context.Context, *ent.SysMessageTemplateQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysMessageTemplateFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysMessageTemplateQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysMessageTemplateQuery", q)
}

// The TraverseSysMessageTemplate type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysMessageTemplate func(context.Context, *ent.SysMessageTemplateQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysMessageTemplate) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysMessageTemplate) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysMessageTemplateQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysMessageTemplateQuery", q)
}

// The SysModuleFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysModuleFunc func(context.Context, *ent.SysModuleQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysModuleFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysModuleQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysModuleQuery", q)
}

// The TraverseSysModule type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysModule func(context.Context, *ent.SysModuleQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysModule) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysModule) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysModuleQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysModuleQuery", q)
}

// The SysModuleResourceFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysModuleResourceFunc func(context.Context, *ent.SysModuleResourceQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysModuleResourceFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysModuleResourceQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysModuleResourceQuery", q)
}

// The TraverseSysModuleResource type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysModuleResource func(context.Context, *ent.SysModuleResourceQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysModuleResource) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysModuleResource) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysModuleResourceQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysModuleResourceQuery", q)
}

// The SysOrganizationFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysOrganizationFunc func(context.Context, *ent.SysOrganizationQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysOrganizationFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysOrganizationQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysOrganizationQuery", q)
}

// The TraverseSysOrganization type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysOrganization func(context.Context, *ent.SysOrganizationQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysOrganization) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysOrganization) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysOrganizationQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysOrganizationQuery", q)
}

// The SysPageCodeFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysPageCodeFunc func(context.Context, *ent.SysPageCodeQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysPageCodeFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysPageCodeQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysPageCodeQuery", q)
}

// The TraverseSysPageCode type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysPageCode func(context.Context, *ent.SysPageCodeQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysPageCode) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysPageCode) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysPageCodeQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysPageCodeQuery", q)
}

// The SysPageCodeHistoryFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysPageCodeHistoryFunc func(context.Context, *ent.SysPageCodeHistoryQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysPageCodeHistoryFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysPageCodeHistoryQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysPageCodeHistoryQuery", q)
}

// The TraverseSysPageCodeHistory type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysPageCodeHistory func(context.Context, *ent.SysPageCodeHistoryQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysPageCodeHistory) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysPageCodeHistory) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysPageCodeHistoryQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysPageCodeHistoryQuery", q)
}

// The SysProjectFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysProjectFunc func(context.Context, *ent.SysProjectQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysProjectFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysProjectQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysProjectQuery", q)
}

// The TraverseSysProject type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysProject func(context.Context, *ent.SysProjectQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysProject) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysProject) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysProjectQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysProjectQuery", q)
}

// The SysProvinceFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysProvinceFunc func(context.Context, *ent.SysProvinceQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysProvinceFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysProvinceQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysProvinceQuery", q)
}

// The TraverseSysProvince type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysProvince func(context.Context, *ent.SysProvinceQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysProvince) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysProvince) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysProvinceQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysProvinceQuery", q)
}

// The SysResourceFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysResourceFunc func(context.Context, *ent.SysResourceQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysResourceFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysResourceQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysResourceQuery", q)
}

// The TraverseSysResource type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysResource func(context.Context, *ent.SysResourceQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysResource) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysResource) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysResourceQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysResourceQuery", q)
}

// The SysRoleFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysRoleFunc func(context.Context, *ent.SysRoleQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysRoleFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysRoleQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysRoleQuery", q)
}

// The TraverseSysRole type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysRole func(context.Context, *ent.SysRoleQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysRole) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysRole) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysRoleQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysRoleQuery", q)
}

// The SysRoleMenuFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysRoleMenuFunc func(context.Context, *ent.SysRoleMenuQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysRoleMenuFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysRoleMenuQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysRoleMenuQuery", q)
}

// The TraverseSysRoleMenu type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysRoleMenu func(context.Context, *ent.SysRoleMenuQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysRoleMenu) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysRoleMenu) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysRoleMenuQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysRoleMenuQuery", q)
}

// The SysRoleResourceFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysRoleResourceFunc func(context.Context, *ent.SysRoleResourceQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysRoleResourceFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysRoleResourceQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysRoleResourceQuery", q)
}

// The TraverseSysRoleResource type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysRoleResource func(context.Context, *ent.SysRoleResourceQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysRoleResource) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysRoleResource) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysRoleResourceQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysRoleResourceQuery", q)
}

// The SysStreetFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysStreetFunc func(context.Context, *ent.SysStreetQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysStreetFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysStreetQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysStreetQuery", q)
}

// The TraverseSysStreet type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysStreet func(context.Context, *ent.SysStreetQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysStreet) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysStreet) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysStreetQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysStreetQuery", q)
}

// The SysTeamFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysTeamFunc func(context.Context, *ent.SysTeamQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysTeamFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysTeamQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysTeamQuery", q)
}

// The TraverseSysTeam type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysTeam func(context.Context, *ent.SysTeamQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysTeam) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysTeam) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysTeamQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysTeamQuery", q)
}

// The SysTeamApplicationFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysTeamApplicationFunc func(context.Context, *ent.SysTeamApplicationQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysTeamApplicationFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysTeamApplicationQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysTeamApplicationQuery", q)
}

// The TraverseSysTeamApplication type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysTeamApplication func(context.Context, *ent.SysTeamApplicationQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysTeamApplication) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysTeamApplication) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysTeamApplicationQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysTeamApplicationQuery", q)
}

// The SysUploadFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysUploadFunc func(context.Context, *ent.SysUploadQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysUploadFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysUploadQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysUploadQuery", q)
}

// The TraverseSysUpload type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysUpload func(context.Context, *ent.SysUploadQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysUpload) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysUpload) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysUploadQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysUploadQuery", q)
}

// The SysUserFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysUserFunc func(context.Context, *ent.SysUserQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysUserFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysUserQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysUserQuery", q)
}

// The TraverseSysUser type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysUser func(context.Context, *ent.SysUserQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysUser) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysUser) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysUserQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysUserQuery", q)
}

// The SysUserAuthFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysUserAuthFunc func(context.Context, *ent.SysUserAuthQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysUserAuthFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysUserAuthQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysUserAuthQuery", q)
}

// The TraverseSysUserAuth type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysUserAuth func(context.Context, *ent.SysUserAuthQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysUserAuth) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysUserAuth) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysUserAuthQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysUserAuthQuery", q)
}

// The SysUserLogFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysUserLogFunc func(context.Context, *ent.SysUserLogQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysUserLogFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysUserLogQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysUserLogQuery", q)
}

// The TraverseSysUserLog type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysUserLog func(context.Context, *ent.SysUserLogQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysUserLog) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysUserLog) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysUserLogQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysUserLogQuery", q)
}

// The SysUserOrganizationFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysUserOrganizationFunc func(context.Context, *ent.SysUserOrganizationQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysUserOrganizationFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysUserOrganizationQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysUserOrganizationQuery", q)
}

// The TraverseSysUserOrganization type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysUserOrganization func(context.Context, *ent.SysUserOrganizationQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysUserOrganization) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysUserOrganization) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysUserOrganizationQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysUserOrganizationQuery", q)
}

// The SysUserRoleFunc type is an adapter to allow the use of ordinary function as a Querier.
type SysUserRoleFunc func(context.Context, *ent.SysUserRoleQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SysUserRoleFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SysUserRoleQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SysUserRoleQuery", q)
}

// The TraverseSysUserRole type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSysUserRole func(context.Context, *ent.SysUserRoleQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSysUserRole) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSysUserRole) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SysUserRoleQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SysUserRoleQuery", q)
}

// The WmsAccessDoorLogFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsAccessDoorLogFunc func(context.Context, *ent.WmsAccessDoorLogQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsAccessDoorLogFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsAccessDoorLogQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsAccessDoorLogQuery", q)
}

// The TraverseWmsAccessDoorLog type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsAccessDoorLog func(context.Context, *ent.WmsAccessDoorLogQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsAccessDoorLog) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsAccessDoorLog) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsAccessDoorLogQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsAccessDoorLogQuery", q)
}

// The WmsApprovalTaskFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsApprovalTaskFunc func(context.Context, *ent.WmsApprovalTaskQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsApprovalTaskFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsApprovalTaskQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsApprovalTaskQuery", q)
}

// The TraverseWmsApprovalTask type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsApprovalTask func(context.Context, *ent.WmsApprovalTaskQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsApprovalTask) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsApprovalTask) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsApprovalTaskQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsApprovalTaskQuery", q)
}

// The WmsApprovalTaskDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsApprovalTaskDetailFunc func(context.Context, *ent.WmsApprovalTaskDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsApprovalTaskDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsApprovalTaskDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsApprovalTaskDetailQuery", q)
}

// The TraverseWmsApprovalTaskDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsApprovalTaskDetail func(context.Context, *ent.WmsApprovalTaskDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsApprovalTaskDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsApprovalTaskDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsApprovalTaskDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsApprovalTaskDetailQuery", q)
}

// The WmsApprovalTaskOperationFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsApprovalTaskOperationFunc func(context.Context, *ent.WmsApprovalTaskOperationQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsApprovalTaskOperationFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsApprovalTaskOperationQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsApprovalTaskOperationQuery", q)
}

// The TraverseWmsApprovalTaskOperation type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsApprovalTaskOperation func(context.Context, *ent.WmsApprovalTaskOperationQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsApprovalTaskOperation) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsApprovalTaskOperation) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsApprovalTaskOperationQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsApprovalTaskOperationQuery", q)
}

// The WmsApprovalTaskRemarkFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsApprovalTaskRemarkFunc func(context.Context, *ent.WmsApprovalTaskRemarkQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsApprovalTaskRemarkFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsApprovalTaskRemarkQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsApprovalTaskRemarkQuery", q)
}

// The TraverseWmsApprovalTaskRemark type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsApprovalTaskRemark func(context.Context, *ent.WmsApprovalTaskRemarkQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsApprovalTaskRemark) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsApprovalTaskRemark) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsApprovalTaskRemarkQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsApprovalTaskRemarkQuery", q)
}

// The WmsAuditPlanFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsAuditPlanFunc func(context.Context, *ent.WmsAuditPlanQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsAuditPlanFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsAuditPlanQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsAuditPlanQuery", q)
}

// The TraverseWmsAuditPlan type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsAuditPlan func(context.Context, *ent.WmsAuditPlanQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsAuditPlan) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsAuditPlan) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsAuditPlanQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsAuditPlanQuery", q)
}

// The WmsAuditPlanDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsAuditPlanDetailFunc func(context.Context, *ent.WmsAuditPlanDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsAuditPlanDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsAuditPlanDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsAuditPlanDetailQuery", q)
}

// The TraverseWmsAuditPlanDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsAuditPlanDetail func(context.Context, *ent.WmsAuditPlanDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsAuditPlanDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsAuditPlanDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsAuditPlanDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsAuditPlanDetailQuery", q)
}

// The WmsBorrowOrderFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsBorrowOrderFunc func(context.Context, *ent.WmsBorrowOrderQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsBorrowOrderFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsBorrowOrderQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsBorrowOrderQuery", q)
}

// The TraverseWmsBorrowOrder type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsBorrowOrder func(context.Context, *ent.WmsBorrowOrderQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsBorrowOrder) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsBorrowOrder) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsBorrowOrderQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsBorrowOrderQuery", q)
}

// The WmsBorrowOrderDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsBorrowOrderDetailFunc func(context.Context, *ent.WmsBorrowOrderDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsBorrowOrderDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsBorrowOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsBorrowOrderDetailQuery", q)
}

// The TraverseWmsBorrowOrderDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsBorrowOrderDetail func(context.Context, *ent.WmsBorrowOrderDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsBorrowOrderDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsBorrowOrderDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsBorrowOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsBorrowOrderDetailQuery", q)
}

// The WmsCarFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsCarFunc func(context.Context, *ent.WmsCarQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsCarFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsCarQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsCarQuery", q)
}

// The TraverseWmsCar type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsCar func(context.Context, *ent.WmsCarQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsCar) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsCar) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsCarQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsCarQuery", q)
}

// The WmsClaimOrderFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsClaimOrderFunc func(context.Context, *ent.WmsClaimOrderQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsClaimOrderFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsClaimOrderQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsClaimOrderQuery", q)
}

// The TraverseWmsClaimOrder type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsClaimOrder func(context.Context, *ent.WmsClaimOrderQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsClaimOrder) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsClaimOrder) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsClaimOrderQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsClaimOrderQuery", q)
}

// The WmsClaimOrderDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsClaimOrderDetailFunc func(context.Context, *ent.WmsClaimOrderDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsClaimOrderDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsClaimOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsClaimOrderDetailQuery", q)
}

// The TraverseWmsClaimOrderDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsClaimOrderDetail func(context.Context, *ent.WmsClaimOrderDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsClaimOrderDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsClaimOrderDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsClaimOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsClaimOrderDetailQuery", q)
}

// The WmsCompanyFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsCompanyFunc func(context.Context, *ent.WmsCompanyQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsCompanyFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsCompanyQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsCompanyQuery", q)
}

// The TraverseWmsCompany type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsCompany func(context.Context, *ent.WmsCompanyQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsCompany) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsCompany) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsCompanyQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsCompanyQuery", q)
}

// The WmsCompanyAddressFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsCompanyAddressFunc func(context.Context, *ent.WmsCompanyAddressQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsCompanyAddressFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsCompanyAddressQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsCompanyAddressQuery", q)
}

// The TraverseWmsCompanyAddress type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsCompanyAddress func(context.Context, *ent.WmsCompanyAddressQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsCompanyAddress) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsCompanyAddress) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsCompanyAddressQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsCompanyAddressQuery", q)
}

// The WmsDiscardMeetingFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsDiscardMeetingFunc func(context.Context, *ent.WmsDiscardMeetingQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsDiscardMeetingFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsDiscardMeetingQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsDiscardMeetingQuery", q)
}

// The TraverseWmsDiscardMeeting type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsDiscardMeeting func(context.Context, *ent.WmsDiscardMeetingQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsDiscardMeeting) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsDiscardMeeting) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsDiscardMeetingQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsDiscardMeetingQuery", q)
}

// The WmsDiscardMeetingDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsDiscardMeetingDetailFunc func(context.Context, *ent.WmsDiscardMeetingDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsDiscardMeetingDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsDiscardMeetingDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsDiscardMeetingDetailQuery", q)
}

// The TraverseWmsDiscardMeetingDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsDiscardMeetingDetail func(context.Context, *ent.WmsDiscardMeetingDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsDiscardMeetingDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsDiscardMeetingDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsDiscardMeetingDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsDiscardMeetingDetailQuery", q)
}

// The WmsDiscardOrderFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsDiscardOrderFunc func(context.Context, *ent.WmsDiscardOrderQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsDiscardOrderFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsDiscardOrderQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsDiscardOrderQuery", q)
}

// The TraverseWmsDiscardOrder type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsDiscardOrder func(context.Context, *ent.WmsDiscardOrderQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsDiscardOrder) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsDiscardOrder) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsDiscardOrderQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsDiscardOrderQuery", q)
}

// The WmsDiscardOrderDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsDiscardOrderDetailFunc func(context.Context, *ent.WmsDiscardOrderDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsDiscardOrderDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsDiscardOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsDiscardOrderDetailQuery", q)
}

// The TraverseWmsDiscardOrderDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsDiscardOrderDetail func(context.Context, *ent.WmsDiscardOrderDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsDiscardOrderDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsDiscardOrderDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsDiscardOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsDiscardOrderDetailQuery", q)
}

// The WmsDiscardPlanOrderFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsDiscardPlanOrderFunc func(context.Context, *ent.WmsDiscardPlanOrderQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsDiscardPlanOrderFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsDiscardPlanOrderQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsDiscardPlanOrderQuery", q)
}

// The TraverseWmsDiscardPlanOrder type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsDiscardPlanOrder func(context.Context, *ent.WmsDiscardPlanOrderQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsDiscardPlanOrder) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsDiscardPlanOrder) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsDiscardPlanOrderQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsDiscardPlanOrderQuery", q)
}

// The WmsDiscardPlanOrderDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsDiscardPlanOrderDetailFunc func(context.Context, *ent.WmsDiscardPlanOrderDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsDiscardPlanOrderDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsDiscardPlanOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsDiscardPlanOrderDetailQuery", q)
}

// The TraverseWmsDiscardPlanOrderDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsDiscardPlanOrderDetail func(context.Context, *ent.WmsDiscardPlanOrderDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsDiscardPlanOrderDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsDiscardPlanOrderDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsDiscardPlanOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsDiscardPlanOrderDetailQuery", q)
}

// The WmsDocumentFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsDocumentFunc func(context.Context, *ent.WmsDocumentQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsDocumentFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsDocumentQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsDocumentQuery", q)
}

// The TraverseWmsDocument type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsDocument func(context.Context, *ent.WmsDocumentQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsDocument) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsDocument) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsDocumentQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsDocumentQuery", q)
}

// The WmsEnterRepositoryOrderFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsEnterRepositoryOrderFunc func(context.Context, *ent.WmsEnterRepositoryOrderQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsEnterRepositoryOrderFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsEnterRepositoryOrderQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsEnterRepositoryOrderQuery", q)
}

// The TraverseWmsEnterRepositoryOrder type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsEnterRepositoryOrder func(context.Context, *ent.WmsEnterRepositoryOrderQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsEnterRepositoryOrder) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsEnterRepositoryOrder) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsEnterRepositoryOrderQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsEnterRepositoryOrderQuery", q)
}

// The WmsEnterRepositoryOrderDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsEnterRepositoryOrderDetailFunc func(context.Context, *ent.WmsEnterRepositoryOrderDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsEnterRepositoryOrderDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsEnterRepositoryOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsEnterRepositoryOrderDetailQuery", q)
}

// The TraverseWmsEnterRepositoryOrderDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsEnterRepositoryOrderDetail func(context.Context, *ent.WmsEnterRepositoryOrderDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsEnterRepositoryOrderDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsEnterRepositoryOrderDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsEnterRepositoryOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsEnterRepositoryOrderDetailQuery", q)
}

// The WmsEquipmentFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsEquipmentFunc func(context.Context, *ent.WmsEquipmentQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsEquipmentFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsEquipmentQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsEquipmentQuery", q)
}

// The TraverseWmsEquipment type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsEquipment func(context.Context, *ent.WmsEquipmentQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsEquipment) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsEquipment) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsEquipmentQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsEquipmentQuery", q)
}

// The WmsEquipmentDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsEquipmentDetailFunc func(context.Context, *ent.WmsEquipmentDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsEquipmentDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsEquipmentDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsEquipmentDetailQuery", q)
}

// The TraverseWmsEquipmentDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsEquipmentDetail func(context.Context, *ent.WmsEquipmentDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsEquipmentDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsEquipmentDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsEquipmentDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsEquipmentDetailQuery", q)
}

// The WmsEquipmentTypeFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsEquipmentTypeFunc func(context.Context, *ent.WmsEquipmentTypeQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsEquipmentTypeFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsEquipmentTypeQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsEquipmentTypeQuery", q)
}

// The TraverseWmsEquipmentType type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsEquipmentType func(context.Context, *ent.WmsEquipmentTypeQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsEquipmentType) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsEquipmentType) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsEquipmentTypeQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsEquipmentTypeQuery", q)
}

// The WmsEquipmentTypePropertyFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsEquipmentTypePropertyFunc func(context.Context, *ent.WmsEquipmentTypePropertyQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsEquipmentTypePropertyFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsEquipmentTypePropertyQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsEquipmentTypePropertyQuery", q)
}

// The TraverseWmsEquipmentTypeProperty type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsEquipmentTypeProperty func(context.Context, *ent.WmsEquipmentTypePropertyQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsEquipmentTypeProperty) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsEquipmentTypeProperty) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsEquipmentTypePropertyQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsEquipmentTypePropertyQuery", q)
}

// The WmsEquipmentTypePropertyGroupFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsEquipmentTypePropertyGroupFunc func(context.Context, *ent.WmsEquipmentTypePropertyGroupQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsEquipmentTypePropertyGroupFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsEquipmentTypePropertyGroupQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsEquipmentTypePropertyGroupQuery", q)
}

// The TraverseWmsEquipmentTypePropertyGroup type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsEquipmentTypePropertyGroup func(context.Context, *ent.WmsEquipmentTypePropertyGroupQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsEquipmentTypePropertyGroup) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsEquipmentTypePropertyGroup) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsEquipmentTypePropertyGroupQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsEquipmentTypePropertyGroupQuery", q)
}

// The WmsEquipmentTypePropertyOptionFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsEquipmentTypePropertyOptionFunc func(context.Context, *ent.WmsEquipmentTypePropertyOptionQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsEquipmentTypePropertyOptionFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsEquipmentTypePropertyOptionQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsEquipmentTypePropertyOptionQuery", q)
}

// The TraverseWmsEquipmentTypePropertyOption type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsEquipmentTypePropertyOption func(context.Context, *ent.WmsEquipmentTypePropertyOptionQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsEquipmentTypePropertyOption) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsEquipmentTypePropertyOption) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsEquipmentTypePropertyOptionQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsEquipmentTypePropertyOptionQuery", q)
}

// The WmsEquipmentUserGroupFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsEquipmentUserGroupFunc func(context.Context, *ent.WmsEquipmentUserGroupQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsEquipmentUserGroupFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsEquipmentUserGroupQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsEquipmentUserGroupQuery", q)
}

// The TraverseWmsEquipmentUserGroup type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsEquipmentUserGroup func(context.Context, *ent.WmsEquipmentUserGroupQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsEquipmentUserGroup) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsEquipmentUserGroup) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsEquipmentUserGroupQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsEquipmentUserGroupQuery", q)
}

// The WmsFireStationFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsFireStationFunc func(context.Context, *ent.WmsFireStationQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsFireStationFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsFireStationQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsFireStationQuery", q)
}

// The TraverseWmsFireStation type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsFireStation func(context.Context, *ent.WmsFireStationQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsFireStation) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsFireStation) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsFireStationQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsFireStationQuery", q)
}

// The WmsGPSFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsGPSFunc func(context.Context, *ent.WmsGPSQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsGPSFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsGPSQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsGPSQuery", q)
}

// The TraverseWmsGPS type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsGPS func(context.Context, *ent.WmsGPSQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsGPS) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsGPS) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsGPSQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsGPSQuery", q)
}

// The WmsGPSRecordFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsGPSRecordFunc func(context.Context, *ent.WmsGPSRecordQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsGPSRecordFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsGPSRecordQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsGPSRecordQuery", q)
}

// The TraverseWmsGPSRecord type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsGPSRecord func(context.Context, *ent.WmsGPSRecordQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsGPSRecord) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsGPSRecord) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsGPSRecordQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsGPSRecordQuery", q)
}

// The WmsLearningCourseFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsLearningCourseFunc func(context.Context, *ent.WmsLearningCourseQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsLearningCourseFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsLearningCourseQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsLearningCourseQuery", q)
}

// The TraverseWmsLearningCourse type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsLearningCourse func(context.Context, *ent.WmsLearningCourseQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsLearningCourse) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsLearningCourse) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsLearningCourseQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsLearningCourseQuery", q)
}

// The WmsLearningCourseCoursewareFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsLearningCourseCoursewareFunc func(context.Context, *ent.WmsLearningCourseCoursewareQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsLearningCourseCoursewareFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsLearningCourseCoursewareQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsLearningCourseCoursewareQuery", q)
}

// The TraverseWmsLearningCourseCourseware type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsLearningCourseCourseware func(context.Context, *ent.WmsLearningCourseCoursewareQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsLearningCourseCourseware) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsLearningCourseCourseware) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsLearningCourseCoursewareQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsLearningCourseCoursewareQuery", q)
}

// The WmsLearningCourseLogFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsLearningCourseLogFunc func(context.Context, *ent.WmsLearningCourseLogQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsLearningCourseLogFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsLearningCourseLogQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsLearningCourseLogQuery", q)
}

// The TraverseWmsLearningCourseLog type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsLearningCourseLog func(context.Context, *ent.WmsLearningCourseLogQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsLearningCourseLog) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsLearningCourseLog) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsLearningCourseLogQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsLearningCourseLogQuery", q)
}

// The WmsLearningCourseRecordFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsLearningCourseRecordFunc func(context.Context, *ent.WmsLearningCourseRecordQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsLearningCourseRecordFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsLearningCourseRecordQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsLearningCourseRecordQuery", q)
}

// The TraverseWmsLearningCourseRecord type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsLearningCourseRecord func(context.Context, *ent.WmsLearningCourseRecordQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsLearningCourseRecord) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsLearningCourseRecord) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsLearningCourseRecordQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsLearningCourseRecordQuery", q)
}

// The WmsLearningCoursewareFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsLearningCoursewareFunc func(context.Context, *ent.WmsLearningCoursewareQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsLearningCoursewareFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsLearningCoursewareQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsLearningCoursewareQuery", q)
}

// The TraverseWmsLearningCourseware type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsLearningCourseware func(context.Context, *ent.WmsLearningCoursewareQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsLearningCourseware) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsLearningCourseware) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsLearningCoursewareQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsLearningCoursewareQuery", q)
}

// The WmsLearningCoursewareRecordFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsLearningCoursewareRecordFunc func(context.Context, *ent.WmsLearningCoursewareRecordQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsLearningCoursewareRecordFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsLearningCoursewareRecordQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsLearningCoursewareRecordQuery", q)
}

// The TraverseWmsLearningCoursewareRecord type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsLearningCoursewareRecord func(context.Context, *ent.WmsLearningCoursewareRecordQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsLearningCoursewareRecord) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsLearningCoursewareRecord) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsLearningCoursewareRecordQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsLearningCoursewareRecordQuery", q)
}

// The WmsLearningPlanFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsLearningPlanFunc func(context.Context, *ent.WmsLearningPlanQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsLearningPlanFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsLearningPlanQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsLearningPlanQuery", q)
}

// The TraverseWmsLearningPlan type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsLearningPlan func(context.Context, *ent.WmsLearningPlanQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsLearningPlan) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsLearningPlan) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsLearningPlanQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsLearningPlanQuery", q)
}

// The WmsLearningPlanRecordFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsLearningPlanRecordFunc func(context.Context, *ent.WmsLearningPlanRecordQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsLearningPlanRecordFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsLearningPlanRecordQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsLearningPlanRecordQuery", q)
}

// The TraverseWmsLearningPlanRecord type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsLearningPlanRecord func(context.Context, *ent.WmsLearningPlanRecordQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsLearningPlanRecord) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsLearningPlanRecord) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsLearningPlanRecordQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsLearningPlanRecordQuery", q)
}

// The WmsMaintainOrderFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsMaintainOrderFunc func(context.Context, *ent.WmsMaintainOrderQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsMaintainOrderFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsMaintainOrderQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsMaintainOrderQuery", q)
}

// The TraverseWmsMaintainOrder type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsMaintainOrder func(context.Context, *ent.WmsMaintainOrderQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsMaintainOrder) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsMaintainOrder) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsMaintainOrderQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsMaintainOrderQuery", q)
}

// The WmsMaintainOrderDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsMaintainOrderDetailFunc func(context.Context, *ent.WmsMaintainOrderDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsMaintainOrderDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsMaintainOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsMaintainOrderDetailQuery", q)
}

// The TraverseWmsMaintainOrderDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsMaintainOrderDetail func(context.Context, *ent.WmsMaintainOrderDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsMaintainOrderDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsMaintainOrderDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsMaintainOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsMaintainOrderDetailQuery", q)
}

// The WmsMaintainPlanFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsMaintainPlanFunc func(context.Context, *ent.WmsMaintainPlanQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsMaintainPlanFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsMaintainPlanQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsMaintainPlanQuery", q)
}

// The TraverseWmsMaintainPlan type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsMaintainPlan func(context.Context, *ent.WmsMaintainPlanQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsMaintainPlan) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsMaintainPlan) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsMaintainPlanQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsMaintainPlanQuery", q)
}

// The WmsMaintainPlanDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsMaintainPlanDetailFunc func(context.Context, *ent.WmsMaintainPlanDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsMaintainPlanDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsMaintainPlanDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsMaintainPlanDetailQuery", q)
}

// The TraverseWmsMaintainPlanDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsMaintainPlanDetail func(context.Context, *ent.WmsMaintainPlanDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsMaintainPlanDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsMaintainPlanDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsMaintainPlanDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsMaintainPlanDetailQuery", q)
}

// The WmsMaterialFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsMaterialFunc func(context.Context, *ent.WmsMaterialQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsMaterialFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsMaterialQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsMaterialQuery", q)
}

// The TraverseWmsMaterial type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsMaterial func(context.Context, *ent.WmsMaterialQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsMaterial) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsMaterial) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsMaterialQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsMaterialQuery", q)
}

// The WmsMaterialLogFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsMaterialLogFunc func(context.Context, *ent.WmsMaterialLogQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsMaterialLogFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsMaterialLogQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsMaterialLogQuery", q)
}

// The TraverseWmsMaterialLog type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsMaterialLog func(context.Context, *ent.WmsMaterialLogQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsMaterialLog) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsMaterialLog) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsMaterialLogQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsMaterialLogQuery", q)
}

// The WmsMeasureUnitFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsMeasureUnitFunc func(context.Context, *ent.WmsMeasureUnitQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsMeasureUnitFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsMeasureUnitQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsMeasureUnitQuery", q)
}

// The TraverseWmsMeasureUnit type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsMeasureUnit func(context.Context, *ent.WmsMeasureUnitQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsMeasureUnit) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsMeasureUnit) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsMeasureUnitQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsMeasureUnitQuery", q)
}

// The WmsOperateLogFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsOperateLogFunc func(context.Context, *ent.WmsOperateLogQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsOperateLogFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsOperateLogQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsOperateLogQuery", q)
}

// The TraverseWmsOperateLog type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsOperateLog func(context.Context, *ent.WmsOperateLogQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsOperateLog) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsOperateLog) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsOperateLogQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsOperateLogQuery", q)
}

// The WmsOutRepositoryOrderFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsOutRepositoryOrderFunc func(context.Context, *ent.WmsOutRepositoryOrderQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsOutRepositoryOrderFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsOutRepositoryOrderQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsOutRepositoryOrderQuery", q)
}

// The TraverseWmsOutRepositoryOrder type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsOutRepositoryOrder func(context.Context, *ent.WmsOutRepositoryOrderQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsOutRepositoryOrder) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsOutRepositoryOrder) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsOutRepositoryOrderQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsOutRepositoryOrderQuery", q)
}

// The WmsOutRepositoryOrderDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsOutRepositoryOrderDetailFunc func(context.Context, *ent.WmsOutRepositoryOrderDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsOutRepositoryOrderDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsOutRepositoryOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsOutRepositoryOrderDetailQuery", q)
}

// The TraverseWmsOutRepositoryOrderDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsOutRepositoryOrderDetail func(context.Context, *ent.WmsOutRepositoryOrderDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsOutRepositoryOrderDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsOutRepositoryOrderDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsOutRepositoryOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsOutRepositoryOrderDetailQuery", q)
}

// The WmsPurchaseOrderFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsPurchaseOrderFunc func(context.Context, *ent.WmsPurchaseOrderQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsPurchaseOrderFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsPurchaseOrderQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsPurchaseOrderQuery", q)
}

// The TraverseWmsPurchaseOrder type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsPurchaseOrder func(context.Context, *ent.WmsPurchaseOrderQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsPurchaseOrder) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsPurchaseOrder) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsPurchaseOrderQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsPurchaseOrderQuery", q)
}

// The WmsPurchaseOrderDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsPurchaseOrderDetailFunc func(context.Context, *ent.WmsPurchaseOrderDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsPurchaseOrderDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsPurchaseOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsPurchaseOrderDetailQuery", q)
}

// The TraverseWmsPurchaseOrderDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsPurchaseOrderDetail func(context.Context, *ent.WmsPurchaseOrderDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsPurchaseOrderDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsPurchaseOrderDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsPurchaseOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsPurchaseOrderDetailQuery", q)
}

// The WmsRepairOrderFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsRepairOrderFunc func(context.Context, *ent.WmsRepairOrderQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsRepairOrderFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsRepairOrderQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsRepairOrderQuery", q)
}

// The TraverseWmsRepairOrder type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsRepairOrder func(context.Context, *ent.WmsRepairOrderQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsRepairOrder) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsRepairOrder) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepairOrderQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsRepairOrderQuery", q)
}

// The WmsRepairOrderDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsRepairOrderDetailFunc func(context.Context, *ent.WmsRepairOrderDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsRepairOrderDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsRepairOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsRepairOrderDetailQuery", q)
}

// The TraverseWmsRepairOrderDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsRepairOrderDetail func(context.Context, *ent.WmsRepairOrderDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsRepairOrderDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsRepairOrderDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepairOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsRepairOrderDetailQuery", q)
}

// The WmsRepairSettlementOrderFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsRepairSettlementOrderFunc func(context.Context, *ent.WmsRepairSettlementOrderQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsRepairSettlementOrderFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsRepairSettlementOrderQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsRepairSettlementOrderQuery", q)
}

// The TraverseWmsRepairSettlementOrder type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsRepairSettlementOrder func(context.Context, *ent.WmsRepairSettlementOrderQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsRepairSettlementOrder) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsRepairSettlementOrder) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepairSettlementOrderQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsRepairSettlementOrderQuery", q)
}

// The WmsRepairSettlementOrderSettlementfeeDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsRepairSettlementOrderSettlementfeeDetailFunc func(context.Context, *ent.WmsRepairSettlementOrderSettlementfeeDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsRepairSettlementOrderSettlementfeeDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsRepairSettlementOrderSettlementfeeDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsRepairSettlementOrderSettlementfeeDetailQuery", q)
}

// The TraverseWmsRepairSettlementOrderSettlementfeeDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsRepairSettlementOrderSettlementfeeDetail func(context.Context, *ent.WmsRepairSettlementOrderSettlementfeeDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsRepairSettlementOrderSettlementfeeDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsRepairSettlementOrderSettlementfeeDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepairSettlementOrderSettlementfeeDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsRepairSettlementOrderSettlementfeeDetailQuery", q)
}

// The WmsRepairSettlementOrderWorkfeeDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsRepairSettlementOrderWorkfeeDetailFunc func(context.Context, *ent.WmsRepairSettlementOrderWorkfeeDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsRepairSettlementOrderWorkfeeDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsRepairSettlementOrderWorkfeeDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsRepairSettlementOrderWorkfeeDetailQuery", q)
}

// The TraverseWmsRepairSettlementOrderWorkfeeDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsRepairSettlementOrderWorkfeeDetail func(context.Context, *ent.WmsRepairSettlementOrderWorkfeeDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsRepairSettlementOrderWorkfeeDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsRepairSettlementOrderWorkfeeDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepairSettlementOrderWorkfeeDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsRepairSettlementOrderWorkfeeDetailQuery", q)
}

// The WmsRepositoryFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsRepositoryFunc func(context.Context, *ent.WmsRepositoryQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsRepositoryFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsRepositoryQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsRepositoryQuery", q)
}

// The TraverseWmsRepository type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsRepository func(context.Context, *ent.WmsRepositoryQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsRepository) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsRepository) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepositoryQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsRepositoryQuery", q)
}

// The WmsRepositoryAdminFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsRepositoryAdminFunc func(context.Context, *ent.WmsRepositoryAdminQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsRepositoryAdminFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsRepositoryAdminQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsRepositoryAdminQuery", q)
}

// The TraverseWmsRepositoryAdmin type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsRepositoryAdmin func(context.Context, *ent.WmsRepositoryAdminQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsRepositoryAdmin) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsRepositoryAdmin) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepositoryAdminQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsRepositoryAdminQuery", q)
}

// The WmsRepositoryAreaFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsRepositoryAreaFunc func(context.Context, *ent.WmsRepositoryAreaQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsRepositoryAreaFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsRepositoryAreaQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsRepositoryAreaQuery", q)
}

// The TraverseWmsRepositoryArea type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsRepositoryArea func(context.Context, *ent.WmsRepositoryAreaQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsRepositoryArea) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsRepositoryArea) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepositoryAreaQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsRepositoryAreaQuery", q)
}

// The WmsRepositoryPositionFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsRepositoryPositionFunc func(context.Context, *ent.WmsRepositoryPositionQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsRepositoryPositionFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsRepositoryPositionQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsRepositoryPositionQuery", q)
}

// The TraverseWmsRepositoryPosition type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsRepositoryPosition func(context.Context, *ent.WmsRepositoryPositionQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsRepositoryPosition) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsRepositoryPosition) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepositoryPositionQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsRepositoryPositionQuery", q)
}

// The WmsRepositoryScreenFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsRepositoryScreenFunc func(context.Context, *ent.WmsRepositoryScreenQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsRepositoryScreenFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsRepositoryScreenQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsRepositoryScreenQuery", q)
}

// The TraverseWmsRepositoryScreen type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsRepositoryScreen func(context.Context, *ent.WmsRepositoryScreenQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsRepositoryScreen) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsRepositoryScreen) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepositoryScreenQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsRepositoryScreenQuery", q)
}

// The WmsRepositoryScreenRepositoryAreaFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsRepositoryScreenRepositoryAreaFunc func(context.Context, *ent.WmsRepositoryScreenRepositoryAreaQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsRepositoryScreenRepositoryAreaFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsRepositoryScreenRepositoryAreaQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsRepositoryScreenRepositoryAreaQuery", q)
}

// The TraverseWmsRepositoryScreenRepositoryArea type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsRepositoryScreenRepositoryArea func(context.Context, *ent.WmsRepositoryScreenRepositoryAreaQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsRepositoryScreenRepositoryArea) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsRepositoryScreenRepositoryArea) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRepositoryScreenRepositoryAreaQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsRepositoryScreenRepositoryAreaQuery", q)
}

// The WmsReturnOrderFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsReturnOrderFunc func(context.Context, *ent.WmsReturnOrderQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsReturnOrderFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsReturnOrderQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsReturnOrderQuery", q)
}

// The TraverseWmsReturnOrder type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsReturnOrder func(context.Context, *ent.WmsReturnOrderQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsReturnOrder) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsReturnOrder) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsReturnOrderQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsReturnOrderQuery", q)
}

// The WmsReturnOrderDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsReturnOrderDetailFunc func(context.Context, *ent.WmsReturnOrderDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsReturnOrderDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsReturnOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsReturnOrderDetailQuery", q)
}

// The TraverseWmsReturnOrderDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsReturnOrderDetail func(context.Context, *ent.WmsReturnOrderDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsReturnOrderDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsReturnOrderDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsReturnOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsReturnOrderDetailQuery", q)
}

// The WmsRfidFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsRfidFunc func(context.Context, *ent.WmsRfidQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsRfidFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsRfidQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsRfidQuery", q)
}

// The TraverseWmsRfid type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsRfid func(context.Context, *ent.WmsRfidQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsRfid) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsRfid) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRfidQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsRfidQuery", q)
}

// The WmsRfidReaderFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsRfidReaderFunc func(context.Context, *ent.WmsRfidReaderQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsRfidReaderFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsRfidReaderQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsRfidReaderQuery", q)
}

// The TraverseWmsRfidReader type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsRfidReader func(context.Context, *ent.WmsRfidReaderQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsRfidReader) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsRfidReader) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRfidReaderQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsRfidReaderQuery", q)
}

// The WmsRfidReaderRecordFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsRfidReaderRecordFunc func(context.Context, *ent.WmsRfidReaderRecordQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsRfidReaderRecordFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsRfidReaderRecordQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsRfidReaderRecordQuery", q)
}

// The TraverseWmsRfidReaderRecord type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsRfidReaderRecord func(context.Context, *ent.WmsRfidReaderRecordQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsRfidReaderRecord) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsRfidReaderRecord) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsRfidReaderRecordQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsRfidReaderRecordQuery", q)
}

// The WmsTransferOrderFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsTransferOrderFunc func(context.Context, *ent.WmsTransferOrderQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsTransferOrderFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsTransferOrderQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsTransferOrderQuery", q)
}

// The TraverseWmsTransferOrder type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsTransferOrder func(context.Context, *ent.WmsTransferOrderQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsTransferOrder) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsTransferOrder) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsTransferOrderQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsTransferOrderQuery", q)
}

// The WmsTransferOrderDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsTransferOrderDetailFunc func(context.Context, *ent.WmsTransferOrderDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsTransferOrderDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsTransferOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsTransferOrderDetailQuery", q)
}

// The TraverseWmsTransferOrderDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsTransferOrderDetail func(context.Context, *ent.WmsTransferOrderDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsTransferOrderDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsTransferOrderDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsTransferOrderDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsTransferOrderDetailQuery", q)
}

// The WmsVehicleRepairOrderFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsVehicleRepairOrderFunc func(context.Context, *ent.WmsVehicleRepairOrderQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsVehicleRepairOrderFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsVehicleRepairOrderQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsVehicleRepairOrderQuery", q)
}

// The TraverseWmsVehicleRepairOrder type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsVehicleRepairOrder func(context.Context, *ent.WmsVehicleRepairOrderQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsVehicleRepairOrder) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsVehicleRepairOrder) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsVehicleRepairOrderQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsVehicleRepairOrderQuery", q)
}

// The WmsVehicleRepairOrderMaterialfeeDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsVehicleRepairOrderMaterialfeeDetailFunc func(context.Context, *ent.WmsVehicleRepairOrderMaterialfeeDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsVehicleRepairOrderMaterialfeeDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsVehicleRepairOrderMaterialfeeDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsVehicleRepairOrderMaterialfeeDetailQuery", q)
}

// The TraverseWmsVehicleRepairOrderMaterialfeeDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsVehicleRepairOrderMaterialfeeDetail func(context.Context, *ent.WmsVehicleRepairOrderMaterialfeeDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsVehicleRepairOrderMaterialfeeDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsVehicleRepairOrderMaterialfeeDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsVehicleRepairOrderMaterialfeeDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsVehicleRepairOrderMaterialfeeDetailQuery", q)
}

// The WmsVehicleRepairOrderSettlementfeeDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsVehicleRepairOrderSettlementfeeDetailFunc func(context.Context, *ent.WmsVehicleRepairOrderSettlementfeeDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsVehicleRepairOrderSettlementfeeDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsVehicleRepairOrderSettlementfeeDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsVehicleRepairOrderSettlementfeeDetailQuery", q)
}

// The TraverseWmsVehicleRepairOrderSettlementfeeDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsVehicleRepairOrderSettlementfeeDetail func(context.Context, *ent.WmsVehicleRepairOrderSettlementfeeDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsVehicleRepairOrderSettlementfeeDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsVehicleRepairOrderSettlementfeeDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsVehicleRepairOrderSettlementfeeDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsVehicleRepairOrderSettlementfeeDetailQuery", q)
}

// The WmsVehicleRepairOrderWorkfeeDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type WmsVehicleRepairOrderWorkfeeDetailFunc func(context.Context, *ent.WmsVehicleRepairOrderWorkfeeDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WmsVehicleRepairOrderWorkfeeDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WmsVehicleRepairOrderWorkfeeDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WmsVehicleRepairOrderWorkfeeDetailQuery", q)
}

// The TraverseWmsVehicleRepairOrderWorkfeeDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWmsVehicleRepairOrderWorkfeeDetail func(context.Context, *ent.WmsVehicleRepairOrderWorkfeeDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWmsVehicleRepairOrderWorkfeeDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWmsVehicleRepairOrderWorkfeeDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WmsVehicleRepairOrderWorkfeeDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WmsVehicleRepairOrderWorkfeeDetailQuery", q)
}

// The WorkWxApprovalMessageFunc type is an adapter to allow the use of ordinary function as a Querier.
type WorkWxApprovalMessageFunc func(context.Context, *ent.WorkWxApprovalMessageQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WorkWxApprovalMessageFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WorkWxApprovalMessageQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WorkWxApprovalMessageQuery", q)
}

// The TraverseWorkWxApprovalMessage type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWorkWxApprovalMessage func(context.Context, *ent.WorkWxApprovalMessageQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWorkWxApprovalMessage) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWorkWxApprovalMessage) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WorkWxApprovalMessageQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WorkWxApprovalMessageQuery", q)
}

// The WorkWxApprovalNodeFunc type is an adapter to allow the use of ordinary function as a Querier.
type WorkWxApprovalNodeFunc func(context.Context, *ent.WorkWxApprovalNodeQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WorkWxApprovalNodeFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WorkWxApprovalNodeQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WorkWxApprovalNodeQuery", q)
}

// The TraverseWorkWxApprovalNode type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWorkWxApprovalNode func(context.Context, *ent.WorkWxApprovalNodeQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWorkWxApprovalNode) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWorkWxApprovalNode) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WorkWxApprovalNodeQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WorkWxApprovalNodeQuery", q)
}

// The WorkWxNotifyNodeFunc type is an adapter to allow the use of ordinary function as a Querier.
type WorkWxNotifyNodeFunc func(context.Context, *ent.WorkWxNotifyNodeQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f WorkWxNotifyNodeFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.WorkWxNotifyNodeQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.WorkWxNotifyNodeQuery", q)
}

// The TraverseWorkWxNotifyNode type is an adapter to allow the use of ordinary function as Traverser.
type TraverseWorkWxNotifyNode func(context.Context, *ent.WorkWxNotifyNodeQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseWorkWxNotifyNode) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseWorkWxNotifyNode) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WorkWxNotifyNodeQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.WorkWxNotifyNodeQuery", q)
}

// NewQuery returns the generic Query interface for the given typed query.
func NewQuery(q ent.Query) (Query, error) {
	switch q := q.(type) {
	case *ent.AppApproveActivityQuery:
		return &query[*ent.AppApproveActivityQuery, predicate.AppApproveActivity, appapproveactivity.OrderOption]{typ: ent.TypeAppApproveActivity, tq: q}, nil
	case *ent.AppApproveBasicQuery:
		return &query[*ent.AppApproveBasicQuery, predicate.AppApproveBasic, appapprovebasic.OrderOption]{typ: ent.TypeAppApproveBasic, tq: q}, nil
	case *ent.AppApproveGroupQuery:
		return &query[*ent.AppApproveGroupQuery, predicate.AppApproveGroup, appapprovegroup.OrderOption]{typ: ent.TypeAppApproveGroup, tq: q}, nil
	case *ent.AppApproveRecordQuery:
		return &query[*ent.AppApproveRecordQuery, predicate.AppApproveRecord, appapproverecord.OrderOption]{typ: ent.TypeAppApproveRecord, tq: q}, nil
	case *ent.AppApproveRecordLogQuery:
		return &query[*ent.AppApproveRecordLogQuery, predicate.AppApproveRecordLog, appapproverecordlog.OrderOption]{typ: ent.TypeAppApproveRecordLog, tq: q}, nil
	case *ent.AppApproveRecordQueueQuery:
		return &query[*ent.AppApproveRecordQueueQuery, predicate.AppApproveRecordQueue, appapproverecordqueue.OrderOption]{typ: ent.TypeAppApproveRecordQueue, tq: q}, nil
	case *ent.AppApproveWorkflowQuery:
		return &query[*ent.AppApproveWorkflowQuery, predicate.AppApproveWorkflow, appapproveworkflow.OrderOption]{typ: ent.TypeAppApproveWorkflow, tq: q}, nil
	case *ent.DemoBuildingQuery:
		return &query[*ent.DemoBuildingQuery, predicate.DemoBuilding, demobuilding.OrderOption]{typ: ent.TypeDemoBuilding, tq: q}, nil
	case *ent.SysApplicationQuery:
		return &query[*ent.SysApplicationQuery, predicate.SysApplication, sysapplication.OrderOption]{typ: ent.TypeSysApplication, tq: q}, nil
	case *ent.SysApplicationModuleQuery:
		return &query[*ent.SysApplicationModuleQuery, predicate.SysApplicationModule, sysapplicationmodule.OrderOption]{typ: ent.TypeSysApplicationModule, tq: q}, nil
	case *ent.SysApplicationModuleResourceQuery:
		return &query[*ent.SysApplicationModuleResourceQuery, predicate.SysApplicationModuleResource, sysapplicationmoduleresource.OrderOption]{typ: ent.TypeSysApplicationModuleResource, tq: q}, nil
	case *ent.SysAreaQuery:
		return &query[*ent.SysAreaQuery, predicate.SysArea, sysarea.OrderOption]{typ: ent.TypeSysArea, tq: q}, nil
	case *ent.SysCityQuery:
		return &query[*ent.SysCityQuery, predicate.SysCity, syscity.OrderOption]{typ: ent.TypeSysCity, tq: q}, nil
	case *ent.SysConfigQuery:
		return &query[*ent.SysConfigQuery, predicate.SysConfig, sysconfig.OrderOption]{typ: ent.TypeSysConfig, tq: q}, nil
	case *ent.SysDictionaryQuery:
		return &query[*ent.SysDictionaryQuery, predicate.SysDictionary, sysdictionary.OrderOption]{typ: ent.TypeSysDictionary, tq: q}, nil
	case *ent.SysDictionaryDetailQuery:
		return &query[*ent.SysDictionaryDetailQuery, predicate.SysDictionaryDetail, sysdictionarydetail.OrderOption]{typ: ent.TypeSysDictionaryDetail, tq: q}, nil
	case *ent.SysGameQuery:
		return &query[*ent.SysGameQuery, predicate.SysGame, sysgame.OrderOption]{typ: ent.TypeSysGame, tq: q}, nil
	case *ent.SysLabelQuery:
		return &query[*ent.SysLabelQuery, predicate.SysLabel, syslabel.OrderOption]{typ: ent.TypeSysLabel, tq: q}, nil
	case *ent.SysLogQuery:
		return &query[*ent.SysLogQuery, predicate.SysLog, syslog.OrderOption]{typ: ent.TypeSysLog, tq: q}, nil
	case *ent.SysMenuQuery:
		return &query[*ent.SysMenuQuery, predicate.SysMenu, sysmenu.OrderOption]{typ: ent.TypeSysMenu, tq: q}, nil
	case *ent.SysMessageTemplateQuery:
		return &query[*ent.SysMessageTemplateQuery, predicate.SysMessageTemplate, sysmessagetemplate.OrderOption]{typ: ent.TypeSysMessageTemplate, tq: q}, nil
	case *ent.SysModuleQuery:
		return &query[*ent.SysModuleQuery, predicate.SysModule, sysmodule.OrderOption]{typ: ent.TypeSysModule, tq: q}, nil
	case *ent.SysModuleResourceQuery:
		return &query[*ent.SysModuleResourceQuery, predicate.SysModuleResource, sysmoduleresource.OrderOption]{typ: ent.TypeSysModuleResource, tq: q}, nil
	case *ent.SysOrganizationQuery:
		return &query[*ent.SysOrganizationQuery, predicate.SysOrganization, sysorganization.OrderOption]{typ: ent.TypeSysOrganization, tq: q}, nil
	case *ent.SysPageCodeQuery:
		return &query[*ent.SysPageCodeQuery, predicate.SysPageCode, syspagecode.OrderOption]{typ: ent.TypeSysPageCode, tq: q}, nil
	case *ent.SysPageCodeHistoryQuery:
		return &query[*ent.SysPageCodeHistoryQuery, predicate.SysPageCodeHistory, syspagecodehistory.OrderOption]{typ: ent.TypeSysPageCodeHistory, tq: q}, nil
	case *ent.SysProjectQuery:
		return &query[*ent.SysProjectQuery, predicate.SysProject, sysproject.OrderOption]{typ: ent.TypeSysProject, tq: q}, nil
	case *ent.SysProvinceQuery:
		return &query[*ent.SysProvinceQuery, predicate.SysProvince, sysprovince.OrderOption]{typ: ent.TypeSysProvince, tq: q}, nil
	case *ent.SysResourceQuery:
		return &query[*ent.SysResourceQuery, predicate.SysResource, sysresource.OrderOption]{typ: ent.TypeSysResource, tq: q}, nil
	case *ent.SysRoleQuery:
		return &query[*ent.SysRoleQuery, predicate.SysRole, sysrole.OrderOption]{typ: ent.TypeSysRole, tq: q}, nil
	case *ent.SysRoleMenuQuery:
		return &query[*ent.SysRoleMenuQuery, predicate.SysRoleMenu, sysrolemenu.OrderOption]{typ: ent.TypeSysRoleMenu, tq: q}, nil
	case *ent.SysRoleResourceQuery:
		return &query[*ent.SysRoleResourceQuery, predicate.SysRoleResource, sysroleresource.OrderOption]{typ: ent.TypeSysRoleResource, tq: q}, nil
	case *ent.SysStreetQuery:
		return &query[*ent.SysStreetQuery, predicate.SysStreet, sysstreet.OrderOption]{typ: ent.TypeSysStreet, tq: q}, nil
	case *ent.SysTeamQuery:
		return &query[*ent.SysTeamQuery, predicate.SysTeam, systeam.OrderOption]{typ: ent.TypeSysTeam, tq: q}, nil
	case *ent.SysTeamApplicationQuery:
		return &query[*ent.SysTeamApplicationQuery, predicate.SysTeamApplication, systeamapplication.OrderOption]{typ: ent.TypeSysTeamApplication, tq: q}, nil
	case *ent.SysUploadQuery:
		return &query[*ent.SysUploadQuery, predicate.SysUpload, sysupload.OrderOption]{typ: ent.TypeSysUpload, tq: q}, nil
	case *ent.SysUserQuery:
		return &query[*ent.SysUserQuery, predicate.SysUser, sysuser.OrderOption]{typ: ent.TypeSysUser, tq: q}, nil
	case *ent.SysUserAuthQuery:
		return &query[*ent.SysUserAuthQuery, predicate.SysUserAuth, sysuserauth.OrderOption]{typ: ent.TypeSysUserAuth, tq: q}, nil
	case *ent.SysUserLogQuery:
		return &query[*ent.SysUserLogQuery, predicate.SysUserLog, sysuserlog.OrderOption]{typ: ent.TypeSysUserLog, tq: q}, nil
	case *ent.SysUserOrganizationQuery:
		return &query[*ent.SysUserOrganizationQuery, predicate.SysUserOrganization, sysuserorganization.OrderOption]{typ: ent.TypeSysUserOrganization, tq: q}, nil
	case *ent.SysUserRoleQuery:
		return &query[*ent.SysUserRoleQuery, predicate.SysUserRole, sysuserrole.OrderOption]{typ: ent.TypeSysUserRole, tq: q}, nil
	case *ent.WmsAccessDoorLogQuery:
		return &query[*ent.WmsAccessDoorLogQuery, predicate.WmsAccessDoorLog, wmsaccessdoorlog.OrderOption]{typ: ent.TypeWmsAccessDoorLog, tq: q}, nil
	case *ent.WmsApprovalTaskQuery:
		return &query[*ent.WmsApprovalTaskQuery, predicate.WmsApprovalTask, wmsapprovaltask.OrderOption]{typ: ent.TypeWmsApprovalTask, tq: q}, nil
	case *ent.WmsApprovalTaskDetailQuery:
		return &query[*ent.WmsApprovalTaskDetailQuery, predicate.WmsApprovalTaskDetail, wmsapprovaltaskdetail.OrderOption]{typ: ent.TypeWmsApprovalTaskDetail, tq: q}, nil
	case *ent.WmsApprovalTaskOperationQuery:
		return &query[*ent.WmsApprovalTaskOperationQuery, predicate.WmsApprovalTaskOperation, wmsapprovaltaskoperation.OrderOption]{typ: ent.TypeWmsApprovalTaskOperation, tq: q}, nil
	case *ent.WmsApprovalTaskRemarkQuery:
		return &query[*ent.WmsApprovalTaskRemarkQuery, predicate.WmsApprovalTaskRemark, wmsapprovaltaskremark.OrderOption]{typ: ent.TypeWmsApprovalTaskRemark, tq: q}, nil
	case *ent.WmsAuditPlanQuery:
		return &query[*ent.WmsAuditPlanQuery, predicate.WmsAuditPlan, wmsauditplan.OrderOption]{typ: ent.TypeWmsAuditPlan, tq: q}, nil
	case *ent.WmsAuditPlanDetailQuery:
		return &query[*ent.WmsAuditPlanDetailQuery, predicate.WmsAuditPlanDetail, wmsauditplandetail.OrderOption]{typ: ent.TypeWmsAuditPlanDetail, tq: q}, nil
	case *ent.WmsBorrowOrderQuery:
		return &query[*ent.WmsBorrowOrderQuery, predicate.WmsBorrowOrder, wmsborroworder.OrderOption]{typ: ent.TypeWmsBorrowOrder, tq: q}, nil
	case *ent.WmsBorrowOrderDetailQuery:
		return &query[*ent.WmsBorrowOrderDetailQuery, predicate.WmsBorrowOrderDetail, wmsborroworderdetail.OrderOption]{typ: ent.TypeWmsBorrowOrderDetail, tq: q}, nil
	case *ent.WmsCarQuery:
		return &query[*ent.WmsCarQuery, predicate.WmsCar, wmscar.OrderOption]{typ: ent.TypeWmsCar, tq: q}, nil
	case *ent.WmsClaimOrderQuery:
		return &query[*ent.WmsClaimOrderQuery, predicate.WmsClaimOrder, wmsclaimorder.OrderOption]{typ: ent.TypeWmsClaimOrder, tq: q}, nil
	case *ent.WmsClaimOrderDetailQuery:
		return &query[*ent.WmsClaimOrderDetailQuery, predicate.WmsClaimOrderDetail, wmsclaimorderdetail.OrderOption]{typ: ent.TypeWmsClaimOrderDetail, tq: q}, nil
	case *ent.WmsCompanyQuery:
		return &query[*ent.WmsCompanyQuery, predicate.WmsCompany, wmscompany.OrderOption]{typ: ent.TypeWmsCompany, tq: q}, nil
	case *ent.WmsCompanyAddressQuery:
		return &query[*ent.WmsCompanyAddressQuery, predicate.WmsCompanyAddress, wmscompanyaddress.OrderOption]{typ: ent.TypeWmsCompanyAddress, tq: q}, nil
	case *ent.WmsDiscardMeetingQuery:
		return &query[*ent.WmsDiscardMeetingQuery, predicate.WmsDiscardMeeting, wmsdiscardmeeting.OrderOption]{typ: ent.TypeWmsDiscardMeeting, tq: q}, nil
	case *ent.WmsDiscardMeetingDetailQuery:
		return &query[*ent.WmsDiscardMeetingDetailQuery, predicate.WmsDiscardMeetingDetail, wmsdiscardmeetingdetail.OrderOption]{typ: ent.TypeWmsDiscardMeetingDetail, tq: q}, nil
	case *ent.WmsDiscardOrderQuery:
		return &query[*ent.WmsDiscardOrderQuery, predicate.WmsDiscardOrder, wmsdiscardorder.OrderOption]{typ: ent.TypeWmsDiscardOrder, tq: q}, nil
	case *ent.WmsDiscardOrderDetailQuery:
		return &query[*ent.WmsDiscardOrderDetailQuery, predicate.WmsDiscardOrderDetail, wmsdiscardorderdetail.OrderOption]{typ: ent.TypeWmsDiscardOrderDetail, tq: q}, nil
	case *ent.WmsDiscardPlanOrderQuery:
		return &query[*ent.WmsDiscardPlanOrderQuery, predicate.WmsDiscardPlanOrder, wmsdiscardplanorder.OrderOption]{typ: ent.TypeWmsDiscardPlanOrder, tq: q}, nil
	case *ent.WmsDiscardPlanOrderDetailQuery:
		return &query[*ent.WmsDiscardPlanOrderDetailQuery, predicate.WmsDiscardPlanOrderDetail, wmsdiscardplanorderdetail.OrderOption]{typ: ent.TypeWmsDiscardPlanOrderDetail, tq: q}, nil
	case *ent.WmsDocumentQuery:
		return &query[*ent.WmsDocumentQuery, predicate.WmsDocument, wmsdocument.OrderOption]{typ: ent.TypeWmsDocument, tq: q}, nil
	case *ent.WmsEnterRepositoryOrderQuery:
		return &query[*ent.WmsEnterRepositoryOrderQuery, predicate.WmsEnterRepositoryOrder, wmsenterrepositoryorder.OrderOption]{typ: ent.TypeWmsEnterRepositoryOrder, tq: q}, nil
	case *ent.WmsEnterRepositoryOrderDetailQuery:
		return &query[*ent.WmsEnterRepositoryOrderDetailQuery, predicate.WmsEnterRepositoryOrderDetail, wmsenterrepositoryorderdetail.OrderOption]{typ: ent.TypeWmsEnterRepositoryOrderDetail, tq: q}, nil
	case *ent.WmsEquipmentQuery:
		return &query[*ent.WmsEquipmentQuery, predicate.WmsEquipment, wmsequipment.OrderOption]{typ: ent.TypeWmsEquipment, tq: q}, nil
	case *ent.WmsEquipmentDetailQuery:
		return &query[*ent.WmsEquipmentDetailQuery, predicate.WmsEquipmentDetail, wmsequipmentdetail.OrderOption]{typ: ent.TypeWmsEquipmentDetail, tq: q}, nil
	case *ent.WmsEquipmentTypeQuery:
		return &query[*ent.WmsEquipmentTypeQuery, predicate.WmsEquipmentType, wmsequipmenttype.OrderOption]{typ: ent.TypeWmsEquipmentType, tq: q}, nil
	case *ent.WmsEquipmentTypePropertyQuery:
		return &query[*ent.WmsEquipmentTypePropertyQuery, predicate.WmsEquipmentTypeProperty, wmsequipmenttypeproperty.OrderOption]{typ: ent.TypeWmsEquipmentTypeProperty, tq: q}, nil
	case *ent.WmsEquipmentTypePropertyGroupQuery:
		return &query[*ent.WmsEquipmentTypePropertyGroupQuery, predicate.WmsEquipmentTypePropertyGroup, wmsequipmenttypepropertygroup.OrderOption]{typ: ent.TypeWmsEquipmentTypePropertyGroup, tq: q}, nil
	case *ent.WmsEquipmentTypePropertyOptionQuery:
		return &query[*ent.WmsEquipmentTypePropertyOptionQuery, predicate.WmsEquipmentTypePropertyOption, wmsequipmenttypepropertyoption.OrderOption]{typ: ent.TypeWmsEquipmentTypePropertyOption, tq: q}, nil
	case *ent.WmsEquipmentUserGroupQuery:
		return &query[*ent.WmsEquipmentUserGroupQuery, predicate.WmsEquipmentUserGroup, wmsequipmentusergroup.OrderOption]{typ: ent.TypeWmsEquipmentUserGroup, tq: q}, nil
	case *ent.WmsFireStationQuery:
		return &query[*ent.WmsFireStationQuery, predicate.WmsFireStation, wmsfirestation.OrderOption]{typ: ent.TypeWmsFireStation, tq: q}, nil
	case *ent.WmsGPSQuery:
		return &query[*ent.WmsGPSQuery, predicate.WmsGPS, wmsgps.OrderOption]{typ: ent.TypeWmsGPS, tq: q}, nil
	case *ent.WmsGPSRecordQuery:
		return &query[*ent.WmsGPSRecordQuery, predicate.WmsGPSRecord, wmsgpsrecord.OrderOption]{typ: ent.TypeWmsGPSRecord, tq: q}, nil
	case *ent.WmsLearningCourseQuery:
		return &query[*ent.WmsLearningCourseQuery, predicate.WmsLearningCourse, wmslearningcourse.OrderOption]{typ: ent.TypeWmsLearningCourse, tq: q}, nil
	case *ent.WmsLearningCourseCoursewareQuery:
		return &query[*ent.WmsLearningCourseCoursewareQuery, predicate.WmsLearningCourseCourseware, wmslearningcoursecourseware.OrderOption]{typ: ent.TypeWmsLearningCourseCourseware, tq: q}, nil
	case *ent.WmsLearningCourseLogQuery:
		return &query[*ent.WmsLearningCourseLogQuery, predicate.WmsLearningCourseLog, wmslearningcourselog.OrderOption]{typ: ent.TypeWmsLearningCourseLog, tq: q}, nil
	case *ent.WmsLearningCourseRecordQuery:
		return &query[*ent.WmsLearningCourseRecordQuery, predicate.WmsLearningCourseRecord, wmslearningcourserecord.OrderOption]{typ: ent.TypeWmsLearningCourseRecord, tq: q}, nil
	case *ent.WmsLearningCoursewareQuery:
		return &query[*ent.WmsLearningCoursewareQuery, predicate.WmsLearningCourseware, wmslearningcourseware.OrderOption]{typ: ent.TypeWmsLearningCourseware, tq: q}, nil
	case *ent.WmsLearningCoursewareRecordQuery:
		return &query[*ent.WmsLearningCoursewareRecordQuery, predicate.WmsLearningCoursewareRecord, wmslearningcoursewarerecord.OrderOption]{typ: ent.TypeWmsLearningCoursewareRecord, tq: q}, nil
	case *ent.WmsLearningPlanQuery:
		return &query[*ent.WmsLearningPlanQuery, predicate.WmsLearningPlan, wmslearningplan.OrderOption]{typ: ent.TypeWmsLearningPlan, tq: q}, nil
	case *ent.WmsLearningPlanRecordQuery:
		return &query[*ent.WmsLearningPlanRecordQuery, predicate.WmsLearningPlanRecord, wmslearningplanrecord.OrderOption]{typ: ent.TypeWmsLearningPlanRecord, tq: q}, nil
	case *ent.WmsMaintainOrderQuery:
		return &query[*ent.WmsMaintainOrderQuery, predicate.WmsMaintainOrder, wmsmaintainorder.OrderOption]{typ: ent.TypeWmsMaintainOrder, tq: q}, nil
	case *ent.WmsMaintainOrderDetailQuery:
		return &query[*ent.WmsMaintainOrderDetailQuery, predicate.WmsMaintainOrderDetail, wmsmaintainorderdetail.OrderOption]{typ: ent.TypeWmsMaintainOrderDetail, tq: q}, nil
	case *ent.WmsMaintainPlanQuery:
		return &query[*ent.WmsMaintainPlanQuery, predicate.WmsMaintainPlan, wmsmaintainplan.OrderOption]{typ: ent.TypeWmsMaintainPlan, tq: q}, nil
	case *ent.WmsMaintainPlanDetailQuery:
		return &query[*ent.WmsMaintainPlanDetailQuery, predicate.WmsMaintainPlanDetail, wmsmaintainplandetail.OrderOption]{typ: ent.TypeWmsMaintainPlanDetail, tq: q}, nil
	case *ent.WmsMaterialQuery:
		return &query[*ent.WmsMaterialQuery, predicate.WmsMaterial, wmsmaterial.OrderOption]{typ: ent.TypeWmsMaterial, tq: q}, nil
	case *ent.WmsMaterialLogQuery:
		return &query[*ent.WmsMaterialLogQuery, predicate.WmsMaterialLog, wmsmateriallog.OrderOption]{typ: ent.TypeWmsMaterialLog, tq: q}, nil
	case *ent.WmsMeasureUnitQuery:
		return &query[*ent.WmsMeasureUnitQuery, predicate.WmsMeasureUnit, wmsmeasureunit.OrderOption]{typ: ent.TypeWmsMeasureUnit, tq: q}, nil
	case *ent.WmsOperateLogQuery:
		return &query[*ent.WmsOperateLogQuery, predicate.WmsOperateLog, wmsoperatelog.OrderOption]{typ: ent.TypeWmsOperateLog, tq: q}, nil
	case *ent.WmsOutRepositoryOrderQuery:
		return &query[*ent.WmsOutRepositoryOrderQuery, predicate.WmsOutRepositoryOrder, wmsoutrepositoryorder.OrderOption]{typ: ent.TypeWmsOutRepositoryOrder, tq: q}, nil
	case *ent.WmsOutRepositoryOrderDetailQuery:
		return &query[*ent.WmsOutRepositoryOrderDetailQuery, predicate.WmsOutRepositoryOrderDetail, wmsoutrepositoryorderdetail.OrderOption]{typ: ent.TypeWmsOutRepositoryOrderDetail, tq: q}, nil
	case *ent.WmsPurchaseOrderQuery:
		return &query[*ent.WmsPurchaseOrderQuery, predicate.WmsPurchaseOrder, wmspurchaseorder.OrderOption]{typ: ent.TypeWmsPurchaseOrder, tq: q}, nil
	case *ent.WmsPurchaseOrderDetailQuery:
		return &query[*ent.WmsPurchaseOrderDetailQuery, predicate.WmsPurchaseOrderDetail, wmspurchaseorderdetail.OrderOption]{typ: ent.TypeWmsPurchaseOrderDetail, tq: q}, nil
	case *ent.WmsRepairOrderQuery:
		return &query[*ent.WmsRepairOrderQuery, predicate.WmsRepairOrder, wmsrepairorder.OrderOption]{typ: ent.TypeWmsRepairOrder, tq: q}, nil
	case *ent.WmsRepairOrderDetailQuery:
		return &query[*ent.WmsRepairOrderDetailQuery, predicate.WmsRepairOrderDetail, wmsrepairorderdetail.OrderOption]{typ: ent.TypeWmsRepairOrderDetail, tq: q}, nil
	case *ent.WmsRepairSettlementOrderQuery:
		return &query[*ent.WmsRepairSettlementOrderQuery, predicate.WmsRepairSettlementOrder, wmsrepairsettlementorder.OrderOption]{typ: ent.TypeWmsRepairSettlementOrder, tq: q}, nil
	case *ent.WmsRepairSettlementOrderSettlementfeeDetailQuery:
		return &query[*ent.WmsRepairSettlementOrderSettlementfeeDetailQuery, predicate.WmsRepairSettlementOrderSettlementfeeDetail, wmsrepairsettlementordersettlementfeedetail.OrderOption]{typ: ent.TypeWmsRepairSettlementOrderSettlementfeeDetail, tq: q}, nil
	case *ent.WmsRepairSettlementOrderWorkfeeDetailQuery:
		return &query[*ent.WmsRepairSettlementOrderWorkfeeDetailQuery, predicate.WmsRepairSettlementOrderWorkfeeDetail, wmsrepairsettlementorderworkfeedetail.OrderOption]{typ: ent.TypeWmsRepairSettlementOrderWorkfeeDetail, tq: q}, nil
	case *ent.WmsRepositoryQuery:
		return &query[*ent.WmsRepositoryQuery, predicate.WmsRepository, wmsrepository.OrderOption]{typ: ent.TypeWmsRepository, tq: q}, nil
	case *ent.WmsRepositoryAdminQuery:
		return &query[*ent.WmsRepositoryAdminQuery, predicate.WmsRepositoryAdmin, wmsrepositoryadmin.OrderOption]{typ: ent.TypeWmsRepositoryAdmin, tq: q}, nil
	case *ent.WmsRepositoryAreaQuery:
		return &query[*ent.WmsRepositoryAreaQuery, predicate.WmsRepositoryArea, wmsrepositoryarea.OrderOption]{typ: ent.TypeWmsRepositoryArea, tq: q}, nil
	case *ent.WmsRepositoryPositionQuery:
		return &query[*ent.WmsRepositoryPositionQuery, predicate.WmsRepositoryPosition, wmsrepositoryposition.OrderOption]{typ: ent.TypeWmsRepositoryPosition, tq: q}, nil
	case *ent.WmsRepositoryScreenQuery:
		return &query[*ent.WmsRepositoryScreenQuery, predicate.WmsRepositoryScreen, wmsrepositoryscreen.OrderOption]{typ: ent.TypeWmsRepositoryScreen, tq: q}, nil
	case *ent.WmsRepositoryScreenRepositoryAreaQuery:
		return &query[*ent.WmsRepositoryScreenRepositoryAreaQuery, predicate.WmsRepositoryScreenRepositoryArea, wmsrepositoryscreenrepositoryarea.OrderOption]{typ: ent.TypeWmsRepositoryScreenRepositoryArea, tq: q}, nil
	case *ent.WmsReturnOrderQuery:
		return &query[*ent.WmsReturnOrderQuery, predicate.WmsReturnOrder, wmsreturnorder.OrderOption]{typ: ent.TypeWmsReturnOrder, tq: q}, nil
	case *ent.WmsReturnOrderDetailQuery:
		return &query[*ent.WmsReturnOrderDetailQuery, predicate.WmsReturnOrderDetail, wmsreturnorderdetail.OrderOption]{typ: ent.TypeWmsReturnOrderDetail, tq: q}, nil
	case *ent.WmsRfidQuery:
		return &query[*ent.WmsRfidQuery, predicate.WmsRfid, wmsrfid.OrderOption]{typ: ent.TypeWmsRfid, tq: q}, nil
	case *ent.WmsRfidReaderQuery:
		return &query[*ent.WmsRfidReaderQuery, predicate.WmsRfidReader, wmsrfidreader.OrderOption]{typ: ent.TypeWmsRfidReader, tq: q}, nil
	case *ent.WmsRfidReaderRecordQuery:
		return &query[*ent.WmsRfidReaderRecordQuery, predicate.WmsRfidReaderRecord, wmsrfidreaderrecord.OrderOption]{typ: ent.TypeWmsRfidReaderRecord, tq: q}, nil
	case *ent.WmsTransferOrderQuery:
		return &query[*ent.WmsTransferOrderQuery, predicate.WmsTransferOrder, wmstransferorder.OrderOption]{typ: ent.TypeWmsTransferOrder, tq: q}, nil
	case *ent.WmsTransferOrderDetailQuery:
		return &query[*ent.WmsTransferOrderDetailQuery, predicate.WmsTransferOrderDetail, wmstransferorderdetail.OrderOption]{typ: ent.TypeWmsTransferOrderDetail, tq: q}, nil
	case *ent.WmsVehicleRepairOrderQuery:
		return &query[*ent.WmsVehicleRepairOrderQuery, predicate.WmsVehicleRepairOrder, wmsvehiclerepairorder.OrderOption]{typ: ent.TypeWmsVehicleRepairOrder, tq: q}, nil
	case *ent.WmsVehicleRepairOrderMaterialfeeDetailQuery:
		return &query[*ent.WmsVehicleRepairOrderMaterialfeeDetailQuery, predicate.WmsVehicleRepairOrderMaterialfeeDetail, wmsvehiclerepairordermaterialfeedetail.OrderOption]{typ: ent.TypeWmsVehicleRepairOrderMaterialfeeDetail, tq: q}, nil
	case *ent.WmsVehicleRepairOrderSettlementfeeDetailQuery:
		return &query[*ent.WmsVehicleRepairOrderSettlementfeeDetailQuery, predicate.WmsVehicleRepairOrderSettlementfeeDetail, wmsvehiclerepairordersettlementfeedetail.OrderOption]{typ: ent.TypeWmsVehicleRepairOrderSettlementfeeDetail, tq: q}, nil
	case *ent.WmsVehicleRepairOrderWorkfeeDetailQuery:
		return &query[*ent.WmsVehicleRepairOrderWorkfeeDetailQuery, predicate.WmsVehicleRepairOrderWorkfeeDetail, wmsvehiclerepairorderworkfeedetail.OrderOption]{typ: ent.TypeWmsVehicleRepairOrderWorkfeeDetail, tq: q}, nil
	case *ent.WorkWxApprovalMessageQuery:
		return &query[*ent.WorkWxApprovalMessageQuery, predicate.WorkWxApprovalMessage, workwxapprovalmessage.OrderOption]{typ: ent.TypeWorkWxApprovalMessage, tq: q}, nil
	case *ent.WorkWxApprovalNodeQuery:
		return &query[*ent.WorkWxApprovalNodeQuery, predicate.WorkWxApprovalNode, workwxapprovalnode.OrderOption]{typ: ent.TypeWorkWxApprovalNode, tq: q}, nil
	case *ent.WorkWxNotifyNodeQuery:
		return &query[*ent.WorkWxNotifyNodeQuery, predicate.WorkWxNotifyNode, workwxnotifynode.OrderOption]{typ: ent.TypeWorkWxNotifyNode, tq: q}, nil
	default:
		return nil, fmt.Errorf("unknown query type %T", q)
	}
}

type query[T any, P ~func(*sql.Selector), R ~func(*sql.Selector)] struct {
	typ string
	tq  interface {
		Limit(int) T
		Offset(int) T
		Unique(bool) T
		Order(...R) T
		Where(...P) T
	}
}

func (q query[T, P, R]) Type() string {
	return q.typ
}

func (q query[T, P, R]) Limit(limit int) {
	q.tq.Limit(limit)
}

func (q query[T, P, R]) Offset(offset int) {
	q.tq.Offset(offset)
}

func (q query[T, P, R]) Unique(unique bool) {
	q.tq.Unique(unique)
}

func (q query[T, P, R]) Order(orders ...func(*sql.Selector)) {
	rs := make([]R, len(orders))
	for i := range orders {
		rs[i] = orders[i]
	}
	q.tq.Order(rs...)
}

func (q query[T, P, R]) WhereP(ps ...func(*sql.Selector)) {
	p := make([]P, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	q.tq.Where(p...)
}
