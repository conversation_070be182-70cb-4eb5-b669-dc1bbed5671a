// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"kratos-mono-demo/internal/data/ent/predicate"
	"kratos-mono-demo/internal/data/ent/wmsreturnorderdetail"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WmsReturnOrderDetailQuery is the builder for querying WmsReturnOrderDetail entities.
type WmsReturnOrderDetailQuery struct {
	config
	ctx        *QueryContext
	order      []wmsreturnorderdetail.OrderOption
	inters     []Interceptor
	predicates []predicate.WmsReturnOrderDetail
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the WmsReturnOrderDetailQuery builder.
func (wrodq *WmsReturnOrderDetailQuery) Where(ps ...predicate.WmsReturnOrderDetail) *WmsReturnOrderDetailQuery {
	wrodq.predicates = append(wrodq.predicates, ps...)
	return wrodq
}

// Limit the number of records to be returned by this query.
func (wrodq *WmsReturnOrderDetailQuery) Limit(limit int) *WmsReturnOrderDetailQuery {
	wrodq.ctx.Limit = &limit
	return wrodq
}

// Offset to start from.
func (wrodq *WmsReturnOrderDetailQuery) Offset(offset int) *WmsReturnOrderDetailQuery {
	wrodq.ctx.Offset = &offset
	return wrodq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (wrodq *WmsReturnOrderDetailQuery) Unique(unique bool) *WmsReturnOrderDetailQuery {
	wrodq.ctx.Unique = &unique
	return wrodq
}

// Order specifies how the records should be ordered.
func (wrodq *WmsReturnOrderDetailQuery) Order(o ...wmsreturnorderdetail.OrderOption) *WmsReturnOrderDetailQuery {
	wrodq.order = append(wrodq.order, o...)
	return wrodq
}

// First returns the first WmsReturnOrderDetail entity from the query.
// Returns a *NotFoundError when no WmsReturnOrderDetail was found.
func (wrodq *WmsReturnOrderDetailQuery) First(ctx context.Context) (*WmsReturnOrderDetail, error) {
	nodes, err := wrodq.Limit(1).All(setContextOp(ctx, wrodq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{wmsreturnorderdetail.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (wrodq *WmsReturnOrderDetailQuery) FirstX(ctx context.Context) *WmsReturnOrderDetail {
	node, err := wrodq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first WmsReturnOrderDetail ID from the query.
// Returns a *NotFoundError when no WmsReturnOrderDetail ID was found.
func (wrodq *WmsReturnOrderDetailQuery) FirstID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = wrodq.Limit(1).IDs(setContextOp(ctx, wrodq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{wmsreturnorderdetail.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (wrodq *WmsReturnOrderDetailQuery) FirstIDX(ctx context.Context) string {
	id, err := wrodq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single WmsReturnOrderDetail entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one WmsReturnOrderDetail entity is found.
// Returns a *NotFoundError when no WmsReturnOrderDetail entities are found.
func (wrodq *WmsReturnOrderDetailQuery) Only(ctx context.Context) (*WmsReturnOrderDetail, error) {
	nodes, err := wrodq.Limit(2).All(setContextOp(ctx, wrodq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{wmsreturnorderdetail.Label}
	default:
		return nil, &NotSingularError{wmsreturnorderdetail.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (wrodq *WmsReturnOrderDetailQuery) OnlyX(ctx context.Context) *WmsReturnOrderDetail {
	node, err := wrodq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only WmsReturnOrderDetail ID in the query.
// Returns a *NotSingularError when more than one WmsReturnOrderDetail ID is found.
// Returns a *NotFoundError when no entities are found.
func (wrodq *WmsReturnOrderDetailQuery) OnlyID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = wrodq.Limit(2).IDs(setContextOp(ctx, wrodq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{wmsreturnorderdetail.Label}
	default:
		err = &NotSingularError{wmsreturnorderdetail.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (wrodq *WmsReturnOrderDetailQuery) OnlyIDX(ctx context.Context) string {
	id, err := wrodq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of WmsReturnOrderDetails.
func (wrodq *WmsReturnOrderDetailQuery) All(ctx context.Context) ([]*WmsReturnOrderDetail, error) {
	ctx = setContextOp(ctx, wrodq.ctx, ent.OpQueryAll)
	if err := wrodq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*WmsReturnOrderDetail, *WmsReturnOrderDetailQuery]()
	return withInterceptors[[]*WmsReturnOrderDetail](ctx, wrodq, qr, wrodq.inters)
}

// AllX is like All, but panics if an error occurs.
func (wrodq *WmsReturnOrderDetailQuery) AllX(ctx context.Context) []*WmsReturnOrderDetail {
	nodes, err := wrodq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of WmsReturnOrderDetail IDs.
func (wrodq *WmsReturnOrderDetailQuery) IDs(ctx context.Context) (ids []string, err error) {
	if wrodq.ctx.Unique == nil && wrodq.path != nil {
		wrodq.Unique(true)
	}
	ctx = setContextOp(ctx, wrodq.ctx, ent.OpQueryIDs)
	if err = wrodq.Select(wmsreturnorderdetail.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (wrodq *WmsReturnOrderDetailQuery) IDsX(ctx context.Context) []string {
	ids, err := wrodq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (wrodq *WmsReturnOrderDetailQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, wrodq.ctx, ent.OpQueryCount)
	if err := wrodq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, wrodq, querierCount[*WmsReturnOrderDetailQuery](), wrodq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (wrodq *WmsReturnOrderDetailQuery) CountX(ctx context.Context) int {
	count, err := wrodq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (wrodq *WmsReturnOrderDetailQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, wrodq.ctx, ent.OpQueryExist)
	switch _, err := wrodq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (wrodq *WmsReturnOrderDetailQuery) ExistX(ctx context.Context) bool {
	exist, err := wrodq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the WmsReturnOrderDetailQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (wrodq *WmsReturnOrderDetailQuery) Clone() *WmsReturnOrderDetailQuery {
	if wrodq == nil {
		return nil
	}
	return &WmsReturnOrderDetailQuery{
		config:     wrodq.config,
		ctx:        wrodq.ctx.Clone(),
		order:      append([]wmsreturnorderdetail.OrderOption{}, wrodq.order...),
		inters:     append([]Interceptor{}, wrodq.inters...),
		predicates: append([]predicate.WmsReturnOrderDetail{}, wrodq.predicates...),
		// clone intermediate query.
		sql:       wrodq.sql.Clone(),
		path:      wrodq.path,
		modifiers: append([]func(*sql.Selector){}, wrodq.modifiers...),
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.WmsReturnOrderDetail.Query().
//		GroupBy(wmsreturnorderdetail.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (wrodq *WmsReturnOrderDetailQuery) GroupBy(field string, fields ...string) *WmsReturnOrderDetailGroupBy {
	wrodq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &WmsReturnOrderDetailGroupBy{build: wrodq}
	grbuild.flds = &wrodq.ctx.Fields
	grbuild.label = wmsreturnorderdetail.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.WmsReturnOrderDetail.Query().
//		Select(wmsreturnorderdetail.FieldCreatedAt).
//		Scan(ctx, &v)
func (wrodq *WmsReturnOrderDetailQuery) Select(fields ...string) *WmsReturnOrderDetailSelect {
	wrodq.ctx.Fields = append(wrodq.ctx.Fields, fields...)
	sbuild := &WmsReturnOrderDetailSelect{WmsReturnOrderDetailQuery: wrodq}
	sbuild.label = wmsreturnorderdetail.Label
	sbuild.flds, sbuild.scan = &wrodq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a WmsReturnOrderDetailSelect configured with the given aggregations.
func (wrodq *WmsReturnOrderDetailQuery) Aggregate(fns ...AggregateFunc) *WmsReturnOrderDetailSelect {
	return wrodq.Select().Aggregate(fns...)
}

func (wrodq *WmsReturnOrderDetailQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range wrodq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, wrodq); err != nil {
				return err
			}
		}
	}
	for _, f := range wrodq.ctx.Fields {
		if !wmsreturnorderdetail.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if wrodq.path != nil {
		prev, err := wrodq.path(ctx)
		if err != nil {
			return err
		}
		wrodq.sql = prev
	}
	return nil
}

func (wrodq *WmsReturnOrderDetailQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*WmsReturnOrderDetail, error) {
	var (
		nodes = []*WmsReturnOrderDetail{}
		_spec = wrodq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*WmsReturnOrderDetail).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &WmsReturnOrderDetail{config: wrodq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(wrodq.modifiers) > 0 {
		_spec.Modifiers = wrodq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, wrodq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (wrodq *WmsReturnOrderDetailQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := wrodq.querySpec()
	if len(wrodq.modifiers) > 0 {
		_spec.Modifiers = wrodq.modifiers
	}
	_spec.Node.Columns = wrodq.ctx.Fields
	if len(wrodq.ctx.Fields) > 0 {
		_spec.Unique = wrodq.ctx.Unique != nil && *wrodq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, wrodq.driver, _spec)
}

func (wrodq *WmsReturnOrderDetailQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(wmsreturnorderdetail.Table, wmsreturnorderdetail.Columns, sqlgraph.NewFieldSpec(wmsreturnorderdetail.FieldID, field.TypeString))
	_spec.From = wrodq.sql
	if unique := wrodq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if wrodq.path != nil {
		_spec.Unique = true
	}
	if fields := wrodq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, wmsreturnorderdetail.FieldID)
		for i := range fields {
			if fields[i] != wmsreturnorderdetail.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := wrodq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := wrodq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := wrodq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := wrodq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (wrodq *WmsReturnOrderDetailQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(wrodq.driver.Dialect())
	t1 := builder.Table(wmsreturnorderdetail.Table)
	columns := wrodq.ctx.Fields
	if len(columns) == 0 {
		columns = wmsreturnorderdetail.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if wrodq.sql != nil {
		selector = wrodq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if wrodq.ctx.Unique != nil && *wrodq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range wrodq.modifiers {
		m(selector)
	}
	for _, p := range wrodq.predicates {
		p(selector)
	}
	for _, p := range wrodq.order {
		p(selector)
	}
	if offset := wrodq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := wrodq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// Modify adds a query modifier for attaching custom logic to queries.
func (wrodq *WmsReturnOrderDetailQuery) Modify(modifiers ...func(s *sql.Selector)) *WmsReturnOrderDetailSelect {
	wrodq.modifiers = append(wrodq.modifiers, modifiers...)
	return wrodq.Select()
}

// WmsReturnOrderDetailGroupBy is the group-by builder for WmsReturnOrderDetail entities.
type WmsReturnOrderDetailGroupBy struct {
	selector
	build *WmsReturnOrderDetailQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (wrodgb *WmsReturnOrderDetailGroupBy) Aggregate(fns ...AggregateFunc) *WmsReturnOrderDetailGroupBy {
	wrodgb.fns = append(wrodgb.fns, fns...)
	return wrodgb
}

// Scan applies the selector query and scans the result into the given value.
func (wrodgb *WmsReturnOrderDetailGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, wrodgb.build.ctx, ent.OpQueryGroupBy)
	if err := wrodgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*WmsReturnOrderDetailQuery, *WmsReturnOrderDetailGroupBy](ctx, wrodgb.build, wrodgb, wrodgb.build.inters, v)
}

func (wrodgb *WmsReturnOrderDetailGroupBy) sqlScan(ctx context.Context, root *WmsReturnOrderDetailQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(wrodgb.fns))
	for _, fn := range wrodgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*wrodgb.flds)+len(wrodgb.fns))
		for _, f := range *wrodgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*wrodgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := wrodgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// WmsReturnOrderDetailSelect is the builder for selecting fields of WmsReturnOrderDetail entities.
type WmsReturnOrderDetailSelect struct {
	*WmsReturnOrderDetailQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (wrods *WmsReturnOrderDetailSelect) Aggregate(fns ...AggregateFunc) *WmsReturnOrderDetailSelect {
	wrods.fns = append(wrods.fns, fns...)
	return wrods
}

// Scan applies the selector query and scans the result into the given value.
func (wrods *WmsReturnOrderDetailSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, wrods.ctx, ent.OpQuerySelect)
	if err := wrods.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*WmsReturnOrderDetailQuery, *WmsReturnOrderDetailSelect](ctx, wrods.WmsReturnOrderDetailQuery, wrods, wrods.inters, v)
}

func (wrods *WmsReturnOrderDetailSelect) sqlScan(ctx context.Context, root *WmsReturnOrderDetailQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(wrods.fns))
	for _, fn := range wrods.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*wrods.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := wrods.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (wrods *WmsReturnOrderDetailSelect) Modify(modifiers ...func(s *sql.Selector)) *WmsReturnOrderDetailSelect {
	wrods.modifiers = append(wrods.modifiers, modifiers...)
	return wrods
}
