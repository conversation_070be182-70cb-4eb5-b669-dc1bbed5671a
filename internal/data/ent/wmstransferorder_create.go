// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-mono-demo/internal/data/ent/wmstransferorder"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WmsTransferOrderCreate is the builder for creating a WmsTransferOrder entity.
type WmsTransferOrderCreate struct {
	config
	mutation *WmsTransferOrderMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (wtoc *WmsTransferOrderCreate) SetCreatedAt(t time.Time) *WmsTransferOrderCreate {
	wtoc.mutation.SetCreatedAt(t)
	return wtoc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (wtoc *WmsTransferOrderCreate) SetNillableCreatedAt(t *time.Time) *WmsTransferOrderCreate {
	if t != nil {
		wtoc.SetCreatedAt(*t)
	}
	return wtoc
}

// SetUpdatedAt sets the "updated_at" field.
func (wtoc *WmsTransferOrderCreate) SetUpdatedAt(t time.Time) *WmsTransferOrderCreate {
	wtoc.mutation.SetUpdatedAt(t)
	return wtoc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (wtoc *WmsTransferOrderCreate) SetNillableUpdatedAt(t *time.Time) *WmsTransferOrderCreate {
	if t != nil {
		wtoc.SetUpdatedAt(*t)
	}
	return wtoc
}

// SetCreatedBy sets the "created_by" field.
func (wtoc *WmsTransferOrderCreate) SetCreatedBy(s string) *WmsTransferOrderCreate {
	wtoc.mutation.SetCreatedBy(s)
	return wtoc
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (wtoc *WmsTransferOrderCreate) SetNillableCreatedBy(s *string) *WmsTransferOrderCreate {
	if s != nil {
		wtoc.SetCreatedBy(*s)
	}
	return wtoc
}

// SetUpdatedBy sets the "updated_by" field.
func (wtoc *WmsTransferOrderCreate) SetUpdatedBy(s string) *WmsTransferOrderCreate {
	wtoc.mutation.SetUpdatedBy(s)
	return wtoc
}

// SetNillableUpdatedBy sets the "updated_by" field if the given value is not nil.
func (wtoc *WmsTransferOrderCreate) SetNillableUpdatedBy(s *string) *WmsTransferOrderCreate {
	if s != nil {
		wtoc.SetUpdatedBy(*s)
	}
	return wtoc
}

// SetRemark sets the "remark" field.
func (wtoc *WmsTransferOrderCreate) SetRemark(s string) *WmsTransferOrderCreate {
	wtoc.mutation.SetRemark(s)
	return wtoc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (wtoc *WmsTransferOrderCreate) SetNillableRemark(s *string) *WmsTransferOrderCreate {
	if s != nil {
		wtoc.SetRemark(*s)
	}
	return wtoc
}

// SetContractUrls sets the "contract_urls" field.
func (wtoc *WmsTransferOrderCreate) SetContractUrls(s string) *WmsTransferOrderCreate {
	wtoc.mutation.SetContractUrls(s)
	return wtoc
}

// SetNillableContractUrls sets the "contract_urls" field if the given value is not nil.
func (wtoc *WmsTransferOrderCreate) SetNillableContractUrls(s *string) *WmsTransferOrderCreate {
	if s != nil {
		wtoc.SetContractUrls(*s)
	}
	return wtoc
}

// SetInvoiceUrls sets the "invoice_urls" field.
func (wtoc *WmsTransferOrderCreate) SetInvoiceUrls(s string) *WmsTransferOrderCreate {
	wtoc.mutation.SetInvoiceUrls(s)
	return wtoc
}

// SetNillableInvoiceUrls sets the "invoice_urls" field if the given value is not nil.
func (wtoc *WmsTransferOrderCreate) SetNillableInvoiceUrls(s *string) *WmsTransferOrderCreate {
	if s != nil {
		wtoc.SetInvoiceUrls(*s)
	}
	return wtoc
}

// SetAuditUrls sets the "audit_urls" field.
func (wtoc *WmsTransferOrderCreate) SetAuditUrls(s string) *WmsTransferOrderCreate {
	wtoc.mutation.SetAuditUrls(s)
	return wtoc
}

// SetNillableAuditUrls sets the "audit_urls" field if the given value is not nil.
func (wtoc *WmsTransferOrderCreate) SetNillableAuditUrls(s *string) *WmsTransferOrderCreate {
	if s != nil {
		wtoc.SetAuditUrls(*s)
	}
	return wtoc
}

// SetOtherUrls sets the "other_urls" field.
func (wtoc *WmsTransferOrderCreate) SetOtherUrls(s string) *WmsTransferOrderCreate {
	wtoc.mutation.SetOtherUrls(s)
	return wtoc
}

// SetNillableOtherUrls sets the "other_urls" field if the given value is not nil.
func (wtoc *WmsTransferOrderCreate) SetNillableOtherUrls(s *string) *WmsTransferOrderCreate {
	if s != nil {
		wtoc.SetOtherUrls(*s)
	}
	return wtoc
}

// SetOrderNo sets the "order_no" field.
func (wtoc *WmsTransferOrderCreate) SetOrderNo(s string) *WmsTransferOrderCreate {
	wtoc.mutation.SetOrderNo(s)
	return wtoc
}

// SetEquipmentNum sets the "equipment_num" field.
func (wtoc *WmsTransferOrderCreate) SetEquipmentNum(u uint32) *WmsTransferOrderCreate {
	wtoc.mutation.SetEquipmentNum(u)
	return wtoc
}

// SetNillableEquipmentNum sets the "equipment_num" field if the given value is not nil.
func (wtoc *WmsTransferOrderCreate) SetNillableEquipmentNum(u *uint32) *WmsTransferOrderCreate {
	if u != nil {
		wtoc.SetEquipmentNum(*u)
	}
	return wtoc
}

// SetFromRepositoryID sets the "from_repository_id" field.
func (wtoc *WmsTransferOrderCreate) SetFromRepositoryID(s string) *WmsTransferOrderCreate {
	wtoc.mutation.SetFromRepositoryID(s)
	return wtoc
}

// SetNillableFromRepositoryID sets the "from_repository_id" field if the given value is not nil.
func (wtoc *WmsTransferOrderCreate) SetNillableFromRepositoryID(s *string) *WmsTransferOrderCreate {
	if s != nil {
		wtoc.SetFromRepositoryID(*s)
	}
	return wtoc
}

// SetToRepositoryID sets the "to_repository_id" field.
func (wtoc *WmsTransferOrderCreate) SetToRepositoryID(s string) *WmsTransferOrderCreate {
	wtoc.mutation.SetToRepositoryID(s)
	return wtoc
}

// SetNillableToRepositoryID sets the "to_repository_id" field if the given value is not nil.
func (wtoc *WmsTransferOrderCreate) SetNillableToRepositoryID(s *string) *WmsTransferOrderCreate {
	if s != nil {
		wtoc.SetToRepositoryID(*s)
	}
	return wtoc
}

// SetStatus sets the "status" field.
func (wtoc *WmsTransferOrderCreate) SetStatus(s string) *WmsTransferOrderCreate {
	wtoc.mutation.SetStatus(s)
	return wtoc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (wtoc *WmsTransferOrderCreate) SetNillableStatus(s *string) *WmsTransferOrderCreate {
	if s != nil {
		wtoc.SetStatus(*s)
	}
	return wtoc
}

// SetTransferTime sets the "transfer_time" field.
func (wtoc *WmsTransferOrderCreate) SetTransferTime(t time.Time) *WmsTransferOrderCreate {
	wtoc.mutation.SetTransferTime(t)
	return wtoc
}

// SetNillableTransferTime sets the "transfer_time" field if the given value is not nil.
func (wtoc *WmsTransferOrderCreate) SetNillableTransferTime(t *time.Time) *WmsTransferOrderCreate {
	if t != nil {
		wtoc.SetTransferTime(*t)
	}
	return wtoc
}

// SetID sets the "id" field.
func (wtoc *WmsTransferOrderCreate) SetID(s string) *WmsTransferOrderCreate {
	wtoc.mutation.SetID(s)
	return wtoc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (wtoc *WmsTransferOrderCreate) SetNillableID(s *string) *WmsTransferOrderCreate {
	if s != nil {
		wtoc.SetID(*s)
	}
	return wtoc
}

// Mutation returns the WmsTransferOrderMutation object of the builder.
func (wtoc *WmsTransferOrderCreate) Mutation() *WmsTransferOrderMutation {
	return wtoc.mutation
}

// Save creates the WmsTransferOrder in the database.
func (wtoc *WmsTransferOrderCreate) Save(ctx context.Context) (*WmsTransferOrder, error) {
	wtoc.defaults()
	return withHooks(ctx, wtoc.sqlSave, wtoc.mutation, wtoc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (wtoc *WmsTransferOrderCreate) SaveX(ctx context.Context) *WmsTransferOrder {
	v, err := wtoc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (wtoc *WmsTransferOrderCreate) Exec(ctx context.Context) error {
	_, err := wtoc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wtoc *WmsTransferOrderCreate) ExecX(ctx context.Context) {
	if err := wtoc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wtoc *WmsTransferOrderCreate) defaults() {
	if _, ok := wtoc.mutation.CreatedAt(); !ok {
		v := wmstransferorder.DefaultCreatedAt
		wtoc.mutation.SetCreatedAt(v)
	}
	if _, ok := wtoc.mutation.UpdatedAt(); !ok {
		v := wmstransferorder.DefaultUpdatedAt()
		wtoc.mutation.SetUpdatedAt(v)
	}
	if _, ok := wtoc.mutation.ID(); !ok {
		v := wmstransferorder.DefaultID()
		wtoc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (wtoc *WmsTransferOrderCreate) check() error {
	if _, ok := wtoc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "WmsTransferOrder.created_at"`)}
	}
	if _, ok := wtoc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "WmsTransferOrder.updated_at"`)}
	}
	if _, ok := wtoc.mutation.OrderNo(); !ok {
		return &ValidationError{Name: "order_no", err: errors.New(`ent: missing required field "WmsTransferOrder.order_no"`)}
	}
	return nil
}

func (wtoc *WmsTransferOrderCreate) sqlSave(ctx context.Context) (*WmsTransferOrder, error) {
	if err := wtoc.check(); err != nil {
		return nil, err
	}
	_node, _spec := wtoc.createSpec()
	if err := sqlgraph.CreateNode(ctx, wtoc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected WmsTransferOrder.ID type: %T", _spec.ID.Value)
		}
	}
	wtoc.mutation.id = &_node.ID
	wtoc.mutation.done = true
	return _node, nil
}

func (wtoc *WmsTransferOrderCreate) createSpec() (*WmsTransferOrder, *sqlgraph.CreateSpec) {
	var (
		_node = &WmsTransferOrder{config: wtoc.config}
		_spec = sqlgraph.NewCreateSpec(wmstransferorder.Table, sqlgraph.NewFieldSpec(wmstransferorder.FieldID, field.TypeString))
	)
	_spec.OnConflict = wtoc.conflict
	if id, ok := wtoc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := wtoc.mutation.CreatedAt(); ok {
		_spec.SetField(wmstransferorder.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := wtoc.mutation.UpdatedAt(); ok {
		_spec.SetField(wmstransferorder.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := wtoc.mutation.CreatedBy(); ok {
		_spec.SetField(wmstransferorder.FieldCreatedBy, field.TypeString, value)
		_node.CreatedBy = value
	}
	if value, ok := wtoc.mutation.UpdatedBy(); ok {
		_spec.SetField(wmstransferorder.FieldUpdatedBy, field.TypeString, value)
		_node.UpdatedBy = &value
	}
	if value, ok := wtoc.mutation.Remark(); ok {
		_spec.SetField(wmstransferorder.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := wtoc.mutation.ContractUrls(); ok {
		_spec.SetField(wmstransferorder.FieldContractUrls, field.TypeString, value)
		_node.ContractUrls = value
	}
	if value, ok := wtoc.mutation.InvoiceUrls(); ok {
		_spec.SetField(wmstransferorder.FieldInvoiceUrls, field.TypeString, value)
		_node.InvoiceUrls = value
	}
	if value, ok := wtoc.mutation.AuditUrls(); ok {
		_spec.SetField(wmstransferorder.FieldAuditUrls, field.TypeString, value)
		_node.AuditUrls = value
	}
	if value, ok := wtoc.mutation.OtherUrls(); ok {
		_spec.SetField(wmstransferorder.FieldOtherUrls, field.TypeString, value)
		_node.OtherUrls = value
	}
	if value, ok := wtoc.mutation.OrderNo(); ok {
		_spec.SetField(wmstransferorder.FieldOrderNo, field.TypeString, value)
		_node.OrderNo = value
	}
	if value, ok := wtoc.mutation.EquipmentNum(); ok {
		_spec.SetField(wmstransferorder.FieldEquipmentNum, field.TypeUint32, value)
		_node.EquipmentNum = value
	}
	if value, ok := wtoc.mutation.FromRepositoryID(); ok {
		_spec.SetField(wmstransferorder.FieldFromRepositoryID, field.TypeString, value)
		_node.FromRepositoryID = value
	}
	if value, ok := wtoc.mutation.ToRepositoryID(); ok {
		_spec.SetField(wmstransferorder.FieldToRepositoryID, field.TypeString, value)
		_node.ToRepositoryID = value
	}
	if value, ok := wtoc.mutation.Status(); ok {
		_spec.SetField(wmstransferorder.FieldStatus, field.TypeString, value)
		_node.Status = value
	}
	if value, ok := wtoc.mutation.TransferTime(); ok {
		_spec.SetField(wmstransferorder.FieldTransferTime, field.TypeTime, value)
		_node.TransferTime = &value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.WmsTransferOrder.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.WmsTransferOrderUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (wtoc *WmsTransferOrderCreate) OnConflict(opts ...sql.ConflictOption) *WmsTransferOrderUpsertOne {
	wtoc.conflict = opts
	return &WmsTransferOrderUpsertOne{
		create: wtoc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.WmsTransferOrder.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (wtoc *WmsTransferOrderCreate) OnConflictColumns(columns ...string) *WmsTransferOrderUpsertOne {
	wtoc.conflict = append(wtoc.conflict, sql.ConflictColumns(columns...))
	return &WmsTransferOrderUpsertOne{
		create: wtoc,
	}
}

type (
	// WmsTransferOrderUpsertOne is the builder for "upsert"-ing
	//  one WmsTransferOrder node.
	WmsTransferOrderUpsertOne struct {
		create *WmsTransferOrderCreate
	}

	// WmsTransferOrderUpsert is the "OnConflict" setter.
	WmsTransferOrderUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *WmsTransferOrderUpsert) SetUpdatedAt(v time.Time) *WmsTransferOrderUpsert {
	u.Set(wmstransferorder.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *WmsTransferOrderUpsert) UpdateUpdatedAt() *WmsTransferOrderUpsert {
	u.SetExcluded(wmstransferorder.FieldUpdatedAt)
	return u
}

// SetCreatedBy sets the "created_by" field.
func (u *WmsTransferOrderUpsert) SetCreatedBy(v string) *WmsTransferOrderUpsert {
	u.Set(wmstransferorder.FieldCreatedBy, v)
	return u
}

// UpdateCreatedBy sets the "created_by" field to the value that was provided on create.
func (u *WmsTransferOrderUpsert) UpdateCreatedBy() *WmsTransferOrderUpsert {
	u.SetExcluded(wmstransferorder.FieldCreatedBy)
	return u
}

// ClearCreatedBy clears the value of the "created_by" field.
func (u *WmsTransferOrderUpsert) ClearCreatedBy() *WmsTransferOrderUpsert {
	u.SetNull(wmstransferorder.FieldCreatedBy)
	return u
}

// SetUpdatedBy sets the "updated_by" field.
func (u *WmsTransferOrderUpsert) SetUpdatedBy(v string) *WmsTransferOrderUpsert {
	u.Set(wmstransferorder.FieldUpdatedBy, v)
	return u
}

// UpdateUpdatedBy sets the "updated_by" field to the value that was provided on create.
func (u *WmsTransferOrderUpsert) UpdateUpdatedBy() *WmsTransferOrderUpsert {
	u.SetExcluded(wmstransferorder.FieldUpdatedBy)
	return u
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (u *WmsTransferOrderUpsert) ClearUpdatedBy() *WmsTransferOrderUpsert {
	u.SetNull(wmstransferorder.FieldUpdatedBy)
	return u
}

// SetRemark sets the "remark" field.
func (u *WmsTransferOrderUpsert) SetRemark(v string) *WmsTransferOrderUpsert {
	u.Set(wmstransferorder.FieldRemark, v)
	return u
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *WmsTransferOrderUpsert) UpdateRemark() *WmsTransferOrderUpsert {
	u.SetExcluded(wmstransferorder.FieldRemark)
	return u
}

// ClearRemark clears the value of the "remark" field.
func (u *WmsTransferOrderUpsert) ClearRemark() *WmsTransferOrderUpsert {
	u.SetNull(wmstransferorder.FieldRemark)
	return u
}

// SetContractUrls sets the "contract_urls" field.
func (u *WmsTransferOrderUpsert) SetContractUrls(v string) *WmsTransferOrderUpsert {
	u.Set(wmstransferorder.FieldContractUrls, v)
	return u
}

// UpdateContractUrls sets the "contract_urls" field to the value that was provided on create.
func (u *WmsTransferOrderUpsert) UpdateContractUrls() *WmsTransferOrderUpsert {
	u.SetExcluded(wmstransferorder.FieldContractUrls)
	return u
}

// ClearContractUrls clears the value of the "contract_urls" field.
func (u *WmsTransferOrderUpsert) ClearContractUrls() *WmsTransferOrderUpsert {
	u.SetNull(wmstransferorder.FieldContractUrls)
	return u
}

// SetInvoiceUrls sets the "invoice_urls" field.
func (u *WmsTransferOrderUpsert) SetInvoiceUrls(v string) *WmsTransferOrderUpsert {
	u.Set(wmstransferorder.FieldInvoiceUrls, v)
	return u
}

// UpdateInvoiceUrls sets the "invoice_urls" field to the value that was provided on create.
func (u *WmsTransferOrderUpsert) UpdateInvoiceUrls() *WmsTransferOrderUpsert {
	u.SetExcluded(wmstransferorder.FieldInvoiceUrls)
	return u
}

// ClearInvoiceUrls clears the value of the "invoice_urls" field.
func (u *WmsTransferOrderUpsert) ClearInvoiceUrls() *WmsTransferOrderUpsert {
	u.SetNull(wmstransferorder.FieldInvoiceUrls)
	return u
}

// SetAuditUrls sets the "audit_urls" field.
func (u *WmsTransferOrderUpsert) SetAuditUrls(v string) *WmsTransferOrderUpsert {
	u.Set(wmstransferorder.FieldAuditUrls, v)
	return u
}

// UpdateAuditUrls sets the "audit_urls" field to the value that was provided on create.
func (u *WmsTransferOrderUpsert) UpdateAuditUrls() *WmsTransferOrderUpsert {
	u.SetExcluded(wmstransferorder.FieldAuditUrls)
	return u
}

// ClearAuditUrls clears the value of the "audit_urls" field.
func (u *WmsTransferOrderUpsert) ClearAuditUrls() *WmsTransferOrderUpsert {
	u.SetNull(wmstransferorder.FieldAuditUrls)
	return u
}

// SetOtherUrls sets the "other_urls" field.
func (u *WmsTransferOrderUpsert) SetOtherUrls(v string) *WmsTransferOrderUpsert {
	u.Set(wmstransferorder.FieldOtherUrls, v)
	return u
}

// UpdateOtherUrls sets the "other_urls" field to the value that was provided on create.
func (u *WmsTransferOrderUpsert) UpdateOtherUrls() *WmsTransferOrderUpsert {
	u.SetExcluded(wmstransferorder.FieldOtherUrls)
	return u
}

// ClearOtherUrls clears the value of the "other_urls" field.
func (u *WmsTransferOrderUpsert) ClearOtherUrls() *WmsTransferOrderUpsert {
	u.SetNull(wmstransferorder.FieldOtherUrls)
	return u
}

// SetOrderNo sets the "order_no" field.
func (u *WmsTransferOrderUpsert) SetOrderNo(v string) *WmsTransferOrderUpsert {
	u.Set(wmstransferorder.FieldOrderNo, v)
	return u
}

// UpdateOrderNo sets the "order_no" field to the value that was provided on create.
func (u *WmsTransferOrderUpsert) UpdateOrderNo() *WmsTransferOrderUpsert {
	u.SetExcluded(wmstransferorder.FieldOrderNo)
	return u
}

// SetEquipmentNum sets the "equipment_num" field.
func (u *WmsTransferOrderUpsert) SetEquipmentNum(v uint32) *WmsTransferOrderUpsert {
	u.Set(wmstransferorder.FieldEquipmentNum, v)
	return u
}

// UpdateEquipmentNum sets the "equipment_num" field to the value that was provided on create.
func (u *WmsTransferOrderUpsert) UpdateEquipmentNum() *WmsTransferOrderUpsert {
	u.SetExcluded(wmstransferorder.FieldEquipmentNum)
	return u
}

// AddEquipmentNum adds v to the "equipment_num" field.
func (u *WmsTransferOrderUpsert) AddEquipmentNum(v uint32) *WmsTransferOrderUpsert {
	u.Add(wmstransferorder.FieldEquipmentNum, v)
	return u
}

// ClearEquipmentNum clears the value of the "equipment_num" field.
func (u *WmsTransferOrderUpsert) ClearEquipmentNum() *WmsTransferOrderUpsert {
	u.SetNull(wmstransferorder.FieldEquipmentNum)
	return u
}

// SetFromRepositoryID sets the "from_repository_id" field.
func (u *WmsTransferOrderUpsert) SetFromRepositoryID(v string) *WmsTransferOrderUpsert {
	u.Set(wmstransferorder.FieldFromRepositoryID, v)
	return u
}

// UpdateFromRepositoryID sets the "from_repository_id" field to the value that was provided on create.
func (u *WmsTransferOrderUpsert) UpdateFromRepositoryID() *WmsTransferOrderUpsert {
	u.SetExcluded(wmstransferorder.FieldFromRepositoryID)
	return u
}

// ClearFromRepositoryID clears the value of the "from_repository_id" field.
func (u *WmsTransferOrderUpsert) ClearFromRepositoryID() *WmsTransferOrderUpsert {
	u.SetNull(wmstransferorder.FieldFromRepositoryID)
	return u
}

// SetToRepositoryID sets the "to_repository_id" field.
func (u *WmsTransferOrderUpsert) SetToRepositoryID(v string) *WmsTransferOrderUpsert {
	u.Set(wmstransferorder.FieldToRepositoryID, v)
	return u
}

// UpdateToRepositoryID sets the "to_repository_id" field to the value that was provided on create.
func (u *WmsTransferOrderUpsert) UpdateToRepositoryID() *WmsTransferOrderUpsert {
	u.SetExcluded(wmstransferorder.FieldToRepositoryID)
	return u
}

// ClearToRepositoryID clears the value of the "to_repository_id" field.
func (u *WmsTransferOrderUpsert) ClearToRepositoryID() *WmsTransferOrderUpsert {
	u.SetNull(wmstransferorder.FieldToRepositoryID)
	return u
}

// SetStatus sets the "status" field.
func (u *WmsTransferOrderUpsert) SetStatus(v string) *WmsTransferOrderUpsert {
	u.Set(wmstransferorder.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *WmsTransferOrderUpsert) UpdateStatus() *WmsTransferOrderUpsert {
	u.SetExcluded(wmstransferorder.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *WmsTransferOrderUpsert) ClearStatus() *WmsTransferOrderUpsert {
	u.SetNull(wmstransferorder.FieldStatus)
	return u
}

// SetTransferTime sets the "transfer_time" field.
func (u *WmsTransferOrderUpsert) SetTransferTime(v time.Time) *WmsTransferOrderUpsert {
	u.Set(wmstransferorder.FieldTransferTime, v)
	return u
}

// UpdateTransferTime sets the "transfer_time" field to the value that was provided on create.
func (u *WmsTransferOrderUpsert) UpdateTransferTime() *WmsTransferOrderUpsert {
	u.SetExcluded(wmstransferorder.FieldTransferTime)
	return u
}

// ClearTransferTime clears the value of the "transfer_time" field.
func (u *WmsTransferOrderUpsert) ClearTransferTime() *WmsTransferOrderUpsert {
	u.SetNull(wmstransferorder.FieldTransferTime)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.WmsTransferOrder.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(wmstransferorder.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *WmsTransferOrderUpsertOne) UpdateNewValues() *WmsTransferOrderUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(wmstransferorder.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(wmstransferorder.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.WmsTransferOrder.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *WmsTransferOrderUpsertOne) Ignore() *WmsTransferOrderUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *WmsTransferOrderUpsertOne) DoNothing() *WmsTransferOrderUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the WmsTransferOrderCreate.OnConflict
// documentation for more info.
func (u *WmsTransferOrderUpsertOne) Update(set func(*WmsTransferOrderUpsert)) *WmsTransferOrderUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&WmsTransferOrderUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *WmsTransferOrderUpsertOne) SetUpdatedAt(v time.Time) *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertOne) UpdateUpdatedAt() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetCreatedBy sets the "created_by" field.
func (u *WmsTransferOrderUpsertOne) SetCreatedBy(v string) *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetCreatedBy(v)
	})
}

// UpdateCreatedBy sets the "created_by" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertOne) UpdateCreatedBy() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateCreatedBy()
	})
}

// ClearCreatedBy clears the value of the "created_by" field.
func (u *WmsTransferOrderUpsertOne) ClearCreatedBy() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearCreatedBy()
	})
}

// SetUpdatedBy sets the "updated_by" field.
func (u *WmsTransferOrderUpsertOne) SetUpdatedBy(v string) *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetUpdatedBy(v)
	})
}

// UpdateUpdatedBy sets the "updated_by" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertOne) UpdateUpdatedBy() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateUpdatedBy()
	})
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (u *WmsTransferOrderUpsertOne) ClearUpdatedBy() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearUpdatedBy()
	})
}

// SetRemark sets the "remark" field.
func (u *WmsTransferOrderUpsertOne) SetRemark(v string) *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertOne) UpdateRemark() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *WmsTransferOrderUpsertOne) ClearRemark() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearRemark()
	})
}

// SetContractUrls sets the "contract_urls" field.
func (u *WmsTransferOrderUpsertOne) SetContractUrls(v string) *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetContractUrls(v)
	})
}

// UpdateContractUrls sets the "contract_urls" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertOne) UpdateContractUrls() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateContractUrls()
	})
}

// ClearContractUrls clears the value of the "contract_urls" field.
func (u *WmsTransferOrderUpsertOne) ClearContractUrls() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearContractUrls()
	})
}

// SetInvoiceUrls sets the "invoice_urls" field.
func (u *WmsTransferOrderUpsertOne) SetInvoiceUrls(v string) *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetInvoiceUrls(v)
	})
}

// UpdateInvoiceUrls sets the "invoice_urls" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertOne) UpdateInvoiceUrls() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateInvoiceUrls()
	})
}

// ClearInvoiceUrls clears the value of the "invoice_urls" field.
func (u *WmsTransferOrderUpsertOne) ClearInvoiceUrls() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearInvoiceUrls()
	})
}

// SetAuditUrls sets the "audit_urls" field.
func (u *WmsTransferOrderUpsertOne) SetAuditUrls(v string) *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetAuditUrls(v)
	})
}

// UpdateAuditUrls sets the "audit_urls" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertOne) UpdateAuditUrls() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateAuditUrls()
	})
}

// ClearAuditUrls clears the value of the "audit_urls" field.
func (u *WmsTransferOrderUpsertOne) ClearAuditUrls() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearAuditUrls()
	})
}

// SetOtherUrls sets the "other_urls" field.
func (u *WmsTransferOrderUpsertOne) SetOtherUrls(v string) *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetOtherUrls(v)
	})
}

// UpdateOtherUrls sets the "other_urls" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertOne) UpdateOtherUrls() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateOtherUrls()
	})
}

// ClearOtherUrls clears the value of the "other_urls" field.
func (u *WmsTransferOrderUpsertOne) ClearOtherUrls() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearOtherUrls()
	})
}

// SetOrderNo sets the "order_no" field.
func (u *WmsTransferOrderUpsertOne) SetOrderNo(v string) *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetOrderNo(v)
	})
}

// UpdateOrderNo sets the "order_no" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertOne) UpdateOrderNo() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateOrderNo()
	})
}

// SetEquipmentNum sets the "equipment_num" field.
func (u *WmsTransferOrderUpsertOne) SetEquipmentNum(v uint32) *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetEquipmentNum(v)
	})
}

// AddEquipmentNum adds v to the "equipment_num" field.
func (u *WmsTransferOrderUpsertOne) AddEquipmentNum(v uint32) *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.AddEquipmentNum(v)
	})
}

// UpdateEquipmentNum sets the "equipment_num" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertOne) UpdateEquipmentNum() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateEquipmentNum()
	})
}

// ClearEquipmentNum clears the value of the "equipment_num" field.
func (u *WmsTransferOrderUpsertOne) ClearEquipmentNum() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearEquipmentNum()
	})
}

// SetFromRepositoryID sets the "from_repository_id" field.
func (u *WmsTransferOrderUpsertOne) SetFromRepositoryID(v string) *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetFromRepositoryID(v)
	})
}

// UpdateFromRepositoryID sets the "from_repository_id" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertOne) UpdateFromRepositoryID() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateFromRepositoryID()
	})
}

// ClearFromRepositoryID clears the value of the "from_repository_id" field.
func (u *WmsTransferOrderUpsertOne) ClearFromRepositoryID() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearFromRepositoryID()
	})
}

// SetToRepositoryID sets the "to_repository_id" field.
func (u *WmsTransferOrderUpsertOne) SetToRepositoryID(v string) *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetToRepositoryID(v)
	})
}

// UpdateToRepositoryID sets the "to_repository_id" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertOne) UpdateToRepositoryID() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateToRepositoryID()
	})
}

// ClearToRepositoryID clears the value of the "to_repository_id" field.
func (u *WmsTransferOrderUpsertOne) ClearToRepositoryID() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearToRepositoryID()
	})
}

// SetStatus sets the "status" field.
func (u *WmsTransferOrderUpsertOne) SetStatus(v string) *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertOne) UpdateStatus() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *WmsTransferOrderUpsertOne) ClearStatus() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearStatus()
	})
}

// SetTransferTime sets the "transfer_time" field.
func (u *WmsTransferOrderUpsertOne) SetTransferTime(v time.Time) *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetTransferTime(v)
	})
}

// UpdateTransferTime sets the "transfer_time" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertOne) UpdateTransferTime() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateTransferTime()
	})
}

// ClearTransferTime clears the value of the "transfer_time" field.
func (u *WmsTransferOrderUpsertOne) ClearTransferTime() *WmsTransferOrderUpsertOne {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearTransferTime()
	})
}

// Exec executes the query.
func (u *WmsTransferOrderUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for WmsTransferOrderCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *WmsTransferOrderUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *WmsTransferOrderUpsertOne) ID(ctx context.Context) (id string, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: WmsTransferOrderUpsertOne.ID is not supported by MySQL driver. Use WmsTransferOrderUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *WmsTransferOrderUpsertOne) IDX(ctx context.Context) string {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// WmsTransferOrderCreateBulk is the builder for creating many WmsTransferOrder entities in bulk.
type WmsTransferOrderCreateBulk struct {
	config
	err      error
	builders []*WmsTransferOrderCreate
	conflict []sql.ConflictOption
}

// Save creates the WmsTransferOrder entities in the database.
func (wtocb *WmsTransferOrderCreateBulk) Save(ctx context.Context) ([]*WmsTransferOrder, error) {
	if wtocb.err != nil {
		return nil, wtocb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(wtocb.builders))
	nodes := make([]*WmsTransferOrder, len(wtocb.builders))
	mutators := make([]Mutator, len(wtocb.builders))
	for i := range wtocb.builders {
		func(i int, root context.Context) {
			builder := wtocb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*WmsTransferOrderMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, wtocb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = wtocb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, wtocb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, wtocb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (wtocb *WmsTransferOrderCreateBulk) SaveX(ctx context.Context) []*WmsTransferOrder {
	v, err := wtocb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (wtocb *WmsTransferOrderCreateBulk) Exec(ctx context.Context) error {
	_, err := wtocb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wtocb *WmsTransferOrderCreateBulk) ExecX(ctx context.Context) {
	if err := wtocb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.WmsTransferOrder.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.WmsTransferOrderUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (wtocb *WmsTransferOrderCreateBulk) OnConflict(opts ...sql.ConflictOption) *WmsTransferOrderUpsertBulk {
	wtocb.conflict = opts
	return &WmsTransferOrderUpsertBulk{
		create: wtocb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.WmsTransferOrder.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (wtocb *WmsTransferOrderCreateBulk) OnConflictColumns(columns ...string) *WmsTransferOrderUpsertBulk {
	wtocb.conflict = append(wtocb.conflict, sql.ConflictColumns(columns...))
	return &WmsTransferOrderUpsertBulk{
		create: wtocb,
	}
}

// WmsTransferOrderUpsertBulk is the builder for "upsert"-ing
// a bulk of WmsTransferOrder nodes.
type WmsTransferOrderUpsertBulk struct {
	create *WmsTransferOrderCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.WmsTransferOrder.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(wmstransferorder.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *WmsTransferOrderUpsertBulk) UpdateNewValues() *WmsTransferOrderUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(wmstransferorder.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(wmstransferorder.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.WmsTransferOrder.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *WmsTransferOrderUpsertBulk) Ignore() *WmsTransferOrderUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *WmsTransferOrderUpsertBulk) DoNothing() *WmsTransferOrderUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the WmsTransferOrderCreateBulk.OnConflict
// documentation for more info.
func (u *WmsTransferOrderUpsertBulk) Update(set func(*WmsTransferOrderUpsert)) *WmsTransferOrderUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&WmsTransferOrderUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *WmsTransferOrderUpsertBulk) SetUpdatedAt(v time.Time) *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertBulk) UpdateUpdatedAt() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetCreatedBy sets the "created_by" field.
func (u *WmsTransferOrderUpsertBulk) SetCreatedBy(v string) *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetCreatedBy(v)
	})
}

// UpdateCreatedBy sets the "created_by" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertBulk) UpdateCreatedBy() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateCreatedBy()
	})
}

// ClearCreatedBy clears the value of the "created_by" field.
func (u *WmsTransferOrderUpsertBulk) ClearCreatedBy() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearCreatedBy()
	})
}

// SetUpdatedBy sets the "updated_by" field.
func (u *WmsTransferOrderUpsertBulk) SetUpdatedBy(v string) *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetUpdatedBy(v)
	})
}

// UpdateUpdatedBy sets the "updated_by" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertBulk) UpdateUpdatedBy() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateUpdatedBy()
	})
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (u *WmsTransferOrderUpsertBulk) ClearUpdatedBy() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearUpdatedBy()
	})
}

// SetRemark sets the "remark" field.
func (u *WmsTransferOrderUpsertBulk) SetRemark(v string) *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertBulk) UpdateRemark() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *WmsTransferOrderUpsertBulk) ClearRemark() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearRemark()
	})
}

// SetContractUrls sets the "contract_urls" field.
func (u *WmsTransferOrderUpsertBulk) SetContractUrls(v string) *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetContractUrls(v)
	})
}

// UpdateContractUrls sets the "contract_urls" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertBulk) UpdateContractUrls() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateContractUrls()
	})
}

// ClearContractUrls clears the value of the "contract_urls" field.
func (u *WmsTransferOrderUpsertBulk) ClearContractUrls() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearContractUrls()
	})
}

// SetInvoiceUrls sets the "invoice_urls" field.
func (u *WmsTransferOrderUpsertBulk) SetInvoiceUrls(v string) *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetInvoiceUrls(v)
	})
}

// UpdateInvoiceUrls sets the "invoice_urls" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertBulk) UpdateInvoiceUrls() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateInvoiceUrls()
	})
}

// ClearInvoiceUrls clears the value of the "invoice_urls" field.
func (u *WmsTransferOrderUpsertBulk) ClearInvoiceUrls() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearInvoiceUrls()
	})
}

// SetAuditUrls sets the "audit_urls" field.
func (u *WmsTransferOrderUpsertBulk) SetAuditUrls(v string) *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetAuditUrls(v)
	})
}

// UpdateAuditUrls sets the "audit_urls" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertBulk) UpdateAuditUrls() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateAuditUrls()
	})
}

// ClearAuditUrls clears the value of the "audit_urls" field.
func (u *WmsTransferOrderUpsertBulk) ClearAuditUrls() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearAuditUrls()
	})
}

// SetOtherUrls sets the "other_urls" field.
func (u *WmsTransferOrderUpsertBulk) SetOtherUrls(v string) *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetOtherUrls(v)
	})
}

// UpdateOtherUrls sets the "other_urls" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertBulk) UpdateOtherUrls() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateOtherUrls()
	})
}

// ClearOtherUrls clears the value of the "other_urls" field.
func (u *WmsTransferOrderUpsertBulk) ClearOtherUrls() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearOtherUrls()
	})
}

// SetOrderNo sets the "order_no" field.
func (u *WmsTransferOrderUpsertBulk) SetOrderNo(v string) *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetOrderNo(v)
	})
}

// UpdateOrderNo sets the "order_no" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertBulk) UpdateOrderNo() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateOrderNo()
	})
}

// SetEquipmentNum sets the "equipment_num" field.
func (u *WmsTransferOrderUpsertBulk) SetEquipmentNum(v uint32) *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetEquipmentNum(v)
	})
}

// AddEquipmentNum adds v to the "equipment_num" field.
func (u *WmsTransferOrderUpsertBulk) AddEquipmentNum(v uint32) *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.AddEquipmentNum(v)
	})
}

// UpdateEquipmentNum sets the "equipment_num" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertBulk) UpdateEquipmentNum() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateEquipmentNum()
	})
}

// ClearEquipmentNum clears the value of the "equipment_num" field.
func (u *WmsTransferOrderUpsertBulk) ClearEquipmentNum() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearEquipmentNum()
	})
}

// SetFromRepositoryID sets the "from_repository_id" field.
func (u *WmsTransferOrderUpsertBulk) SetFromRepositoryID(v string) *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetFromRepositoryID(v)
	})
}

// UpdateFromRepositoryID sets the "from_repository_id" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertBulk) UpdateFromRepositoryID() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateFromRepositoryID()
	})
}

// ClearFromRepositoryID clears the value of the "from_repository_id" field.
func (u *WmsTransferOrderUpsertBulk) ClearFromRepositoryID() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearFromRepositoryID()
	})
}

// SetToRepositoryID sets the "to_repository_id" field.
func (u *WmsTransferOrderUpsertBulk) SetToRepositoryID(v string) *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetToRepositoryID(v)
	})
}

// UpdateToRepositoryID sets the "to_repository_id" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertBulk) UpdateToRepositoryID() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateToRepositoryID()
	})
}

// ClearToRepositoryID clears the value of the "to_repository_id" field.
func (u *WmsTransferOrderUpsertBulk) ClearToRepositoryID() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearToRepositoryID()
	})
}

// SetStatus sets the "status" field.
func (u *WmsTransferOrderUpsertBulk) SetStatus(v string) *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertBulk) UpdateStatus() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *WmsTransferOrderUpsertBulk) ClearStatus() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearStatus()
	})
}

// SetTransferTime sets the "transfer_time" field.
func (u *WmsTransferOrderUpsertBulk) SetTransferTime(v time.Time) *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.SetTransferTime(v)
	})
}

// UpdateTransferTime sets the "transfer_time" field to the value that was provided on create.
func (u *WmsTransferOrderUpsertBulk) UpdateTransferTime() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.UpdateTransferTime()
	})
}

// ClearTransferTime clears the value of the "transfer_time" field.
func (u *WmsTransferOrderUpsertBulk) ClearTransferTime() *WmsTransferOrderUpsertBulk {
	return u.Update(func(s *WmsTransferOrderUpsert) {
		s.ClearTransferTime()
	})
}

// Exec executes the query.
func (u *WmsTransferOrderUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the WmsTransferOrderCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for WmsTransferOrderCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *WmsTransferOrderUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
