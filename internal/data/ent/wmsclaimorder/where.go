// Code generated by ent, DO NOT EDIT.

package wmsclaimorder

import (
	"kratos-mono-demo/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContainsFold(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldUpdatedAt, v))
}

// CreatedBy applies equality check predicate on the "created_by" field. It's identical to CreatedByEQ.
func CreatedBy(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldCreatedBy, v))
}

// UpdatedBy applies equality check predicate on the "updated_by" field. It's identical to UpdatedByEQ.
func UpdatedBy(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldUpdatedBy, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldRemark, v))
}

// ContractUrls applies equality check predicate on the "contract_urls" field. It's identical to ContractUrlsEQ.
func ContractUrls(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldContractUrls, v))
}

// InvoiceUrls applies equality check predicate on the "invoice_urls" field. It's identical to InvoiceUrlsEQ.
func InvoiceUrls(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldInvoiceUrls, v))
}

// AuditUrls applies equality check predicate on the "audit_urls" field. It's identical to AuditUrlsEQ.
func AuditUrls(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldAuditUrls, v))
}

// OtherUrls applies equality check predicate on the "other_urls" field. It's identical to OtherUrlsEQ.
func OtherUrls(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldOtherUrls, v))
}

// OrderNo applies equality check predicate on the "order_no" field. It's identical to OrderNoEQ.
func OrderNo(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldOrderNo, v))
}

// RepositoryID applies equality check predicate on the "repository_id" field. It's identical to RepositoryIDEQ.
func RepositoryID(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldRepositoryID, v))
}

// ClaimTime applies equality check predicate on the "claim_time" field. It's identical to ClaimTimeEQ.
func ClaimTime(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldClaimTime, v))
}

// ClaimUserID applies equality check predicate on the "claim_user_id" field. It's identical to ClaimUserIDEQ.
func ClaimUserID(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldClaimUserID, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldStatus, v))
}

// EquipmentNum applies equality check predicate on the "equipment_num" field. It's identical to EquipmentNumEQ.
func EquipmentNum(v uint32) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldEquipmentNum, v))
}

// Reason applies equality check predicate on the "reason" field. It's identical to ReasonEQ.
func Reason(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldReason, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLTE(FieldUpdatedAt, v))
}

// CreatedByEQ applies the EQ predicate on the "created_by" field.
func CreatedByEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldCreatedBy, v))
}

// CreatedByNEQ applies the NEQ predicate on the "created_by" field.
func CreatedByNEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNEQ(FieldCreatedBy, v))
}

// CreatedByIn applies the In predicate on the "created_by" field.
func CreatedByIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIn(FieldCreatedBy, vs...))
}

// CreatedByNotIn applies the NotIn predicate on the "created_by" field.
func CreatedByNotIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotIn(FieldCreatedBy, vs...))
}

// CreatedByGT applies the GT predicate on the "created_by" field.
func CreatedByGT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGT(FieldCreatedBy, v))
}

// CreatedByGTE applies the GTE predicate on the "created_by" field.
func CreatedByGTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGTE(FieldCreatedBy, v))
}

// CreatedByLT applies the LT predicate on the "created_by" field.
func CreatedByLT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLT(FieldCreatedBy, v))
}

// CreatedByLTE applies the LTE predicate on the "created_by" field.
func CreatedByLTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLTE(FieldCreatedBy, v))
}

// CreatedByContains applies the Contains predicate on the "created_by" field.
func CreatedByContains(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContains(FieldCreatedBy, v))
}

// CreatedByHasPrefix applies the HasPrefix predicate on the "created_by" field.
func CreatedByHasPrefix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasPrefix(FieldCreatedBy, v))
}

// CreatedByHasSuffix applies the HasSuffix predicate on the "created_by" field.
func CreatedByHasSuffix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasSuffix(FieldCreatedBy, v))
}

// CreatedByIsNil applies the IsNil predicate on the "created_by" field.
func CreatedByIsNil() predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIsNull(FieldCreatedBy))
}

// CreatedByNotNil applies the NotNil predicate on the "created_by" field.
func CreatedByNotNil() predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotNull(FieldCreatedBy))
}

// CreatedByEqualFold applies the EqualFold predicate on the "created_by" field.
func CreatedByEqualFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEqualFold(FieldCreatedBy, v))
}

// CreatedByContainsFold applies the ContainsFold predicate on the "created_by" field.
func CreatedByContainsFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContainsFold(FieldCreatedBy, v))
}

// UpdatedByEQ applies the EQ predicate on the "updated_by" field.
func UpdatedByEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldUpdatedBy, v))
}

// UpdatedByNEQ applies the NEQ predicate on the "updated_by" field.
func UpdatedByNEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNEQ(FieldUpdatedBy, v))
}

// UpdatedByIn applies the In predicate on the "updated_by" field.
func UpdatedByIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIn(FieldUpdatedBy, vs...))
}

// UpdatedByNotIn applies the NotIn predicate on the "updated_by" field.
func UpdatedByNotIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotIn(FieldUpdatedBy, vs...))
}

// UpdatedByGT applies the GT predicate on the "updated_by" field.
func UpdatedByGT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGT(FieldUpdatedBy, v))
}

// UpdatedByGTE applies the GTE predicate on the "updated_by" field.
func UpdatedByGTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGTE(FieldUpdatedBy, v))
}

// UpdatedByLT applies the LT predicate on the "updated_by" field.
func UpdatedByLT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLT(FieldUpdatedBy, v))
}

// UpdatedByLTE applies the LTE predicate on the "updated_by" field.
func UpdatedByLTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLTE(FieldUpdatedBy, v))
}

// UpdatedByContains applies the Contains predicate on the "updated_by" field.
func UpdatedByContains(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContains(FieldUpdatedBy, v))
}

// UpdatedByHasPrefix applies the HasPrefix predicate on the "updated_by" field.
func UpdatedByHasPrefix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasPrefix(FieldUpdatedBy, v))
}

// UpdatedByHasSuffix applies the HasSuffix predicate on the "updated_by" field.
func UpdatedByHasSuffix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasSuffix(FieldUpdatedBy, v))
}

// UpdatedByIsNil applies the IsNil predicate on the "updated_by" field.
func UpdatedByIsNil() predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIsNull(FieldUpdatedBy))
}

// UpdatedByNotNil applies the NotNil predicate on the "updated_by" field.
func UpdatedByNotNil() predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotNull(FieldUpdatedBy))
}

// UpdatedByEqualFold applies the EqualFold predicate on the "updated_by" field.
func UpdatedByEqualFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEqualFold(FieldUpdatedBy, v))
}

// UpdatedByContainsFold applies the ContainsFold predicate on the "updated_by" field.
func UpdatedByContainsFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContainsFold(FieldUpdatedBy, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContainsFold(FieldRemark, v))
}

// ContractUrlsEQ applies the EQ predicate on the "contract_urls" field.
func ContractUrlsEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldContractUrls, v))
}

// ContractUrlsNEQ applies the NEQ predicate on the "contract_urls" field.
func ContractUrlsNEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNEQ(FieldContractUrls, v))
}

// ContractUrlsIn applies the In predicate on the "contract_urls" field.
func ContractUrlsIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIn(FieldContractUrls, vs...))
}

// ContractUrlsNotIn applies the NotIn predicate on the "contract_urls" field.
func ContractUrlsNotIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotIn(FieldContractUrls, vs...))
}

// ContractUrlsGT applies the GT predicate on the "contract_urls" field.
func ContractUrlsGT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGT(FieldContractUrls, v))
}

// ContractUrlsGTE applies the GTE predicate on the "contract_urls" field.
func ContractUrlsGTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGTE(FieldContractUrls, v))
}

// ContractUrlsLT applies the LT predicate on the "contract_urls" field.
func ContractUrlsLT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLT(FieldContractUrls, v))
}

// ContractUrlsLTE applies the LTE predicate on the "contract_urls" field.
func ContractUrlsLTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLTE(FieldContractUrls, v))
}

// ContractUrlsContains applies the Contains predicate on the "contract_urls" field.
func ContractUrlsContains(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContains(FieldContractUrls, v))
}

// ContractUrlsHasPrefix applies the HasPrefix predicate on the "contract_urls" field.
func ContractUrlsHasPrefix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasPrefix(FieldContractUrls, v))
}

// ContractUrlsHasSuffix applies the HasSuffix predicate on the "contract_urls" field.
func ContractUrlsHasSuffix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasSuffix(FieldContractUrls, v))
}

// ContractUrlsIsNil applies the IsNil predicate on the "contract_urls" field.
func ContractUrlsIsNil() predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIsNull(FieldContractUrls))
}

// ContractUrlsNotNil applies the NotNil predicate on the "contract_urls" field.
func ContractUrlsNotNil() predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotNull(FieldContractUrls))
}

// ContractUrlsEqualFold applies the EqualFold predicate on the "contract_urls" field.
func ContractUrlsEqualFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEqualFold(FieldContractUrls, v))
}

// ContractUrlsContainsFold applies the ContainsFold predicate on the "contract_urls" field.
func ContractUrlsContainsFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContainsFold(FieldContractUrls, v))
}

// InvoiceUrlsEQ applies the EQ predicate on the "invoice_urls" field.
func InvoiceUrlsEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldInvoiceUrls, v))
}

// InvoiceUrlsNEQ applies the NEQ predicate on the "invoice_urls" field.
func InvoiceUrlsNEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNEQ(FieldInvoiceUrls, v))
}

// InvoiceUrlsIn applies the In predicate on the "invoice_urls" field.
func InvoiceUrlsIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIn(FieldInvoiceUrls, vs...))
}

// InvoiceUrlsNotIn applies the NotIn predicate on the "invoice_urls" field.
func InvoiceUrlsNotIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotIn(FieldInvoiceUrls, vs...))
}

// InvoiceUrlsGT applies the GT predicate on the "invoice_urls" field.
func InvoiceUrlsGT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGT(FieldInvoiceUrls, v))
}

// InvoiceUrlsGTE applies the GTE predicate on the "invoice_urls" field.
func InvoiceUrlsGTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGTE(FieldInvoiceUrls, v))
}

// InvoiceUrlsLT applies the LT predicate on the "invoice_urls" field.
func InvoiceUrlsLT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLT(FieldInvoiceUrls, v))
}

// InvoiceUrlsLTE applies the LTE predicate on the "invoice_urls" field.
func InvoiceUrlsLTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLTE(FieldInvoiceUrls, v))
}

// InvoiceUrlsContains applies the Contains predicate on the "invoice_urls" field.
func InvoiceUrlsContains(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContains(FieldInvoiceUrls, v))
}

// InvoiceUrlsHasPrefix applies the HasPrefix predicate on the "invoice_urls" field.
func InvoiceUrlsHasPrefix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasPrefix(FieldInvoiceUrls, v))
}

// InvoiceUrlsHasSuffix applies the HasSuffix predicate on the "invoice_urls" field.
func InvoiceUrlsHasSuffix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasSuffix(FieldInvoiceUrls, v))
}

// InvoiceUrlsIsNil applies the IsNil predicate on the "invoice_urls" field.
func InvoiceUrlsIsNil() predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIsNull(FieldInvoiceUrls))
}

// InvoiceUrlsNotNil applies the NotNil predicate on the "invoice_urls" field.
func InvoiceUrlsNotNil() predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotNull(FieldInvoiceUrls))
}

// InvoiceUrlsEqualFold applies the EqualFold predicate on the "invoice_urls" field.
func InvoiceUrlsEqualFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEqualFold(FieldInvoiceUrls, v))
}

// InvoiceUrlsContainsFold applies the ContainsFold predicate on the "invoice_urls" field.
func InvoiceUrlsContainsFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContainsFold(FieldInvoiceUrls, v))
}

// AuditUrlsEQ applies the EQ predicate on the "audit_urls" field.
func AuditUrlsEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldAuditUrls, v))
}

// AuditUrlsNEQ applies the NEQ predicate on the "audit_urls" field.
func AuditUrlsNEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNEQ(FieldAuditUrls, v))
}

// AuditUrlsIn applies the In predicate on the "audit_urls" field.
func AuditUrlsIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIn(FieldAuditUrls, vs...))
}

// AuditUrlsNotIn applies the NotIn predicate on the "audit_urls" field.
func AuditUrlsNotIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotIn(FieldAuditUrls, vs...))
}

// AuditUrlsGT applies the GT predicate on the "audit_urls" field.
func AuditUrlsGT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGT(FieldAuditUrls, v))
}

// AuditUrlsGTE applies the GTE predicate on the "audit_urls" field.
func AuditUrlsGTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGTE(FieldAuditUrls, v))
}

// AuditUrlsLT applies the LT predicate on the "audit_urls" field.
func AuditUrlsLT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLT(FieldAuditUrls, v))
}

// AuditUrlsLTE applies the LTE predicate on the "audit_urls" field.
func AuditUrlsLTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLTE(FieldAuditUrls, v))
}

// AuditUrlsContains applies the Contains predicate on the "audit_urls" field.
func AuditUrlsContains(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContains(FieldAuditUrls, v))
}

// AuditUrlsHasPrefix applies the HasPrefix predicate on the "audit_urls" field.
func AuditUrlsHasPrefix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasPrefix(FieldAuditUrls, v))
}

// AuditUrlsHasSuffix applies the HasSuffix predicate on the "audit_urls" field.
func AuditUrlsHasSuffix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasSuffix(FieldAuditUrls, v))
}

// AuditUrlsIsNil applies the IsNil predicate on the "audit_urls" field.
func AuditUrlsIsNil() predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIsNull(FieldAuditUrls))
}

// AuditUrlsNotNil applies the NotNil predicate on the "audit_urls" field.
func AuditUrlsNotNil() predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotNull(FieldAuditUrls))
}

// AuditUrlsEqualFold applies the EqualFold predicate on the "audit_urls" field.
func AuditUrlsEqualFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEqualFold(FieldAuditUrls, v))
}

// AuditUrlsContainsFold applies the ContainsFold predicate on the "audit_urls" field.
func AuditUrlsContainsFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContainsFold(FieldAuditUrls, v))
}

// OtherUrlsEQ applies the EQ predicate on the "other_urls" field.
func OtherUrlsEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldOtherUrls, v))
}

// OtherUrlsNEQ applies the NEQ predicate on the "other_urls" field.
func OtherUrlsNEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNEQ(FieldOtherUrls, v))
}

// OtherUrlsIn applies the In predicate on the "other_urls" field.
func OtherUrlsIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIn(FieldOtherUrls, vs...))
}

// OtherUrlsNotIn applies the NotIn predicate on the "other_urls" field.
func OtherUrlsNotIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotIn(FieldOtherUrls, vs...))
}

// OtherUrlsGT applies the GT predicate on the "other_urls" field.
func OtherUrlsGT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGT(FieldOtherUrls, v))
}

// OtherUrlsGTE applies the GTE predicate on the "other_urls" field.
func OtherUrlsGTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGTE(FieldOtherUrls, v))
}

// OtherUrlsLT applies the LT predicate on the "other_urls" field.
func OtherUrlsLT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLT(FieldOtherUrls, v))
}

// OtherUrlsLTE applies the LTE predicate on the "other_urls" field.
func OtherUrlsLTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLTE(FieldOtherUrls, v))
}

// OtherUrlsContains applies the Contains predicate on the "other_urls" field.
func OtherUrlsContains(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContains(FieldOtherUrls, v))
}

// OtherUrlsHasPrefix applies the HasPrefix predicate on the "other_urls" field.
func OtherUrlsHasPrefix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasPrefix(FieldOtherUrls, v))
}

// OtherUrlsHasSuffix applies the HasSuffix predicate on the "other_urls" field.
func OtherUrlsHasSuffix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasSuffix(FieldOtherUrls, v))
}

// OtherUrlsIsNil applies the IsNil predicate on the "other_urls" field.
func OtherUrlsIsNil() predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIsNull(FieldOtherUrls))
}

// OtherUrlsNotNil applies the NotNil predicate on the "other_urls" field.
func OtherUrlsNotNil() predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotNull(FieldOtherUrls))
}

// OtherUrlsEqualFold applies the EqualFold predicate on the "other_urls" field.
func OtherUrlsEqualFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEqualFold(FieldOtherUrls, v))
}

// OtherUrlsContainsFold applies the ContainsFold predicate on the "other_urls" field.
func OtherUrlsContainsFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContainsFold(FieldOtherUrls, v))
}

// OrderNoEQ applies the EQ predicate on the "order_no" field.
func OrderNoEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldOrderNo, v))
}

// OrderNoNEQ applies the NEQ predicate on the "order_no" field.
func OrderNoNEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNEQ(FieldOrderNo, v))
}

// OrderNoIn applies the In predicate on the "order_no" field.
func OrderNoIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIn(FieldOrderNo, vs...))
}

// OrderNoNotIn applies the NotIn predicate on the "order_no" field.
func OrderNoNotIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotIn(FieldOrderNo, vs...))
}

// OrderNoGT applies the GT predicate on the "order_no" field.
func OrderNoGT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGT(FieldOrderNo, v))
}

// OrderNoGTE applies the GTE predicate on the "order_no" field.
func OrderNoGTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGTE(FieldOrderNo, v))
}

// OrderNoLT applies the LT predicate on the "order_no" field.
func OrderNoLT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLT(FieldOrderNo, v))
}

// OrderNoLTE applies the LTE predicate on the "order_no" field.
func OrderNoLTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLTE(FieldOrderNo, v))
}

// OrderNoContains applies the Contains predicate on the "order_no" field.
func OrderNoContains(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContains(FieldOrderNo, v))
}

// OrderNoHasPrefix applies the HasPrefix predicate on the "order_no" field.
func OrderNoHasPrefix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasPrefix(FieldOrderNo, v))
}

// OrderNoHasSuffix applies the HasSuffix predicate on the "order_no" field.
func OrderNoHasSuffix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasSuffix(FieldOrderNo, v))
}

// OrderNoEqualFold applies the EqualFold predicate on the "order_no" field.
func OrderNoEqualFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEqualFold(FieldOrderNo, v))
}

// OrderNoContainsFold applies the ContainsFold predicate on the "order_no" field.
func OrderNoContainsFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContainsFold(FieldOrderNo, v))
}

// RepositoryIDEQ applies the EQ predicate on the "repository_id" field.
func RepositoryIDEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldRepositoryID, v))
}

// RepositoryIDNEQ applies the NEQ predicate on the "repository_id" field.
func RepositoryIDNEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNEQ(FieldRepositoryID, v))
}

// RepositoryIDIn applies the In predicate on the "repository_id" field.
func RepositoryIDIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIn(FieldRepositoryID, vs...))
}

// RepositoryIDNotIn applies the NotIn predicate on the "repository_id" field.
func RepositoryIDNotIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotIn(FieldRepositoryID, vs...))
}

// RepositoryIDGT applies the GT predicate on the "repository_id" field.
func RepositoryIDGT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGT(FieldRepositoryID, v))
}

// RepositoryIDGTE applies the GTE predicate on the "repository_id" field.
func RepositoryIDGTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGTE(FieldRepositoryID, v))
}

// RepositoryIDLT applies the LT predicate on the "repository_id" field.
func RepositoryIDLT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLT(FieldRepositoryID, v))
}

// RepositoryIDLTE applies the LTE predicate on the "repository_id" field.
func RepositoryIDLTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLTE(FieldRepositoryID, v))
}

// RepositoryIDContains applies the Contains predicate on the "repository_id" field.
func RepositoryIDContains(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContains(FieldRepositoryID, v))
}

// RepositoryIDHasPrefix applies the HasPrefix predicate on the "repository_id" field.
func RepositoryIDHasPrefix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasPrefix(FieldRepositoryID, v))
}

// RepositoryIDHasSuffix applies the HasSuffix predicate on the "repository_id" field.
func RepositoryIDHasSuffix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasSuffix(FieldRepositoryID, v))
}

// RepositoryIDIsNil applies the IsNil predicate on the "repository_id" field.
func RepositoryIDIsNil() predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIsNull(FieldRepositoryID))
}

// RepositoryIDNotNil applies the NotNil predicate on the "repository_id" field.
func RepositoryIDNotNil() predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotNull(FieldRepositoryID))
}

// RepositoryIDEqualFold applies the EqualFold predicate on the "repository_id" field.
func RepositoryIDEqualFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEqualFold(FieldRepositoryID, v))
}

// RepositoryIDContainsFold applies the ContainsFold predicate on the "repository_id" field.
func RepositoryIDContainsFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContainsFold(FieldRepositoryID, v))
}

// ClaimTimeEQ applies the EQ predicate on the "claim_time" field.
func ClaimTimeEQ(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldClaimTime, v))
}

// ClaimTimeNEQ applies the NEQ predicate on the "claim_time" field.
func ClaimTimeNEQ(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNEQ(FieldClaimTime, v))
}

// ClaimTimeIn applies the In predicate on the "claim_time" field.
func ClaimTimeIn(vs ...time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIn(FieldClaimTime, vs...))
}

// ClaimTimeNotIn applies the NotIn predicate on the "claim_time" field.
func ClaimTimeNotIn(vs ...time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotIn(FieldClaimTime, vs...))
}

// ClaimTimeGT applies the GT predicate on the "claim_time" field.
func ClaimTimeGT(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGT(FieldClaimTime, v))
}

// ClaimTimeGTE applies the GTE predicate on the "claim_time" field.
func ClaimTimeGTE(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGTE(FieldClaimTime, v))
}

// ClaimTimeLT applies the LT predicate on the "claim_time" field.
func ClaimTimeLT(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLT(FieldClaimTime, v))
}

// ClaimTimeLTE applies the LTE predicate on the "claim_time" field.
func ClaimTimeLTE(v time.Time) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLTE(FieldClaimTime, v))
}

// ClaimUserIDEQ applies the EQ predicate on the "claim_user_id" field.
func ClaimUserIDEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldClaimUserID, v))
}

// ClaimUserIDNEQ applies the NEQ predicate on the "claim_user_id" field.
func ClaimUserIDNEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNEQ(FieldClaimUserID, v))
}

// ClaimUserIDIn applies the In predicate on the "claim_user_id" field.
func ClaimUserIDIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIn(FieldClaimUserID, vs...))
}

// ClaimUserIDNotIn applies the NotIn predicate on the "claim_user_id" field.
func ClaimUserIDNotIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotIn(FieldClaimUserID, vs...))
}

// ClaimUserIDGT applies the GT predicate on the "claim_user_id" field.
func ClaimUserIDGT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGT(FieldClaimUserID, v))
}

// ClaimUserIDGTE applies the GTE predicate on the "claim_user_id" field.
func ClaimUserIDGTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGTE(FieldClaimUserID, v))
}

// ClaimUserIDLT applies the LT predicate on the "claim_user_id" field.
func ClaimUserIDLT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLT(FieldClaimUserID, v))
}

// ClaimUserIDLTE applies the LTE predicate on the "claim_user_id" field.
func ClaimUserIDLTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLTE(FieldClaimUserID, v))
}

// ClaimUserIDContains applies the Contains predicate on the "claim_user_id" field.
func ClaimUserIDContains(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContains(FieldClaimUserID, v))
}

// ClaimUserIDHasPrefix applies the HasPrefix predicate on the "claim_user_id" field.
func ClaimUserIDHasPrefix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasPrefix(FieldClaimUserID, v))
}

// ClaimUserIDHasSuffix applies the HasSuffix predicate on the "claim_user_id" field.
func ClaimUserIDHasSuffix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasSuffix(FieldClaimUserID, v))
}

// ClaimUserIDEqualFold applies the EqualFold predicate on the "claim_user_id" field.
func ClaimUserIDEqualFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEqualFold(FieldClaimUserID, v))
}

// ClaimUserIDContainsFold applies the ContainsFold predicate on the "claim_user_id" field.
func ClaimUserIDContainsFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContainsFold(FieldClaimUserID, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLTE(FieldStatus, v))
}

// StatusContains applies the Contains predicate on the "status" field.
func StatusContains(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContains(FieldStatus, v))
}

// StatusHasPrefix applies the HasPrefix predicate on the "status" field.
func StatusHasPrefix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasPrefix(FieldStatus, v))
}

// StatusHasSuffix applies the HasSuffix predicate on the "status" field.
func StatusHasSuffix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasSuffix(FieldStatus, v))
}

// StatusEqualFold applies the EqualFold predicate on the "status" field.
func StatusEqualFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEqualFold(FieldStatus, v))
}

// StatusContainsFold applies the ContainsFold predicate on the "status" field.
func StatusContainsFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContainsFold(FieldStatus, v))
}

// EquipmentNumEQ applies the EQ predicate on the "equipment_num" field.
func EquipmentNumEQ(v uint32) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldEquipmentNum, v))
}

// EquipmentNumNEQ applies the NEQ predicate on the "equipment_num" field.
func EquipmentNumNEQ(v uint32) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNEQ(FieldEquipmentNum, v))
}

// EquipmentNumIn applies the In predicate on the "equipment_num" field.
func EquipmentNumIn(vs ...uint32) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIn(FieldEquipmentNum, vs...))
}

// EquipmentNumNotIn applies the NotIn predicate on the "equipment_num" field.
func EquipmentNumNotIn(vs ...uint32) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotIn(FieldEquipmentNum, vs...))
}

// EquipmentNumGT applies the GT predicate on the "equipment_num" field.
func EquipmentNumGT(v uint32) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGT(FieldEquipmentNum, v))
}

// EquipmentNumGTE applies the GTE predicate on the "equipment_num" field.
func EquipmentNumGTE(v uint32) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGTE(FieldEquipmentNum, v))
}

// EquipmentNumLT applies the LT predicate on the "equipment_num" field.
func EquipmentNumLT(v uint32) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLT(FieldEquipmentNum, v))
}

// EquipmentNumLTE applies the LTE predicate on the "equipment_num" field.
func EquipmentNumLTE(v uint32) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLTE(FieldEquipmentNum, v))
}

// EquipmentNumIsNil applies the IsNil predicate on the "equipment_num" field.
func EquipmentNumIsNil() predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIsNull(FieldEquipmentNum))
}

// EquipmentNumNotNil applies the NotNil predicate on the "equipment_num" field.
func EquipmentNumNotNil() predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotNull(FieldEquipmentNum))
}

// ReasonEQ applies the EQ predicate on the "reason" field.
func ReasonEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEQ(FieldReason, v))
}

// ReasonNEQ applies the NEQ predicate on the "reason" field.
func ReasonNEQ(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNEQ(FieldReason, v))
}

// ReasonIn applies the In predicate on the "reason" field.
func ReasonIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIn(FieldReason, vs...))
}

// ReasonNotIn applies the NotIn predicate on the "reason" field.
func ReasonNotIn(vs ...string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotIn(FieldReason, vs...))
}

// ReasonGT applies the GT predicate on the "reason" field.
func ReasonGT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGT(FieldReason, v))
}

// ReasonGTE applies the GTE predicate on the "reason" field.
func ReasonGTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldGTE(FieldReason, v))
}

// ReasonLT applies the LT predicate on the "reason" field.
func ReasonLT(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLT(FieldReason, v))
}

// ReasonLTE applies the LTE predicate on the "reason" field.
func ReasonLTE(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldLTE(FieldReason, v))
}

// ReasonContains applies the Contains predicate on the "reason" field.
func ReasonContains(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContains(FieldReason, v))
}

// ReasonHasPrefix applies the HasPrefix predicate on the "reason" field.
func ReasonHasPrefix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasPrefix(FieldReason, v))
}

// ReasonHasSuffix applies the HasSuffix predicate on the "reason" field.
func ReasonHasSuffix(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldHasSuffix(FieldReason, v))
}

// ReasonIsNil applies the IsNil predicate on the "reason" field.
func ReasonIsNil() predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldIsNull(FieldReason))
}

// ReasonNotNil applies the NotNil predicate on the "reason" field.
func ReasonNotNil() predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldNotNull(FieldReason))
}

// ReasonEqualFold applies the EqualFold predicate on the "reason" field.
func ReasonEqualFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldEqualFold(FieldReason, v))
}

// ReasonContainsFold applies the ContainsFold predicate on the "reason" field.
func ReasonContainsFold(v string) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.FieldContainsFold(FieldReason, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.WmsClaimOrder) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.WmsClaimOrder) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.WmsClaimOrder) predicate.WmsClaimOrder {
	return predicate.WmsClaimOrder(sql.NotPredicates(p))
}
