// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-mono-demo/internal/data/ent/wmsreturnorderdetail"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WmsReturnOrderDetailCreate is the builder for creating a WmsReturnOrderDetail entity.
type WmsReturnOrderDetailCreate struct {
	config
	mutation *WmsReturnOrderDetailMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (wrodc *WmsReturnOrderDetailCreate) SetCreatedAt(t time.Time) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetCreatedAt(t)
	return wrodc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (wrodc *WmsReturnOrderDetailCreate) SetNillableCreatedAt(t *time.Time) *WmsReturnOrderDetailCreate {
	if t != nil {
		wrodc.SetCreatedAt(*t)
	}
	return wrodc
}

// SetUpdatedAt sets the "updated_at" field.
func (wrodc *WmsReturnOrderDetailCreate) SetUpdatedAt(t time.Time) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetUpdatedAt(t)
	return wrodc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (wrodc *WmsReturnOrderDetailCreate) SetNillableUpdatedAt(t *time.Time) *WmsReturnOrderDetailCreate {
	if t != nil {
		wrodc.SetUpdatedAt(*t)
	}
	return wrodc
}

// SetCreatedBy sets the "created_by" field.
func (wrodc *WmsReturnOrderDetailCreate) SetCreatedBy(s string) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetCreatedBy(s)
	return wrodc
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (wrodc *WmsReturnOrderDetailCreate) SetNillableCreatedBy(s *string) *WmsReturnOrderDetailCreate {
	if s != nil {
		wrodc.SetCreatedBy(*s)
	}
	return wrodc
}

// SetUpdatedBy sets the "updated_by" field.
func (wrodc *WmsReturnOrderDetailCreate) SetUpdatedBy(s string) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetUpdatedBy(s)
	return wrodc
}

// SetNillableUpdatedBy sets the "updated_by" field if the given value is not nil.
func (wrodc *WmsReturnOrderDetailCreate) SetNillableUpdatedBy(s *string) *WmsReturnOrderDetailCreate {
	if s != nil {
		wrodc.SetUpdatedBy(*s)
	}
	return wrodc
}

// SetRemark sets the "remark" field.
func (wrodc *WmsReturnOrderDetailCreate) SetRemark(s string) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetRemark(s)
	return wrodc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (wrodc *WmsReturnOrderDetailCreate) SetNillableRemark(s *string) *WmsReturnOrderDetailCreate {
	if s != nil {
		wrodc.SetRemark(*s)
	}
	return wrodc
}

// SetOrderID sets the "order_id" field.
func (wrodc *WmsReturnOrderDetailCreate) SetOrderID(s string) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetOrderID(s)
	return wrodc
}

// SetNillableOrderID sets the "order_id" field if the given value is not nil.
func (wrodc *WmsReturnOrderDetailCreate) SetNillableOrderID(s *string) *WmsReturnOrderDetailCreate {
	if s != nil {
		wrodc.SetOrderID(*s)
	}
	return wrodc
}

// SetMaterialID sets the "material_id" field.
func (wrodc *WmsReturnOrderDetailCreate) SetMaterialID(s string) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetMaterialID(s)
	return wrodc
}

// SetNillableMaterialID sets the "material_id" field if the given value is not nil.
func (wrodc *WmsReturnOrderDetailCreate) SetNillableMaterialID(s *string) *WmsReturnOrderDetailCreate {
	if s != nil {
		wrodc.SetMaterialID(*s)
	}
	return wrodc
}

// SetMaterialName sets the "material_name" field.
func (wrodc *WmsReturnOrderDetailCreate) SetMaterialName(s string) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetMaterialName(s)
	return wrodc
}

// SetNillableMaterialName sets the "material_name" field if the given value is not nil.
func (wrodc *WmsReturnOrderDetailCreate) SetNillableMaterialName(s *string) *WmsReturnOrderDetailCreate {
	if s != nil {
		wrodc.SetMaterialName(*s)
	}
	return wrodc
}

// SetCode sets the "code" field.
func (wrodc *WmsReturnOrderDetailCreate) SetCode(s string) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetCode(s)
	return wrodc
}

// SetEquipmentID sets the "equipment_id" field.
func (wrodc *WmsReturnOrderDetailCreate) SetEquipmentID(s string) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetEquipmentID(s)
	return wrodc
}

// SetNillableEquipmentID sets the "equipment_id" field if the given value is not nil.
func (wrodc *WmsReturnOrderDetailCreate) SetNillableEquipmentID(s *string) *WmsReturnOrderDetailCreate {
	if s != nil {
		wrodc.SetEquipmentID(*s)
	}
	return wrodc
}

// SetEquipmentTypeID sets the "equipment_type_id" field.
func (wrodc *WmsReturnOrderDetailCreate) SetEquipmentTypeID(s string) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetEquipmentTypeID(s)
	return wrodc
}

// SetNillableEquipmentTypeID sets the "equipment_type_id" field if the given value is not nil.
func (wrodc *WmsReturnOrderDetailCreate) SetNillableEquipmentTypeID(s *string) *WmsReturnOrderDetailCreate {
	if s != nil {
		wrodc.SetEquipmentTypeID(*s)
	}
	return wrodc
}

// SetFeature sets the "feature" field.
func (wrodc *WmsReturnOrderDetailCreate) SetFeature(m map[string]interface{}) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetFeature(m)
	return wrodc
}

// SetOwnerID sets the "owner_id" field.
func (wrodc *WmsReturnOrderDetailCreate) SetOwnerID(s string) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetOwnerID(s)
	return wrodc
}

// SetNillableOwnerID sets the "owner_id" field if the given value is not nil.
func (wrodc *WmsReturnOrderDetailCreate) SetNillableOwnerID(s *string) *WmsReturnOrderDetailCreate {
	if s != nil {
		wrodc.SetOwnerID(*s)
	}
	return wrodc
}

// SetModelNo sets the "ModelNo" field.
func (wrodc *WmsReturnOrderDetailCreate) SetModelNo(s string) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetModelNo(s)
	return wrodc
}

// SetMeasureUnitID sets the "measure_unit_id" field.
func (wrodc *WmsReturnOrderDetailCreate) SetMeasureUnitID(s string) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetMeasureUnitID(s)
	return wrodc
}

// SetNillableMeasureUnitID sets the "measure_unit_id" field if the given value is not nil.
func (wrodc *WmsReturnOrderDetailCreate) SetNillableMeasureUnitID(s *string) *WmsReturnOrderDetailCreate {
	if s != nil {
		wrodc.SetMeasureUnitID(*s)
	}
	return wrodc
}

// SetNum sets the "num" field.
func (wrodc *WmsReturnOrderDetailCreate) SetNum(u uint32) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetNum(u)
	return wrodc
}

// SetToRepositoryID sets the "to_repository_id" field.
func (wrodc *WmsReturnOrderDetailCreate) SetToRepositoryID(s string) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetToRepositoryID(s)
	return wrodc
}

// SetNillableToRepositoryID sets the "to_repository_id" field if the given value is not nil.
func (wrodc *WmsReturnOrderDetailCreate) SetNillableToRepositoryID(s *string) *WmsReturnOrderDetailCreate {
	if s != nil {
		wrodc.SetToRepositoryID(*s)
	}
	return wrodc
}

// SetToRepositoryAreaID sets the "to_repository_area_id" field.
func (wrodc *WmsReturnOrderDetailCreate) SetToRepositoryAreaID(s string) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetToRepositoryAreaID(s)
	return wrodc
}

// SetNillableToRepositoryAreaID sets the "to_repository_area_id" field if the given value is not nil.
func (wrodc *WmsReturnOrderDetailCreate) SetNillableToRepositoryAreaID(s *string) *WmsReturnOrderDetailCreate {
	if s != nil {
		wrodc.SetToRepositoryAreaID(*s)
	}
	return wrodc
}

// SetToRepositoryPositionID sets the "to_repository_position_id" field.
func (wrodc *WmsReturnOrderDetailCreate) SetToRepositoryPositionID(s string) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetToRepositoryPositionID(s)
	return wrodc
}

// SetNillableToRepositoryPositionID sets the "to_repository_position_id" field if the given value is not nil.
func (wrodc *WmsReturnOrderDetailCreate) SetNillableToRepositoryPositionID(s *string) *WmsReturnOrderDetailCreate {
	if s != nil {
		wrodc.SetToRepositoryPositionID(*s)
	}
	return wrodc
}

// SetReason sets the "reason" field.
func (wrodc *WmsReturnOrderDetailCreate) SetReason(s string) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetReason(s)
	return wrodc
}

// SetNillableReason sets the "reason" field if the given value is not nil.
func (wrodc *WmsReturnOrderDetailCreate) SetNillableReason(s *string) *WmsReturnOrderDetailCreate {
	if s != nil {
		wrodc.SetReason(*s)
	}
	return wrodc
}

// SetReturnTime sets the "return_time" field.
func (wrodc *WmsReturnOrderDetailCreate) SetReturnTime(t time.Time) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetReturnTime(t)
	return wrodc
}

// SetNillableReturnTime sets the "return_time" field if the given value is not nil.
func (wrodc *WmsReturnOrderDetailCreate) SetNillableReturnTime(t *time.Time) *WmsReturnOrderDetailCreate {
	if t != nil {
		wrodc.SetReturnTime(*t)
	}
	return wrodc
}

// SetID sets the "id" field.
func (wrodc *WmsReturnOrderDetailCreate) SetID(s string) *WmsReturnOrderDetailCreate {
	wrodc.mutation.SetID(s)
	return wrodc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (wrodc *WmsReturnOrderDetailCreate) SetNillableID(s *string) *WmsReturnOrderDetailCreate {
	if s != nil {
		wrodc.SetID(*s)
	}
	return wrodc
}

// Mutation returns the WmsReturnOrderDetailMutation object of the builder.
func (wrodc *WmsReturnOrderDetailCreate) Mutation() *WmsReturnOrderDetailMutation {
	return wrodc.mutation
}

// Save creates the WmsReturnOrderDetail in the database.
func (wrodc *WmsReturnOrderDetailCreate) Save(ctx context.Context) (*WmsReturnOrderDetail, error) {
	wrodc.defaults()
	return withHooks(ctx, wrodc.sqlSave, wrodc.mutation, wrodc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (wrodc *WmsReturnOrderDetailCreate) SaveX(ctx context.Context) *WmsReturnOrderDetail {
	v, err := wrodc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (wrodc *WmsReturnOrderDetailCreate) Exec(ctx context.Context) error {
	_, err := wrodc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wrodc *WmsReturnOrderDetailCreate) ExecX(ctx context.Context) {
	if err := wrodc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wrodc *WmsReturnOrderDetailCreate) defaults() {
	if _, ok := wrodc.mutation.CreatedAt(); !ok {
		v := wmsreturnorderdetail.DefaultCreatedAt
		wrodc.mutation.SetCreatedAt(v)
	}
	if _, ok := wrodc.mutation.UpdatedAt(); !ok {
		v := wmsreturnorderdetail.DefaultUpdatedAt()
		wrodc.mutation.SetUpdatedAt(v)
	}
	if _, ok := wrodc.mutation.Feature(); !ok {
		v := wmsreturnorderdetail.DefaultFeature
		wrodc.mutation.SetFeature(v)
	}
	if _, ok := wrodc.mutation.ID(); !ok {
		v := wmsreturnorderdetail.DefaultID()
		wrodc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (wrodc *WmsReturnOrderDetailCreate) check() error {
	if _, ok := wrodc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "WmsReturnOrderDetail.created_at"`)}
	}
	if _, ok := wrodc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "WmsReturnOrderDetail.updated_at"`)}
	}
	if _, ok := wrodc.mutation.Code(); !ok {
		return &ValidationError{Name: "code", err: errors.New(`ent: missing required field "WmsReturnOrderDetail.code"`)}
	}
	if _, ok := wrodc.mutation.ModelNo(); !ok {
		return &ValidationError{Name: "ModelNo", err: errors.New(`ent: missing required field "WmsReturnOrderDetail.ModelNo"`)}
	}
	if _, ok := wrodc.mutation.Num(); !ok {
		return &ValidationError{Name: "num", err: errors.New(`ent: missing required field "WmsReturnOrderDetail.num"`)}
	}
	return nil
}

func (wrodc *WmsReturnOrderDetailCreate) sqlSave(ctx context.Context) (*WmsReturnOrderDetail, error) {
	if err := wrodc.check(); err != nil {
		return nil, err
	}
	_node, _spec := wrodc.createSpec()
	if err := sqlgraph.CreateNode(ctx, wrodc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected WmsReturnOrderDetail.ID type: %T", _spec.ID.Value)
		}
	}
	wrodc.mutation.id = &_node.ID
	wrodc.mutation.done = true
	return _node, nil
}

func (wrodc *WmsReturnOrderDetailCreate) createSpec() (*WmsReturnOrderDetail, *sqlgraph.CreateSpec) {
	var (
		_node = &WmsReturnOrderDetail{config: wrodc.config}
		_spec = sqlgraph.NewCreateSpec(wmsreturnorderdetail.Table, sqlgraph.NewFieldSpec(wmsreturnorderdetail.FieldID, field.TypeString))
	)
	_spec.OnConflict = wrodc.conflict
	if id, ok := wrodc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := wrodc.mutation.CreatedAt(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := wrodc.mutation.UpdatedAt(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := wrodc.mutation.CreatedBy(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldCreatedBy, field.TypeString, value)
		_node.CreatedBy = value
	}
	if value, ok := wrodc.mutation.UpdatedBy(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldUpdatedBy, field.TypeString, value)
		_node.UpdatedBy = &value
	}
	if value, ok := wrodc.mutation.Remark(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := wrodc.mutation.OrderID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldOrderID, field.TypeString, value)
		_node.OrderID = value
	}
	if value, ok := wrodc.mutation.MaterialID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldMaterialID, field.TypeString, value)
		_node.MaterialID = value
	}
	if value, ok := wrodc.mutation.MaterialName(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldMaterialName, field.TypeString, value)
		_node.MaterialName = value
	}
	if value, ok := wrodc.mutation.Code(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldCode, field.TypeString, value)
		_node.Code = value
	}
	if value, ok := wrodc.mutation.EquipmentID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldEquipmentID, field.TypeString, value)
		_node.EquipmentID = value
	}
	if value, ok := wrodc.mutation.EquipmentTypeID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldEquipmentTypeID, field.TypeString, value)
		_node.EquipmentTypeID = value
	}
	if value, ok := wrodc.mutation.Feature(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldFeature, field.TypeJSON, value)
		_node.Feature = value
	}
	if value, ok := wrodc.mutation.OwnerID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldOwnerID, field.TypeString, value)
		_node.OwnerID = &value
	}
	if value, ok := wrodc.mutation.ModelNo(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldModelNo, field.TypeString, value)
		_node.ModelNo = value
	}
	if value, ok := wrodc.mutation.MeasureUnitID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldMeasureUnitID, field.TypeString, value)
		_node.MeasureUnitID = value
	}
	if value, ok := wrodc.mutation.Num(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldNum, field.TypeUint32, value)
		_node.Num = value
	}
	if value, ok := wrodc.mutation.ToRepositoryID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldToRepositoryID, field.TypeString, value)
		_node.ToRepositoryID = &value
	}
	if value, ok := wrodc.mutation.ToRepositoryAreaID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldToRepositoryAreaID, field.TypeString, value)
		_node.ToRepositoryAreaID = &value
	}
	if value, ok := wrodc.mutation.ToRepositoryPositionID(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldToRepositoryPositionID, field.TypeString, value)
		_node.ToRepositoryPositionID = &value
	}
	if value, ok := wrodc.mutation.Reason(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldReason, field.TypeString, value)
		_node.Reason = &value
	}
	if value, ok := wrodc.mutation.ReturnTime(); ok {
		_spec.SetField(wmsreturnorderdetail.FieldReturnTime, field.TypeTime, value)
		_node.ReturnTime = &value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.WmsReturnOrderDetail.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.WmsReturnOrderDetailUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (wrodc *WmsReturnOrderDetailCreate) OnConflict(opts ...sql.ConflictOption) *WmsReturnOrderDetailUpsertOne {
	wrodc.conflict = opts
	return &WmsReturnOrderDetailUpsertOne{
		create: wrodc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.WmsReturnOrderDetail.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (wrodc *WmsReturnOrderDetailCreate) OnConflictColumns(columns ...string) *WmsReturnOrderDetailUpsertOne {
	wrodc.conflict = append(wrodc.conflict, sql.ConflictColumns(columns...))
	return &WmsReturnOrderDetailUpsertOne{
		create: wrodc,
	}
}

type (
	// WmsReturnOrderDetailUpsertOne is the builder for "upsert"-ing
	//  one WmsReturnOrderDetail node.
	WmsReturnOrderDetailUpsertOne struct {
		create *WmsReturnOrderDetailCreate
	}

	// WmsReturnOrderDetailUpsert is the "OnConflict" setter.
	WmsReturnOrderDetailUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *WmsReturnOrderDetailUpsert) SetUpdatedAt(v time.Time) *WmsReturnOrderDetailUpsert {
	u.Set(wmsreturnorderdetail.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsert) UpdateUpdatedAt() *WmsReturnOrderDetailUpsert {
	u.SetExcluded(wmsreturnorderdetail.FieldUpdatedAt)
	return u
}

// SetCreatedBy sets the "created_by" field.
func (u *WmsReturnOrderDetailUpsert) SetCreatedBy(v string) *WmsReturnOrderDetailUpsert {
	u.Set(wmsreturnorderdetail.FieldCreatedBy, v)
	return u
}

// UpdateCreatedBy sets the "created_by" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsert) UpdateCreatedBy() *WmsReturnOrderDetailUpsert {
	u.SetExcluded(wmsreturnorderdetail.FieldCreatedBy)
	return u
}

// ClearCreatedBy clears the value of the "created_by" field.
func (u *WmsReturnOrderDetailUpsert) ClearCreatedBy() *WmsReturnOrderDetailUpsert {
	u.SetNull(wmsreturnorderdetail.FieldCreatedBy)
	return u
}

// SetUpdatedBy sets the "updated_by" field.
func (u *WmsReturnOrderDetailUpsert) SetUpdatedBy(v string) *WmsReturnOrderDetailUpsert {
	u.Set(wmsreturnorderdetail.FieldUpdatedBy, v)
	return u
}

// UpdateUpdatedBy sets the "updated_by" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsert) UpdateUpdatedBy() *WmsReturnOrderDetailUpsert {
	u.SetExcluded(wmsreturnorderdetail.FieldUpdatedBy)
	return u
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (u *WmsReturnOrderDetailUpsert) ClearUpdatedBy() *WmsReturnOrderDetailUpsert {
	u.SetNull(wmsreturnorderdetail.FieldUpdatedBy)
	return u
}

// SetRemark sets the "remark" field.
func (u *WmsReturnOrderDetailUpsert) SetRemark(v string) *WmsReturnOrderDetailUpsert {
	u.Set(wmsreturnorderdetail.FieldRemark, v)
	return u
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsert) UpdateRemark() *WmsReturnOrderDetailUpsert {
	u.SetExcluded(wmsreturnorderdetail.FieldRemark)
	return u
}

// ClearRemark clears the value of the "remark" field.
func (u *WmsReturnOrderDetailUpsert) ClearRemark() *WmsReturnOrderDetailUpsert {
	u.SetNull(wmsreturnorderdetail.FieldRemark)
	return u
}

// SetOrderID sets the "order_id" field.
func (u *WmsReturnOrderDetailUpsert) SetOrderID(v string) *WmsReturnOrderDetailUpsert {
	u.Set(wmsreturnorderdetail.FieldOrderID, v)
	return u
}

// UpdateOrderID sets the "order_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsert) UpdateOrderID() *WmsReturnOrderDetailUpsert {
	u.SetExcluded(wmsreturnorderdetail.FieldOrderID)
	return u
}

// ClearOrderID clears the value of the "order_id" field.
func (u *WmsReturnOrderDetailUpsert) ClearOrderID() *WmsReturnOrderDetailUpsert {
	u.SetNull(wmsreturnorderdetail.FieldOrderID)
	return u
}

// SetMaterialID sets the "material_id" field.
func (u *WmsReturnOrderDetailUpsert) SetMaterialID(v string) *WmsReturnOrderDetailUpsert {
	u.Set(wmsreturnorderdetail.FieldMaterialID, v)
	return u
}

// UpdateMaterialID sets the "material_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsert) UpdateMaterialID() *WmsReturnOrderDetailUpsert {
	u.SetExcluded(wmsreturnorderdetail.FieldMaterialID)
	return u
}

// ClearMaterialID clears the value of the "material_id" field.
func (u *WmsReturnOrderDetailUpsert) ClearMaterialID() *WmsReturnOrderDetailUpsert {
	u.SetNull(wmsreturnorderdetail.FieldMaterialID)
	return u
}

// SetMaterialName sets the "material_name" field.
func (u *WmsReturnOrderDetailUpsert) SetMaterialName(v string) *WmsReturnOrderDetailUpsert {
	u.Set(wmsreturnorderdetail.FieldMaterialName, v)
	return u
}

// UpdateMaterialName sets the "material_name" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsert) UpdateMaterialName() *WmsReturnOrderDetailUpsert {
	u.SetExcluded(wmsreturnorderdetail.FieldMaterialName)
	return u
}

// ClearMaterialName clears the value of the "material_name" field.
func (u *WmsReturnOrderDetailUpsert) ClearMaterialName() *WmsReturnOrderDetailUpsert {
	u.SetNull(wmsreturnorderdetail.FieldMaterialName)
	return u
}

// SetCode sets the "code" field.
func (u *WmsReturnOrderDetailUpsert) SetCode(v string) *WmsReturnOrderDetailUpsert {
	u.Set(wmsreturnorderdetail.FieldCode, v)
	return u
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsert) UpdateCode() *WmsReturnOrderDetailUpsert {
	u.SetExcluded(wmsreturnorderdetail.FieldCode)
	return u
}

// SetEquipmentID sets the "equipment_id" field.
func (u *WmsReturnOrderDetailUpsert) SetEquipmentID(v string) *WmsReturnOrderDetailUpsert {
	u.Set(wmsreturnorderdetail.FieldEquipmentID, v)
	return u
}

// UpdateEquipmentID sets the "equipment_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsert) UpdateEquipmentID() *WmsReturnOrderDetailUpsert {
	u.SetExcluded(wmsreturnorderdetail.FieldEquipmentID)
	return u
}

// ClearEquipmentID clears the value of the "equipment_id" field.
func (u *WmsReturnOrderDetailUpsert) ClearEquipmentID() *WmsReturnOrderDetailUpsert {
	u.SetNull(wmsreturnorderdetail.FieldEquipmentID)
	return u
}

// SetEquipmentTypeID sets the "equipment_type_id" field.
func (u *WmsReturnOrderDetailUpsert) SetEquipmentTypeID(v string) *WmsReturnOrderDetailUpsert {
	u.Set(wmsreturnorderdetail.FieldEquipmentTypeID, v)
	return u
}

// UpdateEquipmentTypeID sets the "equipment_type_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsert) UpdateEquipmentTypeID() *WmsReturnOrderDetailUpsert {
	u.SetExcluded(wmsreturnorderdetail.FieldEquipmentTypeID)
	return u
}

// ClearEquipmentTypeID clears the value of the "equipment_type_id" field.
func (u *WmsReturnOrderDetailUpsert) ClearEquipmentTypeID() *WmsReturnOrderDetailUpsert {
	u.SetNull(wmsreturnorderdetail.FieldEquipmentTypeID)
	return u
}

// SetFeature sets the "feature" field.
func (u *WmsReturnOrderDetailUpsert) SetFeature(v map[string]interface{}) *WmsReturnOrderDetailUpsert {
	u.Set(wmsreturnorderdetail.FieldFeature, v)
	return u
}

// UpdateFeature sets the "feature" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsert) UpdateFeature() *WmsReturnOrderDetailUpsert {
	u.SetExcluded(wmsreturnorderdetail.FieldFeature)
	return u
}

// ClearFeature clears the value of the "feature" field.
func (u *WmsReturnOrderDetailUpsert) ClearFeature() *WmsReturnOrderDetailUpsert {
	u.SetNull(wmsreturnorderdetail.FieldFeature)
	return u
}

// SetOwnerID sets the "owner_id" field.
func (u *WmsReturnOrderDetailUpsert) SetOwnerID(v string) *WmsReturnOrderDetailUpsert {
	u.Set(wmsreturnorderdetail.FieldOwnerID, v)
	return u
}

// UpdateOwnerID sets the "owner_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsert) UpdateOwnerID() *WmsReturnOrderDetailUpsert {
	u.SetExcluded(wmsreturnorderdetail.FieldOwnerID)
	return u
}

// ClearOwnerID clears the value of the "owner_id" field.
func (u *WmsReturnOrderDetailUpsert) ClearOwnerID() *WmsReturnOrderDetailUpsert {
	u.SetNull(wmsreturnorderdetail.FieldOwnerID)
	return u
}

// SetModelNo sets the "ModelNo" field.
func (u *WmsReturnOrderDetailUpsert) SetModelNo(v string) *WmsReturnOrderDetailUpsert {
	u.Set(wmsreturnorderdetail.FieldModelNo, v)
	return u
}

// UpdateModelNo sets the "ModelNo" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsert) UpdateModelNo() *WmsReturnOrderDetailUpsert {
	u.SetExcluded(wmsreturnorderdetail.FieldModelNo)
	return u
}

// SetMeasureUnitID sets the "measure_unit_id" field.
func (u *WmsReturnOrderDetailUpsert) SetMeasureUnitID(v string) *WmsReturnOrderDetailUpsert {
	u.Set(wmsreturnorderdetail.FieldMeasureUnitID, v)
	return u
}

// UpdateMeasureUnitID sets the "measure_unit_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsert) UpdateMeasureUnitID() *WmsReturnOrderDetailUpsert {
	u.SetExcluded(wmsreturnorderdetail.FieldMeasureUnitID)
	return u
}

// ClearMeasureUnitID clears the value of the "measure_unit_id" field.
func (u *WmsReturnOrderDetailUpsert) ClearMeasureUnitID() *WmsReturnOrderDetailUpsert {
	u.SetNull(wmsreturnorderdetail.FieldMeasureUnitID)
	return u
}

// SetNum sets the "num" field.
func (u *WmsReturnOrderDetailUpsert) SetNum(v uint32) *WmsReturnOrderDetailUpsert {
	u.Set(wmsreturnorderdetail.FieldNum, v)
	return u
}

// UpdateNum sets the "num" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsert) UpdateNum() *WmsReturnOrderDetailUpsert {
	u.SetExcluded(wmsreturnorderdetail.FieldNum)
	return u
}

// AddNum adds v to the "num" field.
func (u *WmsReturnOrderDetailUpsert) AddNum(v uint32) *WmsReturnOrderDetailUpsert {
	u.Add(wmsreturnorderdetail.FieldNum, v)
	return u
}

// SetToRepositoryID sets the "to_repository_id" field.
func (u *WmsReturnOrderDetailUpsert) SetToRepositoryID(v string) *WmsReturnOrderDetailUpsert {
	u.Set(wmsreturnorderdetail.FieldToRepositoryID, v)
	return u
}

// UpdateToRepositoryID sets the "to_repository_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsert) UpdateToRepositoryID() *WmsReturnOrderDetailUpsert {
	u.SetExcluded(wmsreturnorderdetail.FieldToRepositoryID)
	return u
}

// ClearToRepositoryID clears the value of the "to_repository_id" field.
func (u *WmsReturnOrderDetailUpsert) ClearToRepositoryID() *WmsReturnOrderDetailUpsert {
	u.SetNull(wmsreturnorderdetail.FieldToRepositoryID)
	return u
}

// SetToRepositoryAreaID sets the "to_repository_area_id" field.
func (u *WmsReturnOrderDetailUpsert) SetToRepositoryAreaID(v string) *WmsReturnOrderDetailUpsert {
	u.Set(wmsreturnorderdetail.FieldToRepositoryAreaID, v)
	return u
}

// UpdateToRepositoryAreaID sets the "to_repository_area_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsert) UpdateToRepositoryAreaID() *WmsReturnOrderDetailUpsert {
	u.SetExcluded(wmsreturnorderdetail.FieldToRepositoryAreaID)
	return u
}

// ClearToRepositoryAreaID clears the value of the "to_repository_area_id" field.
func (u *WmsReturnOrderDetailUpsert) ClearToRepositoryAreaID() *WmsReturnOrderDetailUpsert {
	u.SetNull(wmsreturnorderdetail.FieldToRepositoryAreaID)
	return u
}

// SetToRepositoryPositionID sets the "to_repository_position_id" field.
func (u *WmsReturnOrderDetailUpsert) SetToRepositoryPositionID(v string) *WmsReturnOrderDetailUpsert {
	u.Set(wmsreturnorderdetail.FieldToRepositoryPositionID, v)
	return u
}

// UpdateToRepositoryPositionID sets the "to_repository_position_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsert) UpdateToRepositoryPositionID() *WmsReturnOrderDetailUpsert {
	u.SetExcluded(wmsreturnorderdetail.FieldToRepositoryPositionID)
	return u
}

// ClearToRepositoryPositionID clears the value of the "to_repository_position_id" field.
func (u *WmsReturnOrderDetailUpsert) ClearToRepositoryPositionID() *WmsReturnOrderDetailUpsert {
	u.SetNull(wmsreturnorderdetail.FieldToRepositoryPositionID)
	return u
}

// SetReason sets the "reason" field.
func (u *WmsReturnOrderDetailUpsert) SetReason(v string) *WmsReturnOrderDetailUpsert {
	u.Set(wmsreturnorderdetail.FieldReason, v)
	return u
}

// UpdateReason sets the "reason" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsert) UpdateReason() *WmsReturnOrderDetailUpsert {
	u.SetExcluded(wmsreturnorderdetail.FieldReason)
	return u
}

// ClearReason clears the value of the "reason" field.
func (u *WmsReturnOrderDetailUpsert) ClearReason() *WmsReturnOrderDetailUpsert {
	u.SetNull(wmsreturnorderdetail.FieldReason)
	return u
}

// SetReturnTime sets the "return_time" field.
func (u *WmsReturnOrderDetailUpsert) SetReturnTime(v time.Time) *WmsReturnOrderDetailUpsert {
	u.Set(wmsreturnorderdetail.FieldReturnTime, v)
	return u
}

// UpdateReturnTime sets the "return_time" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsert) UpdateReturnTime() *WmsReturnOrderDetailUpsert {
	u.SetExcluded(wmsreturnorderdetail.FieldReturnTime)
	return u
}

// ClearReturnTime clears the value of the "return_time" field.
func (u *WmsReturnOrderDetailUpsert) ClearReturnTime() *WmsReturnOrderDetailUpsert {
	u.SetNull(wmsreturnorderdetail.FieldReturnTime)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.WmsReturnOrderDetail.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(wmsreturnorderdetail.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *WmsReturnOrderDetailUpsertOne) UpdateNewValues() *WmsReturnOrderDetailUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(wmsreturnorderdetail.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(wmsreturnorderdetail.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.WmsReturnOrderDetail.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *WmsReturnOrderDetailUpsertOne) Ignore() *WmsReturnOrderDetailUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *WmsReturnOrderDetailUpsertOne) DoNothing() *WmsReturnOrderDetailUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the WmsReturnOrderDetailCreate.OnConflict
// documentation for more info.
func (u *WmsReturnOrderDetailUpsertOne) Update(set func(*WmsReturnOrderDetailUpsert)) *WmsReturnOrderDetailUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&WmsReturnOrderDetailUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *WmsReturnOrderDetailUpsertOne) SetUpdatedAt(v time.Time) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertOne) UpdateUpdatedAt() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetCreatedBy sets the "created_by" field.
func (u *WmsReturnOrderDetailUpsertOne) SetCreatedBy(v string) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetCreatedBy(v)
	})
}

// UpdateCreatedBy sets the "created_by" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertOne) UpdateCreatedBy() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateCreatedBy()
	})
}

// ClearCreatedBy clears the value of the "created_by" field.
func (u *WmsReturnOrderDetailUpsertOne) ClearCreatedBy() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearCreatedBy()
	})
}

// SetUpdatedBy sets the "updated_by" field.
func (u *WmsReturnOrderDetailUpsertOne) SetUpdatedBy(v string) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetUpdatedBy(v)
	})
}

// UpdateUpdatedBy sets the "updated_by" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertOne) UpdateUpdatedBy() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateUpdatedBy()
	})
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (u *WmsReturnOrderDetailUpsertOne) ClearUpdatedBy() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearUpdatedBy()
	})
}

// SetRemark sets the "remark" field.
func (u *WmsReturnOrderDetailUpsertOne) SetRemark(v string) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertOne) UpdateRemark() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *WmsReturnOrderDetailUpsertOne) ClearRemark() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearRemark()
	})
}

// SetOrderID sets the "order_id" field.
func (u *WmsReturnOrderDetailUpsertOne) SetOrderID(v string) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetOrderID(v)
	})
}

// UpdateOrderID sets the "order_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertOne) UpdateOrderID() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateOrderID()
	})
}

// ClearOrderID clears the value of the "order_id" field.
func (u *WmsReturnOrderDetailUpsertOne) ClearOrderID() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearOrderID()
	})
}

// SetMaterialID sets the "material_id" field.
func (u *WmsReturnOrderDetailUpsertOne) SetMaterialID(v string) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetMaterialID(v)
	})
}

// UpdateMaterialID sets the "material_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertOne) UpdateMaterialID() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateMaterialID()
	})
}

// ClearMaterialID clears the value of the "material_id" field.
func (u *WmsReturnOrderDetailUpsertOne) ClearMaterialID() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearMaterialID()
	})
}

// SetMaterialName sets the "material_name" field.
func (u *WmsReturnOrderDetailUpsertOne) SetMaterialName(v string) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetMaterialName(v)
	})
}

// UpdateMaterialName sets the "material_name" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertOne) UpdateMaterialName() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateMaterialName()
	})
}

// ClearMaterialName clears the value of the "material_name" field.
func (u *WmsReturnOrderDetailUpsertOne) ClearMaterialName() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearMaterialName()
	})
}

// SetCode sets the "code" field.
func (u *WmsReturnOrderDetailUpsertOne) SetCode(v string) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertOne) UpdateCode() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateCode()
	})
}

// SetEquipmentID sets the "equipment_id" field.
func (u *WmsReturnOrderDetailUpsertOne) SetEquipmentID(v string) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetEquipmentID(v)
	})
}

// UpdateEquipmentID sets the "equipment_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertOne) UpdateEquipmentID() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateEquipmentID()
	})
}

// ClearEquipmentID clears the value of the "equipment_id" field.
func (u *WmsReturnOrderDetailUpsertOne) ClearEquipmentID() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearEquipmentID()
	})
}

// SetEquipmentTypeID sets the "equipment_type_id" field.
func (u *WmsReturnOrderDetailUpsertOne) SetEquipmentTypeID(v string) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetEquipmentTypeID(v)
	})
}

// UpdateEquipmentTypeID sets the "equipment_type_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertOne) UpdateEquipmentTypeID() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateEquipmentTypeID()
	})
}

// ClearEquipmentTypeID clears the value of the "equipment_type_id" field.
func (u *WmsReturnOrderDetailUpsertOne) ClearEquipmentTypeID() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearEquipmentTypeID()
	})
}

// SetFeature sets the "feature" field.
func (u *WmsReturnOrderDetailUpsertOne) SetFeature(v map[string]interface{}) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetFeature(v)
	})
}

// UpdateFeature sets the "feature" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertOne) UpdateFeature() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateFeature()
	})
}

// ClearFeature clears the value of the "feature" field.
func (u *WmsReturnOrderDetailUpsertOne) ClearFeature() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearFeature()
	})
}

// SetOwnerID sets the "owner_id" field.
func (u *WmsReturnOrderDetailUpsertOne) SetOwnerID(v string) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetOwnerID(v)
	})
}

// UpdateOwnerID sets the "owner_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertOne) UpdateOwnerID() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateOwnerID()
	})
}

// ClearOwnerID clears the value of the "owner_id" field.
func (u *WmsReturnOrderDetailUpsertOne) ClearOwnerID() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearOwnerID()
	})
}

// SetModelNo sets the "ModelNo" field.
func (u *WmsReturnOrderDetailUpsertOne) SetModelNo(v string) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetModelNo(v)
	})
}

// UpdateModelNo sets the "ModelNo" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertOne) UpdateModelNo() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateModelNo()
	})
}

// SetMeasureUnitID sets the "measure_unit_id" field.
func (u *WmsReturnOrderDetailUpsertOne) SetMeasureUnitID(v string) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetMeasureUnitID(v)
	})
}

// UpdateMeasureUnitID sets the "measure_unit_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertOne) UpdateMeasureUnitID() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateMeasureUnitID()
	})
}

// ClearMeasureUnitID clears the value of the "measure_unit_id" field.
func (u *WmsReturnOrderDetailUpsertOne) ClearMeasureUnitID() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearMeasureUnitID()
	})
}

// SetNum sets the "num" field.
func (u *WmsReturnOrderDetailUpsertOne) SetNum(v uint32) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetNum(v)
	})
}

// AddNum adds v to the "num" field.
func (u *WmsReturnOrderDetailUpsertOne) AddNum(v uint32) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.AddNum(v)
	})
}

// UpdateNum sets the "num" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertOne) UpdateNum() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateNum()
	})
}

// SetToRepositoryID sets the "to_repository_id" field.
func (u *WmsReturnOrderDetailUpsertOne) SetToRepositoryID(v string) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetToRepositoryID(v)
	})
}

// UpdateToRepositoryID sets the "to_repository_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertOne) UpdateToRepositoryID() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateToRepositoryID()
	})
}

// ClearToRepositoryID clears the value of the "to_repository_id" field.
func (u *WmsReturnOrderDetailUpsertOne) ClearToRepositoryID() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearToRepositoryID()
	})
}

// SetToRepositoryAreaID sets the "to_repository_area_id" field.
func (u *WmsReturnOrderDetailUpsertOne) SetToRepositoryAreaID(v string) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetToRepositoryAreaID(v)
	})
}

// UpdateToRepositoryAreaID sets the "to_repository_area_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertOne) UpdateToRepositoryAreaID() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateToRepositoryAreaID()
	})
}

// ClearToRepositoryAreaID clears the value of the "to_repository_area_id" field.
func (u *WmsReturnOrderDetailUpsertOne) ClearToRepositoryAreaID() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearToRepositoryAreaID()
	})
}

// SetToRepositoryPositionID sets the "to_repository_position_id" field.
func (u *WmsReturnOrderDetailUpsertOne) SetToRepositoryPositionID(v string) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetToRepositoryPositionID(v)
	})
}

// UpdateToRepositoryPositionID sets the "to_repository_position_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertOne) UpdateToRepositoryPositionID() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateToRepositoryPositionID()
	})
}

// ClearToRepositoryPositionID clears the value of the "to_repository_position_id" field.
func (u *WmsReturnOrderDetailUpsertOne) ClearToRepositoryPositionID() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearToRepositoryPositionID()
	})
}

// SetReason sets the "reason" field.
func (u *WmsReturnOrderDetailUpsertOne) SetReason(v string) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetReason(v)
	})
}

// UpdateReason sets the "reason" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertOne) UpdateReason() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateReason()
	})
}

// ClearReason clears the value of the "reason" field.
func (u *WmsReturnOrderDetailUpsertOne) ClearReason() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearReason()
	})
}

// SetReturnTime sets the "return_time" field.
func (u *WmsReturnOrderDetailUpsertOne) SetReturnTime(v time.Time) *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetReturnTime(v)
	})
}

// UpdateReturnTime sets the "return_time" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertOne) UpdateReturnTime() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateReturnTime()
	})
}

// ClearReturnTime clears the value of the "return_time" field.
func (u *WmsReturnOrderDetailUpsertOne) ClearReturnTime() *WmsReturnOrderDetailUpsertOne {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearReturnTime()
	})
}

// Exec executes the query.
func (u *WmsReturnOrderDetailUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for WmsReturnOrderDetailCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *WmsReturnOrderDetailUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *WmsReturnOrderDetailUpsertOne) ID(ctx context.Context) (id string, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: WmsReturnOrderDetailUpsertOne.ID is not supported by MySQL driver. Use WmsReturnOrderDetailUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *WmsReturnOrderDetailUpsertOne) IDX(ctx context.Context) string {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// WmsReturnOrderDetailCreateBulk is the builder for creating many WmsReturnOrderDetail entities in bulk.
type WmsReturnOrderDetailCreateBulk struct {
	config
	err      error
	builders []*WmsReturnOrderDetailCreate
	conflict []sql.ConflictOption
}

// Save creates the WmsReturnOrderDetail entities in the database.
func (wrodcb *WmsReturnOrderDetailCreateBulk) Save(ctx context.Context) ([]*WmsReturnOrderDetail, error) {
	if wrodcb.err != nil {
		return nil, wrodcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(wrodcb.builders))
	nodes := make([]*WmsReturnOrderDetail, len(wrodcb.builders))
	mutators := make([]Mutator, len(wrodcb.builders))
	for i := range wrodcb.builders {
		func(i int, root context.Context) {
			builder := wrodcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*WmsReturnOrderDetailMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, wrodcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = wrodcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, wrodcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, wrodcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (wrodcb *WmsReturnOrderDetailCreateBulk) SaveX(ctx context.Context) []*WmsReturnOrderDetail {
	v, err := wrodcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (wrodcb *WmsReturnOrderDetailCreateBulk) Exec(ctx context.Context) error {
	_, err := wrodcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wrodcb *WmsReturnOrderDetailCreateBulk) ExecX(ctx context.Context) {
	if err := wrodcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.WmsReturnOrderDetail.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.WmsReturnOrderDetailUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (wrodcb *WmsReturnOrderDetailCreateBulk) OnConflict(opts ...sql.ConflictOption) *WmsReturnOrderDetailUpsertBulk {
	wrodcb.conflict = opts
	return &WmsReturnOrderDetailUpsertBulk{
		create: wrodcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.WmsReturnOrderDetail.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (wrodcb *WmsReturnOrderDetailCreateBulk) OnConflictColumns(columns ...string) *WmsReturnOrderDetailUpsertBulk {
	wrodcb.conflict = append(wrodcb.conflict, sql.ConflictColumns(columns...))
	return &WmsReturnOrderDetailUpsertBulk{
		create: wrodcb,
	}
}

// WmsReturnOrderDetailUpsertBulk is the builder for "upsert"-ing
// a bulk of WmsReturnOrderDetail nodes.
type WmsReturnOrderDetailUpsertBulk struct {
	create *WmsReturnOrderDetailCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.WmsReturnOrderDetail.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(wmsreturnorderdetail.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *WmsReturnOrderDetailUpsertBulk) UpdateNewValues() *WmsReturnOrderDetailUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(wmsreturnorderdetail.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(wmsreturnorderdetail.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.WmsReturnOrderDetail.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *WmsReturnOrderDetailUpsertBulk) Ignore() *WmsReturnOrderDetailUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *WmsReturnOrderDetailUpsertBulk) DoNothing() *WmsReturnOrderDetailUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the WmsReturnOrderDetailCreateBulk.OnConflict
// documentation for more info.
func (u *WmsReturnOrderDetailUpsertBulk) Update(set func(*WmsReturnOrderDetailUpsert)) *WmsReturnOrderDetailUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&WmsReturnOrderDetailUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *WmsReturnOrderDetailUpsertBulk) SetUpdatedAt(v time.Time) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertBulk) UpdateUpdatedAt() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetCreatedBy sets the "created_by" field.
func (u *WmsReturnOrderDetailUpsertBulk) SetCreatedBy(v string) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetCreatedBy(v)
	})
}

// UpdateCreatedBy sets the "created_by" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertBulk) UpdateCreatedBy() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateCreatedBy()
	})
}

// ClearCreatedBy clears the value of the "created_by" field.
func (u *WmsReturnOrderDetailUpsertBulk) ClearCreatedBy() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearCreatedBy()
	})
}

// SetUpdatedBy sets the "updated_by" field.
func (u *WmsReturnOrderDetailUpsertBulk) SetUpdatedBy(v string) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetUpdatedBy(v)
	})
}

// UpdateUpdatedBy sets the "updated_by" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertBulk) UpdateUpdatedBy() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateUpdatedBy()
	})
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (u *WmsReturnOrderDetailUpsertBulk) ClearUpdatedBy() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearUpdatedBy()
	})
}

// SetRemark sets the "remark" field.
func (u *WmsReturnOrderDetailUpsertBulk) SetRemark(v string) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertBulk) UpdateRemark() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *WmsReturnOrderDetailUpsertBulk) ClearRemark() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearRemark()
	})
}

// SetOrderID sets the "order_id" field.
func (u *WmsReturnOrderDetailUpsertBulk) SetOrderID(v string) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetOrderID(v)
	})
}

// UpdateOrderID sets the "order_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertBulk) UpdateOrderID() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateOrderID()
	})
}

// ClearOrderID clears the value of the "order_id" field.
func (u *WmsReturnOrderDetailUpsertBulk) ClearOrderID() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearOrderID()
	})
}

// SetMaterialID sets the "material_id" field.
func (u *WmsReturnOrderDetailUpsertBulk) SetMaterialID(v string) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetMaterialID(v)
	})
}

// UpdateMaterialID sets the "material_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertBulk) UpdateMaterialID() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateMaterialID()
	})
}

// ClearMaterialID clears the value of the "material_id" field.
func (u *WmsReturnOrderDetailUpsertBulk) ClearMaterialID() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearMaterialID()
	})
}

// SetMaterialName sets the "material_name" field.
func (u *WmsReturnOrderDetailUpsertBulk) SetMaterialName(v string) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetMaterialName(v)
	})
}

// UpdateMaterialName sets the "material_name" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertBulk) UpdateMaterialName() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateMaterialName()
	})
}

// ClearMaterialName clears the value of the "material_name" field.
func (u *WmsReturnOrderDetailUpsertBulk) ClearMaterialName() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearMaterialName()
	})
}

// SetCode sets the "code" field.
func (u *WmsReturnOrderDetailUpsertBulk) SetCode(v string) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertBulk) UpdateCode() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateCode()
	})
}

// SetEquipmentID sets the "equipment_id" field.
func (u *WmsReturnOrderDetailUpsertBulk) SetEquipmentID(v string) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetEquipmentID(v)
	})
}

// UpdateEquipmentID sets the "equipment_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertBulk) UpdateEquipmentID() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateEquipmentID()
	})
}

// ClearEquipmentID clears the value of the "equipment_id" field.
func (u *WmsReturnOrderDetailUpsertBulk) ClearEquipmentID() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearEquipmentID()
	})
}

// SetEquipmentTypeID sets the "equipment_type_id" field.
func (u *WmsReturnOrderDetailUpsertBulk) SetEquipmentTypeID(v string) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetEquipmentTypeID(v)
	})
}

// UpdateEquipmentTypeID sets the "equipment_type_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertBulk) UpdateEquipmentTypeID() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateEquipmentTypeID()
	})
}

// ClearEquipmentTypeID clears the value of the "equipment_type_id" field.
func (u *WmsReturnOrderDetailUpsertBulk) ClearEquipmentTypeID() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearEquipmentTypeID()
	})
}

// SetFeature sets the "feature" field.
func (u *WmsReturnOrderDetailUpsertBulk) SetFeature(v map[string]interface{}) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetFeature(v)
	})
}

// UpdateFeature sets the "feature" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertBulk) UpdateFeature() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateFeature()
	})
}

// ClearFeature clears the value of the "feature" field.
func (u *WmsReturnOrderDetailUpsertBulk) ClearFeature() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearFeature()
	})
}

// SetOwnerID sets the "owner_id" field.
func (u *WmsReturnOrderDetailUpsertBulk) SetOwnerID(v string) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetOwnerID(v)
	})
}

// UpdateOwnerID sets the "owner_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertBulk) UpdateOwnerID() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateOwnerID()
	})
}

// ClearOwnerID clears the value of the "owner_id" field.
func (u *WmsReturnOrderDetailUpsertBulk) ClearOwnerID() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearOwnerID()
	})
}

// SetModelNo sets the "ModelNo" field.
func (u *WmsReturnOrderDetailUpsertBulk) SetModelNo(v string) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetModelNo(v)
	})
}

// UpdateModelNo sets the "ModelNo" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertBulk) UpdateModelNo() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateModelNo()
	})
}

// SetMeasureUnitID sets the "measure_unit_id" field.
func (u *WmsReturnOrderDetailUpsertBulk) SetMeasureUnitID(v string) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetMeasureUnitID(v)
	})
}

// UpdateMeasureUnitID sets the "measure_unit_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertBulk) UpdateMeasureUnitID() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateMeasureUnitID()
	})
}

// ClearMeasureUnitID clears the value of the "measure_unit_id" field.
func (u *WmsReturnOrderDetailUpsertBulk) ClearMeasureUnitID() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearMeasureUnitID()
	})
}

// SetNum sets the "num" field.
func (u *WmsReturnOrderDetailUpsertBulk) SetNum(v uint32) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetNum(v)
	})
}

// AddNum adds v to the "num" field.
func (u *WmsReturnOrderDetailUpsertBulk) AddNum(v uint32) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.AddNum(v)
	})
}

// UpdateNum sets the "num" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertBulk) UpdateNum() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateNum()
	})
}

// SetToRepositoryID sets the "to_repository_id" field.
func (u *WmsReturnOrderDetailUpsertBulk) SetToRepositoryID(v string) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetToRepositoryID(v)
	})
}

// UpdateToRepositoryID sets the "to_repository_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertBulk) UpdateToRepositoryID() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateToRepositoryID()
	})
}

// ClearToRepositoryID clears the value of the "to_repository_id" field.
func (u *WmsReturnOrderDetailUpsertBulk) ClearToRepositoryID() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearToRepositoryID()
	})
}

// SetToRepositoryAreaID sets the "to_repository_area_id" field.
func (u *WmsReturnOrderDetailUpsertBulk) SetToRepositoryAreaID(v string) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetToRepositoryAreaID(v)
	})
}

// UpdateToRepositoryAreaID sets the "to_repository_area_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertBulk) UpdateToRepositoryAreaID() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateToRepositoryAreaID()
	})
}

// ClearToRepositoryAreaID clears the value of the "to_repository_area_id" field.
func (u *WmsReturnOrderDetailUpsertBulk) ClearToRepositoryAreaID() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearToRepositoryAreaID()
	})
}

// SetToRepositoryPositionID sets the "to_repository_position_id" field.
func (u *WmsReturnOrderDetailUpsertBulk) SetToRepositoryPositionID(v string) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetToRepositoryPositionID(v)
	})
}

// UpdateToRepositoryPositionID sets the "to_repository_position_id" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertBulk) UpdateToRepositoryPositionID() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateToRepositoryPositionID()
	})
}

// ClearToRepositoryPositionID clears the value of the "to_repository_position_id" field.
func (u *WmsReturnOrderDetailUpsertBulk) ClearToRepositoryPositionID() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearToRepositoryPositionID()
	})
}

// SetReason sets the "reason" field.
func (u *WmsReturnOrderDetailUpsertBulk) SetReason(v string) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetReason(v)
	})
}

// UpdateReason sets the "reason" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertBulk) UpdateReason() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateReason()
	})
}

// ClearReason clears the value of the "reason" field.
func (u *WmsReturnOrderDetailUpsertBulk) ClearReason() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearReason()
	})
}

// SetReturnTime sets the "return_time" field.
func (u *WmsReturnOrderDetailUpsertBulk) SetReturnTime(v time.Time) *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.SetReturnTime(v)
	})
}

// UpdateReturnTime sets the "return_time" field to the value that was provided on create.
func (u *WmsReturnOrderDetailUpsertBulk) UpdateReturnTime() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.UpdateReturnTime()
	})
}

// ClearReturnTime clears the value of the "return_time" field.
func (u *WmsReturnOrderDetailUpsertBulk) ClearReturnTime() *WmsReturnOrderDetailUpsertBulk {
	return u.Update(func(s *WmsReturnOrderDetailUpsert) {
		s.ClearReturnTime()
	})
}

// Exec executes the query.
func (u *WmsReturnOrderDetailUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the WmsReturnOrderDetailCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for WmsReturnOrderDetailCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *WmsReturnOrderDetailUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
