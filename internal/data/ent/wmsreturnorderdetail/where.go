// Code generated by ent, DO NOT EDIT.

package wmsreturnorderdetail

import (
	"kratos-mono-demo/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContainsFold(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldUpdatedAt, v))
}

// CreatedBy applies equality check predicate on the "created_by" field. It's identical to CreatedByEQ.
func CreatedBy(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldCreatedBy, v))
}

// UpdatedBy applies equality check predicate on the "updated_by" field. It's identical to UpdatedByEQ.
func UpdatedBy(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldUpdatedBy, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldRemark, v))
}

// OrderID applies equality check predicate on the "order_id" field. It's identical to OrderIDEQ.
func OrderID(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldOrderID, v))
}

// MaterialID applies equality check predicate on the "material_id" field. It's identical to MaterialIDEQ.
func MaterialID(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldMaterialID, v))
}

// MaterialName applies equality check predicate on the "material_name" field. It's identical to MaterialNameEQ.
func MaterialName(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldMaterialName, v))
}

// Code applies equality check predicate on the "code" field. It's identical to CodeEQ.
func Code(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldCode, v))
}

// EquipmentID applies equality check predicate on the "equipment_id" field. It's identical to EquipmentIDEQ.
func EquipmentID(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldEquipmentID, v))
}

// EquipmentTypeID applies equality check predicate on the "equipment_type_id" field. It's identical to EquipmentTypeIDEQ.
func EquipmentTypeID(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldEquipmentTypeID, v))
}

// OwnerID applies equality check predicate on the "owner_id" field. It's identical to OwnerIDEQ.
func OwnerID(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldOwnerID, v))
}

// ModelNo applies equality check predicate on the "ModelNo" field. It's identical to ModelNoEQ.
func ModelNo(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldModelNo, v))
}

// MeasureUnitID applies equality check predicate on the "measure_unit_id" field. It's identical to MeasureUnitIDEQ.
func MeasureUnitID(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldMeasureUnitID, v))
}

// Num applies equality check predicate on the "num" field. It's identical to NumEQ.
func Num(v uint32) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldNum, v))
}

// ToRepositoryID applies equality check predicate on the "to_repository_id" field. It's identical to ToRepositoryIDEQ.
func ToRepositoryID(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldToRepositoryID, v))
}

// ToRepositoryAreaID applies equality check predicate on the "to_repository_area_id" field. It's identical to ToRepositoryAreaIDEQ.
func ToRepositoryAreaID(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldToRepositoryAreaID, v))
}

// ToRepositoryPositionID applies equality check predicate on the "to_repository_position_id" field. It's identical to ToRepositoryPositionIDEQ.
func ToRepositoryPositionID(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldToRepositoryPositionID, v))
}

// Reason applies equality check predicate on the "reason" field. It's identical to ReasonEQ.
func Reason(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldReason, v))
}

// ReturnTime applies equality check predicate on the "return_time" field. It's identical to ReturnTimeEQ.
func ReturnTime(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldReturnTime, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldUpdatedAt, v))
}

// CreatedByEQ applies the EQ predicate on the "created_by" field.
func CreatedByEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldCreatedBy, v))
}

// CreatedByNEQ applies the NEQ predicate on the "created_by" field.
func CreatedByNEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldCreatedBy, v))
}

// CreatedByIn applies the In predicate on the "created_by" field.
func CreatedByIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldCreatedBy, vs...))
}

// CreatedByNotIn applies the NotIn predicate on the "created_by" field.
func CreatedByNotIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldCreatedBy, vs...))
}

// CreatedByGT applies the GT predicate on the "created_by" field.
func CreatedByGT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldCreatedBy, v))
}

// CreatedByGTE applies the GTE predicate on the "created_by" field.
func CreatedByGTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldCreatedBy, v))
}

// CreatedByLT applies the LT predicate on the "created_by" field.
func CreatedByLT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldCreatedBy, v))
}

// CreatedByLTE applies the LTE predicate on the "created_by" field.
func CreatedByLTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldCreatedBy, v))
}

// CreatedByContains applies the Contains predicate on the "created_by" field.
func CreatedByContains(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContains(FieldCreatedBy, v))
}

// CreatedByHasPrefix applies the HasPrefix predicate on the "created_by" field.
func CreatedByHasPrefix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasPrefix(FieldCreatedBy, v))
}

// CreatedByHasSuffix applies the HasSuffix predicate on the "created_by" field.
func CreatedByHasSuffix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasSuffix(FieldCreatedBy, v))
}

// CreatedByIsNil applies the IsNil predicate on the "created_by" field.
func CreatedByIsNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIsNull(FieldCreatedBy))
}

// CreatedByNotNil applies the NotNil predicate on the "created_by" field.
func CreatedByNotNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotNull(FieldCreatedBy))
}

// CreatedByEqualFold applies the EqualFold predicate on the "created_by" field.
func CreatedByEqualFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEqualFold(FieldCreatedBy, v))
}

// CreatedByContainsFold applies the ContainsFold predicate on the "created_by" field.
func CreatedByContainsFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContainsFold(FieldCreatedBy, v))
}

// UpdatedByEQ applies the EQ predicate on the "updated_by" field.
func UpdatedByEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldUpdatedBy, v))
}

// UpdatedByNEQ applies the NEQ predicate on the "updated_by" field.
func UpdatedByNEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldUpdatedBy, v))
}

// UpdatedByIn applies the In predicate on the "updated_by" field.
func UpdatedByIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldUpdatedBy, vs...))
}

// UpdatedByNotIn applies the NotIn predicate on the "updated_by" field.
func UpdatedByNotIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldUpdatedBy, vs...))
}

// UpdatedByGT applies the GT predicate on the "updated_by" field.
func UpdatedByGT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldUpdatedBy, v))
}

// UpdatedByGTE applies the GTE predicate on the "updated_by" field.
func UpdatedByGTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldUpdatedBy, v))
}

// UpdatedByLT applies the LT predicate on the "updated_by" field.
func UpdatedByLT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldUpdatedBy, v))
}

// UpdatedByLTE applies the LTE predicate on the "updated_by" field.
func UpdatedByLTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldUpdatedBy, v))
}

// UpdatedByContains applies the Contains predicate on the "updated_by" field.
func UpdatedByContains(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContains(FieldUpdatedBy, v))
}

// UpdatedByHasPrefix applies the HasPrefix predicate on the "updated_by" field.
func UpdatedByHasPrefix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasPrefix(FieldUpdatedBy, v))
}

// UpdatedByHasSuffix applies the HasSuffix predicate on the "updated_by" field.
func UpdatedByHasSuffix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasSuffix(FieldUpdatedBy, v))
}

// UpdatedByIsNil applies the IsNil predicate on the "updated_by" field.
func UpdatedByIsNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIsNull(FieldUpdatedBy))
}

// UpdatedByNotNil applies the NotNil predicate on the "updated_by" field.
func UpdatedByNotNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotNull(FieldUpdatedBy))
}

// UpdatedByEqualFold applies the EqualFold predicate on the "updated_by" field.
func UpdatedByEqualFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEqualFold(FieldUpdatedBy, v))
}

// UpdatedByContainsFold applies the ContainsFold predicate on the "updated_by" field.
func UpdatedByContainsFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContainsFold(FieldUpdatedBy, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContainsFold(FieldRemark, v))
}

// OrderIDEQ applies the EQ predicate on the "order_id" field.
func OrderIDEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldOrderID, v))
}

// OrderIDNEQ applies the NEQ predicate on the "order_id" field.
func OrderIDNEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldOrderID, v))
}

// OrderIDIn applies the In predicate on the "order_id" field.
func OrderIDIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldOrderID, vs...))
}

// OrderIDNotIn applies the NotIn predicate on the "order_id" field.
func OrderIDNotIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldOrderID, vs...))
}

// OrderIDGT applies the GT predicate on the "order_id" field.
func OrderIDGT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldOrderID, v))
}

// OrderIDGTE applies the GTE predicate on the "order_id" field.
func OrderIDGTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldOrderID, v))
}

// OrderIDLT applies the LT predicate on the "order_id" field.
func OrderIDLT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldOrderID, v))
}

// OrderIDLTE applies the LTE predicate on the "order_id" field.
func OrderIDLTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldOrderID, v))
}

// OrderIDContains applies the Contains predicate on the "order_id" field.
func OrderIDContains(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContains(FieldOrderID, v))
}

// OrderIDHasPrefix applies the HasPrefix predicate on the "order_id" field.
func OrderIDHasPrefix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasPrefix(FieldOrderID, v))
}

// OrderIDHasSuffix applies the HasSuffix predicate on the "order_id" field.
func OrderIDHasSuffix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasSuffix(FieldOrderID, v))
}

// OrderIDIsNil applies the IsNil predicate on the "order_id" field.
func OrderIDIsNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIsNull(FieldOrderID))
}

// OrderIDNotNil applies the NotNil predicate on the "order_id" field.
func OrderIDNotNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotNull(FieldOrderID))
}

// OrderIDEqualFold applies the EqualFold predicate on the "order_id" field.
func OrderIDEqualFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEqualFold(FieldOrderID, v))
}

// OrderIDContainsFold applies the ContainsFold predicate on the "order_id" field.
func OrderIDContainsFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContainsFold(FieldOrderID, v))
}

// MaterialIDEQ applies the EQ predicate on the "material_id" field.
func MaterialIDEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldMaterialID, v))
}

// MaterialIDNEQ applies the NEQ predicate on the "material_id" field.
func MaterialIDNEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldMaterialID, v))
}

// MaterialIDIn applies the In predicate on the "material_id" field.
func MaterialIDIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldMaterialID, vs...))
}

// MaterialIDNotIn applies the NotIn predicate on the "material_id" field.
func MaterialIDNotIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldMaterialID, vs...))
}

// MaterialIDGT applies the GT predicate on the "material_id" field.
func MaterialIDGT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldMaterialID, v))
}

// MaterialIDGTE applies the GTE predicate on the "material_id" field.
func MaterialIDGTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldMaterialID, v))
}

// MaterialIDLT applies the LT predicate on the "material_id" field.
func MaterialIDLT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldMaterialID, v))
}

// MaterialIDLTE applies the LTE predicate on the "material_id" field.
func MaterialIDLTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldMaterialID, v))
}

// MaterialIDContains applies the Contains predicate on the "material_id" field.
func MaterialIDContains(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContains(FieldMaterialID, v))
}

// MaterialIDHasPrefix applies the HasPrefix predicate on the "material_id" field.
func MaterialIDHasPrefix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasPrefix(FieldMaterialID, v))
}

// MaterialIDHasSuffix applies the HasSuffix predicate on the "material_id" field.
func MaterialIDHasSuffix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasSuffix(FieldMaterialID, v))
}

// MaterialIDIsNil applies the IsNil predicate on the "material_id" field.
func MaterialIDIsNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIsNull(FieldMaterialID))
}

// MaterialIDNotNil applies the NotNil predicate on the "material_id" field.
func MaterialIDNotNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotNull(FieldMaterialID))
}

// MaterialIDEqualFold applies the EqualFold predicate on the "material_id" field.
func MaterialIDEqualFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEqualFold(FieldMaterialID, v))
}

// MaterialIDContainsFold applies the ContainsFold predicate on the "material_id" field.
func MaterialIDContainsFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContainsFold(FieldMaterialID, v))
}

// MaterialNameEQ applies the EQ predicate on the "material_name" field.
func MaterialNameEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldMaterialName, v))
}

// MaterialNameNEQ applies the NEQ predicate on the "material_name" field.
func MaterialNameNEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldMaterialName, v))
}

// MaterialNameIn applies the In predicate on the "material_name" field.
func MaterialNameIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldMaterialName, vs...))
}

// MaterialNameNotIn applies the NotIn predicate on the "material_name" field.
func MaterialNameNotIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldMaterialName, vs...))
}

// MaterialNameGT applies the GT predicate on the "material_name" field.
func MaterialNameGT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldMaterialName, v))
}

// MaterialNameGTE applies the GTE predicate on the "material_name" field.
func MaterialNameGTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldMaterialName, v))
}

// MaterialNameLT applies the LT predicate on the "material_name" field.
func MaterialNameLT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldMaterialName, v))
}

// MaterialNameLTE applies the LTE predicate on the "material_name" field.
func MaterialNameLTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldMaterialName, v))
}

// MaterialNameContains applies the Contains predicate on the "material_name" field.
func MaterialNameContains(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContains(FieldMaterialName, v))
}

// MaterialNameHasPrefix applies the HasPrefix predicate on the "material_name" field.
func MaterialNameHasPrefix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasPrefix(FieldMaterialName, v))
}

// MaterialNameHasSuffix applies the HasSuffix predicate on the "material_name" field.
func MaterialNameHasSuffix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasSuffix(FieldMaterialName, v))
}

// MaterialNameIsNil applies the IsNil predicate on the "material_name" field.
func MaterialNameIsNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIsNull(FieldMaterialName))
}

// MaterialNameNotNil applies the NotNil predicate on the "material_name" field.
func MaterialNameNotNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotNull(FieldMaterialName))
}

// MaterialNameEqualFold applies the EqualFold predicate on the "material_name" field.
func MaterialNameEqualFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEqualFold(FieldMaterialName, v))
}

// MaterialNameContainsFold applies the ContainsFold predicate on the "material_name" field.
func MaterialNameContainsFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContainsFold(FieldMaterialName, v))
}

// CodeEQ applies the EQ predicate on the "code" field.
func CodeEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldCode, v))
}

// CodeNEQ applies the NEQ predicate on the "code" field.
func CodeNEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldCode, v))
}

// CodeIn applies the In predicate on the "code" field.
func CodeIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldCode, vs...))
}

// CodeNotIn applies the NotIn predicate on the "code" field.
func CodeNotIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldCode, vs...))
}

// CodeGT applies the GT predicate on the "code" field.
func CodeGT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldCode, v))
}

// CodeGTE applies the GTE predicate on the "code" field.
func CodeGTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldCode, v))
}

// CodeLT applies the LT predicate on the "code" field.
func CodeLT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldCode, v))
}

// CodeLTE applies the LTE predicate on the "code" field.
func CodeLTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldCode, v))
}

// CodeContains applies the Contains predicate on the "code" field.
func CodeContains(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContains(FieldCode, v))
}

// CodeHasPrefix applies the HasPrefix predicate on the "code" field.
func CodeHasPrefix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasPrefix(FieldCode, v))
}

// CodeHasSuffix applies the HasSuffix predicate on the "code" field.
func CodeHasSuffix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasSuffix(FieldCode, v))
}

// CodeEqualFold applies the EqualFold predicate on the "code" field.
func CodeEqualFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEqualFold(FieldCode, v))
}

// CodeContainsFold applies the ContainsFold predicate on the "code" field.
func CodeContainsFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContainsFold(FieldCode, v))
}

// EquipmentIDEQ applies the EQ predicate on the "equipment_id" field.
func EquipmentIDEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldEquipmentID, v))
}

// EquipmentIDNEQ applies the NEQ predicate on the "equipment_id" field.
func EquipmentIDNEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldEquipmentID, v))
}

// EquipmentIDIn applies the In predicate on the "equipment_id" field.
func EquipmentIDIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldEquipmentID, vs...))
}

// EquipmentIDNotIn applies the NotIn predicate on the "equipment_id" field.
func EquipmentIDNotIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldEquipmentID, vs...))
}

// EquipmentIDGT applies the GT predicate on the "equipment_id" field.
func EquipmentIDGT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldEquipmentID, v))
}

// EquipmentIDGTE applies the GTE predicate on the "equipment_id" field.
func EquipmentIDGTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldEquipmentID, v))
}

// EquipmentIDLT applies the LT predicate on the "equipment_id" field.
func EquipmentIDLT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldEquipmentID, v))
}

// EquipmentIDLTE applies the LTE predicate on the "equipment_id" field.
func EquipmentIDLTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldEquipmentID, v))
}

// EquipmentIDContains applies the Contains predicate on the "equipment_id" field.
func EquipmentIDContains(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContains(FieldEquipmentID, v))
}

// EquipmentIDHasPrefix applies the HasPrefix predicate on the "equipment_id" field.
func EquipmentIDHasPrefix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasPrefix(FieldEquipmentID, v))
}

// EquipmentIDHasSuffix applies the HasSuffix predicate on the "equipment_id" field.
func EquipmentIDHasSuffix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasSuffix(FieldEquipmentID, v))
}

// EquipmentIDIsNil applies the IsNil predicate on the "equipment_id" field.
func EquipmentIDIsNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIsNull(FieldEquipmentID))
}

// EquipmentIDNotNil applies the NotNil predicate on the "equipment_id" field.
func EquipmentIDNotNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotNull(FieldEquipmentID))
}

// EquipmentIDEqualFold applies the EqualFold predicate on the "equipment_id" field.
func EquipmentIDEqualFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEqualFold(FieldEquipmentID, v))
}

// EquipmentIDContainsFold applies the ContainsFold predicate on the "equipment_id" field.
func EquipmentIDContainsFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContainsFold(FieldEquipmentID, v))
}

// EquipmentTypeIDEQ applies the EQ predicate on the "equipment_type_id" field.
func EquipmentTypeIDEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDNEQ applies the NEQ predicate on the "equipment_type_id" field.
func EquipmentTypeIDNEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDIn applies the In predicate on the "equipment_type_id" field.
func EquipmentTypeIDIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldEquipmentTypeID, vs...))
}

// EquipmentTypeIDNotIn applies the NotIn predicate on the "equipment_type_id" field.
func EquipmentTypeIDNotIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldEquipmentTypeID, vs...))
}

// EquipmentTypeIDGT applies the GT predicate on the "equipment_type_id" field.
func EquipmentTypeIDGT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDGTE applies the GTE predicate on the "equipment_type_id" field.
func EquipmentTypeIDGTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDLT applies the LT predicate on the "equipment_type_id" field.
func EquipmentTypeIDLT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDLTE applies the LTE predicate on the "equipment_type_id" field.
func EquipmentTypeIDLTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDContains applies the Contains predicate on the "equipment_type_id" field.
func EquipmentTypeIDContains(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContains(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDHasPrefix applies the HasPrefix predicate on the "equipment_type_id" field.
func EquipmentTypeIDHasPrefix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasPrefix(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDHasSuffix applies the HasSuffix predicate on the "equipment_type_id" field.
func EquipmentTypeIDHasSuffix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasSuffix(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDIsNil applies the IsNil predicate on the "equipment_type_id" field.
func EquipmentTypeIDIsNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIsNull(FieldEquipmentTypeID))
}

// EquipmentTypeIDNotNil applies the NotNil predicate on the "equipment_type_id" field.
func EquipmentTypeIDNotNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotNull(FieldEquipmentTypeID))
}

// EquipmentTypeIDEqualFold applies the EqualFold predicate on the "equipment_type_id" field.
func EquipmentTypeIDEqualFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEqualFold(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDContainsFold applies the ContainsFold predicate on the "equipment_type_id" field.
func EquipmentTypeIDContainsFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContainsFold(FieldEquipmentTypeID, v))
}

// FeatureIsNil applies the IsNil predicate on the "feature" field.
func FeatureIsNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIsNull(FieldFeature))
}

// FeatureNotNil applies the NotNil predicate on the "feature" field.
func FeatureNotNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotNull(FieldFeature))
}

// OwnerIDEQ applies the EQ predicate on the "owner_id" field.
func OwnerIDEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldOwnerID, v))
}

// OwnerIDNEQ applies the NEQ predicate on the "owner_id" field.
func OwnerIDNEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldOwnerID, v))
}

// OwnerIDIn applies the In predicate on the "owner_id" field.
func OwnerIDIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldOwnerID, vs...))
}

// OwnerIDNotIn applies the NotIn predicate on the "owner_id" field.
func OwnerIDNotIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldOwnerID, vs...))
}

// OwnerIDGT applies the GT predicate on the "owner_id" field.
func OwnerIDGT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldOwnerID, v))
}

// OwnerIDGTE applies the GTE predicate on the "owner_id" field.
func OwnerIDGTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldOwnerID, v))
}

// OwnerIDLT applies the LT predicate on the "owner_id" field.
func OwnerIDLT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldOwnerID, v))
}

// OwnerIDLTE applies the LTE predicate on the "owner_id" field.
func OwnerIDLTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldOwnerID, v))
}

// OwnerIDContains applies the Contains predicate on the "owner_id" field.
func OwnerIDContains(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContains(FieldOwnerID, v))
}

// OwnerIDHasPrefix applies the HasPrefix predicate on the "owner_id" field.
func OwnerIDHasPrefix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasPrefix(FieldOwnerID, v))
}

// OwnerIDHasSuffix applies the HasSuffix predicate on the "owner_id" field.
func OwnerIDHasSuffix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasSuffix(FieldOwnerID, v))
}

// OwnerIDIsNil applies the IsNil predicate on the "owner_id" field.
func OwnerIDIsNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIsNull(FieldOwnerID))
}

// OwnerIDNotNil applies the NotNil predicate on the "owner_id" field.
func OwnerIDNotNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotNull(FieldOwnerID))
}

// OwnerIDEqualFold applies the EqualFold predicate on the "owner_id" field.
func OwnerIDEqualFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEqualFold(FieldOwnerID, v))
}

// OwnerIDContainsFold applies the ContainsFold predicate on the "owner_id" field.
func OwnerIDContainsFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContainsFold(FieldOwnerID, v))
}

// ModelNoEQ applies the EQ predicate on the "ModelNo" field.
func ModelNoEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldModelNo, v))
}

// ModelNoNEQ applies the NEQ predicate on the "ModelNo" field.
func ModelNoNEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldModelNo, v))
}

// ModelNoIn applies the In predicate on the "ModelNo" field.
func ModelNoIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldModelNo, vs...))
}

// ModelNoNotIn applies the NotIn predicate on the "ModelNo" field.
func ModelNoNotIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldModelNo, vs...))
}

// ModelNoGT applies the GT predicate on the "ModelNo" field.
func ModelNoGT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldModelNo, v))
}

// ModelNoGTE applies the GTE predicate on the "ModelNo" field.
func ModelNoGTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldModelNo, v))
}

// ModelNoLT applies the LT predicate on the "ModelNo" field.
func ModelNoLT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldModelNo, v))
}

// ModelNoLTE applies the LTE predicate on the "ModelNo" field.
func ModelNoLTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldModelNo, v))
}

// ModelNoContains applies the Contains predicate on the "ModelNo" field.
func ModelNoContains(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContains(FieldModelNo, v))
}

// ModelNoHasPrefix applies the HasPrefix predicate on the "ModelNo" field.
func ModelNoHasPrefix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasPrefix(FieldModelNo, v))
}

// ModelNoHasSuffix applies the HasSuffix predicate on the "ModelNo" field.
func ModelNoHasSuffix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasSuffix(FieldModelNo, v))
}

// ModelNoEqualFold applies the EqualFold predicate on the "ModelNo" field.
func ModelNoEqualFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEqualFold(FieldModelNo, v))
}

// ModelNoContainsFold applies the ContainsFold predicate on the "ModelNo" field.
func ModelNoContainsFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContainsFold(FieldModelNo, v))
}

// MeasureUnitIDEQ applies the EQ predicate on the "measure_unit_id" field.
func MeasureUnitIDEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldMeasureUnitID, v))
}

// MeasureUnitIDNEQ applies the NEQ predicate on the "measure_unit_id" field.
func MeasureUnitIDNEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldMeasureUnitID, v))
}

// MeasureUnitIDIn applies the In predicate on the "measure_unit_id" field.
func MeasureUnitIDIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldMeasureUnitID, vs...))
}

// MeasureUnitIDNotIn applies the NotIn predicate on the "measure_unit_id" field.
func MeasureUnitIDNotIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldMeasureUnitID, vs...))
}

// MeasureUnitIDGT applies the GT predicate on the "measure_unit_id" field.
func MeasureUnitIDGT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldMeasureUnitID, v))
}

// MeasureUnitIDGTE applies the GTE predicate on the "measure_unit_id" field.
func MeasureUnitIDGTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldMeasureUnitID, v))
}

// MeasureUnitIDLT applies the LT predicate on the "measure_unit_id" field.
func MeasureUnitIDLT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldMeasureUnitID, v))
}

// MeasureUnitIDLTE applies the LTE predicate on the "measure_unit_id" field.
func MeasureUnitIDLTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldMeasureUnitID, v))
}

// MeasureUnitIDContains applies the Contains predicate on the "measure_unit_id" field.
func MeasureUnitIDContains(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContains(FieldMeasureUnitID, v))
}

// MeasureUnitIDHasPrefix applies the HasPrefix predicate on the "measure_unit_id" field.
func MeasureUnitIDHasPrefix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasPrefix(FieldMeasureUnitID, v))
}

// MeasureUnitIDHasSuffix applies the HasSuffix predicate on the "measure_unit_id" field.
func MeasureUnitIDHasSuffix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasSuffix(FieldMeasureUnitID, v))
}

// MeasureUnitIDIsNil applies the IsNil predicate on the "measure_unit_id" field.
func MeasureUnitIDIsNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIsNull(FieldMeasureUnitID))
}

// MeasureUnitIDNotNil applies the NotNil predicate on the "measure_unit_id" field.
func MeasureUnitIDNotNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotNull(FieldMeasureUnitID))
}

// MeasureUnitIDEqualFold applies the EqualFold predicate on the "measure_unit_id" field.
func MeasureUnitIDEqualFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEqualFold(FieldMeasureUnitID, v))
}

// MeasureUnitIDContainsFold applies the ContainsFold predicate on the "measure_unit_id" field.
func MeasureUnitIDContainsFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContainsFold(FieldMeasureUnitID, v))
}

// NumEQ applies the EQ predicate on the "num" field.
func NumEQ(v uint32) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldNum, v))
}

// NumNEQ applies the NEQ predicate on the "num" field.
func NumNEQ(v uint32) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldNum, v))
}

// NumIn applies the In predicate on the "num" field.
func NumIn(vs ...uint32) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldNum, vs...))
}

// NumNotIn applies the NotIn predicate on the "num" field.
func NumNotIn(vs ...uint32) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldNum, vs...))
}

// NumGT applies the GT predicate on the "num" field.
func NumGT(v uint32) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldNum, v))
}

// NumGTE applies the GTE predicate on the "num" field.
func NumGTE(v uint32) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldNum, v))
}

// NumLT applies the LT predicate on the "num" field.
func NumLT(v uint32) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldNum, v))
}

// NumLTE applies the LTE predicate on the "num" field.
func NumLTE(v uint32) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldNum, v))
}

// ToRepositoryIDEQ applies the EQ predicate on the "to_repository_id" field.
func ToRepositoryIDEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldToRepositoryID, v))
}

// ToRepositoryIDNEQ applies the NEQ predicate on the "to_repository_id" field.
func ToRepositoryIDNEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldToRepositoryID, v))
}

// ToRepositoryIDIn applies the In predicate on the "to_repository_id" field.
func ToRepositoryIDIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldToRepositoryID, vs...))
}

// ToRepositoryIDNotIn applies the NotIn predicate on the "to_repository_id" field.
func ToRepositoryIDNotIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldToRepositoryID, vs...))
}

// ToRepositoryIDGT applies the GT predicate on the "to_repository_id" field.
func ToRepositoryIDGT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldToRepositoryID, v))
}

// ToRepositoryIDGTE applies the GTE predicate on the "to_repository_id" field.
func ToRepositoryIDGTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldToRepositoryID, v))
}

// ToRepositoryIDLT applies the LT predicate on the "to_repository_id" field.
func ToRepositoryIDLT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldToRepositoryID, v))
}

// ToRepositoryIDLTE applies the LTE predicate on the "to_repository_id" field.
func ToRepositoryIDLTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldToRepositoryID, v))
}

// ToRepositoryIDContains applies the Contains predicate on the "to_repository_id" field.
func ToRepositoryIDContains(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContains(FieldToRepositoryID, v))
}

// ToRepositoryIDHasPrefix applies the HasPrefix predicate on the "to_repository_id" field.
func ToRepositoryIDHasPrefix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasPrefix(FieldToRepositoryID, v))
}

// ToRepositoryIDHasSuffix applies the HasSuffix predicate on the "to_repository_id" field.
func ToRepositoryIDHasSuffix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasSuffix(FieldToRepositoryID, v))
}

// ToRepositoryIDIsNil applies the IsNil predicate on the "to_repository_id" field.
func ToRepositoryIDIsNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIsNull(FieldToRepositoryID))
}

// ToRepositoryIDNotNil applies the NotNil predicate on the "to_repository_id" field.
func ToRepositoryIDNotNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotNull(FieldToRepositoryID))
}

// ToRepositoryIDEqualFold applies the EqualFold predicate on the "to_repository_id" field.
func ToRepositoryIDEqualFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEqualFold(FieldToRepositoryID, v))
}

// ToRepositoryIDContainsFold applies the ContainsFold predicate on the "to_repository_id" field.
func ToRepositoryIDContainsFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContainsFold(FieldToRepositoryID, v))
}

// ToRepositoryAreaIDEQ applies the EQ predicate on the "to_repository_area_id" field.
func ToRepositoryAreaIDEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldToRepositoryAreaID, v))
}

// ToRepositoryAreaIDNEQ applies the NEQ predicate on the "to_repository_area_id" field.
func ToRepositoryAreaIDNEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldToRepositoryAreaID, v))
}

// ToRepositoryAreaIDIn applies the In predicate on the "to_repository_area_id" field.
func ToRepositoryAreaIDIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldToRepositoryAreaID, vs...))
}

// ToRepositoryAreaIDNotIn applies the NotIn predicate on the "to_repository_area_id" field.
func ToRepositoryAreaIDNotIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldToRepositoryAreaID, vs...))
}

// ToRepositoryAreaIDGT applies the GT predicate on the "to_repository_area_id" field.
func ToRepositoryAreaIDGT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldToRepositoryAreaID, v))
}

// ToRepositoryAreaIDGTE applies the GTE predicate on the "to_repository_area_id" field.
func ToRepositoryAreaIDGTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldToRepositoryAreaID, v))
}

// ToRepositoryAreaIDLT applies the LT predicate on the "to_repository_area_id" field.
func ToRepositoryAreaIDLT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldToRepositoryAreaID, v))
}

// ToRepositoryAreaIDLTE applies the LTE predicate on the "to_repository_area_id" field.
func ToRepositoryAreaIDLTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldToRepositoryAreaID, v))
}

// ToRepositoryAreaIDContains applies the Contains predicate on the "to_repository_area_id" field.
func ToRepositoryAreaIDContains(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContains(FieldToRepositoryAreaID, v))
}

// ToRepositoryAreaIDHasPrefix applies the HasPrefix predicate on the "to_repository_area_id" field.
func ToRepositoryAreaIDHasPrefix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasPrefix(FieldToRepositoryAreaID, v))
}

// ToRepositoryAreaIDHasSuffix applies the HasSuffix predicate on the "to_repository_area_id" field.
func ToRepositoryAreaIDHasSuffix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasSuffix(FieldToRepositoryAreaID, v))
}

// ToRepositoryAreaIDIsNil applies the IsNil predicate on the "to_repository_area_id" field.
func ToRepositoryAreaIDIsNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIsNull(FieldToRepositoryAreaID))
}

// ToRepositoryAreaIDNotNil applies the NotNil predicate on the "to_repository_area_id" field.
func ToRepositoryAreaIDNotNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotNull(FieldToRepositoryAreaID))
}

// ToRepositoryAreaIDEqualFold applies the EqualFold predicate on the "to_repository_area_id" field.
func ToRepositoryAreaIDEqualFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEqualFold(FieldToRepositoryAreaID, v))
}

// ToRepositoryAreaIDContainsFold applies the ContainsFold predicate on the "to_repository_area_id" field.
func ToRepositoryAreaIDContainsFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContainsFold(FieldToRepositoryAreaID, v))
}

// ToRepositoryPositionIDEQ applies the EQ predicate on the "to_repository_position_id" field.
func ToRepositoryPositionIDEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldToRepositoryPositionID, v))
}

// ToRepositoryPositionIDNEQ applies the NEQ predicate on the "to_repository_position_id" field.
func ToRepositoryPositionIDNEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldToRepositoryPositionID, v))
}

// ToRepositoryPositionIDIn applies the In predicate on the "to_repository_position_id" field.
func ToRepositoryPositionIDIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldToRepositoryPositionID, vs...))
}

// ToRepositoryPositionIDNotIn applies the NotIn predicate on the "to_repository_position_id" field.
func ToRepositoryPositionIDNotIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldToRepositoryPositionID, vs...))
}

// ToRepositoryPositionIDGT applies the GT predicate on the "to_repository_position_id" field.
func ToRepositoryPositionIDGT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldToRepositoryPositionID, v))
}

// ToRepositoryPositionIDGTE applies the GTE predicate on the "to_repository_position_id" field.
func ToRepositoryPositionIDGTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldToRepositoryPositionID, v))
}

// ToRepositoryPositionIDLT applies the LT predicate on the "to_repository_position_id" field.
func ToRepositoryPositionIDLT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldToRepositoryPositionID, v))
}

// ToRepositoryPositionIDLTE applies the LTE predicate on the "to_repository_position_id" field.
func ToRepositoryPositionIDLTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldToRepositoryPositionID, v))
}

// ToRepositoryPositionIDContains applies the Contains predicate on the "to_repository_position_id" field.
func ToRepositoryPositionIDContains(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContains(FieldToRepositoryPositionID, v))
}

// ToRepositoryPositionIDHasPrefix applies the HasPrefix predicate on the "to_repository_position_id" field.
func ToRepositoryPositionIDHasPrefix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasPrefix(FieldToRepositoryPositionID, v))
}

// ToRepositoryPositionIDHasSuffix applies the HasSuffix predicate on the "to_repository_position_id" field.
func ToRepositoryPositionIDHasSuffix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasSuffix(FieldToRepositoryPositionID, v))
}

// ToRepositoryPositionIDIsNil applies the IsNil predicate on the "to_repository_position_id" field.
func ToRepositoryPositionIDIsNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIsNull(FieldToRepositoryPositionID))
}

// ToRepositoryPositionIDNotNil applies the NotNil predicate on the "to_repository_position_id" field.
func ToRepositoryPositionIDNotNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotNull(FieldToRepositoryPositionID))
}

// ToRepositoryPositionIDEqualFold applies the EqualFold predicate on the "to_repository_position_id" field.
func ToRepositoryPositionIDEqualFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEqualFold(FieldToRepositoryPositionID, v))
}

// ToRepositoryPositionIDContainsFold applies the ContainsFold predicate on the "to_repository_position_id" field.
func ToRepositoryPositionIDContainsFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContainsFold(FieldToRepositoryPositionID, v))
}

// ReasonEQ applies the EQ predicate on the "reason" field.
func ReasonEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldReason, v))
}

// ReasonNEQ applies the NEQ predicate on the "reason" field.
func ReasonNEQ(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldReason, v))
}

// ReasonIn applies the In predicate on the "reason" field.
func ReasonIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldReason, vs...))
}

// ReasonNotIn applies the NotIn predicate on the "reason" field.
func ReasonNotIn(vs ...string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldReason, vs...))
}

// ReasonGT applies the GT predicate on the "reason" field.
func ReasonGT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldReason, v))
}

// ReasonGTE applies the GTE predicate on the "reason" field.
func ReasonGTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldReason, v))
}

// ReasonLT applies the LT predicate on the "reason" field.
func ReasonLT(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldReason, v))
}

// ReasonLTE applies the LTE predicate on the "reason" field.
func ReasonLTE(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldReason, v))
}

// ReasonContains applies the Contains predicate on the "reason" field.
func ReasonContains(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContains(FieldReason, v))
}

// ReasonHasPrefix applies the HasPrefix predicate on the "reason" field.
func ReasonHasPrefix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasPrefix(FieldReason, v))
}

// ReasonHasSuffix applies the HasSuffix predicate on the "reason" field.
func ReasonHasSuffix(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldHasSuffix(FieldReason, v))
}

// ReasonIsNil applies the IsNil predicate on the "reason" field.
func ReasonIsNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIsNull(FieldReason))
}

// ReasonNotNil applies the NotNil predicate on the "reason" field.
func ReasonNotNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotNull(FieldReason))
}

// ReasonEqualFold applies the EqualFold predicate on the "reason" field.
func ReasonEqualFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEqualFold(FieldReason, v))
}

// ReasonContainsFold applies the ContainsFold predicate on the "reason" field.
func ReasonContainsFold(v string) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldContainsFold(FieldReason, v))
}

// ReturnTimeEQ applies the EQ predicate on the "return_time" field.
func ReturnTimeEQ(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldEQ(FieldReturnTime, v))
}

// ReturnTimeNEQ applies the NEQ predicate on the "return_time" field.
func ReturnTimeNEQ(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNEQ(FieldReturnTime, v))
}

// ReturnTimeIn applies the In predicate on the "return_time" field.
func ReturnTimeIn(vs ...time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIn(FieldReturnTime, vs...))
}

// ReturnTimeNotIn applies the NotIn predicate on the "return_time" field.
func ReturnTimeNotIn(vs ...time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotIn(FieldReturnTime, vs...))
}

// ReturnTimeGT applies the GT predicate on the "return_time" field.
func ReturnTimeGT(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGT(FieldReturnTime, v))
}

// ReturnTimeGTE applies the GTE predicate on the "return_time" field.
func ReturnTimeGTE(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldGTE(FieldReturnTime, v))
}

// ReturnTimeLT applies the LT predicate on the "return_time" field.
func ReturnTimeLT(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLT(FieldReturnTime, v))
}

// ReturnTimeLTE applies the LTE predicate on the "return_time" field.
func ReturnTimeLTE(v time.Time) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldLTE(FieldReturnTime, v))
}

// ReturnTimeIsNil applies the IsNil predicate on the "return_time" field.
func ReturnTimeIsNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldIsNull(FieldReturnTime))
}

// ReturnTimeNotNil applies the NotNil predicate on the "return_time" field.
func ReturnTimeNotNil() predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.FieldNotNull(FieldReturnTime))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.WmsReturnOrderDetail) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.WmsReturnOrderDetail) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.WmsReturnOrderDetail) predicate.WmsReturnOrderDetail {
	return predicate.WmsReturnOrderDetail(sql.NotPredicates(p))
}
