// Code generated by ent, DO NOT EDIT.

package wmstransferorderdetail

import (
	"kratos-mono-demo/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContainsFold(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldUpdatedAt, v))
}

// CreatedBy applies equality check predicate on the "created_by" field. It's identical to CreatedByEQ.
func CreatedBy(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldCreatedBy, v))
}

// UpdatedBy applies equality check predicate on the "updated_by" field. It's identical to UpdatedByEQ.
func UpdatedBy(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldUpdatedBy, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldRemark, v))
}

// OrderID applies equality check predicate on the "order_id" field. It's identical to OrderIDEQ.
func OrderID(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldOrderID, v))
}

// MaterialID applies equality check predicate on the "material_id" field. It's identical to MaterialIDEQ.
func MaterialID(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldMaterialID, v))
}

// MaterialName applies equality check predicate on the "material_name" field. It's identical to MaterialNameEQ.
func MaterialName(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldMaterialName, v))
}

// Code applies equality check predicate on the "code" field. It's identical to CodeEQ.
func Code(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldCode, v))
}

// EquipmentID applies equality check predicate on the "equipment_id" field. It's identical to EquipmentIDEQ.
func EquipmentID(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldEquipmentID, v))
}

// EquipmentTypeID applies equality check predicate on the "equipment_type_id" field. It's identical to EquipmentTypeIDEQ.
func EquipmentTypeID(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldEquipmentTypeID, v))
}

// RepositoryID applies equality check predicate on the "repository_id" field. It's identical to RepositoryIDEQ.
func RepositoryID(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldRepositoryID, v))
}

// RepositoryAreaID applies equality check predicate on the "repository_area_id" field. It's identical to RepositoryAreaIDEQ.
func RepositoryAreaID(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldRepositoryAreaID, v))
}

// RepositoryPositionID applies equality check predicate on the "repository_position_id" field. It's identical to RepositoryPositionIDEQ.
func RepositoryPositionID(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldRepositoryPositionID, v))
}

// OwnerID applies equality check predicate on the "owner_id" field. It's identical to OwnerIDEQ.
func OwnerID(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldOwnerID, v))
}

// ModelNo applies equality check predicate on the "ModelNo" field. It's identical to ModelNoEQ.
func ModelNo(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldModelNo, v))
}

// MeasureUnitID applies equality check predicate on the "measure_unit_id" field. It's identical to MeasureUnitIDEQ.
func MeasureUnitID(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldMeasureUnitID, v))
}

// Num applies equality check predicate on the "num" field. It's identical to NumEQ.
func Num(v uint32) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldNum, v))
}

// TransferReason applies equality check predicate on the "transfer_reason" field. It's identical to TransferReasonEQ.
func TransferReason(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldTransferReason, v))
}

// TransferTime applies equality check predicate on the "transfer_time" field. It's identical to TransferTimeEQ.
func TransferTime(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldTransferTime, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldUpdatedAt, v))
}

// CreatedByEQ applies the EQ predicate on the "created_by" field.
func CreatedByEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldCreatedBy, v))
}

// CreatedByNEQ applies the NEQ predicate on the "created_by" field.
func CreatedByNEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldCreatedBy, v))
}

// CreatedByIn applies the In predicate on the "created_by" field.
func CreatedByIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldCreatedBy, vs...))
}

// CreatedByNotIn applies the NotIn predicate on the "created_by" field.
func CreatedByNotIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldCreatedBy, vs...))
}

// CreatedByGT applies the GT predicate on the "created_by" field.
func CreatedByGT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldCreatedBy, v))
}

// CreatedByGTE applies the GTE predicate on the "created_by" field.
func CreatedByGTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldCreatedBy, v))
}

// CreatedByLT applies the LT predicate on the "created_by" field.
func CreatedByLT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldCreatedBy, v))
}

// CreatedByLTE applies the LTE predicate on the "created_by" field.
func CreatedByLTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldCreatedBy, v))
}

// CreatedByContains applies the Contains predicate on the "created_by" field.
func CreatedByContains(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContains(FieldCreatedBy, v))
}

// CreatedByHasPrefix applies the HasPrefix predicate on the "created_by" field.
func CreatedByHasPrefix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasPrefix(FieldCreatedBy, v))
}

// CreatedByHasSuffix applies the HasSuffix predicate on the "created_by" field.
func CreatedByHasSuffix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasSuffix(FieldCreatedBy, v))
}

// CreatedByIsNil applies the IsNil predicate on the "created_by" field.
func CreatedByIsNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIsNull(FieldCreatedBy))
}

// CreatedByNotNil applies the NotNil predicate on the "created_by" field.
func CreatedByNotNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotNull(FieldCreatedBy))
}

// CreatedByEqualFold applies the EqualFold predicate on the "created_by" field.
func CreatedByEqualFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEqualFold(FieldCreatedBy, v))
}

// CreatedByContainsFold applies the ContainsFold predicate on the "created_by" field.
func CreatedByContainsFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContainsFold(FieldCreatedBy, v))
}

// UpdatedByEQ applies the EQ predicate on the "updated_by" field.
func UpdatedByEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldUpdatedBy, v))
}

// UpdatedByNEQ applies the NEQ predicate on the "updated_by" field.
func UpdatedByNEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldUpdatedBy, v))
}

// UpdatedByIn applies the In predicate on the "updated_by" field.
func UpdatedByIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldUpdatedBy, vs...))
}

// UpdatedByNotIn applies the NotIn predicate on the "updated_by" field.
func UpdatedByNotIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldUpdatedBy, vs...))
}

// UpdatedByGT applies the GT predicate on the "updated_by" field.
func UpdatedByGT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldUpdatedBy, v))
}

// UpdatedByGTE applies the GTE predicate on the "updated_by" field.
func UpdatedByGTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldUpdatedBy, v))
}

// UpdatedByLT applies the LT predicate on the "updated_by" field.
func UpdatedByLT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldUpdatedBy, v))
}

// UpdatedByLTE applies the LTE predicate on the "updated_by" field.
func UpdatedByLTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldUpdatedBy, v))
}

// UpdatedByContains applies the Contains predicate on the "updated_by" field.
func UpdatedByContains(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContains(FieldUpdatedBy, v))
}

// UpdatedByHasPrefix applies the HasPrefix predicate on the "updated_by" field.
func UpdatedByHasPrefix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasPrefix(FieldUpdatedBy, v))
}

// UpdatedByHasSuffix applies the HasSuffix predicate on the "updated_by" field.
func UpdatedByHasSuffix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasSuffix(FieldUpdatedBy, v))
}

// UpdatedByIsNil applies the IsNil predicate on the "updated_by" field.
func UpdatedByIsNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIsNull(FieldUpdatedBy))
}

// UpdatedByNotNil applies the NotNil predicate on the "updated_by" field.
func UpdatedByNotNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotNull(FieldUpdatedBy))
}

// UpdatedByEqualFold applies the EqualFold predicate on the "updated_by" field.
func UpdatedByEqualFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEqualFold(FieldUpdatedBy, v))
}

// UpdatedByContainsFold applies the ContainsFold predicate on the "updated_by" field.
func UpdatedByContainsFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContainsFold(FieldUpdatedBy, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContainsFold(FieldRemark, v))
}

// OrderIDEQ applies the EQ predicate on the "order_id" field.
func OrderIDEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldOrderID, v))
}

// OrderIDNEQ applies the NEQ predicate on the "order_id" field.
func OrderIDNEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldOrderID, v))
}

// OrderIDIn applies the In predicate on the "order_id" field.
func OrderIDIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldOrderID, vs...))
}

// OrderIDNotIn applies the NotIn predicate on the "order_id" field.
func OrderIDNotIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldOrderID, vs...))
}

// OrderIDGT applies the GT predicate on the "order_id" field.
func OrderIDGT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldOrderID, v))
}

// OrderIDGTE applies the GTE predicate on the "order_id" field.
func OrderIDGTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldOrderID, v))
}

// OrderIDLT applies the LT predicate on the "order_id" field.
func OrderIDLT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldOrderID, v))
}

// OrderIDLTE applies the LTE predicate on the "order_id" field.
func OrderIDLTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldOrderID, v))
}

// OrderIDContains applies the Contains predicate on the "order_id" field.
func OrderIDContains(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContains(FieldOrderID, v))
}

// OrderIDHasPrefix applies the HasPrefix predicate on the "order_id" field.
func OrderIDHasPrefix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasPrefix(FieldOrderID, v))
}

// OrderIDHasSuffix applies the HasSuffix predicate on the "order_id" field.
func OrderIDHasSuffix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasSuffix(FieldOrderID, v))
}

// OrderIDIsNil applies the IsNil predicate on the "order_id" field.
func OrderIDIsNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIsNull(FieldOrderID))
}

// OrderIDNotNil applies the NotNil predicate on the "order_id" field.
func OrderIDNotNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotNull(FieldOrderID))
}

// OrderIDEqualFold applies the EqualFold predicate on the "order_id" field.
func OrderIDEqualFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEqualFold(FieldOrderID, v))
}

// OrderIDContainsFold applies the ContainsFold predicate on the "order_id" field.
func OrderIDContainsFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContainsFold(FieldOrderID, v))
}

// MaterialIDEQ applies the EQ predicate on the "material_id" field.
func MaterialIDEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldMaterialID, v))
}

// MaterialIDNEQ applies the NEQ predicate on the "material_id" field.
func MaterialIDNEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldMaterialID, v))
}

// MaterialIDIn applies the In predicate on the "material_id" field.
func MaterialIDIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldMaterialID, vs...))
}

// MaterialIDNotIn applies the NotIn predicate on the "material_id" field.
func MaterialIDNotIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldMaterialID, vs...))
}

// MaterialIDGT applies the GT predicate on the "material_id" field.
func MaterialIDGT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldMaterialID, v))
}

// MaterialIDGTE applies the GTE predicate on the "material_id" field.
func MaterialIDGTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldMaterialID, v))
}

// MaterialIDLT applies the LT predicate on the "material_id" field.
func MaterialIDLT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldMaterialID, v))
}

// MaterialIDLTE applies the LTE predicate on the "material_id" field.
func MaterialIDLTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldMaterialID, v))
}

// MaterialIDContains applies the Contains predicate on the "material_id" field.
func MaterialIDContains(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContains(FieldMaterialID, v))
}

// MaterialIDHasPrefix applies the HasPrefix predicate on the "material_id" field.
func MaterialIDHasPrefix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasPrefix(FieldMaterialID, v))
}

// MaterialIDHasSuffix applies the HasSuffix predicate on the "material_id" field.
func MaterialIDHasSuffix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasSuffix(FieldMaterialID, v))
}

// MaterialIDIsNil applies the IsNil predicate on the "material_id" field.
func MaterialIDIsNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIsNull(FieldMaterialID))
}

// MaterialIDNotNil applies the NotNil predicate on the "material_id" field.
func MaterialIDNotNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotNull(FieldMaterialID))
}

// MaterialIDEqualFold applies the EqualFold predicate on the "material_id" field.
func MaterialIDEqualFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEqualFold(FieldMaterialID, v))
}

// MaterialIDContainsFold applies the ContainsFold predicate on the "material_id" field.
func MaterialIDContainsFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContainsFold(FieldMaterialID, v))
}

// MaterialNameEQ applies the EQ predicate on the "material_name" field.
func MaterialNameEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldMaterialName, v))
}

// MaterialNameNEQ applies the NEQ predicate on the "material_name" field.
func MaterialNameNEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldMaterialName, v))
}

// MaterialNameIn applies the In predicate on the "material_name" field.
func MaterialNameIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldMaterialName, vs...))
}

// MaterialNameNotIn applies the NotIn predicate on the "material_name" field.
func MaterialNameNotIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldMaterialName, vs...))
}

// MaterialNameGT applies the GT predicate on the "material_name" field.
func MaterialNameGT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldMaterialName, v))
}

// MaterialNameGTE applies the GTE predicate on the "material_name" field.
func MaterialNameGTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldMaterialName, v))
}

// MaterialNameLT applies the LT predicate on the "material_name" field.
func MaterialNameLT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldMaterialName, v))
}

// MaterialNameLTE applies the LTE predicate on the "material_name" field.
func MaterialNameLTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldMaterialName, v))
}

// MaterialNameContains applies the Contains predicate on the "material_name" field.
func MaterialNameContains(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContains(FieldMaterialName, v))
}

// MaterialNameHasPrefix applies the HasPrefix predicate on the "material_name" field.
func MaterialNameHasPrefix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasPrefix(FieldMaterialName, v))
}

// MaterialNameHasSuffix applies the HasSuffix predicate on the "material_name" field.
func MaterialNameHasSuffix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasSuffix(FieldMaterialName, v))
}

// MaterialNameIsNil applies the IsNil predicate on the "material_name" field.
func MaterialNameIsNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIsNull(FieldMaterialName))
}

// MaterialNameNotNil applies the NotNil predicate on the "material_name" field.
func MaterialNameNotNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotNull(FieldMaterialName))
}

// MaterialNameEqualFold applies the EqualFold predicate on the "material_name" field.
func MaterialNameEqualFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEqualFold(FieldMaterialName, v))
}

// MaterialNameContainsFold applies the ContainsFold predicate on the "material_name" field.
func MaterialNameContainsFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContainsFold(FieldMaterialName, v))
}

// CodeEQ applies the EQ predicate on the "code" field.
func CodeEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldCode, v))
}

// CodeNEQ applies the NEQ predicate on the "code" field.
func CodeNEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldCode, v))
}

// CodeIn applies the In predicate on the "code" field.
func CodeIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldCode, vs...))
}

// CodeNotIn applies the NotIn predicate on the "code" field.
func CodeNotIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldCode, vs...))
}

// CodeGT applies the GT predicate on the "code" field.
func CodeGT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldCode, v))
}

// CodeGTE applies the GTE predicate on the "code" field.
func CodeGTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldCode, v))
}

// CodeLT applies the LT predicate on the "code" field.
func CodeLT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldCode, v))
}

// CodeLTE applies the LTE predicate on the "code" field.
func CodeLTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldCode, v))
}

// CodeContains applies the Contains predicate on the "code" field.
func CodeContains(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContains(FieldCode, v))
}

// CodeHasPrefix applies the HasPrefix predicate on the "code" field.
func CodeHasPrefix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasPrefix(FieldCode, v))
}

// CodeHasSuffix applies the HasSuffix predicate on the "code" field.
func CodeHasSuffix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasSuffix(FieldCode, v))
}

// CodeEqualFold applies the EqualFold predicate on the "code" field.
func CodeEqualFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEqualFold(FieldCode, v))
}

// CodeContainsFold applies the ContainsFold predicate on the "code" field.
func CodeContainsFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContainsFold(FieldCode, v))
}

// EquipmentIDEQ applies the EQ predicate on the "equipment_id" field.
func EquipmentIDEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldEquipmentID, v))
}

// EquipmentIDNEQ applies the NEQ predicate on the "equipment_id" field.
func EquipmentIDNEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldEquipmentID, v))
}

// EquipmentIDIn applies the In predicate on the "equipment_id" field.
func EquipmentIDIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldEquipmentID, vs...))
}

// EquipmentIDNotIn applies the NotIn predicate on the "equipment_id" field.
func EquipmentIDNotIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldEquipmentID, vs...))
}

// EquipmentIDGT applies the GT predicate on the "equipment_id" field.
func EquipmentIDGT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldEquipmentID, v))
}

// EquipmentIDGTE applies the GTE predicate on the "equipment_id" field.
func EquipmentIDGTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldEquipmentID, v))
}

// EquipmentIDLT applies the LT predicate on the "equipment_id" field.
func EquipmentIDLT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldEquipmentID, v))
}

// EquipmentIDLTE applies the LTE predicate on the "equipment_id" field.
func EquipmentIDLTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldEquipmentID, v))
}

// EquipmentIDContains applies the Contains predicate on the "equipment_id" field.
func EquipmentIDContains(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContains(FieldEquipmentID, v))
}

// EquipmentIDHasPrefix applies the HasPrefix predicate on the "equipment_id" field.
func EquipmentIDHasPrefix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasPrefix(FieldEquipmentID, v))
}

// EquipmentIDHasSuffix applies the HasSuffix predicate on the "equipment_id" field.
func EquipmentIDHasSuffix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasSuffix(FieldEquipmentID, v))
}

// EquipmentIDIsNil applies the IsNil predicate on the "equipment_id" field.
func EquipmentIDIsNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIsNull(FieldEquipmentID))
}

// EquipmentIDNotNil applies the NotNil predicate on the "equipment_id" field.
func EquipmentIDNotNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotNull(FieldEquipmentID))
}

// EquipmentIDEqualFold applies the EqualFold predicate on the "equipment_id" field.
func EquipmentIDEqualFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEqualFold(FieldEquipmentID, v))
}

// EquipmentIDContainsFold applies the ContainsFold predicate on the "equipment_id" field.
func EquipmentIDContainsFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContainsFold(FieldEquipmentID, v))
}

// EquipmentTypeIDEQ applies the EQ predicate on the "equipment_type_id" field.
func EquipmentTypeIDEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDNEQ applies the NEQ predicate on the "equipment_type_id" field.
func EquipmentTypeIDNEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDIn applies the In predicate on the "equipment_type_id" field.
func EquipmentTypeIDIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldEquipmentTypeID, vs...))
}

// EquipmentTypeIDNotIn applies the NotIn predicate on the "equipment_type_id" field.
func EquipmentTypeIDNotIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldEquipmentTypeID, vs...))
}

// EquipmentTypeIDGT applies the GT predicate on the "equipment_type_id" field.
func EquipmentTypeIDGT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDGTE applies the GTE predicate on the "equipment_type_id" field.
func EquipmentTypeIDGTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDLT applies the LT predicate on the "equipment_type_id" field.
func EquipmentTypeIDLT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDLTE applies the LTE predicate on the "equipment_type_id" field.
func EquipmentTypeIDLTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDContains applies the Contains predicate on the "equipment_type_id" field.
func EquipmentTypeIDContains(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContains(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDHasPrefix applies the HasPrefix predicate on the "equipment_type_id" field.
func EquipmentTypeIDHasPrefix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasPrefix(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDHasSuffix applies the HasSuffix predicate on the "equipment_type_id" field.
func EquipmentTypeIDHasSuffix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasSuffix(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDIsNil applies the IsNil predicate on the "equipment_type_id" field.
func EquipmentTypeIDIsNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIsNull(FieldEquipmentTypeID))
}

// EquipmentTypeIDNotNil applies the NotNil predicate on the "equipment_type_id" field.
func EquipmentTypeIDNotNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotNull(FieldEquipmentTypeID))
}

// EquipmentTypeIDEqualFold applies the EqualFold predicate on the "equipment_type_id" field.
func EquipmentTypeIDEqualFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEqualFold(FieldEquipmentTypeID, v))
}

// EquipmentTypeIDContainsFold applies the ContainsFold predicate on the "equipment_type_id" field.
func EquipmentTypeIDContainsFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContainsFold(FieldEquipmentTypeID, v))
}

// FeatureIsNil applies the IsNil predicate on the "feature" field.
func FeatureIsNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIsNull(FieldFeature))
}

// FeatureNotNil applies the NotNil predicate on the "feature" field.
func FeatureNotNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotNull(FieldFeature))
}

// RepositoryIDEQ applies the EQ predicate on the "repository_id" field.
func RepositoryIDEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldRepositoryID, v))
}

// RepositoryIDNEQ applies the NEQ predicate on the "repository_id" field.
func RepositoryIDNEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldRepositoryID, v))
}

// RepositoryIDIn applies the In predicate on the "repository_id" field.
func RepositoryIDIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldRepositoryID, vs...))
}

// RepositoryIDNotIn applies the NotIn predicate on the "repository_id" field.
func RepositoryIDNotIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldRepositoryID, vs...))
}

// RepositoryIDGT applies the GT predicate on the "repository_id" field.
func RepositoryIDGT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldRepositoryID, v))
}

// RepositoryIDGTE applies the GTE predicate on the "repository_id" field.
func RepositoryIDGTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldRepositoryID, v))
}

// RepositoryIDLT applies the LT predicate on the "repository_id" field.
func RepositoryIDLT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldRepositoryID, v))
}

// RepositoryIDLTE applies the LTE predicate on the "repository_id" field.
func RepositoryIDLTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldRepositoryID, v))
}

// RepositoryIDContains applies the Contains predicate on the "repository_id" field.
func RepositoryIDContains(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContains(FieldRepositoryID, v))
}

// RepositoryIDHasPrefix applies the HasPrefix predicate on the "repository_id" field.
func RepositoryIDHasPrefix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasPrefix(FieldRepositoryID, v))
}

// RepositoryIDHasSuffix applies the HasSuffix predicate on the "repository_id" field.
func RepositoryIDHasSuffix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasSuffix(FieldRepositoryID, v))
}

// RepositoryIDIsNil applies the IsNil predicate on the "repository_id" field.
func RepositoryIDIsNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIsNull(FieldRepositoryID))
}

// RepositoryIDNotNil applies the NotNil predicate on the "repository_id" field.
func RepositoryIDNotNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotNull(FieldRepositoryID))
}

// RepositoryIDEqualFold applies the EqualFold predicate on the "repository_id" field.
func RepositoryIDEqualFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEqualFold(FieldRepositoryID, v))
}

// RepositoryIDContainsFold applies the ContainsFold predicate on the "repository_id" field.
func RepositoryIDContainsFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContainsFold(FieldRepositoryID, v))
}

// RepositoryAreaIDEQ applies the EQ predicate on the "repository_area_id" field.
func RepositoryAreaIDEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDNEQ applies the NEQ predicate on the "repository_area_id" field.
func RepositoryAreaIDNEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDIn applies the In predicate on the "repository_area_id" field.
func RepositoryAreaIDIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldRepositoryAreaID, vs...))
}

// RepositoryAreaIDNotIn applies the NotIn predicate on the "repository_area_id" field.
func RepositoryAreaIDNotIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldRepositoryAreaID, vs...))
}

// RepositoryAreaIDGT applies the GT predicate on the "repository_area_id" field.
func RepositoryAreaIDGT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDGTE applies the GTE predicate on the "repository_area_id" field.
func RepositoryAreaIDGTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDLT applies the LT predicate on the "repository_area_id" field.
func RepositoryAreaIDLT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDLTE applies the LTE predicate on the "repository_area_id" field.
func RepositoryAreaIDLTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDContains applies the Contains predicate on the "repository_area_id" field.
func RepositoryAreaIDContains(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContains(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDHasPrefix applies the HasPrefix predicate on the "repository_area_id" field.
func RepositoryAreaIDHasPrefix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasPrefix(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDHasSuffix applies the HasSuffix predicate on the "repository_area_id" field.
func RepositoryAreaIDHasSuffix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasSuffix(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDIsNil applies the IsNil predicate on the "repository_area_id" field.
func RepositoryAreaIDIsNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIsNull(FieldRepositoryAreaID))
}

// RepositoryAreaIDNotNil applies the NotNil predicate on the "repository_area_id" field.
func RepositoryAreaIDNotNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotNull(FieldRepositoryAreaID))
}

// RepositoryAreaIDEqualFold applies the EqualFold predicate on the "repository_area_id" field.
func RepositoryAreaIDEqualFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEqualFold(FieldRepositoryAreaID, v))
}

// RepositoryAreaIDContainsFold applies the ContainsFold predicate on the "repository_area_id" field.
func RepositoryAreaIDContainsFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContainsFold(FieldRepositoryAreaID, v))
}

// RepositoryPositionIDEQ applies the EQ predicate on the "repository_position_id" field.
func RepositoryPositionIDEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDNEQ applies the NEQ predicate on the "repository_position_id" field.
func RepositoryPositionIDNEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDIn applies the In predicate on the "repository_position_id" field.
func RepositoryPositionIDIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldRepositoryPositionID, vs...))
}

// RepositoryPositionIDNotIn applies the NotIn predicate on the "repository_position_id" field.
func RepositoryPositionIDNotIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldRepositoryPositionID, vs...))
}

// RepositoryPositionIDGT applies the GT predicate on the "repository_position_id" field.
func RepositoryPositionIDGT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDGTE applies the GTE predicate on the "repository_position_id" field.
func RepositoryPositionIDGTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDLT applies the LT predicate on the "repository_position_id" field.
func RepositoryPositionIDLT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDLTE applies the LTE predicate on the "repository_position_id" field.
func RepositoryPositionIDLTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDContains applies the Contains predicate on the "repository_position_id" field.
func RepositoryPositionIDContains(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContains(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDHasPrefix applies the HasPrefix predicate on the "repository_position_id" field.
func RepositoryPositionIDHasPrefix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasPrefix(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDHasSuffix applies the HasSuffix predicate on the "repository_position_id" field.
func RepositoryPositionIDHasSuffix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasSuffix(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDIsNil applies the IsNil predicate on the "repository_position_id" field.
func RepositoryPositionIDIsNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIsNull(FieldRepositoryPositionID))
}

// RepositoryPositionIDNotNil applies the NotNil predicate on the "repository_position_id" field.
func RepositoryPositionIDNotNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotNull(FieldRepositoryPositionID))
}

// RepositoryPositionIDEqualFold applies the EqualFold predicate on the "repository_position_id" field.
func RepositoryPositionIDEqualFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEqualFold(FieldRepositoryPositionID, v))
}

// RepositoryPositionIDContainsFold applies the ContainsFold predicate on the "repository_position_id" field.
func RepositoryPositionIDContainsFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContainsFold(FieldRepositoryPositionID, v))
}

// OwnerIDEQ applies the EQ predicate on the "owner_id" field.
func OwnerIDEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldOwnerID, v))
}

// OwnerIDNEQ applies the NEQ predicate on the "owner_id" field.
func OwnerIDNEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldOwnerID, v))
}

// OwnerIDIn applies the In predicate on the "owner_id" field.
func OwnerIDIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldOwnerID, vs...))
}

// OwnerIDNotIn applies the NotIn predicate on the "owner_id" field.
func OwnerIDNotIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldOwnerID, vs...))
}

// OwnerIDGT applies the GT predicate on the "owner_id" field.
func OwnerIDGT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldOwnerID, v))
}

// OwnerIDGTE applies the GTE predicate on the "owner_id" field.
func OwnerIDGTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldOwnerID, v))
}

// OwnerIDLT applies the LT predicate on the "owner_id" field.
func OwnerIDLT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldOwnerID, v))
}

// OwnerIDLTE applies the LTE predicate on the "owner_id" field.
func OwnerIDLTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldOwnerID, v))
}

// OwnerIDContains applies the Contains predicate on the "owner_id" field.
func OwnerIDContains(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContains(FieldOwnerID, v))
}

// OwnerIDHasPrefix applies the HasPrefix predicate on the "owner_id" field.
func OwnerIDHasPrefix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasPrefix(FieldOwnerID, v))
}

// OwnerIDHasSuffix applies the HasSuffix predicate on the "owner_id" field.
func OwnerIDHasSuffix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasSuffix(FieldOwnerID, v))
}

// OwnerIDIsNil applies the IsNil predicate on the "owner_id" field.
func OwnerIDIsNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIsNull(FieldOwnerID))
}

// OwnerIDNotNil applies the NotNil predicate on the "owner_id" field.
func OwnerIDNotNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotNull(FieldOwnerID))
}

// OwnerIDEqualFold applies the EqualFold predicate on the "owner_id" field.
func OwnerIDEqualFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEqualFold(FieldOwnerID, v))
}

// OwnerIDContainsFold applies the ContainsFold predicate on the "owner_id" field.
func OwnerIDContainsFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContainsFold(FieldOwnerID, v))
}

// ModelNoEQ applies the EQ predicate on the "ModelNo" field.
func ModelNoEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldModelNo, v))
}

// ModelNoNEQ applies the NEQ predicate on the "ModelNo" field.
func ModelNoNEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldModelNo, v))
}

// ModelNoIn applies the In predicate on the "ModelNo" field.
func ModelNoIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldModelNo, vs...))
}

// ModelNoNotIn applies the NotIn predicate on the "ModelNo" field.
func ModelNoNotIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldModelNo, vs...))
}

// ModelNoGT applies the GT predicate on the "ModelNo" field.
func ModelNoGT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldModelNo, v))
}

// ModelNoGTE applies the GTE predicate on the "ModelNo" field.
func ModelNoGTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldModelNo, v))
}

// ModelNoLT applies the LT predicate on the "ModelNo" field.
func ModelNoLT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldModelNo, v))
}

// ModelNoLTE applies the LTE predicate on the "ModelNo" field.
func ModelNoLTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldModelNo, v))
}

// ModelNoContains applies the Contains predicate on the "ModelNo" field.
func ModelNoContains(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContains(FieldModelNo, v))
}

// ModelNoHasPrefix applies the HasPrefix predicate on the "ModelNo" field.
func ModelNoHasPrefix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasPrefix(FieldModelNo, v))
}

// ModelNoHasSuffix applies the HasSuffix predicate on the "ModelNo" field.
func ModelNoHasSuffix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasSuffix(FieldModelNo, v))
}

// ModelNoEqualFold applies the EqualFold predicate on the "ModelNo" field.
func ModelNoEqualFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEqualFold(FieldModelNo, v))
}

// ModelNoContainsFold applies the ContainsFold predicate on the "ModelNo" field.
func ModelNoContainsFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContainsFold(FieldModelNo, v))
}

// MeasureUnitIDEQ applies the EQ predicate on the "measure_unit_id" field.
func MeasureUnitIDEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldMeasureUnitID, v))
}

// MeasureUnitIDNEQ applies the NEQ predicate on the "measure_unit_id" field.
func MeasureUnitIDNEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldMeasureUnitID, v))
}

// MeasureUnitIDIn applies the In predicate on the "measure_unit_id" field.
func MeasureUnitIDIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldMeasureUnitID, vs...))
}

// MeasureUnitIDNotIn applies the NotIn predicate on the "measure_unit_id" field.
func MeasureUnitIDNotIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldMeasureUnitID, vs...))
}

// MeasureUnitIDGT applies the GT predicate on the "measure_unit_id" field.
func MeasureUnitIDGT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldMeasureUnitID, v))
}

// MeasureUnitIDGTE applies the GTE predicate on the "measure_unit_id" field.
func MeasureUnitIDGTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldMeasureUnitID, v))
}

// MeasureUnitIDLT applies the LT predicate on the "measure_unit_id" field.
func MeasureUnitIDLT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldMeasureUnitID, v))
}

// MeasureUnitIDLTE applies the LTE predicate on the "measure_unit_id" field.
func MeasureUnitIDLTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldMeasureUnitID, v))
}

// MeasureUnitIDContains applies the Contains predicate on the "measure_unit_id" field.
func MeasureUnitIDContains(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContains(FieldMeasureUnitID, v))
}

// MeasureUnitIDHasPrefix applies the HasPrefix predicate on the "measure_unit_id" field.
func MeasureUnitIDHasPrefix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasPrefix(FieldMeasureUnitID, v))
}

// MeasureUnitIDHasSuffix applies the HasSuffix predicate on the "measure_unit_id" field.
func MeasureUnitIDHasSuffix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasSuffix(FieldMeasureUnitID, v))
}

// MeasureUnitIDIsNil applies the IsNil predicate on the "measure_unit_id" field.
func MeasureUnitIDIsNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIsNull(FieldMeasureUnitID))
}

// MeasureUnitIDNotNil applies the NotNil predicate on the "measure_unit_id" field.
func MeasureUnitIDNotNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotNull(FieldMeasureUnitID))
}

// MeasureUnitIDEqualFold applies the EqualFold predicate on the "measure_unit_id" field.
func MeasureUnitIDEqualFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEqualFold(FieldMeasureUnitID, v))
}

// MeasureUnitIDContainsFold applies the ContainsFold predicate on the "measure_unit_id" field.
func MeasureUnitIDContainsFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContainsFold(FieldMeasureUnitID, v))
}

// NumEQ applies the EQ predicate on the "num" field.
func NumEQ(v uint32) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldNum, v))
}

// NumNEQ applies the NEQ predicate on the "num" field.
func NumNEQ(v uint32) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldNum, v))
}

// NumIn applies the In predicate on the "num" field.
func NumIn(vs ...uint32) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldNum, vs...))
}

// NumNotIn applies the NotIn predicate on the "num" field.
func NumNotIn(vs ...uint32) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldNum, vs...))
}

// NumGT applies the GT predicate on the "num" field.
func NumGT(v uint32) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldNum, v))
}

// NumGTE applies the GTE predicate on the "num" field.
func NumGTE(v uint32) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldNum, v))
}

// NumLT applies the LT predicate on the "num" field.
func NumLT(v uint32) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldNum, v))
}

// NumLTE applies the LTE predicate on the "num" field.
func NumLTE(v uint32) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldNum, v))
}

// TransferReasonEQ applies the EQ predicate on the "transfer_reason" field.
func TransferReasonEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldTransferReason, v))
}

// TransferReasonNEQ applies the NEQ predicate on the "transfer_reason" field.
func TransferReasonNEQ(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldTransferReason, v))
}

// TransferReasonIn applies the In predicate on the "transfer_reason" field.
func TransferReasonIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldTransferReason, vs...))
}

// TransferReasonNotIn applies the NotIn predicate on the "transfer_reason" field.
func TransferReasonNotIn(vs ...string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldTransferReason, vs...))
}

// TransferReasonGT applies the GT predicate on the "transfer_reason" field.
func TransferReasonGT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldTransferReason, v))
}

// TransferReasonGTE applies the GTE predicate on the "transfer_reason" field.
func TransferReasonGTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldTransferReason, v))
}

// TransferReasonLT applies the LT predicate on the "transfer_reason" field.
func TransferReasonLT(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldTransferReason, v))
}

// TransferReasonLTE applies the LTE predicate on the "transfer_reason" field.
func TransferReasonLTE(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldTransferReason, v))
}

// TransferReasonContains applies the Contains predicate on the "transfer_reason" field.
func TransferReasonContains(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContains(FieldTransferReason, v))
}

// TransferReasonHasPrefix applies the HasPrefix predicate on the "transfer_reason" field.
func TransferReasonHasPrefix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasPrefix(FieldTransferReason, v))
}

// TransferReasonHasSuffix applies the HasSuffix predicate on the "transfer_reason" field.
func TransferReasonHasSuffix(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldHasSuffix(FieldTransferReason, v))
}

// TransferReasonIsNil applies the IsNil predicate on the "transfer_reason" field.
func TransferReasonIsNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIsNull(FieldTransferReason))
}

// TransferReasonNotNil applies the NotNil predicate on the "transfer_reason" field.
func TransferReasonNotNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotNull(FieldTransferReason))
}

// TransferReasonEqualFold applies the EqualFold predicate on the "transfer_reason" field.
func TransferReasonEqualFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEqualFold(FieldTransferReason, v))
}

// TransferReasonContainsFold applies the ContainsFold predicate on the "transfer_reason" field.
func TransferReasonContainsFold(v string) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldContainsFold(FieldTransferReason, v))
}

// TransferTimeEQ applies the EQ predicate on the "transfer_time" field.
func TransferTimeEQ(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldEQ(FieldTransferTime, v))
}

// TransferTimeNEQ applies the NEQ predicate on the "transfer_time" field.
func TransferTimeNEQ(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNEQ(FieldTransferTime, v))
}

// TransferTimeIn applies the In predicate on the "transfer_time" field.
func TransferTimeIn(vs ...time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIn(FieldTransferTime, vs...))
}

// TransferTimeNotIn applies the NotIn predicate on the "transfer_time" field.
func TransferTimeNotIn(vs ...time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotIn(FieldTransferTime, vs...))
}

// TransferTimeGT applies the GT predicate on the "transfer_time" field.
func TransferTimeGT(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGT(FieldTransferTime, v))
}

// TransferTimeGTE applies the GTE predicate on the "transfer_time" field.
func TransferTimeGTE(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldGTE(FieldTransferTime, v))
}

// TransferTimeLT applies the LT predicate on the "transfer_time" field.
func TransferTimeLT(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLT(FieldTransferTime, v))
}

// TransferTimeLTE applies the LTE predicate on the "transfer_time" field.
func TransferTimeLTE(v time.Time) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldLTE(FieldTransferTime, v))
}

// TransferTimeIsNil applies the IsNil predicate on the "transfer_time" field.
func TransferTimeIsNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldIsNull(FieldTransferTime))
}

// TransferTimeNotNil applies the NotNil predicate on the "transfer_time" field.
func TransferTimeNotNil() predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.FieldNotNull(FieldTransferTime))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.WmsTransferOrderDetail) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.WmsTransferOrderDetail) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.WmsTransferOrderDetail) predicate.WmsTransferOrderDetail {
	return predicate.WmsTransferOrderDetail(sql.NotPredicates(p))
}
