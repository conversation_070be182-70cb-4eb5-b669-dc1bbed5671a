package data

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	v1 "kratos-mono-demo/gen/api/workwx/v1"
	conf "kratos-mono-demo/gen/config/conf/v1"
	"kratos-mono-demo/internal/app/workwx/biz"
	"kratos-mono-demo/internal/data"
	"kratos-mono-demo/internal/data/ent"
	"kratos-mono-demo/internal/data/ent/sysuserauth"
	"kratos-mono-demo/internal/data/ent/workwxapprovalmessage"
	"kratos-mono-demo/internal/data/ent/workwxapprovalnode"
	"kratos-mono-demo/internal/data/ent/workwxnotifynode"
	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	paging "kratos-mono-demo/internal/pkg/go-utils/pagination"
)

var _ biz.WorkwxApprovalRepo = (*WorkwxApprovalRepo)(nil)

type WorkwxApprovalRepo struct {
	log    *log.Helper
	config *conf.Config
	data   *data.Data
}

func NewWorkwxApprovalRepo(config *conf.Config, logger log.Logger, data *data.Data) biz.WorkwxApprovalRepo {
	return &WorkwxApprovalRepo{
		log:    log.NewHelper(logger),
		config: config,
		data:   data,
	}
}

func (r *WorkwxApprovalRepo) getEntClient(ctx context.Context) *ent.Client {
	tx := ent.TxFromContext(ctx)
	if tx != nil {
		return tx.Client()
	}
	return r.data.EntClient.Client()
}

func (r *WorkwxApprovalRepo) GetApprovalList(ctx context.Context, req *v1.GetApprovalListRequest) (*v1.GetApprovalListResponse, error) {
	userId, ok := entutils.GetUserID(ctx)
	if !ok {
		return nil, fmt.Errorf("user ID not found in context")
	}

	// 构建查询
	client := r.getEntClient(ctx)

	// 使用原始SQL进行复杂联表查询
	query := client.WorkWxApprovalMessage.Query().
		Where(func(s *sql.Selector) {
			t1 := sql.Table(workwxapprovalnode.Table)
			t2 := sql.Table(sysuserauth.Table)

			s.Join(t1).On(s.C(workwxapprovalmessage.FieldID), t1.C(workwxapprovalnode.FieldApprovalMessageID))
			s.LeftJoin(t2).On(t1.C(workwxapprovalnode.FieldItemUserID), t2.C(sysuserauth.FieldIdentifier))
			s.Where(sql.EQ(t2.C(sysuserauth.FieldUserID), userId))
			s.GroupBy(s.C(workwxapprovalmessage.FieldID), s.C(workwxapprovalmessage.FieldUpdatedAt))
			s.OrderBy(sql.Desc(s.C(workwxapprovalmessage.FieldUpdatedAt)))
		})
	countQuery := client.WorkWxApprovalMessage.Query().
		Where(func(s *sql.Selector) {
			t1 := sql.Table(workwxapprovalnode.Table)
			t2 := sql.Table(sysuserauth.Table)

			s.Join(t1).On(s.C(workwxapprovalmessage.FieldID), t1.C(workwxapprovalnode.FieldApprovalMessageID))
			s.LeftJoin(t2).On(t1.C(workwxapprovalnode.FieldItemUserID), t2.C(sysuserauth.FieldIdentifier))
			s.Where(sql.EQ(t2.C(sysuserauth.FieldUserID), userId))
		})

	// 计算总记录数
	total, err := countQuery.Count(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return &v1.GetApprovalListResponse{
				Items: []*v1.WorkWxApprovalMessage{},
				Total: 0,
			}, nil
		}
		return nil, fmt.Errorf("failed to count approval messages: %w", err)
	}

	// 应用分页
	noPaging := false
	if req.NoPaging != nil {
		noPaging = *req.NoPaging
	}

	if !noPaging {
		page := int32(1)
		if req.Page != nil {
			page = *req.Page
		}
		pageSize := int32(10)
		if req.PageSize != nil {
			pageSize = *req.PageSize
		}

		query = query.Offset(paging.GetPageOffset(page, pageSize)).Limit(int(pageSize))
	}

	// 应用过滤条件
	if req.Query != nil && *req.Query != "" {
		var queryMap map[string]interface{}
		if err := json.Unmarshal([]byte(*req.Query), &queryMap); err == nil {
			query = applyQueryFilters(query, queryMap)
		}
	}

	// 执行查询
	messages, err := query.All(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return &v1.GetApprovalListResponse{
				Items: []*v1.WorkWxApprovalMessage{},
				Total: int32(total),
			}, nil
		}
		return nil, fmt.Errorf("failed to query approval messages: %w", err)
	}

	// 转换为响应格式
	items := make([]*v1.WorkWxApprovalMessage, len(messages))
	for i, msg := range messages {
		items[i] = &v1.WorkWxApprovalMessage{
			Id:             msg.ID,
			ThirdNo:        msg.ThirdNo,
			SpType:         msg.SpType,
			OpenSpName:     msg.OpenSpName,
			OpenTemplateId: msg.OpenTemplateID,
			OpenSpStatus:   int32(msg.OpenSpStatus),
			ApplyTime:      time.Unix(msg.ApplyTime, 0).Format(time.RFC3339),
			ApplyUserName:  msg.ApplyUserName,
			ApplyUserId:    msg.ApplyUserID,
			ApplyUserParty: msg.ApplyUserParty,
			ApplyUserImage: msg.ApplyUserImage,
			AgentId:        strconv.Itoa(msg.AgentID),
			ToUserName:     msg.ToUserName,
			FromUserName:   msg.FromUserName,
			CreateTime:     msg.CreateTime,
			MsgType:        msg.MsgType,
			Event:          msg.Event,
		}
	}

	return &v1.GetApprovalListResponse{
		Items: items,
		Total: int32(total),
	}, nil
}

func (r *WorkwxApprovalRepo) GetCcApprovalList(ctx context.Context, req *v1.GetCcApprovalListRequest) (*v1.GetCcApprovalListResponse, error) {
	userId, ok := entutils.GetUserID(ctx)
	if !ok {
		return nil, fmt.Errorf("user ID not found in context")
	}

	// 构建查询
	client := r.getEntClient(ctx)

	// 使用原始SQL进行复杂联表查询
	query := client.WorkWxApprovalMessage.Query().
		Where(func(s *sql.Selector) {
			t1 := sql.Table(workwxnotifynode.Table)
			t2 := sql.Table(sysuserauth.Table)

			s.Join(t1).On(s.C(workwxapprovalmessage.FieldID), t1.C(workwxnotifynode.FieldApprovalMessageID))
			s.LeftJoin(t2).On(t1.C(workwxnotifynode.FieldItemUserID), t2.C(sysuserauth.FieldIdentifier))
			s.Where(sql.EQ(t2.C(sysuserauth.FieldUserID), userId))
			s.GroupBy(s.C(workwxapprovalmessage.FieldID), s.C(workwxapprovalmessage.FieldUpdatedAt))
			s.OrderBy(sql.Desc(s.C(workwxapprovalmessage.FieldUpdatedAt)))
		})
	countQuery := client.WorkWxApprovalMessage.Query().
		Where(func(s *sql.Selector) {
			t1 := sql.Table(workwxnotifynode.Table)
			t2 := sql.Table(sysuserauth.Table)

			s.Join(t1).On(s.C(workwxapprovalmessage.FieldID), t1.C(workwxnotifynode.FieldApprovalMessageID))
			s.LeftJoin(t2).On(t1.C(workwxnotifynode.FieldItemUserID), t2.C(sysuserauth.FieldIdentifier))
			s.Where(sql.EQ(t2.C(sysuserauth.FieldUserID), userId))
			s.GroupBy(s.C(workwxapprovalmessage.FieldID), s.C(workwxapprovalmessage.FieldUpdatedAt))
			s.OrderBy(sql.Desc(s.C(workwxapprovalmessage.FieldUpdatedAt)))
		})

	// 计算总记录数
	total, err := countQuery.Count(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return &v1.GetCcApprovalListResponse{
				Items: []*v1.WorkWxApprovalMessage{},
				Total: 0,
			}, nil
		}
		return nil, fmt.Errorf("failed to count cc approval messages: %w", err)
	}

	// 应用分页
	noPaging := false
	if req.NoPaging != nil {
		noPaging = *req.NoPaging
	}

	if !noPaging {
		page := int32(1)
		if req.Page != nil {
			page = *req.Page
		}
		pageSize := int32(10)
		if req.PageSize != nil {
			pageSize = *req.PageSize
		}

		query = query.Offset(paging.GetPageOffset(page, pageSize)).Limit(int(pageSize))
	}

	// 应用过滤条件
	if req.Query != nil && *req.Query != "" {
		var queryMap map[string]interface{}
		if err := json.Unmarshal([]byte(*req.Query), &queryMap); err == nil {
			query = applyQueryFilters(query, queryMap)
		}
	}

	// 执行查询
	messages, err := query.All(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return &v1.GetCcApprovalListResponse{
				Items: []*v1.WorkWxApprovalMessage{},
				Total: int32(total),
			}, nil
		}
		return nil, fmt.Errorf("failed to query cc approval messages: %w", err)
	}

	// 转换为响应格式
	items := make([]*v1.WorkWxApprovalMessage, len(messages))
	for i, msg := range messages {
		items[i] = &v1.WorkWxApprovalMessage{
			Id:             msg.ID,
			ThirdNo:        msg.ThirdNo,
			SpType:         msg.SpType,
			OpenSpName:     msg.OpenSpName,
			OpenTemplateId: msg.OpenTemplateID,
			OpenSpStatus:   int32(msg.OpenSpStatus),
			ApplyTime:      time.Unix(msg.ApplyTime, 0).Format(time.RFC3339),
			ApplyUserName:  msg.ApplyUserName,
			ApplyUserId:    msg.ApplyUserID,
			ApplyUserParty: msg.ApplyUserParty,
			ApplyUserImage: msg.ApplyUserImage,
			AgentId:        strconv.Itoa(msg.AgentID),
			ToUserName:     msg.ToUserName,
			FromUserName:   msg.FromUserName,
			CreateTime:     msg.CreateTime,
			MsgType:        msg.MsgType,
			Event:          msg.Event,
		}
	}

	return &v1.GetCcApprovalListResponse{
		Items: items,
		Total: int32(total),
	}, nil
}

func (r *WorkwxApprovalRepo) GetMyApprovalList(ctx context.Context, req *v1.GetMyApprovalListRequest) (*v1.GetMyApprovalListResponse, error) {
	userId, ok := entutils.GetUserID(ctx)
	if !ok {
		return nil, fmt.Errorf("user ID not found in context")
	}

	// 构建查询
	client := r.getEntClient(ctx)

	// 使用原始SQL进行复杂联表查询
	query := client.WorkWxApprovalMessage.Query().
		Where(func(s *sql.Selector) {
			t := sql.Table(sysuserauth.Table)

			s.LeftJoin(t).On(s.C(workwxapprovalmessage.FieldApplyUserID), t.C(sysuserauth.FieldIdentifier))
			s.Where(sql.EQ(t.C(sysuserauth.FieldUserID), userId))
			s.GroupBy(s.C(workwxapprovalmessage.FieldID), s.C(workwxapprovalmessage.FieldUpdatedAt))
			s.OrderBy(sql.Desc(s.C(workwxapprovalmessage.FieldUpdatedAt)))
		})

	countQuery := client.WorkWxApprovalMessage.Query().
		Where(func(s *sql.Selector) {
			t := sql.Table(sysuserauth.Table)

			s.LeftJoin(t).On(s.C(workwxapprovalmessage.FieldApplyUserID), t.C(sysuserauth.FieldIdentifier))
			s.Where(sql.EQ(t.C(sysuserauth.FieldUserID), userId))
			s.GroupBy(s.C(workwxapprovalmessage.FieldID), s.C(workwxapprovalmessage.FieldUpdatedAt))
			s.OrderBy(sql.Desc(s.C(workwxapprovalmessage.FieldUpdatedAt)))
		})

	// 计算总记录数
	total, err := countQuery.Count(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return &v1.GetMyApprovalListResponse{
				Items: []*v1.WorkWxApprovalMessage{},
				Total: 0,
			}, nil
		}
		return nil, fmt.Errorf("failed to count my approval messages: %w", err)
	}

	// 应用分页
	noPaging := false
	if req.NoPaging != nil {
		noPaging = *req.NoPaging
	}

	if !noPaging {
		page := int32(1)
		if req.Page != nil {
			page = *req.Page
		}
		pageSize := int32(10)
		if req.PageSize != nil {
			pageSize = *req.PageSize
		}

		query = query.Offset(paging.GetPageOffset(page, pageSize)).Limit(int(pageSize))
	}

	// 应用过滤条件
	if req.Query != nil && *req.Query != "" {
		var queryMap map[string]interface{}
		if err := json.Unmarshal([]byte(*req.Query), &queryMap); err == nil {
			query = applyQueryFilters(query, queryMap)
		}
	}

	// 执行查询
	messages, err := query.All(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return &v1.GetMyApprovalListResponse{
				Items: []*v1.WorkWxApprovalMessage{},
				Total: int32(total),
			}, nil
		}
		return nil, fmt.Errorf("failed to query my approval messages: %w", err)
	}

	// 转换为响应格式
	items := make([]*v1.WorkWxApprovalMessage, len(messages))
	for i, msg := range messages {
		items[i] = &v1.WorkWxApprovalMessage{
			Id:             msg.ID,
			ThirdNo:        msg.ThirdNo,
			SpType:         msg.SpType,
			OpenSpName:     msg.OpenSpName,
			OpenTemplateId: msg.OpenTemplateID,
			OpenSpStatus:   int32(msg.OpenSpStatus),
			ApplyTime:      time.Unix(msg.ApplyTime, 0).Format(time.RFC3339),
			ApplyUserName:  msg.ApplyUserName,
			ApplyUserId:    msg.ApplyUserID,
			ApplyUserParty: msg.ApplyUserParty,
			ApplyUserImage: msg.ApplyUserImage,
			AgentId:        strconv.Itoa(msg.AgentID),
			ToUserName:     msg.ToUserName,
			FromUserName:   msg.FromUserName,
			CreateTime:     msg.CreateTime,
			MsgType:        msg.MsgType,
			Event:          msg.Event,
		}
	}

	return &v1.GetMyApprovalListResponse{
		Items: items,
		Total: int32(total),
	}, nil
}

// 应用查询过滤条件
func applyQueryFilters(query *ent.WorkWxApprovalMessageQuery, filterMap map[string]interface{}) *ent.WorkWxApprovalMessageQuery {
	for k, v := range filterMap {
		switch val := v.(type) {
		case []interface{}:
			// 处理数组类型的查询条件 (IN 查询)
			query = query.Where(func(s *sql.Selector) {
				s.Where(sql.In(s.C(k), val...))
			})
		default:
			// 处理普通类型的查询条件 (相等查询)
			query = query.Where(func(s *sql.Selector) {
				s.Where(sql.EQ(s.C(k), v))
			})
		}
	}
	return query
}
