package biz

import (
	"context"
	"fmt"

	v1 "kratos-mono-demo/gen/api/wms/v1"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	"kratos-mono-demo/internal/pkg/go-utils/arrayutil"
	"kratos-mono-demo/internal/pkg/go-utils/trans"

	"github.com/go-kratos/kratos/v2/log"
)

const (
	MAINTAIN_ORDER_STATUS_CANCELLED  = "-1"  // 已取消
	MAINTAIN_ORDER_STATUS_REJECTED   = "-2"  // 已驳回
	MAINTAIN_ORDER_STATUS_PENDING    = "0"   // 待提交
	MAINTAIN_ORDER_STATUS_PROCESSING = "1"   // 审批中
	MAINTAIN_ORDER_STATUS_APPROVED   = "2"   // 已审批
	MAINTAIN_ORDER_STATUS_COMPLETED  = "100" // 已完成
)

type WmsMaintainOrderRepo interface {
	ListWmsMaintainOrder(ctx context.Context, req *v1.ListWmsMaintainOrderRequest) (*v1.ListWmsMaintainOrderResponse, error)
	GetWmsMaintainOrder(ctx context.Context, id string) (*v1.WmsMaintainOrder, error)
	CreateWmsMaintainOrder(ctx context.Context, req *v1.CreateWmsMaintainOrderRequest) (*v1.WmsMaintainOrder, error)
	UpdateWmsMaintainOrder(ctx context.Context, req *v1.UpdateWmsMaintainOrderRequest) (*v1.WmsMaintainOrder, error)
	DeleteWmsMaintainOrder(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsMaintainOrder(ctx context.Context, ids []string) (bool, error)

	// 根据关联流程单号查询保养单
	GetWmsMaintainOrderByOrderNo(ctx context.Context, orderNo string) (*v1.WmsMaintainOrder, error)

	// 获取保养单申请用户的组织名称
	GetWmsMaintainOrderUserOrganizationNameMap(ctx context.Context, ids []string) (map[string]string, error)

	// 根据ID列表获取保养单
	GetWmsMaintainOrdersByIds(ctx context.Context, ids []string) ([]*v1.WmsMaintainOrder, error)
}

type WmsMaintainOrderUsecase struct {
	log           *log.Helper
	repo          WmsMaintainOrderRepo
	tm            entutils.Transaction
	detailRepo    WmsMaintainOrderDetailRepo
	documentRepo  WmsDocumentRepo
	logRepo       WmsOperateLogRepo
	equipmentRepo WmsEquipmentRepo
	materialRepo  WmsMaterialRepo
}

func NewWmsMaintainOrderUsecase(
	repo WmsMaintainOrderRepo,
	tm entutils.Transaction,
	logger log.Logger,
	detailRepo WmsMaintainOrderDetailRepo,
	documentRepo WmsDocumentRepo,
	logRepo WmsOperateLogRepo,
	equipmentRepo WmsEquipmentRepo,
	materialRepo WmsMaterialRepo,
) *WmsMaintainOrderUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_maintain_order/usecase"))
	return &WmsMaintainOrderUsecase{
		log:           l,
		repo:          repo,
		tm:            tm,
		detailRepo:    detailRepo,
		documentRepo:  documentRepo,
		logRepo:       logRepo,
		equipmentRepo: equipmentRepo,
		materialRepo:  materialRepo,
	}
}

func (uc *WmsMaintainOrderUsecase) ListWmsMaintainOrder(ctx context.Context, req *v1.ListWmsMaintainOrderRequest) (*v1.ListWmsMaintainOrderResponse, error) {

	// // 自定义加入查询条件
	// newQuery := entgo.AppendQueryKeys(req.GetQuery(), map[string]string{
	// 	"createdBy": userId,
	// })
	// req.Query = &newQuery

	res, err := uc.repo.ListWmsMaintainOrder(ctx, req)
	if err != nil {
		return nil, err
	}

	items := res.Items

	uc.setDetails(ctx, items)  // 设置保养单明细
	uc.setUserInfo(ctx, items) // 设置保养单用户信息

	return res, nil
}

func (uc *WmsMaintainOrderUsecase) GetWmsMaintainOrder(ctx context.Context, id string) (*v1.WmsMaintainOrder, error) {
	res, err := uc.repo.GetWmsMaintainOrder(ctx, id)
	if err != nil {
		return nil, err
	}

	uc.setDetails(ctx, []*v1.WmsMaintainOrder{res})  // 设置保养单明细
	uc.setUserInfo(ctx, []*v1.WmsMaintainOrder{res}) // 设置保养单用户信息

	return res, nil
}

func (uc *WmsMaintainOrderUsecase) CreateWmsMaintainOrder(ctx context.Context, req *v1.CreateWmsMaintainOrderRequest) (*v1.WmsMaintainOrder, error) {
	var err error
	var order *v1.WmsMaintainOrder

	// 开启事务
	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		order, err = uc.repo.CreateWmsMaintainOrder(ctx, req)
		if err != nil {
			return err
		}

		// 创建保养单明细
		if len(req.Details) > 0 {
			for _, detail := range req.Details {
				detail.OrderId = &order.Id // 设置保养单id
				detailRes, err := uc.detailRepo.CreateWmsMaintainOrderDetail(ctx, detail)
				if err != nil {
					return err
				}

				order.Details = append(order.Details, detailRes)
			}
		}

		// 创建操作日志
		log := fmt.Sprintf("创建保养单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_MAINTAIN_ORDER, log)
		if err != nil {
			return err
		}

		// 创建系统文档
		source := fmt.Sprintf("保养单: %s", trans.StringValue(order.OrderNo))
		err = uc.documentRepo.BatchCreateUpdateSystemDocuments(ctx, source, DOCUMENT_MAINTAIN_ORDER_CATEGEORY, order.Id, req)
		if err != nil {
			return err
		}

		// 如果审批通过，保养
		if order.GetStatus() == MAINTAIN_ORDER_STATUS_APPROVED {
			// 保养
			err = uc.handleMaintain(ctx, order)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return order, nil
}

func (uc *WmsMaintainOrderUsecase) UpdateWmsMaintainOrder(ctx context.Context, req *v1.UpdateWmsMaintainOrderRequest) (*v1.WmsMaintainOrder, error) {
	var err error
	var order *v1.WmsMaintainOrder

	// 开启事务
	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		order, err = uc.repo.UpdateWmsMaintainOrder(ctx, req)
		if err != nil {
			return err
		}

		// 更新保养单明细
		if len(req.Details) > 0 {
			// 删除保养单明细
			_, err = uc.detailRepo.DeleteWmsMaintainOrderDetailByOrderId(ctx, order.Id)
			if err != nil {
				return err
			}

			// 创建保养单明细
			for _, detail := range req.Details {
				detail.OrderId = &order.Id
				detailRes, err := uc.detailRepo.CreateWmsMaintainOrderDetail(ctx, detail)
				if err != nil {
					return err
				}

				order.Details = append(order.Details, detailRes)
			}
		}

		// 创建操作日志
		log := fmt.Sprintf("更新保养单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_MAINTAIN_ORDER, log)
		if err != nil {
			return err
		}

		// 更新系统文档
		source := fmt.Sprintf("保养单: %s", trans.StringValue(order.OrderNo))
		err = uc.documentRepo.BatchCreateUpdateSystemDocuments(ctx, source, DOCUMENT_MAINTAIN_ORDER_CATEGEORY, order.Id, req)
		if err != nil {
			return err
		}

		// 如果审批通过，保养
		if order.GetStatus() == MAINTAIN_ORDER_STATUS_APPROVED {
			// 保养
			err = uc.handleMaintain(ctx, order)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return order, nil
}

func (uc *WmsMaintainOrderUsecase) DeleteWmsMaintainOrder(ctx context.Context, id string) (bool, error) {
	var err error

	order, err := uc.repo.GetWmsMaintainOrder(ctx, id)
	if err != nil {
		return false, err
	}

	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		_, err = uc.repo.DeleteWmsMaintainOrder(ctx, id)
		if err != nil {
			return err
		}

		// 删除保养单明细
		_, err = uc.detailRepo.DeleteWmsMaintainOrderDetailByOrderId(ctx, id)
		if err != nil {
			return err
		}

		// 创建操作日志
		log := fmt.Sprintf("删除保养单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, id, LOG_CATEGEORY_MAINTAIN_ORDER, log)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return false, err
	}

	return true, nil
}

func (uc *WmsMaintainOrderUsecase) MultiDeleteWmsMaintainOrder(ctx context.Context, ids []string) (bool, error) {
	var err error

	orders, err := uc.repo.GetWmsMaintainOrdersByIds(ctx, ids)
	if err != nil {
		return false, err
	}

	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		_, err = uc.repo.MultiDeleteWmsMaintainOrder(ctx, ids)
		if err != nil {
			return err
		}

		// 删除保养单明细
		for _, id := range ids {
			_, err = uc.detailRepo.DeleteWmsMaintainOrderDetailByOrderId(ctx, id)
			if err != nil {
				return err
			}
		}

		// 创建操作日志
		for _, order := range orders {
			log := fmt.Sprintf("删除保养单 %s", trans.StringValue(order.OrderNo))
			_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_MAINTAIN_ORDER, log)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return false, err
	}

	return true, nil
}

// 设置保养单明细
func (uc *WmsMaintainOrderUsecase) setDetails(ctx context.Context, orders []*v1.WmsMaintainOrder) {
	orderIds := make([]string, 0)
	for _, order := range orders {
		orderIds = append(orderIds, order.Id)
	}

	details, err := uc.detailRepo.GetWmsMaintainOrderDetailsByOrderIds(ctx, orderIds)
	if err != nil {
		return
	}

	// 设置保养单明细详细规格
	uc.setDetailsFeatureStr(ctx, details)

	detailsMap := make(map[string][]*v1.WmsMaintainOrderDetail)
	for _, detail := range details {
		detailsMap[detail.GetOrderId()] = append(detailsMap[detail.GetOrderId()], detail)
	}

	for _, order := range orders {
		order.Details = detailsMap[order.Id]
	}
}

// 设置保养单用户信息
func (uc *WmsMaintainOrderUsecase) setUserInfo(ctx context.Context, orders []*v1.WmsMaintainOrder) {
	ids := arrayutil.Map(orders, func(order *v1.WmsMaintainOrder) string {
		return order.Id
	})

	// 获取保养单申请用户的组织名称
	organization_name_map, err := uc.repo.GetWmsMaintainOrderUserOrganizationNameMap(ctx, ids)
	if err != nil {
		return
	}

	for _, order := range orders {
		order.CreateByOrganizationName = trans.String(organization_name_map[order.Id])
	}
}

// 设置保养单明细详细规格
func (uc *WmsMaintainOrderUsecase) setDetailsFeatureStr(ctx context.Context, details []*v1.WmsMaintainOrderDetail) {
	equipmentIds := arrayutil.Map(details, func(item *v1.WmsMaintainOrderDetail) string {
		return item.GetEquipmentId()
	})

	features, err := uc.equipmentRepo.GetWmsEquipmentFeaturesByIds(ctx, equipmentIds)

	if err != nil {
		uc.log.Errorf("get equipment features error: %v", err)
	}

	for _, detail := range details {
		feature := detail.GetFeature()

		if feature != nil {
			feature_str := uc.equipmentRepo.ConvertFeatureToFeatureStr(feature.AsMap(), features)
			detail.FeatureStr = trans.String(feature_str)
		}
	}
}

func (uc *WmsMaintainOrderUsecase) handleMaintain(ctx context.Context, order *v1.WmsMaintainOrder) error {
	// 获取保养单详情
	details, err := uc.detailRepo.GetWmsMaintainOrderDetailsByOrderId(ctx, order.Id)
	if err != nil {
		return err
	}

	for _, detail := range details {
		split_material, err := uc.materialRepo.SplitMaterial(ctx,
			detail.GetMaterialId(),
			"",
			nil,
			nil,
			trans.Int32(MATERIAL_MAINTAIN_STATUS_MAINTAINING),
			int32(detail.GetNum()),
			nil,
		)

		if err != nil {
			return err
		}

		_, err = uc.detailRepo.UpdateWmsMaintainOrderDetail(ctx, &v1.UpdateWmsMaintainOrderDetailRequest{
			Id:         detail.Id,
			MaterialId: trans.String(split_material.Id),
		})

		if err != nil {
			return err
		}
	}

	// 更新保养单状态
	_, err = uc.repo.UpdateWmsMaintainOrder(ctx, &v1.UpdateWmsMaintainOrderRequest{
		Id:     order.Id,
		Status: trans.String(MAINTAIN_ORDER_STATUS_COMPLETED),
	})
	if err != nil {
		return err
	}

	return nil
}
