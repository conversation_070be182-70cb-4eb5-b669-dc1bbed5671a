package biz

import (
	"context"

	v1 "kratos-mono-demo/gen/api/wms/v1"

	"github.com/go-kratos/kratos/v2/log"
)

const (
	LOG_CATEGEORY_ENTER_REPOSITORY_ORDER = "enter_repository_order"
	LOG_CATEGEORY_DISCARD_PLAN_ORDER     = "discard_plan_order"
	LOG_CATEGEORY_DISCARDPLANMEETING     = "discard_meeting"
	LOG_CATEGEORY_DISCARD_ORDER          = "discard_order"
	LOG_CATEGEORY_MAINTAIN_PLAN          = "maintain_plan"
	LOG_CATEGEORY_PURCHASE_ORDER         = "purchase_order"
	LOG_CATEGEORY_APPROVAL_TASK          = "approval_task"
	LOG_CATEGEORY_BORROW_ORDER           = "borrow_order"
	LOG_CATEGEORY_CLAIM_ORDER            = "claim_order"
	LOG_CATEGEORY_RETURN_ORDER           = "return_order"
	LOG_CATEGEORY_REPAIR_ORDER           = "repair_order"
	LOG_CATEGEORY_MAINTAIN_ORDER         = "maintain_order"
	LOG_CATEGEORY_TRANSFER_ORDER         = "transfer_order"
)

type WmsOperateLogRepo interface {
	ListWmsOperateLog(ctx context.Context, req *v1.ListWmsOperateLogRequest) (*v1.ListWmsOperateLogResponse, error)
	GetWmsOperateLog(ctx context.Context, id string) (*v1.WmsOperateLog, error)
	CreateWmsOperateLog(ctx context.Context, req *v1.CreateWmsOperateLogRequest) (*v1.WmsOperateLog, error)
	UpdateWmsOperateLog(ctx context.Context, req *v1.UpdateWmsOperateLogRequest) (*v1.WmsOperateLog, error)
	DeleteWmsOperateLog(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsOperateLog(ctx context.Context, ids []string) (bool, error)

	CreateWmsOperateLogEx(ctx context.Context, ObjectId string, Category string, Content string) (*v1.WmsOperateLog, error)
}

type WmsOperateLogUsecase struct {
	log  *log.Helper
	repo WmsOperateLogRepo
}

func NewWmsOperateLogUsecase(repo WmsOperateLogRepo, logger log.Logger) *WmsOperateLogUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_operate_log/usecase"))
	return &WmsOperateLogUsecase{
		log:  l,
		repo: repo,
	}
}

func (uc *WmsOperateLogUsecase) ListWmsOperateLog(ctx context.Context, req *v1.ListWmsOperateLogRequest) (*v1.ListWmsOperateLogResponse, error) {
	return uc.repo.ListWmsOperateLog(ctx, req)
}

func (uc *WmsOperateLogUsecase) GetWmsOperateLog(ctx context.Context, id string) (*v1.WmsOperateLog, error) {
	return uc.repo.GetWmsOperateLog(ctx, id)
}

func (uc *WmsOperateLogUsecase) CreateWmsOperateLog(ctx context.Context, req *v1.CreateWmsOperateLogRequest) (*v1.WmsOperateLog, error) {
	return uc.repo.CreateWmsOperateLog(ctx, req)
}

func (uc *WmsOperateLogUsecase) UpdateWmsOperateLog(ctx context.Context, req *v1.UpdateWmsOperateLogRequest) (*v1.WmsOperateLog, error) {
	return uc.repo.UpdateWmsOperateLog(ctx, req)
}

func (uc *WmsOperateLogUsecase) DeleteWmsOperateLog(ctx context.Context, id string) (bool, error) {
	return uc.repo.DeleteWmsOperateLog(ctx, id)
}

func (uc *WmsOperateLogUsecase) MultiDeleteWmsOperateLog(ctx context.Context, ids []string) (bool, error) {
	return uc.repo.MultiDeleteWmsOperateLog(ctx, ids)
}
