package biz

import (
	"context"
	"errors"

	v1 "kratos-mono-demo/gen/api/wms/v1"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	"kratos-mono-demo/internal/pkg/go-utils/arrayutil"
	"kratos-mono-demo/internal/pkg/go-utils/trans"

	"github.com/go-kratos/kratos/v2/log"
)

type WmsOutRepositoryOrderDetailRepo interface {
	ListWmsOutRepositoryOrderDetail(ctx context.Context, req *v1.ListWmsOutRepositoryOrderDetailRequest) (*v1.ListWmsOutRepositoryOrderDetailResponse, error)
	GetWmsOutRepositoryOrderDetail(ctx context.Context, id string) (*v1.WmsOutRepositoryOrderDetail, error)
	CreateWmsOutRepositoryOrderDetail(ctx context.Context, req *v1.CreateWmsOutRepositoryOrderDetailRequest) (*v1.WmsOutRepositoryOrderDetail, error)
	UpdateWmsOutRepositoryOrderDetail(ctx context.Context, req *v1.UpdateWmsOutRepositoryOrderDetailRequest) (*v1.WmsOutRepositoryOrderDetail, error)
	DeleteWmsOutRepositoryOrderDetail(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsOutRepositoryOrderDetail(ctx context.Context, ids []string) (bool, error)

	// 根据出库单ID查询出库单明细
	ListWmsOutRepositoryOrderDetailByOrderID(ctx context.Context, orderID string) (*v1.ListWmsOutRepositoryOrderDetailResponse, error)

	// 根据出库单ID删除出库单明细
	DeleteWmsOutRepositoryOrderDetailByOrderID(ctx context.Context, orderID string) (bool, error)
}

type WmsOutRepositoryOrderDetailUsecase struct {
	log  *log.Helper
	repo WmsOutRepositoryOrderDetailRepo
	tm   entutils.Transaction

	orderRepo              WmsOutRepositoryOrderRepo
	approvalTaskRepo       WmsApprovalTaskRepo
	approvalTaskDetailRepo WmsApprovalTaskDetailRepo
	materialRepo           WmsMaterialRepo
	equipmentRepo          WmsEquipmentRepo
}

func NewWmsOutRepositoryOrderDetailUsecase(
	repo WmsOutRepositoryOrderDetailRepo,
	tm entutils.Transaction,
	logger log.Logger,

	orderRepo WmsOutRepositoryOrderRepo,
	approvalTaskRepo WmsApprovalTaskRepo,
	approvalTaskDetailRepo WmsApprovalTaskDetailRepo,
	materialRepo WmsMaterialRepo,
	equipmentRepo WmsEquipmentRepo,

) *WmsOutRepositoryOrderDetailUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_out_repository_order_detail/usecase"))
	return &WmsOutRepositoryOrderDetailUsecase{
		log:  l,
		repo: repo,
		tm:   tm,

		orderRepo:              orderRepo,
		approvalTaskRepo:       approvalTaskRepo,
		approvalTaskDetailRepo: approvalTaskDetailRepo,
		materialRepo:           materialRepo,
		equipmentRepo:          equipmentRepo,
	}
}

func (uc *WmsOutRepositoryOrderDetailUsecase) ListWmsOutRepositoryOrderDetail(ctx context.Context, req *v1.ListWmsOutRepositoryOrderDetailRequest) (*v1.ListWmsOutRepositoryOrderDetailResponse, error) {

	// // 自定义加入查询条件
	// newQuery := entgo.AppendQueryKeys(req.GetQuery(), map[string]string{
	// 	"createdBy": userId,
	// })
	// req.Query = &newQuery

	res, err := uc.repo.ListWmsOutRepositoryOrderDetail(ctx, req)

	if err != nil {
		uc.log.Errorf("list wms_out_repository_order_detail error: %v", err)
	}

	// 转换详细规格为字符串
	equipmentIds := arrayutil.Map(res.GetItems(), func(item *v1.WmsOutRepositoryOrderDetail) string {
		return item.GetEquipmentId()
	})

	equipments, err := uc.equipmentRepo.GetWmsEquipmentsByIds(ctx, equipmentIds)

	if err != nil {
		uc.log.Errorf("get equipments error: %v", err)
	}

	features, err := uc.equipmentRepo.GetWmsEquipmentFeaturesByIds(ctx, equipmentIds)

	if err != nil {
		uc.log.Errorf("get equipment features error: %v", err)
	}

	for _, detail := range res.GetItems() {
		feature := detail.GetFeature()

		if feature != nil {
			feature_str := uc.equipmentRepo.ConvertFeatureToFeatureStr(feature.AsMap(), features)
			detail.FeatureStr = trans.String(feature_str)
		}

		find_equipment := arrayutil.Find(equipments, func(item *v1.WmsEquipment) bool {
			return item.GetId() == detail.GetEquipmentId()
		})

		if find_equipment != nil {
			detail.MaterialType = find_equipment.Type
		}
	}

	return res, err
}

func (uc *WmsOutRepositoryOrderDetailUsecase) GetWmsOutRepositoryOrderDetail(ctx context.Context, id string) (*v1.WmsOutRepositoryOrderDetail, error) {
	res, err := uc.repo.GetWmsOutRepositoryOrderDetail(ctx, id)

	if err != nil {
		uc.log.Errorf("get wms_out_repository_order_detail error: %v", err)
	}

	equipment, err := uc.equipmentRepo.GetWmsEquipment(ctx, res.GetEquipmentId())

	if err != nil {
		uc.log.Errorf("get equipment error: %v", err)
	}

	res.MaterialType = equipment.Type

	// 转换详细规格为字符串
	features, err := uc.equipmentRepo.GetWmsEquipmentFeaturesByIds(ctx, []string{res.GetEquipmentId()})

	if err != nil {
		uc.log.Errorf("get equipment features error: %v", err)
	}

	feature := res.GetFeature()

	if feature != nil {
		feature_str := uc.equipmentRepo.ConvertFeatureToFeatureStr(feature.AsMap(), features)
		res.FeatureStr = trans.String(feature_str)
	}

	return res, err
}

func (uc *WmsOutRepositoryOrderDetailUsecase) CreateWmsOutRepositoryOrderDetail(ctx context.Context, req *v1.CreateWmsOutRepositoryOrderDetailRequest) (*v1.WmsOutRepositoryOrderDetail, error) {
	var detail *v1.WmsOutRepositoryOrderDetail
	var err error

	if err != nil {
		return nil, err
	}

	if req.GetCode() == "" {
		return nil, errors.New("编码不能为空")
	}

	if req.GetOrderNo() == "" {
		return nil, errors.New("出库单号不能为空")
	}

	if req.GetEquipmentId() == "" {
		return nil, errors.New("装备ID不能为空")
	}

	if req.GetRepositoryId() == "" {
		return nil, errors.New("仓库不能为空")
	}

	if req.GetRepositoryAreaId() == "" {
		return nil, errors.New("库区不能为空")
	}

	if req.GetRepositoryPositionId() == "" {
		return nil, errors.New("库位不能为空")
	}

	equipment, err := uc.equipmentRepo.GetWmsEquipment(ctx, req.GetEquipmentId())

	if err != nil {
		return nil, err
	}

	if equipment == nil {
		return nil, errors.New("装备不存在")
	}

	key := uc.materialRepo.GenerateMaterialKey(
		req.GetRepositoryId(),
		req.GetRepositoryAreaId(),
		req.GetRepositoryPositionId(),
		req.GetCode(),
		req.GetFeature(),
		"",
	)

	material, err := uc.materialRepo.GetMaterialsByKeyAndStatusAndEquipmentStatus(ctx, key, MATERIAL_STATUS_AVAILABLE, MATERIAL_MAINTAIN_STATUS_NORMAL)

	if err != nil {
		return nil, err
	}

	material_num := material.GetNum()

	// 校验库存数量是否充足
	num := req.GetNum()
	detail_res, err := uc.repo.ListWmsOutRepositoryOrderDetailByOrderID(ctx, req.GetOutRepositoryOrderId())

	if err != nil {
		return nil, err
	}

	find_details := arrayutil.Filter(detail_res.Items, func(item *v1.WmsOutRepositoryOrderDetail) bool {
		detail_key := uc.materialRepo.GenerateMaterialKey(
			item.GetRepositoryId(),
			item.GetRepositoryAreaId(),
			item.GetRepositoryPositionId(),
			item.GetCode(),
			item.GetFeature(),
			"",
		)

		return detail_key == key
	})

	if len(find_details) > 0 {
		for _, item := range find_details {
			num += item.GetNum()
		}
	}

	if num > material_num {
		return nil, errors.New("库存数量不足")
	}

	txErr := uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		detail, err = uc.repo.CreateWmsOutRepositoryOrderDetail(ctx, req)
		if err != nil {
			return err
		}

		// 更新出库单的数量
		err = uc.updateOrderCount(ctx, req.GetOutRepositoryOrderId())
		if err != nil {
			return err
		}

		return nil
	})

	return detail, txErr
}

func (uc *WmsOutRepositoryOrderDetailUsecase) UpdateWmsOutRepositoryOrderDetail(ctx context.Context, req *v1.UpdateWmsOutRepositoryOrderDetailRequest) (*v1.WmsOutRepositoryOrderDetail, error) {
	var detail *v1.WmsOutRepositoryOrderDetail
	var err error

	if req.GetCode() == "" {
		return nil, errors.New("编码不能为空")
	}

	if req.GetRepositoryAreaId() == "" {
		return nil, errors.New("库区不能为空")
	}

	if req.GetRepositoryPositionId() == "" {
		return nil, errors.New("库位不能为空")
	}

	db_detail, err := uc.GetWmsOutRepositoryOrderDetail(ctx, req.GetId())

	if err != nil {
		return nil, err
	}

	equipment, err := uc.equipmentRepo.GetWmsEquipment(ctx, req.GetEquipmentId())

	if err != nil {
		return nil, err
	}

	if equipment == nil {
		return nil, errors.New("装备不存在")
	}

	key := uc.materialRepo.GenerateMaterialKey(
		db_detail.GetRepositoryId(),
		req.GetRepositoryAreaId(),
		req.GetRepositoryPositionId(),
		req.GetCode(),
		db_detail.GetFeature(),
		"",
	)

	material, err := uc.materialRepo.GetMaterialsByKeyAndStatusAndEquipmentStatus(ctx, key, MATERIAL_STATUS_AVAILABLE, MATERIAL_MAINTAIN_STATUS_NORMAL)
	if err != nil {
		return nil, err
	}

	material_num := material.GetNum()

	// 校验库存数量是否充足
	num := req.GetNum()
	detail_res, err := uc.repo.ListWmsOutRepositoryOrderDetailByOrderID(ctx, db_detail.GetOutRepositoryOrderId())

	if err != nil {
		return nil, err
	}

	find_details := arrayutil.Filter(detail_res.Items, func(item *v1.WmsOutRepositoryOrderDetail) bool {
		detail_key := uc.materialRepo.GenerateMaterialKey(
			item.GetRepositoryId(),
			item.GetRepositoryAreaId(),
			item.GetRepositoryPositionId(),
			item.GetCode(),
			item.GetFeature(),
			"",
		)

		return detail_key == key && item.GetId() != req.GetId()
	})

	if len(find_details) > 0 {
		for _, item := range find_details {
			num += item.GetNum()
		}
	}

	if num > material_num {
		return nil, errors.New("库存数量不足")
	}

	order, err := uc.orderRepo.GetWmsOutRepositoryOrder(ctx, db_detail.GetOutRepositoryOrderId())

	if err != nil {
		return nil, err
	}

	// 如果不是其他出库，编辑时装备不能修改
	if order.GetType() != OUTREPOSITORYTYPEOTHER {
		if db_detail.GetEquipmentId() != req.GetEquipmentId() {
			return nil, errors.New("装备不匹配，请重新编辑")
		}

		if db_detail.GetEquipmentTypeId() != req.GetEquipmentTypeId() {
			return nil, errors.New("装备类型不匹配，请重新编辑")
		}

		if db_detail.GetFeature() != nil && req.GetFeature() != nil {
			ok := uc.equipmentRepo.CompareFeature(db_detail.GetFeature().AsMap(), req.GetFeature().AsMap())

			if !ok {
				return nil, errors.New("规格不匹配，请重新编辑")
			}
		}
	}

	txErr := uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		detail, err = uc.repo.UpdateWmsOutRepositoryOrderDetail(ctx, &v1.UpdateWmsOutRepositoryOrderDetailRequest{
			Id:                   req.GetId(),
			Code:                 req.Code,
			MaterialId:           material.GetId(),
			RepositoryAreaId:     req.RepositoryAreaId,
			RepositoryPositionId: req.RepositoryPositionId,
			Remark:               req.Remark,
		})
		if err != nil {
			return err
		}

		// 更新出库单的数量
		err = uc.updateOrderCount(ctx, req.GetOutRepositoryOrderId())
		if err != nil {
			return err
		}

		return nil
	})

	return detail, txErr
}

func (uc *WmsOutRepositoryOrderDetailUsecase) DeleteWmsOutRepositoryOrderDetail(ctx context.Context, id string) (bool, error) {
	var ok bool
	var err error

	var detail *v1.WmsOutRepositoryOrderDetail
	detail, err = uc.GetWmsOutRepositoryOrderDetail(ctx, id)
	if err != nil {
		return false, err
	}

	txErr := uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		ok, err = uc.repo.DeleteWmsOutRepositoryOrderDetail(ctx, id)
		if err != nil {
			return err
		}

		// 更新出库单的数量
		err = uc.updateOrderCount(ctx, detail.GetOutRepositoryOrderId())
		if err != nil {
			return err
		}

		return nil
	})

	return ok, txErr
}

func (uc *WmsOutRepositoryOrderDetailUsecase) MultiDeleteWmsOutRepositoryOrderDetail(ctx context.Context, ids []string) (bool, error) {

	if len(ids) == 0 {
		return false, nil
	}

	var ok bool
	var err error
	var detail *v1.WmsOutRepositoryOrderDetail
	detail, err = uc.GetWmsOutRepositoryOrderDetail(ctx, ids[0])
	if err != nil {
		return false, err
	}

	txErr := uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		ok, err = uc.repo.MultiDeleteWmsOutRepositoryOrderDetail(ctx, ids)
		if err != nil {
			return err
		}

		// 更新出库单的数量
		err = uc.updateOrderCount(ctx, detail.GetOutRepositoryOrderId())
		if err != nil {
			return err
		}
		return nil
	})

	return ok, txErr
}

// 更新出库单数量
func (uc *WmsOutRepositoryOrderDetailUsecase) updateOrderCount(ctx context.Context, orderID string) error {
	if orderID == "" {
		return nil
	}

	detailList, err := uc.repo.ListWmsOutRepositoryOrderDetailByOrderID(ctx, orderID)
	if err != nil {
		return err
	}

	_, err = uc.orderRepo.UpdateWmsOutRepositoryOrder(ctx, &v1.UpdateWmsOutRepositoryOrderRequest{
		Id:           orderID,
		EquipmentNum: trans.Uint32(uint32(detailList.Total)),
	})

	return err
}
