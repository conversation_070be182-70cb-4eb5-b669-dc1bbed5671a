package biz

import (
	"context"
	"fmt"
	"time"

	v1 "kratos-mono-demo/gen/api/wms/v1"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	"kratos-mono-demo/internal/pkg/go-utils/arrayutil"
	"kratos-mono-demo/internal/pkg/go-utils/trans"

	"github.com/go-kratos/kratos/v2/log"
)

const (
	TRANSFER_ORDER_STATUS_PENDING          = "0"   // 待提交
	TRANSFER_ORDER_STATUS_PROCESSING       = "1"   // 审批中
	TRANSFER_ORDER_STATUS_APPROVED         = "2"   // 已审批
	TRANSFER_ORDER_STATUS_CANCELLED        = "-1"  // 已取消
	TRANSFER_ORDER_STATUS_REJECTED         = "-2"  // 已驳回
	TRANSFER_ORDER_STATUS_OUT_REPOSITORY   = "3"   // 已出库
	TRANSFER_ORDER_STATUS_ENTER_REPOSITORY = "4"   // 已入库
	TRANSFER_ORDER_STATUS_COMPLETED        = "100" // 已完成
)

type WmsTransferOrderRepo interface {
	ListWmsTransferOrder(ctx context.Context, req *v1.ListWmsTransferOrderRequest) (*v1.ListWmsTransferOrderResponse, error)
	GetWmsTransferOrder(ctx context.Context, id string) (*v1.WmsTransferOrder, error)
	CreateWmsTransferOrder(ctx context.Context, req *v1.CreateWmsTransferOrderRequest) (*v1.WmsTransferOrder, error)
	UpdateWmsTransferOrder(ctx context.Context, req *v1.UpdateWmsTransferOrderRequest) (*v1.WmsTransferOrder, error)
	DeleteWmsTransferOrder(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsTransferOrder(ctx context.Context, ids []string) (bool, error)

	// 根据关联流程单号查询调拨单
	GetWmsTransferOrderByOrderNo(ctx context.Context, orderNo string) (*v1.WmsTransferOrder, error)

	// 获取调拨单申请用户的组织名称
	GetWmsTransferOrderUserOrganizationNameMap(ctx context.Context, ids []string) (map[string]string, error)

	// 根据ID列表获取调拨单
	GetWmsTransferOrdersByIds(ctx context.Context, ids []string) ([]*v1.WmsTransferOrder, error)

	// 根据状态获取调拨单
	GetWmsTransferOrdersByStatus(ctx context.Context, status []string) ([]*v1.WmsTransferOrder, error)
}

type WmsTransferOrderUsecase struct {
	log                            *log.Helper
	repo                           WmsTransferOrderRepo
	tm                             entutils.Transaction
	detailRepo                     WmsTransferOrderDetailRepo
	logRepo                        WmsOperateLogRepo
	documentRepo                   WmsDocumentRepo
	equipmentRepo                  WmsEquipmentRepo
	materialRepo                   WmsMaterialRepo
	outRepositoryOrderRepo         WmsOutRepositoryOrderRepo
	outRepositoryOrderDetailRepo   WmsOutRepositoryOrderDetailRepo
	enterRepositoryOrderRepo       WmsEnterRepositoryOrderRepo
	enterRepositoryOrderDetailRepo WmsEnterRepositoryOrderDetailRepo
}

func NewWmsTransferOrderUsecase(
	repo WmsTransferOrderRepo,
	detailRepo WmsTransferOrderDetailRepo,
	logRepo WmsOperateLogRepo,
	documentRepo WmsDocumentRepo,
	equipmentRepo WmsEquipmentRepo,
	materialRepo WmsMaterialRepo,
	tm entutils.Transaction,
	logger log.Logger,
	outRepositoryOrderRepo WmsOutRepositoryOrderRepo,
	outRepositoryOrderDetailRepo WmsOutRepositoryOrderDetailRepo,
	enterRepositoryOrderRepo WmsEnterRepositoryOrderRepo,
	enterRepositoryOrderDetailRepo WmsEnterRepositoryOrderDetailRepo,
) *WmsTransferOrderUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_transfer_order/usecase"))
	return &WmsTransferOrderUsecase{
		log:                            l,
		repo:                           repo,
		tm:                             tm,
		detailRepo:                     detailRepo,
		logRepo:                        logRepo,
		documentRepo:                   documentRepo,
		equipmentRepo:                  equipmentRepo,
		materialRepo:                   materialRepo,
		outRepositoryOrderRepo:         outRepositoryOrderRepo,
		outRepositoryOrderDetailRepo:   outRepositoryOrderDetailRepo,
		enterRepositoryOrderRepo:       enterRepositoryOrderRepo,
		enterRepositoryOrderDetailRepo: enterRepositoryOrderDetailRepo,
	}
}

func (uc *WmsTransferOrderUsecase) ListWmsTransferOrder(ctx context.Context, req *v1.ListWmsTransferOrderRequest) (*v1.ListWmsTransferOrderResponse, error) {

	// // 自定义加入查询条件
	// newQuery := entgo.AppendQueryKeys(req.GetQuery(), map[string]string{
	// 	"createdBy": userId,
	// })
	// req.Query = &newQuery

	res, err := uc.repo.ListWmsTransferOrder(ctx, req)
	if err != nil {
		return nil, err
	}

	items := res.Items

	uc.setDetails(ctx, items)  // 设置调拨单明细
	uc.setUserInfo(ctx, items) // 设置调拨单用户信息

	return res, nil
}

func (uc *WmsTransferOrderUsecase) GetWmsTransferOrder(ctx context.Context, id string) (*v1.WmsTransferOrder, error) {
	res, err := uc.repo.GetWmsTransferOrder(ctx, id)
	if err != nil {
		return nil, err
	}

	uc.setDetails(ctx, []*v1.WmsTransferOrder{res})  // 设置调拨单明细
	uc.setUserInfo(ctx, []*v1.WmsTransferOrder{res}) // 设置调拨单用户信息

	return res, nil
}

func (uc *WmsTransferOrderUsecase) CreateWmsTransferOrder(ctx context.Context, req *v1.CreateWmsTransferOrderRequest) (*v1.WmsTransferOrder, error) {
	var err error
	var order *v1.WmsTransferOrder

	// 开启事务
	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		order, err = uc.repo.CreateWmsTransferOrder(ctx, req)
		if err != nil {
			return err
		}

		// 创建调拨单明细
		if len(req.Details) > 0 {
			for _, detail := range req.Details {
				detail.OrderId = &order.Id // 设置调拨单id
				detailRes, err := uc.detailRepo.CreateWmsTransferOrderDetail(ctx, detail)
				if err != nil {
					return err
				}

				order.Details = append(order.Details, detailRes)
			}
		}

		// 创建操作日志
		log := fmt.Sprintf("创建调拨单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_TRANSFER_ORDER, log)
		if err != nil {
			return err
		}

		// 创建系统文档
		source := fmt.Sprintf("调拨单: %s", trans.StringValue(order.OrderNo))
		err = uc.documentRepo.BatchCreateUpdateSystemDocuments(ctx, source, DOCUMENT_TRANSFER_ORDER_CATEGEORY, order.Id, req)
		if err != nil {
			return err
		}

		// 如果审批通过，调拨
		if order.GetStatus() == TRANSFER_ORDER_STATUS_APPROVED {
			// 调拨
			err = uc.handleTransfer(ctx, order)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return order, nil
}

func (uc *WmsTransferOrderUsecase) UpdateWmsTransferOrder(ctx context.Context, req *v1.UpdateWmsTransferOrderRequest) (*v1.WmsTransferOrder, error) {
	var err error
	var order *v1.WmsTransferOrder

	// 开启事务
	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		order, err = uc.repo.UpdateWmsTransferOrder(ctx, req)
		if err != nil {
			return err
		}

		// 更新调拨单明细
		if len(req.Details) > 0 {
			// 删除调拨单明细
			_, err = uc.detailRepo.DeleteWmsTransferOrderDetailByOrderId(ctx, order.Id)
			if err != nil {
				return err
			}

			// 创建调拨单明细
			for _, detail := range req.Details {
				detail.OrderId = &order.Id
				detailRes, err := uc.detailRepo.CreateWmsTransferOrderDetail(ctx, detail)
				if err != nil {
					return err
				}

				order.Details = append(order.Details, detailRes)
			}
		}

		// 创建操作日志
		log := fmt.Sprintf("更新调拨单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_TRANSFER_ORDER, log)
		if err != nil {
			return err
		}

		// 更新系统文档
		source := fmt.Sprintf("调拨单: %s", trans.StringValue(order.OrderNo))
		err = uc.documentRepo.BatchCreateUpdateSystemDocuments(ctx, source, DOCUMENT_TRANSFER_ORDER_CATEGEORY, order.Id, req)
		if err != nil {
			return err
		}

		// 如果审批通过，调拨
		if order.GetStatus() == TRANSFER_ORDER_STATUS_APPROVED {
			// 调拨
			err = uc.handleTransfer(ctx, order)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return order, nil
}

func (uc *WmsTransferOrderUsecase) DeleteWmsTransferOrder(ctx context.Context, id string) (bool, error) {
	var err error

	order, err := uc.repo.GetWmsTransferOrder(ctx, id)
	if err != nil {
		return false, err
	}

	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		_, err = uc.repo.DeleteWmsTransferOrder(ctx, id)
		if err != nil {
			return err
		}

		// 删除调拨单明细
		_, err = uc.detailRepo.DeleteWmsTransferOrderDetailByOrderId(ctx, id)
		if err != nil {
			return err
		}

		// 创建操作日志
		log := fmt.Sprintf("删除调拨单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, id, LOG_CATEGEORY_TRANSFER_ORDER, log)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return false, err
	}

	return true, nil
}

func (uc *WmsTransferOrderUsecase) MultiDeleteWmsTransferOrder(ctx context.Context, ids []string) (bool, error) {
	var err error

	orders, err := uc.repo.GetWmsTransferOrdersByIds(ctx, ids)
	if err != nil {
		return false, err
	}

	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		_, err = uc.repo.MultiDeleteWmsTransferOrder(ctx, ids)
		if err != nil {
			return err
		}

		// 删除调拨单明细
		for _, id := range ids {
			_, err = uc.detailRepo.DeleteWmsTransferOrderDetailByOrderId(ctx, id)
			if err != nil {
				return err
			}
		}

		// 创建操作日志
		for _, order := range orders {
			log := fmt.Sprintf("删除调拨单 %s", trans.StringValue(order.OrderNo))
			_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_TRANSFER_ORDER, log)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return false, err
	}

	return true, nil
}

// 设置调拨单明细
func (uc *WmsTransferOrderUsecase) setDetails(ctx context.Context, orders []*v1.WmsTransferOrder) {
	orderIds := make([]string, 0)
	for _, order := range orders {
		orderIds = append(orderIds, order.Id)
	}

	details, err := uc.detailRepo.GetWmsTransferOrderDetailsByOrderIds(ctx, orderIds)
	if err != nil {
		return
	}

	// 设置调拨单明细详细规格
	uc.setDetailsFeatureStr(ctx, details)

	detailsMap := make(map[string][]*v1.WmsTransferOrderDetail)
	for _, detail := range details {
		detailsMap[detail.GetOrderId()] = append(detailsMap[detail.GetOrderId()], detail)
	}

	for _, order := range orders {
		order.Details = detailsMap[order.Id]
	}
}

// 设置调拨单用户信息
func (uc *WmsTransferOrderUsecase) setUserInfo(ctx context.Context, orders []*v1.WmsTransferOrder) {
	ids := arrayutil.Map(orders, func(order *v1.WmsTransferOrder) string {
		return order.Id
	})

	// 获取调拨单申请用户的组织名称
	organization_name_map, err := uc.repo.GetWmsTransferOrderUserOrganizationNameMap(ctx, ids)
	if err != nil {
		return
	}

	for _, order := range orders {
		order.CreateByOrganizationName = trans.String(organization_name_map[order.Id])
	}
}

// 设置调拨单明细详细规格
func (uc *WmsTransferOrderUsecase) setDetailsFeatureStr(ctx context.Context, details []*v1.WmsTransferOrderDetail) {
	equipmentIds := arrayutil.Map(details, func(item *v1.WmsTransferOrderDetail) string {
		return item.GetEquipmentId()
	})

	features, err := uc.equipmentRepo.GetWmsEquipmentFeaturesByIds(ctx, equipmentIds)

	if err != nil {
		uc.log.Errorf("get equipment features error: %v", err)
	}

	for _, detail := range details {
		feature := detail.GetFeature()

		if feature != nil {
			feature_str := uc.equipmentRepo.ConvertFeatureToFeatureStr(feature.AsMap(), features)
			detail.FeatureStr = trans.String(feature_str)
		}
	}
}

func (uc *WmsTransferOrderUsecase) handleTransfer(ctx context.Context, order *v1.WmsTransferOrder) error {
	// 获取调拨单详情
	details, err := uc.detailRepo.GetWmsTransferOrderDetailsByOrderId(ctx, order.Id)
	if err != nil {
		return err
	}

	// 创建出库单
	out_repository_order_req := &v1.CreateWmsOutRepositoryOrderRequest{
		Type:            trans.String(OUTREPOSITORYTYPEALLOTING),
		RepositoryId:    order.FromRepositoryId,
		RelationOrderNo: order.OrderNo,
		OutTime:         trans.TimeToStrPtr(time.Now()),
		Status:          trans.String(OUTREPOSITORYSTATUSPENDING),
	}

	out_repository_order, err := uc.outRepositoryOrderRepo.CreateWmsOutRepositoryOrder(ctx, out_repository_order_req)
	if err != nil {
		return err
	}

	for _, detail := range details {
		out_repository_order_detail_req := &v1.CreateWmsOutRepositoryOrderDetailRequest{
			OutRepositoryOrderId: trans.String(out_repository_order.Id),
			MaterialId:           *detail.MaterialId,
			EquipmentId:          detail.EquipmentId,
			EquipmentTypeId:      detail.EquipmentTypeId,
			RepositoryId:         detail.RepositoryId,
			Name:                 detail.MaterialName,
			ModelNo:              detail.ModelNo,
			Num:                  detail.Num,
			Feature:              detail.Feature,
		}

		_, err = uc.outRepositoryOrderDetailRepo.CreateWmsOutRepositoryOrderDetail(ctx, out_repository_order_detail_req)
		if err != nil {
			return err
		}
	}

	return nil
}
