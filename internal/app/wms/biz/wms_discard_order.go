package biz

import (
	"context"
	"fmt"

	v1 "kratos-mono-demo/gen/api/wms/v1"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	"kratos-mono-demo/internal/pkg/go-utils/arrayutil"
	"kratos-mono-demo/internal/pkg/go-utils/trans"

	"github.com/go-kratos/kratos/v2/log"
)

const (
	DISCARD_ORDER_STATUS_CANCELLED  = "-1"  // 已取消
	DISCARD_ORDER_STATUS_REJECTED   = "-2"  // 已驳回
	DISCARD_ORDER_STATUS_PENDING    = "0"   // 待提交
	DISCARD_ORDER_STATUS_PROCESSING = "1"   // 审批中
	DISCARD_ORDER_STATUS_APPROVED   = "2"   // 已审批
	DISCARD_ORDER_STATUS_COMPLETED  = "100" // 已完成
)

type WmsDiscardOrderRepo interface {
	ListWmsDiscardOrder(ctx context.Context, req *v1.ListWmsDiscardOrderRequest) (*v1.ListWmsDiscardOrderResponse, error)
	GetWmsDiscardOrder(ctx context.Context, id string) (*v1.WmsDiscardOrder, error)
	CreateWmsDiscardOrder(ctx context.Context, req *v1.CreateWmsDiscardOrderRequest) (*v1.WmsDiscardOrder, error)
	UpdateWmsDiscardOrder(ctx context.Context, req *v1.UpdateWmsDiscardOrderRequest) (*v1.WmsDiscardOrder, error)
	DeleteWmsDiscardOrder(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsDiscardOrder(ctx context.Context, ids []string) (bool, error)

	// 根据关联流程单号查询报废单
	GetWmsDiscardOrderByOrderNo(ctx context.Context, orderNo string) (*v1.WmsDiscardOrder, error)

	// 获取报废单申请用户的组织名称
	GetWmsDiscardOrderUserOrganizationNameMap(ctx context.Context, ids []string) (map[string]string, error)

	// 根据ID列表获取报废单
	GetWmsDiscardOrdersByIds(ctx context.Context, ids []string) ([]*v1.WmsDiscardOrder, error)
}

type WmsDiscardOrderUsecase struct {
	log           *log.Helper
	repo          WmsDiscardOrderRepo
	tm            entutils.Transaction
	detailRepo    WmsDiscardOrderDetailRepo
	documentRepo  WmsDocumentRepo
	logRepo       WmsOperateLogRepo
	equipmentRepo WmsEquipmentRepo
	materialRepo  WmsMaterialRepo
}

func NewWmsDiscardOrderUsecase(
	repo WmsDiscardOrderRepo,
	tm entutils.Transaction,
	logger log.Logger,
	detailRepo WmsDiscardOrderDetailRepo,
	documentRepo WmsDocumentRepo,
	logRepo WmsOperateLogRepo,
	equipmentRepo WmsEquipmentRepo,
	materialRepo WmsMaterialRepo,
) *WmsDiscardOrderUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_discard_order/usecase"))
	return &WmsDiscardOrderUsecase{
		log:           l,
		repo:          repo,
		tm:            tm,
		detailRepo:    detailRepo,
		documentRepo:  documentRepo,
		logRepo:       logRepo,
		equipmentRepo: equipmentRepo,
		materialRepo:  materialRepo,
	}
}

func (uc *WmsDiscardOrderUsecase) ListWmsDiscardOrder(ctx context.Context, req *v1.ListWmsDiscardOrderRequest) (*v1.ListWmsDiscardOrderResponse, error) {

	// // 自定义加入查询条件
	// newQuery := entgo.AppendQueryKeys(req.GetQuery(), map[string]string{
	// 	"createdBy": userId,
	// })
	// req.Query = &newQuery

	res, err := uc.repo.ListWmsDiscardOrder(ctx, req)
	if err != nil {
		return nil, err
	}

	items := res.Items

	uc.setDetails(ctx, items)  // 设置报废单明细
	uc.setUserInfo(ctx, items) // 设置报废单用户信息

	return res, nil
}

func (uc *WmsDiscardOrderUsecase) GetWmsDiscardOrder(ctx context.Context, id string) (*v1.WmsDiscardOrder, error) {
	res, err := uc.repo.GetWmsDiscardOrder(ctx, id)
	if err != nil {
		return nil, err
	}

	uc.setDetails(ctx, []*v1.WmsDiscardOrder{res})  // 设置报废单明细
	uc.setUserInfo(ctx, []*v1.WmsDiscardOrder{res}) // 设置报废单用户信息

	return res, nil
}

func (uc *WmsDiscardOrderUsecase) CreateWmsDiscardOrder(ctx context.Context, req *v1.CreateWmsDiscardOrderRequest) (*v1.WmsDiscardOrder, error) {
	var err error
	var order *v1.WmsDiscardOrder

	// 开启事务
	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		order, err = uc.repo.CreateWmsDiscardOrder(ctx, req)
		if err != nil {
			return err
		}

		// 创建报废单明细
		if len(req.Details) > 0 {
			for _, detail := range req.Details {
				detail.OrderId = &order.Id // 设置报废单id
				detailRes, err := uc.detailRepo.CreateWmsDiscardOrderDetail(ctx, detail)
				if err != nil {
					return err
				}

				order.Details = append(order.Details, detailRes)
			}
		}

		// 创建操作日志
		log := fmt.Sprintf("创建报废单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_DISCARD_ORDER, log)
		if err != nil {
			return err
		}

		// 创建系统文档
		source := fmt.Sprintf("报废单: %s", trans.StringValue(order.OrderNo))
		err = uc.documentRepo.BatchCreateUpdateSystemDocuments(ctx, source, DOCUMENT_DISCARD_ORDER_CATEGEORY, order.Id, req)
		if err != nil {
			return err
		}

		// 如果审批通过，报废
		if order.GetStatus() == DISCARD_ORDER_STATUS_APPROVED {
			// 报废
			err = uc.handleDiscard(ctx, order)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return order, nil
}

func (uc *WmsDiscardOrderUsecase) UpdateWmsDiscardOrder(ctx context.Context, req *v1.UpdateWmsDiscardOrderRequest) (*v1.WmsDiscardOrder, error) {
	var err error
	var order *v1.WmsDiscardOrder

	// 开启事务
	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		order, err = uc.repo.UpdateWmsDiscardOrder(ctx, req)
		if err != nil {
			return err
		}

		// 更新报废单明细
		if len(req.Details) > 0 {
			// 删除报废单明细
			_, err = uc.detailRepo.DeleteWmsDiscardOrderDetailByOrderId(ctx, order.Id)
			if err != nil {
				return err
			}

			// 创建报废单明细
			for _, detail := range req.Details {
				detail.OrderId = &order.Id
				detailRes, err := uc.detailRepo.CreateWmsDiscardOrderDetail(ctx, detail)
				if err != nil {
					return err
				}

				order.Details = append(order.Details, detailRes)
			}
		}

		// 创建操作日志
		log := fmt.Sprintf("更新报废单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_DISCARD_ORDER, log)
		if err != nil {
			return err
		}

		// 更新系统文档
		source := fmt.Sprintf("报废单: %s", trans.StringValue(order.OrderNo))
		err = uc.documentRepo.BatchCreateUpdateSystemDocuments(ctx, source, DOCUMENT_DISCARD_ORDER_CATEGEORY, order.Id, req)
		if err != nil {
			return err
		}

		// 如果审批通过，报废
		if order.GetStatus() == DISCARD_ORDER_STATUS_APPROVED {
			// 报废
			err = uc.handleDiscard(ctx, order)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return order, nil
}

func (uc *WmsDiscardOrderUsecase) DeleteWmsDiscardOrder(ctx context.Context, id string) (bool, error) {
	var err error

	order, err := uc.repo.GetWmsDiscardOrder(ctx, id)
	if err != nil {
		return false, err
	}

	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		_, err = uc.repo.DeleteWmsDiscardOrder(ctx, id)
		if err != nil {
			return err
		}

		// 删除报废单明细
		_, err = uc.detailRepo.DeleteWmsDiscardOrderDetailByOrderId(ctx, id)
		if err != nil {
			return err
		}

		// 创建操作日志
		log := fmt.Sprintf("删除报废单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, id, LOG_CATEGEORY_DISCARD_ORDER, log)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return false, err
	}

	return true, nil
}

func (uc *WmsDiscardOrderUsecase) MultiDeleteWmsDiscardOrder(ctx context.Context, ids []string) (bool, error) {
	var err error

	orders, err := uc.repo.GetWmsDiscardOrdersByIds(ctx, ids)
	if err != nil {
		return false, err
	}

	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		_, err = uc.repo.MultiDeleteWmsDiscardOrder(ctx, ids)
		if err != nil {
			return err
		}

		// 删除报废单明细
		for _, id := range ids {
			_, err = uc.detailRepo.DeleteWmsDiscardOrderDetailByOrderId(ctx, id)
			if err != nil {
				return err
			}
		}

		// 创建操作日志
		for _, order := range orders {
			log := fmt.Sprintf("删除报废单 %s", trans.StringValue(order.OrderNo))
			_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_DISCARD_ORDER, log)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return false, err
	}

	return true, nil
}

// 设置报废单明细
func (uc *WmsDiscardOrderUsecase) setDetails(ctx context.Context, orders []*v1.WmsDiscardOrder) {
	orderIds := make([]string, 0)
	for _, order := range orders {
		orderIds = append(orderIds, order.Id)
	}

	details, err := uc.detailRepo.GetWmsDiscardOrderDetailsByOrderIds(ctx, orderIds)
	if err != nil {
		return
	}

	// 设置借用单明细详细规格
	uc.setDetailsFeatureStr(ctx, details)

	detailsMap := make(map[string][]*v1.WmsDiscardOrderDetail)
	for _, detail := range details {
		detailsMap[detail.GetOrderId()] = append(detailsMap[detail.GetOrderId()], detail)
	}

	for _, order := range orders {
		order.Details = detailsMap[order.Id]
	}
}

// 设置报废单用户信息
func (uc *WmsDiscardOrderUsecase) setUserInfo(ctx context.Context, orders []*v1.WmsDiscardOrder) {
	ids := arrayutil.Map(orders, func(order *v1.WmsDiscardOrder) string {
		return order.Id
	})

	// 获取报废单申请用户的组织名称
	organization_name_map, err := uc.repo.GetWmsDiscardOrderUserOrganizationNameMap(ctx, ids)
	if err != nil {
		return
	}

	for _, order := range orders {
		order.CreateByOrganizationName = trans.String(organization_name_map[order.Id])
	}
}

// 设置报废单明细详细规格
func (uc *WmsDiscardOrderUsecase) setDetailsFeatureStr(ctx context.Context, details []*v1.WmsDiscardOrderDetail) {
	equipmentIds := arrayutil.Map(details, func(item *v1.WmsDiscardOrderDetail) string {
		return item.GetEquipmentId()
	})

	features, err := uc.equipmentRepo.GetWmsEquipmentFeaturesByIds(ctx, equipmentIds)

	if err != nil {
		uc.log.Errorf("get equipment features error: %v", err)
	}

	for _, detail := range details {
		feature := detail.GetFeature()

		if feature != nil {
			feature_str := uc.equipmentRepo.ConvertFeatureToFeatureStr(feature.AsMap(), features)
			detail.FeatureStr = trans.String(feature_str)
		}
	}
}

func (uc *WmsDiscardOrderUsecase) handleDiscard(ctx context.Context, order *v1.WmsDiscardOrder) error {
	// 获取报废单详情
	details, err := uc.detailRepo.GetWmsDiscardOrderDetailsByOrderId(ctx, order.Id)
	if err != nil {
		return err
	}

	materialIds := arrayutil.Map(details, func(item *v1.WmsDiscardOrderDetail) string {
		return item.GetMaterialId()
	})

	materials, err := uc.materialRepo.ListMaterialByIds(ctx, materialIds)
	if err != nil {
		return err
	}

	for _, material := range materials {
		equipment := material.GetEquipment()
		if equipment == nil {
			continue
		}

		if equipment.DiscardMethod == EQUIPMENT_DISCARD_METHOD_OUTREPOSITORY {
			return fmt.Errorf("%s:%s 不支持审批报废", material.GetCode(), material.GetName())
		}
	}

	for _, detail := range details {
		split_material, err := uc.materialRepo.SplitMaterial(ctx,
			detail.GetMaterialId(),
			"",
			nil,
			trans.Int32(MATERIAL_STATUS_UNAVAILABLE),
			nil,
			int32(detail.GetNum()),
			nil,
		)

		if err != nil {
			return err
		}

		_, err = uc.detailRepo.UpdateWmsDiscardOrderDetail(ctx, &v1.UpdateWmsDiscardOrderDetailRequest{
			Id:         detail.Id,
			MaterialId: trans.String(split_material.Id),
		})

		if err != nil {
			return err
		}
	}

	// 设置报废单状态为已完成
	_, err = uc.repo.UpdateWmsDiscardOrder(ctx, &v1.UpdateWmsDiscardOrderRequest{
		Id:     order.GetId(),
		Status: trans.String(DISCARD_ORDER_STATUS_COMPLETED),
	})
	if err != nil {
		return err
	}

	return nil
}
