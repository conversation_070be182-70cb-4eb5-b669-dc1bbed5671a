package biz

import (
	"context"

	v1 "kratos-mono-demo/gen/api/wms/v1"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"

	"github.com/go-kratos/kratos/v2/log"
)

type WmsReturnOrderDetailRepo interface {
	ListWmsReturnOrderDetail(ctx context.Context, req *v1.ListWmsReturnOrderDetailRequest) (*v1.ListWmsReturnOrderDetailResponse, error)
	GetWmsReturnOrderDetail(ctx context.Context, id string) (*v1.WmsReturnOrderDetail, error)
	CreateWmsReturnOrderDetail(ctx context.Context, req *v1.CreateWmsReturnOrderDetailRequest) (*v1.WmsReturnOrderDetail, error)
	UpdateWmsReturnOrderDetail(ctx context.Context, req *v1.UpdateWmsReturnOrderDetailRequest) (*v1.WmsReturnOrderDetail, error)
	DeleteWmsReturnOrderDetail(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsReturnOrderDetail(ctx context.Context, ids []string) (bool, error)

	DeleteWmsReturnOrderDetailByOrderId(ctx context.Context, orderId string) (bool, error)
	GetWmsReturnOrderDetailsByOrderIds(ctx context.Context, orderIds []string) ([]*v1.WmsReturnOrderDetail, error)
}

type WmsReturnOrderDetailUsecase struct {
	log  *log.Helper
	repo WmsReturnOrderDetailRepo
	tm   entutils.Transaction
}

func NewWmsReturnOrderDetailUsecase(repo WmsReturnOrderDetailRepo, tm entutils.Transaction, logger log.Logger) *WmsReturnOrderDetailUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_return_order_detail/usecase"))
	return &WmsReturnOrderDetailUsecase{
		log:  l,
		repo: repo,
		tm:   tm,
	}
}

func (uc *WmsReturnOrderDetailUsecase) ListWmsReturnOrderDetail(ctx context.Context, req *v1.ListWmsReturnOrderDetailRequest) (*v1.ListWmsReturnOrderDetailResponse, error) {

	// // 自定义加入查询条件
	// newQuery := entgo.AppendQueryKeys(req.GetQuery(), map[string]string{
	// 	"createdBy": userId,
	// })
	// req.Query = &newQuery

	return uc.repo.ListWmsReturnOrderDetail(ctx, req)
}

func (uc *WmsReturnOrderDetailUsecase) GetWmsReturnOrderDetail(ctx context.Context, id string) (*v1.WmsReturnOrderDetail, error) {
	return uc.repo.GetWmsReturnOrderDetail(ctx, id)
}

func (uc *WmsReturnOrderDetailUsecase) CreateWmsReturnOrderDetail(ctx context.Context, req *v1.CreateWmsReturnOrderDetailRequest) (*v1.WmsReturnOrderDetail, error) {
	return uc.repo.CreateWmsReturnOrderDetail(ctx, req)
}

func (uc *WmsReturnOrderDetailUsecase) UpdateWmsReturnOrderDetail(ctx context.Context, req *v1.UpdateWmsReturnOrderDetailRequest) (*v1.WmsReturnOrderDetail, error) {
	return uc.repo.UpdateWmsReturnOrderDetail(ctx, req)
}

func (uc *WmsReturnOrderDetailUsecase) DeleteWmsReturnOrderDetail(ctx context.Context, id string) (bool, error) {
	return uc.repo.DeleteWmsReturnOrderDetail(ctx, id)
}

func (uc *WmsReturnOrderDetailUsecase) MultiDeleteWmsReturnOrderDetail(ctx context.Context, ids []string) (bool, error) {
	return uc.repo.MultiDeleteWmsReturnOrderDetail(ctx, ids)
}
