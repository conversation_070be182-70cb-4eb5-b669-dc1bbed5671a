package biz

import (
	"context"

	v1 "kratos-mono-demo/gen/api/wms/v1"

	"github.com/go-kratos/kratos/v2/log"
)

type DocumentType string

const (
	DOCUMENT_TYPE_CONTRACT DocumentType = "contract"
	DOCUMENT_TYPE_INVOICE  DocumentType = "invoice"
	DOCUMENT_TYPE_AUDIT    DocumentType = "audit"
	DOCUMENT_TYPE_OTHER    DocumentType = "other"
	DOCUMENT_TYPE_PURCHASE DocumentType = "purchase"
	DOCUMENT_TYPE_REPAIR   DocumentType = "repair"

	// 类型
	DOCUMENT_ENTER_REPOSITORY_ORDER_CATEGEORY = LOG_CATEGEORY_ENTER_REPOSITORY_ORDER
	DOCUMENT_DISCARD_PLAN_ORDER_CATEGEORY     = LOG_CATEGEORY_DISCARD_PLAN_ORDER
	DOCUMENT_DISCARDPLANMEETING_CATEGEORY     = LOG_CATEGEORY_DISCARDPLANMEETING
	DOCUMENT_DISCARD_ORDER_CATEGEORY          = LOG_CATEGEORY_DISCARD_ORDER
	DOCUMENT_MAINTAIN_PLAN_CATEGEORY          = LOG_CATEGEORY_MAINTAIN_PLAN
	DOCUMENT_PURCHASE_ORDER_CATEGEORY         = LOG_CATEGEORY_PURCHASE_ORDER
	DOCUMENT_APPROVAL_TASK_CATEGEORY          = LOG_CATEGEORY_APPROVAL_TASK
	DOCUMENT_HAND_CREATE_CATEGEORY            = "hand_create"
	DOCUMENT_BORROW_ORDER_CATEGEORY           = "borrow_order"
	DOCUMENT_CLAIM_ORDER_CATEGEORY            = "claim_order"
	DOCUMENT_RETURN_ORDER_CATEGEORY           = "return_order"
	DOCUMENT_REPAIR_ORDER_CATEGEORY           = "repair_order"
	DOCUMENT_MAINTAIN_ORDER_CATEGEORY         = "maintain_order"
	DOCUMENT_TRANSFER_ORDER_CATEGEORY         = "transfer_order"
)

type DocumentInfo struct {
	Type DocumentType
	Url  string
}

type IDocumentUrls interface {
	GetAuditUrls() string
	GetContractUrls() string
	GetInvoiceUrls() string
	GetOtherUrls() string
}

type WmsDocumentRepo interface {
	ListWmsDocument(ctx context.Context, req *v1.ListWmsDocumentRequest) (*v1.ListWmsDocumentResponse, error)
	GetWmsDocument(ctx context.Context, id string) (*v1.WmsDocument, error)
	CreateWmsDocument(ctx context.Context, req *v1.CreateWmsDocumentRequest) (*v1.WmsDocument, error)
	UpdateWmsDocument(ctx context.Context, req *v1.UpdateWmsDocumentRequest) (*v1.WmsDocument, error)
	DeleteWmsDocument(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsDocument(ctx context.Context, ids []string) (bool, error)

	// 批量创建更新单据
	BatchCreateUpdateDocuments(ctx context.Context, enterWay, source, sourceType, sourceId string, documents []*DocumentInfo) error
	BatchCreateUpdateSystemDocuments(ctx context.Context, source, sourceType, sourceId string, documentUrls IDocumentUrls) error

	// 根据来源ID删除附件
	DeleteWmsDocumentBySourceId(ctx context.Context, sourceId string) error
	// 根据来源ID查询附件
	QueryWmsDocumentBySourceId(ctx context.Context, sourceId string) ([]*v1.WmsDocument, error)
	// 获取票据统计
	GetDocumentStatistics(ctx context.Context, req *v1.GetDocumentStatisticsRequest) (*v1.GetDocumentStatisticsResponse, error)
	// 获取票据用户统计
	GetDocumentUserStatistics(ctx context.Context, req *v1.GetDocumentStatisticsRequest) ([]*v1.DocumentUserStatistics, error)
	// 获取票据时间统计
	GetDocumentTimeStatistics(ctx context.Context, req *v1.GetDocumentStatisticsRequest) ([]*v1.DocumentTimeStatistics, error)
}

type WmsDocumentUsecase struct {
	log  *log.Helper
	repo WmsDocumentRepo
}

func NewWmsDocumentUsecase(repo WmsDocumentRepo, logger log.Logger) *WmsDocumentUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_document/usecase"))
	return &WmsDocumentUsecase{
		log:  l,
		repo: repo,
	}
}

func (uc *WmsDocumentUsecase) ListWmsDocument(ctx context.Context, req *v1.ListWmsDocumentRequest) (*v1.ListWmsDocumentResponse, error) {
	return uc.repo.ListWmsDocument(ctx, req)
}

func (uc *WmsDocumentUsecase) GetWmsDocument(ctx context.Context, id string) (*v1.WmsDocument, error) {
	return uc.repo.GetWmsDocument(ctx, id)
}

func (uc *WmsDocumentUsecase) CreateWmsDocument(ctx context.Context, req *v1.CreateWmsDocumentRequest) (*v1.WmsDocument, error) {
	return uc.repo.CreateWmsDocument(ctx, req)
}

func (uc *WmsDocumentUsecase) UpdateWmsDocument(ctx context.Context, req *v1.UpdateWmsDocumentRequest) (*v1.WmsDocument, error) {
	return uc.repo.UpdateWmsDocument(ctx, req)
}

func (uc *WmsDocumentUsecase) DeleteWmsDocument(ctx context.Context, id string) (bool, error) {
	return uc.repo.DeleteWmsDocument(ctx, id)
}

func (uc *WmsDocumentUsecase) MultiDeleteWmsDocument(ctx context.Context, ids []string) (bool, error) {
	return uc.repo.MultiDeleteWmsDocument(ctx, ids)
}
