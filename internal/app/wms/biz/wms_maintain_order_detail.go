package biz

import (
	"context"

	v1 "kratos-mono-demo/gen/api/wms/v1"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"

	"github.com/go-kratos/kratos/v2/log"
)

type WmsMaintainOrderDetailRepo interface {
	ListWmsMaintainOrderDetail(ctx context.Context, req *v1.ListWmsMaintainOrderDetailRequest) (*v1.ListWmsMaintainOrderDetailResponse, error)
	GetWmsMaintainOrderDetail(ctx context.Context, id string) (*v1.WmsMaintainOrderDetail, error)
	CreateWmsMaintainOrderDetail(ctx context.Context, req *v1.CreateWmsMaintainOrderDetailRequest) (*v1.WmsMaintainOrderDetail, error)
	UpdateWmsMaintainOrderDetail(ctx context.Context, req *v1.UpdateWmsMaintainOrderDetailRequest) (*v1.WmsMaintainOrderDetail, error)
	DeleteWmsMaintainOrderDetail(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsMaintainOrderDetail(ctx context.Context, ids []string) (bool, error)

	// 根据保养单ID删除保养单明细
	DeleteWmsMaintainOrderDetailByOrderId(ctx context.Context, orderId string) (bool, error)

	// 根据保养单ID查询保养单明细
	GetWmsMaintainOrderDetailsByOrderIds(ctx context.Context, orderIds []string) ([]*v1.WmsMaintainOrderDetail, error)

	// 根据保养单ID查询保养单明细
	GetWmsMaintainOrderDetailsByOrderId(ctx context.Context, orderId string) ([]*v1.WmsMaintainOrderDetail, error)
}

type WmsMaintainOrderDetailUsecase struct {
	log  *log.Helper
	repo WmsMaintainOrderDetailRepo
	tm   entutils.Transaction
}

func NewWmsMaintainOrderDetailUsecase(repo WmsMaintainOrderDetailRepo, tm entutils.Transaction, logger log.Logger) *WmsMaintainOrderDetailUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_maintain_order_detail/usecase"))
	return &WmsMaintainOrderDetailUsecase{
		log:  l,
		repo: repo,
		tm:   tm,
	}
}

func (uc *WmsMaintainOrderDetailUsecase) ListWmsMaintainOrderDetail(ctx context.Context, req *v1.ListWmsMaintainOrderDetailRequest) (*v1.ListWmsMaintainOrderDetailResponse, error) {

	// // 自定义加入查询条件
	// newQuery := entgo.AppendQueryKeys(req.GetQuery(), map[string]string{
	// 	"createdBy": userId,
	// })
	// req.Query = &newQuery

	return uc.repo.ListWmsMaintainOrderDetail(ctx, req)
}

func (uc *WmsMaintainOrderDetailUsecase) GetWmsMaintainOrderDetail(ctx context.Context, id string) (*v1.WmsMaintainOrderDetail, error) {
	return uc.repo.GetWmsMaintainOrderDetail(ctx, id)
}

func (uc *WmsMaintainOrderDetailUsecase) CreateWmsMaintainOrderDetail(ctx context.Context, req *v1.CreateWmsMaintainOrderDetailRequest) (*v1.WmsMaintainOrderDetail, error) {
	return uc.repo.CreateWmsMaintainOrderDetail(ctx, req)
}

func (uc *WmsMaintainOrderDetailUsecase) UpdateWmsMaintainOrderDetail(ctx context.Context, req *v1.UpdateWmsMaintainOrderDetailRequest) (*v1.WmsMaintainOrderDetail, error) {
	return uc.repo.UpdateWmsMaintainOrderDetail(ctx, req)
}

func (uc *WmsMaintainOrderDetailUsecase) DeleteWmsMaintainOrderDetail(ctx context.Context, id string) (bool, error) {
	return uc.repo.DeleteWmsMaintainOrderDetail(ctx, id)
}

func (uc *WmsMaintainOrderDetailUsecase) MultiDeleteWmsMaintainOrderDetail(ctx context.Context, ids []string) (bool, error) {
	return uc.repo.MultiDeleteWmsMaintainOrderDetail(ctx, ids)
}
