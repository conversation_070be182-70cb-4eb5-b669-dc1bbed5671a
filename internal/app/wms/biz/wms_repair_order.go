package biz

import (
	"context"
	"fmt"

	v1 "kratos-mono-demo/gen/api/wms/v1"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	"kratos-mono-demo/internal/pkg/go-utils/arrayutil"
	"kratos-mono-demo/internal/pkg/go-utils/trans"

	"github.com/go-kratos/kratos/v2/log"
)

const (
	REPAIR_ORDER_STATUS_CANCELLED  = "-1"  // 已取消
	REPAIR_ORDER_STATUS_REJECTED   = "-2"  // 已驳回
	REPAIR_ORDER_STATUS_PENDING    = "0"   // 待提交
	REPAIR_ORDER_STATUS_PROCESSING = "1"   // 审批中
	REPAIR_ORDER_STATUS_APPROVED   = "2"   // 已审批
	REPAIR_ORDER_STATUS_COMPLETED  = "100" // 已完成
)

type WmsRepairOrderRepo interface {
	ListWmsRepairOrder(ctx context.Context, req *v1.ListWmsRepairOrderRequest) (*v1.ListWmsRepairOrderResponse, error)
	GetWmsRepairOrder(ctx context.Context, id string) (*v1.WmsRepairOrder, error)
	CreateWmsRepairOrder(ctx context.Context, req *v1.CreateWmsRepairOrderRequest) (*v1.WmsRepairOrder, error)
	UpdateWmsRepairOrder(ctx context.Context, req *v1.UpdateWmsRepairOrderRequest) (*v1.WmsRepairOrder, error)
	DeleteWmsRepairOrder(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsRepairOrder(ctx context.Context, ids []string) (bool, error)

	GetWmsRepairOrdersByIds(ctx context.Context, ids []string) ([]*v1.WmsRepairOrder, error)
	GetWmsRepairOrderUserOrganizationNameMap(ctx context.Context, ids []string) (map[string]string, error)
}

type WmsRepairOrderUsecase struct {
	log           *log.Helper
	repo          WmsRepairOrderRepo
	tm            entutils.Transaction
	detailRepo    WmsRepairOrderDetailRepo
	logRepo       WmsOperateLogRepo
	documentRepo  WmsDocumentRepo
	equipmentRepo WmsEquipmentRepo
	materialRepo  WmsMaterialRepo
}

func NewWmsRepairOrderUsecase(
	repo WmsRepairOrderRepo,
	tm entutils.Transaction,
	logger log.Logger,
	detailRepo WmsRepairOrderDetailRepo,
	logRepo WmsOperateLogRepo,
	documentRepo WmsDocumentRepo,
	equipmentRepo WmsEquipmentRepo,
	materialRepo WmsMaterialRepo,
) *WmsRepairOrderUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_repair_order/usecase"))
	return &WmsRepairOrderUsecase{
		log:           l,
		repo:          repo,
		tm:            tm,
		detailRepo:    detailRepo,
		logRepo:       logRepo,
		documentRepo:  documentRepo,
		equipmentRepo: equipmentRepo,
		materialRepo:  materialRepo,
	}
}

func (uc *WmsRepairOrderUsecase) ListWmsRepairOrder(ctx context.Context, req *v1.ListWmsRepairOrderRequest) (*v1.ListWmsRepairOrderResponse, error) {

	// // 自定义加入查询条件
	// newQuery := entgo.AppendQueryKeys(req.GetQuery(), map[string]string{
	// 	"createdBy": userId,
	// })
	// req.Query = &newQuery

	res, err := uc.repo.ListWmsRepairOrder(ctx, req)
	if err != nil {
		return nil, err
	}

	items := res.Items

	uc.setDetails(ctx, items)  // 设置明细
	uc.setUserInfo(ctx, items) // 设置用户信息

	return res, nil
}

func (uc *WmsRepairOrderUsecase) GetWmsRepairOrder(ctx context.Context, id string) (*v1.WmsRepairOrder, error) {
	res, err := uc.repo.GetWmsRepairOrder(ctx, id)
	if err != nil {
		return nil, err
	}

	uc.setDetails(ctx, []*v1.WmsRepairOrder{res})  // 设置明细
	uc.setUserInfo(ctx, []*v1.WmsRepairOrder{res}) // 设置用户信息

	return res, nil
}

func (uc *WmsRepairOrderUsecase) CreateWmsRepairOrder(ctx context.Context, req *v1.CreateWmsRepairOrderRequest) (*v1.WmsRepairOrder, error) {
	var err error
	var order *v1.WmsRepairOrder

	// 开启事务
	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		order, err = uc.repo.CreateWmsRepairOrder(ctx, req)
		if err != nil {
			return err
		}

		// 创建明细
		if len(req.Details) > 0 {
			for _, detail := range req.Details {
				detail.OrderId = &order.Id
				detailRes, err := uc.detailRepo.CreateWmsRepairOrderDetail(ctx, detail)
				if err != nil {
					return err
				}

				order.Details = append(order.Details, detailRes)
			}
		}

		// 创建操作日志
		log := fmt.Sprintf("创建维修单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_REPAIR_ORDER, log)
		if err != nil {
			return err
		}

		// 如果审批通过，创建入库单
		if order.GetStatus() == REPAIR_ORDER_STATUS_APPROVED {
			// 设置资产维修中
			err = uc.setMaterialRepairing(ctx, order)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return order, nil
}

func (uc *WmsRepairOrderUsecase) UpdateWmsRepairOrder(ctx context.Context, req *v1.UpdateWmsRepairOrderRequest) (*v1.WmsRepairOrder, error) {
	var err error
	var order *v1.WmsRepairOrder

	// 开启事务
	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		order, err = uc.repo.UpdateWmsRepairOrder(ctx, req)
		if err != nil {
			return err
		}

		// 更新明细
		if len(req.Details) > 0 {
			// 删除明细
			_, err = uc.detailRepo.DeleteWmsRepairOrderDetailByOrderId(ctx, order.Id)
			if err != nil {
				return err
			}

			// 创建明细
			for _, detail := range req.Details {
				detail.OrderId = &order.Id
				detailRes, err := uc.detailRepo.CreateWmsRepairOrderDetail(ctx, detail)
				if err != nil {
					return err
				}

				order.Details = append(order.Details, detailRes)
			}
		}

		// 创建操作日志
		log := fmt.Sprintf("更新维修单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_REPAIR_ORDER, log)
		if err != nil {
			return err
		}

		// 如果审批通过，设置资产维修中
		if order.GetStatus() == REPAIR_ORDER_STATUS_APPROVED {
			// 设置资产维修中
			err = uc.setMaterialRepairing(ctx, order)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return order, nil
}

func (uc *WmsRepairOrderUsecase) DeleteWmsRepairOrder(ctx context.Context, id string) (bool, error) {
	var err error

	order, err := uc.repo.GetWmsRepairOrder(ctx, id)
	if err != nil {
		return false, err
	}

	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		_, err = uc.repo.DeleteWmsRepairOrder(ctx, id)
		if err != nil {
			return err
		}

		// 删除明细
		_, err = uc.detailRepo.DeleteWmsRepairOrderDetailByOrderId(ctx, id)
		if err != nil {
			return err
		}

		// 创建操作日志
		log := fmt.Sprintf("删除维修单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, id, LOG_CATEGEORY_REPAIR_ORDER, log)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return false, err
	}

	return true, nil
}

func (uc *WmsRepairOrderUsecase) MultiDeleteWmsRepairOrder(ctx context.Context, ids []string) (bool, error) {
	var err error

	orders, err := uc.repo.GetWmsRepairOrdersByIds(ctx, ids)
	if err != nil {
		return false, err
	}

	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		_, err = uc.repo.MultiDeleteWmsRepairOrder(ctx, ids)
		if err != nil {
			return err
		}

		// 删除明细
		for _, id := range ids {
			_, err = uc.detailRepo.DeleteWmsRepairOrderDetailByOrderId(ctx, id)
			if err != nil {
				return err
			}
		}

		// 创建操作日志
		for _, order := range orders {
			log := fmt.Sprintf("删除维修单 %s", trans.StringValue(order.OrderNo))
			_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_REPAIR_ORDER, log)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return false, err
	}

	return true, nil
}

// 设置退还单明细
func (uc *WmsRepairOrderUsecase) setDetails(ctx context.Context, orders []*v1.WmsRepairOrder) {
	orderIds := make([]string, 0)
	for _, order := range orders {
		orderIds = append(orderIds, order.Id)
	}

	details, err := uc.detailRepo.GetWmsRepairOrderDetailsByOrderIds(ctx, orderIds)
	if err != nil {
		return
	}

	// 设置退还单明细详细规格
	uc.setDetailsFeatureStr(ctx, details)

	detailsMap := make(map[string][]*v1.WmsRepairOrderDetail)
	for _, detail := range details {
		detailsMap[detail.GetOrderId()] = append(detailsMap[detail.GetOrderId()], detail)
	}

	for _, order := range orders {
		order.Details = detailsMap[order.Id]
	}
}

// 设置退还单用户信息
func (uc *WmsRepairOrderUsecase) setUserInfo(ctx context.Context, orders []*v1.WmsRepairOrder) {
	ids := arrayutil.Map(orders, func(order *v1.WmsRepairOrder) string {
		return order.Id
	})

	// 获取借用单申请用户的组织名称
	organization_name_map, err := uc.repo.GetWmsRepairOrderUserOrganizationNameMap(ctx, ids)
	if err != nil {
		return
	}

	for _, order := range orders {
		order.CreateByOrganizationName = trans.String(organization_name_map[order.Id])
	}
}

// 设置退还单明细详细规格
func (uc *WmsRepairOrderUsecase) setDetailsFeatureStr(ctx context.Context, details []*v1.WmsRepairOrderDetail) {
	equipmentIds := arrayutil.Map(details, func(item *v1.WmsRepairOrderDetail) string {
		return item.GetEquipmentId()
	})

	features, err := uc.equipmentRepo.GetWmsEquipmentFeaturesByIds(ctx, equipmentIds)

	if err != nil {
		uc.log.Errorf("get equipment features error: %v", err)
	}

	for _, detail := range details {
		feature := detail.GetFeature()

		if feature != nil {
			feature_str := uc.equipmentRepo.ConvertFeatureToFeatureStr(feature.AsMap(), features)
			detail.FeatureStr = trans.String(feature_str)
		}
	}
}

func (uc *WmsRepairOrderUsecase) setMaterialRepairing(ctx context.Context, order *v1.WmsRepairOrder) error {
	// 获取维修单明细
	details, err := uc.detailRepo.GetWmsRepairOrderDetailsByOrderIds(ctx, []string{order.Id})
	if err != nil {
		return err
	}

	// 设置资产维修中
	for _, detail := range details {
		material, err := uc.materialRepo.GetWmsMaterial(ctx, detail.GetMaterialId())
		if err != nil {
			return err
		}

		uc.materialRepo.SplitMaterial(ctx,
			material.GetId(),
			"",
			nil,
			nil,
			trans.Int32(MATERIAL_MAINTAIN_STATUS_REPAIRING),
			int32(detail.GetNum()),
			&SplitOptions{
				ForceSplit: true,
				OrderNo:    order.OrderNo,
			},
		)

		if err != nil {
			return err
		}
	}

	// 更新维修单状态
	_, err = uc.repo.UpdateWmsRepairOrder(ctx, &v1.UpdateWmsRepairOrderRequest{
		Id:     order.Id,
		Status: trans.String(REPAIR_ORDER_STATUS_COMPLETED),
	})
	if err != nil {
		return err
	}

	return nil
}
