package biz

import (
	"context"
	"kratos-mono-demo/internal/app/wms/biz/event_manager"
	"kratos-mono-demo/internal/pkg/go-utils/arrayutil"
	"kratos-mono-demo/internal/pkg/go-utils/event"

	v1 "kratos-mono-demo/gen/api/wms/v1"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	"kratos-mono-demo/internal/pkg/go-utils/trans"

	"github.com/go-kratos/kratos/v2/log"
)

type WmsAuditPlanDetailRepo interface {
	ListWmsAuditPlanDetail(ctx context.Context, req *v1.ListWmsAuditPlanDetailRequest) (*v1.ListWmsAuditPlanDetailResponse, error)
	GetWmsAuditPlanDetail(ctx context.Context, id string) (*v1.WmsAuditPlanDetail, error)
	CreateWmsAuditPlanDetail(ctx context.Context, req *v1.CreateWmsAuditPlanDetailRequest) (*v1.WmsAuditPlanDetail, error)
	UpdateWmsAuditPlanDetail(ctx context.Context, req *v1.UpdateWmsAuditPlanDetailRequest) (*v1.WmsAuditPlanDetail, error)
	DeleteWmsAuditPlanDetail(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsAuditPlanDetail(ctx context.Context, ids []string) (bool, error)

	BatchCreateWmsAuditPlanDetail(ctx context.Context, reqs []*v1.CreateWmsAuditPlanDetailRequest) error
	// 发现RFID码，更新盘点计划明细状态
	FoundRfidCode(ctx context.Context, rfidCode string) error
	// 批量更新盘点计划明细
	MultiUpdateWmsAuditPlanDetail(ctx context.Context, req *v1.MultiUpdateWmsAuditPlanDetailRequest) error
	// 获取我的装备盘点计划详情列表
	ListWmsAuditPlanDetailByUserId(ctx context.Context, userId string, auditPlanId string, isAudit *bool) (*v1.ListWmsAuditPlanDetailResponse, error)
	// 更新我的装备盘点计划详情
	UpdateWmsAuditPlanDetailByUserId(ctx context.Context, userId string, req *v1.PostMyWmsAuditPlanDetailRequest) error
}

type WmsAuditPlanDetailUsecase struct {
	log                  *log.Helper
	eventManager         *event_manager.EventManager
	repo                 WmsAuditPlanDetailRepo
	rfidReaderRecordRepo WmsRfidReaderRecordRepo
	tm                   entutils.Transaction
	equipmentRepo        WmsEquipmentRepo
}

func NewWmsAuditPlanDetailUsecase(
	eventManager *event_manager.EventManager,
	repo WmsAuditPlanDetailRepo,
	rfidReaderRecordRepo WmsRfidReaderRecordRepo,
	tm entutils.Transaction,
	logger log.Logger,
	equipmentRepo WmsEquipmentRepo,
) *WmsAuditPlanDetailUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_audit_plan_detail/usecase"))

	uc := &WmsAuditPlanDetailUsecase{
		log:                  l,
		eventManager:         eventManager,
		repo:                 repo,
		rfidReaderRecordRepo: rfidReaderRecordRepo,
		tm:                   tm,
		equipmentRepo:        equipmentRepo,
	}

	eventManager.RfidReader.RegisterListener(event_manager.EventRfidReader, uc.HandleReaderEvent)

	return uc
}

func (uc *WmsAuditPlanDetailUsecase) ListWmsAuditPlanDetail(ctx context.Context, req *v1.ListWmsAuditPlanDetailRequest) (*v1.ListWmsAuditPlanDetailResponse, error) {

	// 自定义加入查询条件
	// newQuery := entgo.AppendQueryKeys(req.GetQuery(), map[string]string{
	// 	"isAudit": "true",
	// })
	// req.Query = &newQuery

	res, err := uc.repo.ListWmsAuditPlanDetail(ctx, req)

	if err != nil {
		return nil, err
	}

	// 转换详细规格为字符串
	equipmentIds := arrayutil.Map(res.GetItems(), func(item *v1.WmsAuditPlanDetail) string {
		return item.GetEquipmentId()
	})

	features, err := uc.equipmentRepo.GetWmsEquipmentFeaturesByIds(ctx, equipmentIds)

	if err != nil {
		uc.log.Errorf("get equipment features error: %v", err)
	}

	for _, detail := range res.GetItems() {
		feature := detail.GetFeature()

		if feature != nil {
			feature_str := uc.equipmentRepo.ConvertFeatureToFeatureStr(feature.AsMap(), features)
			detail.FeatureStr = trans.String(feature_str)
		}
	}

	return res, nil
}

func (uc *WmsAuditPlanDetailUsecase) GetWmsAuditPlanDetail(ctx context.Context, id string) (*v1.WmsAuditPlanDetail, error) {
	detail, err := uc.repo.GetWmsAuditPlanDetail(ctx, id)

	if err != nil {
		uc.log.Errorf("get wms_audit_plan_detail error: %v", err)
	}

	// 转换详细规格为字符串
	features, err := uc.equipmentRepo.GetWmsEquipmentFeaturesByIds(ctx, []string{detail.GetEquipmentId()})

	if err != nil {
		uc.log.Errorf("get equipment features error: %v", err)
	}

	feature := detail.GetFeature()

	if feature != nil {
		feature_str := uc.equipmentRepo.ConvertFeatureToFeatureStr(feature.AsMap(), features)
		detail.FeatureStr = trans.String(feature_str)
	}

	return detail, err
}

func (uc *WmsAuditPlanDetailUsecase) CreateWmsAuditPlanDetail(ctx context.Context, req *v1.CreateWmsAuditPlanDetailRequest) (*v1.WmsAuditPlanDetail, error) {
	return uc.repo.CreateWmsAuditPlanDetail(ctx, req)
}

func (uc *WmsAuditPlanDetailUsecase) UpdateWmsAuditPlanDetail(ctx context.Context, req *v1.UpdateWmsAuditPlanDetailRequest) (*v1.WmsAuditPlanDetail, error) {
	req.IsAudit = trans.Bool(true)
	return uc.repo.UpdateWmsAuditPlanDetail(ctx, req)
}

func (uc *WmsAuditPlanDetailUsecase) DeleteWmsAuditPlanDetail(ctx context.Context, id string) (bool, error) {
	return uc.repo.DeleteWmsAuditPlanDetail(ctx, id)
}

func (uc *WmsAuditPlanDetailUsecase) MultiDeleteWmsAuditPlanDetail(ctx context.Context, ids []string) (bool, error) {
	return uc.repo.MultiDeleteWmsAuditPlanDetail(ctx, ids)
}

func (uc *WmsAuditPlanDetailUsecase) HandleReaderEvent(ctx context.Context, eventType event.EventType, payload event_manager.ReaderEventPayload) error {
	err := uc.repo.FoundRfidCode(ctx, payload.RfidCode)
	if err != nil {
		uc.log.Errorf("FoundRfidCode error: %v", err)
	}
	return nil
}

func (uc *WmsAuditPlanDetailUsecase) MultiUpdateWmsAuditPlanDetail(ctx context.Context, req *v1.MultiUpdateWmsAuditPlanDetailRequest) error {
	return uc.repo.MultiUpdateWmsAuditPlanDetail(ctx, req)
}

// 获取我的装备盘点计划详情列表
func (uc *WmsAuditPlanDetailUsecase) ListMyWmsAuditPlanDetail(ctx context.Context, req *v1.ListMyWmsAuditPlanDetailRequest) (*v1.ListWmsAuditPlanDetailResponse, error) {
	userId, ok := entutils.GetUserID(ctx)
	if !ok {
		return nil, entutils.ErrUserNotLogin
	}

	res, err := uc.repo.ListWmsAuditPlanDetailByUserId(ctx, userId, req.AuditPlanId, req.IsAudit)
	if err != nil {
		return nil, err
	}

	// 转换详细规格为字符串
	equipmentIds := arrayutil.Map(res.GetItems(), func(item *v1.WmsAuditPlanDetail) string {
		return item.GetEquipmentId()
	})

	features, err := uc.equipmentRepo.GetWmsEquipmentFeaturesByIds(ctx, equipmentIds)

	if err != nil {
		uc.log.Errorf("get equipment features error: %v", err)
	}

	for _, detail := range res.GetItems() {
		feature := detail.GetFeature()

		if feature != nil {
			feature_str := uc.equipmentRepo.ConvertFeatureToFeatureStr(feature.AsMap(), features)
			detail.FeatureStr = trans.String(feature_str)
		}
	}

	return res, nil
}

// 提交我得装备盘点计划详情
func (s *WmsAuditPlanDetailUsecase) PostMyWmsAuditPlanDetailState(ctx context.Context, req *v1.PostMyWmsAuditPlanDetailRequest) error {
	userId, ok := entutils.GetUserID(ctx)
	if !ok {
		return entutils.ErrUserNotLogin
	}

	return s.repo.UpdateWmsAuditPlanDetailByUserId(ctx, userId, req)
}
