package biz

import (
	"context"
	"errors"
	"strings"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	"kratos-mono-demo/internal/pkg/go-utils/arrayutil"
	entgo "kratos-mono-demo/internal/pkg/go-utils/entgo/query"
	"kratos-mono-demo/internal/pkg/go-utils/trans"
	jsonutils "kratos-mono-demo/internal/pkg/json-utils"

	systemBiz "kratos-mono-demo/internal/app/system/biz"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/structpb"
)

const (
	EQUIPMENT_DISCARD_METHOD_APPROVE       = 1 // 审核报废
	EQUIPMENT_DISCARD_METHOD_MEETING       = 2 // 上会报废
	EQUIPMENT_DISCARD_METHOD_OUTREPOSITORY = 3 // 出库及报废
)

type EquipmentInventory struct {
	ID             string
	RepositoryId   string
	InventoryCount int
}

type WmsEquipmentRepo interface {
	ListWmsEquipment(ctx context.Context, req *v1.ListWmsEquipmentRequest) (*v1.ListWmsEquipmentResponse, error)
	GetWmsEquipment(ctx context.Context, id string) (*v1.WmsEquipment, error)
	CreateWmsEquipment(ctx context.Context, req *v1.CreateWmsEquipmentRequest) (*v1.WmsEquipment, error)
	UpdateWmsEquipment(ctx context.Context, req *v1.UpdateWmsEquipmentRequest) (*v1.WmsEquipment, error)
	DeleteWmsEquipment(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsEquipment(ctx context.Context, ids []string) (bool, error)

	CreateEquipment(ctx context.Context, req *v1.CreateEquipmentRequest) (*v1.GetEquipmentResponse, error)
	UpdateEquipment(ctx context.Context, req *v1.UpdateEquipmentRequest) (*v1.GetEquipmentResponse, error)

	// 获取设备包含库存数量
	ListWmsEquipmentWithInventoryCount(ctx context.Context, req *v1.ListWmsEquipmentRequest, equipmentIds []string, userId string, organizationIds []string) (*v1.ListWmsEquipmentResponse, error)

	// 获取设备包含库存数量
	ListWmsEquipmentInventoryCount(ctx context.Context) ([]*EquipmentInventory, error)

	// 根据ids获取设备数据
	GetWmsEquipmentsByIds(ctx context.Context, ids []string) ([]*v1.WmsEquipment, error)

	// 获取设备库存通过仓库id
	GetWmsEquipmentInventoryByRepositoryId(ctx context.Context, repositoryId string) ([]*EquipmentInventory, error)

	// 获取装备详细规格
	GetWmsEquipmentFeaturesByIds(ctx context.Context, ids []string) ([]*v1.EquipmentFeature, error)

	// 将feature转换为字符串， 例如：颜色-红色,尺寸-大
	ConvertFeatureToFeatureStr(feature map[string]interface{}, equipment_features []*v1.EquipmentFeature) string

	// 比较feature是否相等
	CompareFeature(feature1 map[string]interface{}, feature2 map[string]interface{}) bool
}

type WmsEquipmentUsecase struct {
	log                       *log.Helper
	tm                        entutils.Transaction
	repo                      WmsEquipmentRepo
	equipmentTypeRepo         WmsEquipmentTypeRepo
	equipmentDetailRepo       WmsEquipmentDetailRepo
	userOrganizationRepo      systemBiz.SysUserOrganizationRepo
	organizationRepo          systemBiz.SysOrganizationRepo
	repositoryRepo            WmsRepositoryRepo
	sysPageCodeRepo           systemBiz.SysPageCodeRepo
	equipmentTypePropertyRepo WmsEquipmentTypePropertyRepo
	materialRepo              WmsMaterialRepo
	measureUnitRepo           WmsMeasureUnitRepo
	borrowOrderRepo           WmsBorrowOrderRepo
	borrowOrderDetailRepo     WmsBorrowOrderDetailRepo
	claimOrderRepo            WmsClaimOrderRepo
	claimOrderDetailRepo      WmsClaimOrderDetailRepo
	transferOrderRepo         WmsTransferOrderRepo
	transferOrderDetailRepo   WmsTransferOrderDetailRepo
	repositoryPositionRepo    WmsRepositoryPositionRepo
}

func NewWmsEquipmentUsecase(
	repo WmsEquipmentRepo,
	equipmentTypeRepo WmsEquipmentTypeRepo,
	equipmentDetailRepo WmsEquipmentDetailRepo,
	userOrganizationRepo systemBiz.SysUserOrganizationRepo,
	organizationRepo systemBiz.SysOrganizationRepo,
	tm entutils.Transaction,
	logger log.Logger,
	repositoryRepo WmsRepositoryRepo,
	sysPageCodeRepo systemBiz.SysPageCodeRepo,
	equipmentTypePropertyRepo WmsEquipmentTypePropertyRepo,
	materialRepo WmsMaterialRepo,
	measureUnitRepo WmsMeasureUnitRepo,
	borrowOrderRepo WmsBorrowOrderRepo,
	borrowOrderDetailRepo WmsBorrowOrderDetailRepo,
	claimOrderRepo WmsClaimOrderRepo,
	claimOrderDetailRepo WmsClaimOrderDetailRepo,
	transferOrderRepo WmsTransferOrderRepo,
	transferOrderDetailRepo WmsTransferOrderDetailRepo,
	repositoryPositionRepo WmsRepositoryPositionRepo,
) *WmsEquipmentUsecase {

	l := log.NewHelper(log.With(logger, "module", "wms_equipment/usecase"))
	return &WmsEquipmentUsecase{
		log:                       l,
		tm:                        tm,
		repo:                      repo,
		equipmentTypeRepo:         equipmentTypeRepo,
		equipmentDetailRepo:       equipmentDetailRepo,
		userOrganizationRepo:      userOrganizationRepo,
		organizationRepo:          organizationRepo,
		repositoryRepo:            repositoryRepo,
		sysPageCodeRepo:           sysPageCodeRepo,
		equipmentTypePropertyRepo: equipmentTypePropertyRepo,
		materialRepo:              materialRepo,
		measureUnitRepo:           measureUnitRepo,
		borrowOrderRepo:           borrowOrderRepo,
		borrowOrderDetailRepo:     borrowOrderDetailRepo,
		claimOrderRepo:            claimOrderRepo,
		claimOrderDetailRepo:      claimOrderDetailRepo,
		transferOrderRepo:         transferOrderRepo,
		transferOrderDetailRepo:   transferOrderDetailRepo,
		repositoryPositionRepo:    repositoryPositionRepo,
	}
}

func (uc *WmsEquipmentUsecase) ListWmsEquipment(ctx context.Context, req *v1.ListWmsEquipmentRequest) (*v1.ListWmsEquipmentResponse, error) {
	query_map := map[string]string{}

	query := entgo.RemoveQueryKeys(req.GetQuery(), []string{"equipmentTypeId"}, query_map)

	if query_map["equipmentTypeId"] != "" {
		equipment_ids, err := uc.equipmentTypeRepo.GetChildrenIds(ctx, []string{query_map["equipmentTypeId"]})

		if err != nil {
			return nil, err
		}

		equipment_id_str := ""
		if len(equipment_ids) != 0 {
			equipment_ids = arrayutil.Map(equipment_ids, func(item string) string {
				return `"` + item + `"`
			})

			equipment_id_str = strings.Join(equipment_ids, ",")
			equipment_id_str = "[" + equipment_id_str + "]"
		}

		query = entgo.AppendQueryKeys(query, map[string]string{
			"equipmentTypeId__in": equipment_id_str,
		})

		req.Query = &query
	}
	return uc.repo.ListWmsEquipment(ctx, req)
}

func (uc *WmsEquipmentUsecase) GetWmsEquipment(ctx context.Context, id string) (*v1.WmsEquipment, error) {
	return uc.repo.GetWmsEquipment(ctx, id)
}

func (uc *WmsEquipmentUsecase) CreateWmsEquipment(ctx context.Context, req *v1.CreateWmsEquipmentRequest) (*v1.WmsEquipment, error) {
	var equipment *v1.WmsEquipment
	var err error
	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		equipment, err = uc.repo.CreateWmsEquipment(ctx, req)
		return err
	})

	if err != nil {
		return nil, err
	}

	return equipment, err
}

func (uc *WmsEquipmentUsecase) UpdateWmsEquipment(ctx context.Context, req *v1.UpdateWmsEquipmentRequest) (*v1.WmsEquipment, error) {
	return uc.repo.UpdateWmsEquipment(ctx, req)
}

func (uc *WmsEquipmentUsecase) DeleteWmsEquipment(ctx context.Context, id string) (bool, error) {
	err := uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		_, err := uc.repo.DeleteWmsEquipment(ctx, id)
		return err
	})

	if err != nil {
		return false, err
	}

	return true, nil
}

func (uc *WmsEquipmentUsecase) MultiDeleteWmsEquipment(ctx context.Context, ids []string) (bool, error) {
	err := uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		_, err := uc.repo.MultiDeleteWmsEquipment(ctx, ids)
		return err
	})

	if err != nil {
		return false, err
	}

	return true, nil
}

func (uc *WmsEquipmentUsecase) GetEquipment(ctx context.Context, id string) (*v1.GetEquipmentResponse, error) {
	equipment, err := uc.repo.GetWmsEquipment(ctx, id)

	if err != nil {
		return nil, err
	}

	db_details := equipment.GetDetails()

	detail_map := uc.convertV1DetailsToDetailMap(db_details)
	detail, err := structpb.NewStruct(detail_map)

	if err != nil {
		return nil, err
	}

	return &v1.GetEquipmentResponse{
		Id:                   equipment.GetId(),
		CreatedAt:            trans.String(equipment.GetCreatedAt()),
		UpdatedAt:            trans.String(equipment.GetUpdatedAt()),
		CreatedBy:            trans.String(equipment.GetCreatedBy()),
		UpdatedBy:            trans.String(equipment.GetUpdatedBy()),
		Status:               trans.Int32(equipment.GetStatus()),
		IsNoCode:             equipment.IsNoCode,
		AuthType:             trans.String(equipment.GetAuthType()),
		AuthUserIds:          trans.String(equipment.GetAuthUserIds()),
		AuthOrganizationIds:  trans.String(equipment.GetAuthOrganizationIds()),
		Type:                 trans.String(equipment.GetType()),
		EquipmentTypeId:      equipment.GetEquipmentTypeId(),
		Name:                 equipment.GetName(),
		ModelNo:              equipment.GetModelNo(),
		Weight:               trans.String(equipment.GetWeight()),
		ProviderName:         equipment.GetProviderName(),
		ProviderCountry:      equipment.GetProviderCountry(),
		ProviderArea:         equipment.GetProviderArea(),
		ProviderCity:         equipment.GetProviderCity(),
		SellerName:           equipment.GetSellerName(),
		Images:               equipment.GetImages(),
		Remark:               equipment.GetRemark(),
		Code:                 equipment.GetCode(),
		Detail:               detail,
		IsOneMaterialOneCode: equipment.GetIsOneMaterialOneCode(),
		DiscardMethod:        equipment.GetDiscardMethod(),
	}, nil
}

// 创建设备
func (uc *WmsEquipmentUsecase) CreateEquipment(ctx context.Context, req *v1.CreateEquipmentRequest) (*v1.GetEquipmentResponse, error) {
	if req.GetEquipmentTypeId() == "" {
		return nil, errors.New("equipment type id is required")
	}

	db_equipmentType, err := uc.equipmentTypeRepo.GetWmsEquipmentType(ctx, req.GetEquipmentTypeId())

	if err != nil {
		return nil, err
	}

	if db_equipmentType == nil {
		return nil, errors.New("equipment type not found")
	}

	var equipment *v1.GetEquipmentResponse

	uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		// 创建设备
		equipment, err = uc.repo.CreateEquipment(ctx, req)

		if err != nil {
			return err
		}

		var details []*v1.WmsEquipmentDetail

		// 创建设备详情
		detail := req.GetDetail()

		if detail != nil {
			// 获取设备类型的属性，用于校验
			db_properties := db_equipmentType.GetProperties()

			if db_properties == nil {
				db_properties = make([]*v1.WmsEquipmentTypeProperty, 0)
			}
			create_details := make([]*v1.CreateWmsEquipmentDetailRequest, 0)

			for key, value := range detail.GetFields() {
				// 验证属性是否存在，不存在则跳过
				find_property := arrayutil.Find(db_properties, func(item *v1.WmsEquipmentTypeProperty) bool {
					return item.GetId() == key
				})

				if find_property == nil {
					continue
				}

				create_details = append(create_details, &v1.CreateWmsEquipmentDetailRequest{
					EquipmentId:                  trans.String(equipment.GetId()),
					EquipmentTypeId:              trans.String(equipment.GetEquipmentTypeId()),
					EquipmentTypePropertyId:      trans.String(key),
					EquipmentTypePropertyGroupId: find_property.GroupId,
					Value:                        trans.String(value.GetStringValue()),
				})
			}

			details, err = uc.equipmentDetailRepo.MutiCreateEquipmentDetail(ctx, create_details)

			if err != nil {
				return err
			}
		}

		detail_map := uc.convertV1DetailsToDetailMap(details)
		detail, err = structpb.NewStruct(detail_map)

		if err != nil {
			return err
		}

		equipment.Detail = detail

		return nil
	})

	if err != nil {
		return nil, err
	}

	return equipment, nil
}

// 更新设备
func (uc *WmsEquipmentUsecase) UpdateEquipment(ctx context.Context, req *v1.UpdateEquipmentRequest) (*v1.GetEquipmentResponse, error) {
	if req.GetId() == "" {
		return nil, errors.New("equipment id is required")
	}

	db_equipment, err := uc.repo.GetWmsEquipment(ctx, req.GetId())

	if err != nil {
		return nil, err
	}

	if db_equipment == nil {
		return nil, errors.New("equipment not found")
	}

	db_equipmentType, err := uc.equipmentTypeRepo.GetWmsEquipmentType(ctx, req.GetEquipmentTypeId())

	if err != nil {
		return nil, err
	}

	if db_equipmentType == nil {
		return nil, errors.New("equipment type not found")
	}

	var equipment *v1.GetEquipmentResponse

	uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		// 更新设备
		equipment, err = uc.repo.UpdateEquipment(ctx, req)

		if err != nil {
			return err
		}

		var details []*v1.WmsEquipmentDetail

		// 若详情没有更新，默认为数据库的详情
		details = db_equipment.GetDetails()

		detail := req.GetDetail()

		if detail != nil {
			// 获取设备类型的属性，用于校验
			db_properties := db_equipmentType.GetProperties()

			if db_properties == nil {
				db_properties = make([]*v1.WmsEquipmentTypeProperty, 0)
			}

			// 删除设备详情
			_, err := uc.equipmentDetailRepo.DeleteByEquipmentId(ctx, equipment.GetId())

			if err != nil {
				return err
			}

			// 创建设备详情
			create_details := make([]*v1.CreateWmsEquipmentDetailRequest, 0)

			for key, value := range detail.GetFields() {
				// 验证属性是否存在，不存在则跳过
				find_property := arrayutil.Find(db_properties, func(item *v1.WmsEquipmentTypeProperty) bool {
					return item.GetId() == key
				})

				if find_property == nil {
					continue
				}

				create_details = append(create_details, &v1.CreateWmsEquipmentDetailRequest{
					EquipmentId:                  trans.String(equipment.GetId()),
					EquipmentTypeId:              trans.String(equipment.GetEquipmentTypeId()),
					EquipmentTypePropertyId:      trans.String(key),
					EquipmentTypePropertyGroupId: find_property.GroupId,
					Value:                        trans.String(value.GetStringValue()),
				})
			}

			details, err = uc.equipmentDetailRepo.MutiCreateEquipmentDetail(ctx, create_details)

			if err != nil {
				return err
			}
		}

		detail_map := uc.convertV1DetailsToDetailMap(details)
		detail, err = structpb.NewStruct(detail_map)

		if err != nil {
			return err
		}

		equipment.Detail = detail

		return nil

	})

	if err != nil {
		return nil, err
	}

	return equipment, nil
}

// 保存设备
func (uc *WmsEquipmentUsecase) SaveEquipment(ctx context.Context, req *v1.UpdateEquipmentRequest) (*v1.GetEquipmentResponse, error) {
	if req.GetId() == "" {
		return uc.CreateEquipment(ctx, &v1.CreateEquipmentRequest{
			Status:               req.Status,
			IsNoCode:             req.IsNoCode,
			AuthType:             req.AuthType,
			AuthUserIds:          req.AuthUserIds,
			AuthOrganizationIds:  req.AuthOrganizationIds,
			Type:                 req.Type,
			EquipmentTypeId:      req.EquipmentTypeId,
			Name:                 req.Name,
			ModelNo:              req.ModelNo,
			Weight:               req.Weight,
			ProviderName:         req.ProviderName,
			ProviderCountry:      req.ProviderCountry,
			Images:               req.Images,
			Remark:               req.Remark,
			Code:                 req.Code,
			Detail:               req.Detail,
			IsOneMaterialOneCode: req.IsOneMaterialOneCode,
			DiscardMethod:        req.DiscardMethod,
		})
	} else {
		return uc.UpdateEquipment(ctx, req)
	}
}

// 将详情转换为map
func (uc *WmsEquipmentUsecase) convertV1DetailsToDetailMap(details []*v1.WmsEquipmentDetail) map[string]interface{} {
	detail_map := make(map[string]interface{})

	for _, detail := range details {
		key := detail.GetEquipmentTypePropertyId()
		value := detail.GetValue()

		detail_map[key] = value
	}

	return detail_map
}

// 获取我能看的设备
func (uc *WmsEquipmentUsecase) ListMyWmsEquipment(ctx context.Context, req *v1.ListWmsEquipmentRequest) (*v1.ListWmsEquipmentResponse, error) {
	query_map := map[string]string{}

	query := entgo.RemoveQueryKeysWithPrefix(req.GetQuery(), []string{"repository_id"}, query_map)
	req.Query = &query

	userId, ok := entutils.GetUserID(ctx)
	if !ok {
		return nil, entutils.ErrUserNotLogin
	}

	// 获取用户的组织
	organizationIds, err := uc.userOrganizationRepo.GetOrganizationIdsByUserId(ctx, userId)
	if err != nil {
		return nil, err
	}

	// 获取组织的子组织
	organizationIds, err = uc.organizationRepo.GetChildrenIds(ctx, organizationIds)
	if err != nil {
		return nil, err
	}

	var equipmentInventory []*EquipmentInventory

	// 获取设备库存
	if query_map["repository_id"] == "" {
		equipmentInventory, err = uc.repo.ListWmsEquipmentInventoryCount(ctx)
		if err != nil {
			return nil, err
		}
	} else {
		equipmentInventory, err = uc.repo.GetWmsEquipmentInventoryByRepositoryId(ctx, query_map["repository_id"])
		if err != nil {
			return nil, err
		}
	}

	equipmentIds := make([]string, 0)
	repositoryIds := make([]string, 0)

	for _, item := range equipmentInventory {
		equipmentIds = append(equipmentIds, item.ID)
		repositoryIds = append(repositoryIds, item.RepositoryId)
	}

	// 只获取有库存的设备，并且需要过滤没有权限的装备
	response, err := uc.repo.ListWmsEquipmentWithInventoryCount(ctx, req, equipmentIds, userId, organizationIds)
	if err != nil {
		return nil, err
	}

	response_items := response.GetItems()

	if response_items == nil {
		response_items = make([]*v1.WmsEquipment, 0)
	}

	// 获取仓库数据
	repositories, err := uc.repositoryRepo.GetRepositoriesByIds(ctx, repositoryIds)
	if err != nil {
		return nil, err
	}

	//构造返回数据
	var res_items []*v1.WmsEquipment

	for _, response_item := range response_items {
		equipment_inventory_item := arrayutil.Find(equipmentInventory, func(item *EquipmentInventory) bool {
			return item.ID == response_item.GetId()
		})

		if equipment_inventory_item == nil {
			continue
		}

		response_item.InventoryCount = trans.Int32(int32(equipment_inventory_item.InventoryCount))

		repository := arrayutil.Find(repositories, func(item *v1.WmsRepository) bool {
			return item.GetId() == equipment_inventory_item.RepositoryId
		})

		if repository == nil {
			continue
		}

		response_item.RepositoryId = trans.String(equipment_inventory_item.RepositoryId)
		response_item.RepositoryName = repository.Name

		res_items = append(res_items, response_item)
	}

	// 去除正在走的流程的装备数量, 锁定库存防止没有库存的装备被申请
	unfinished_order_equipment_num_map := uc.getUnfinishedOrderEquipmentNumMap(ctx)

	for _, item := range res_items {
		item.InventoryCount = trans.Int32(int32(item.GetInventoryCount()) - unfinished_order_equipment_num_map[item.GetId()])

		if item.GetInventoryCount() < 0 {
			item.InventoryCount = trans.Int32(0)
		}
	}

	// for _, equipment_inventory_item := range equipmentInventory {
	// 	response_item := arrayutil.Find(response_items, func(item *v1.WmsEquipment) bool {
	// 		return item.GetId() == equipment_inventory_item.ID
	// 	})

	// 	if response_item == nil {
	// 		continue
	// 	}

	// 	response_item.InventoryCount = trans.Int32(int32(equipment_inventory_item.InventoryCount))

	// 	repository := arrayutil.Find(repositories, func(item *v1.WmsRepository) bool {
	// 		return item.GetId() == equipment_inventory_item.RepositoryId
	// 	})

	// 	if repository == nil {
	// 		continue
	// 	}

	// 	response_item.RepositoryId = trans.String(equipment_inventory_item.RepositoryId)
	// 	response_item.RepositoryName = repository.Name

	// 	res_items = append(res_items, response_item)
	// }

	// 获取计量单位数据
	measure_unit_ids := arrayutil.Map(res_items, func(item *v1.WmsEquipment) string {
		if item.EquipmentType == nil {
			return ""
		}

		return item.EquipmentType.GetMeasureUnit()
	})

	measure_unit_ids = arrayutil.Filter(measure_unit_ids, func(item string) bool {
		return item != ""
	})

	measure_units, err := uc.measureUnitRepo.GetMeasureUnitsByIds(ctx, measure_unit_ids)

	if err != nil {
		return nil, err
	}

	for _, item := range res_items {
		if item.EquipmentType == nil {
			continue
		}

		measure_unit := arrayutil.Find(measure_units, func(unit *v1.WmsMeasureUnit) bool {
			return unit.GetId() == item.EquipmentType.GetMeasureUnit()
		})

		if measure_unit != nil {
			item.EquipmentType.MeasureUnitName = measure_unit.Name
		}
	}

	response.Items = res_items

	return response, nil
}

// 根据ids获取设备数据
func (uc *WmsEquipmentUsecase) GetWmsEquipmentsByIds(ctx context.Context, ids []string) ([]*v1.WmsEquipment, error) {
	return uc.repo.GetWmsEquipmentsByIds(ctx, ids)
}

// 获取页面代码
func (uc *WmsEquipmentUsecase) GetEquipmentPagecode(ctx context.Context, req *v1.GetEquipmentPagecodeRequest) (*structpb.Struct, error) {
	code, err := uc.sysPageCodeRepo.GetSysPageCode(ctx, req.GetName())
	if err != nil {
		return nil, err
	}

	code_map := code.AsMap()

	details, err := uc.equipmentDetailRepo.GetEquipmentDetailsWithFeature(ctx, req.GetId())

	if err != nil {
		return nil, err
	}

	property_ids := arrayutil.Map(details, func(item *v1.WmsEquipmentDetail) string {
		return item.GetEquipmentTypePropertyId()
	})

	properties, err := uc.equipmentTypePropertyRepo.GetEquipmentTypePropertiesByIds(ctx, property_ids)

	body := make([]interface{}, 0)

	for _, detail := range details {
		find_property := arrayutil.Find(properties, func(item *v1.WmsEquipmentTypeProperty) bool {
			return item.GetId() == detail.GetEquipmentTypePropertyId()
		})

		if find_property == nil {
			continue
		}

		equipment_type_name := find_property.Name
		equipment_type_id := find_property.Id

		body_item := make(map[string]interface{})

		body_item["id"] = "feature." + equipment_type_id
		body_item["name"] = "feature." + equipment_type_id
		body_item["type"] = "radios"
		body_item["label"] = equipment_type_name

		body_item_options := make([]interface{}, 0)

		detail_value := detail.GetValue()

		if detail_value == "" {
			continue
		}

		detail_values := strings.Split(detail_value, ",")

		options := find_property.GetOptions()

		for _, value := range detail_values {
			var body_item_option interface{}
			body_item_option_map := make(map[string]interface{})

			find_option := arrayutil.Find(options, func(item *v1.WmsEquipmentTypePropertyOption) bool {
				return item.GetCode() == value
			})

			if find_option == nil {
				continue
			}

			body_item_option_map["label"] = find_option.GetLabel()
			body_item_option_map["value"] = find_option.GetCode()

			body_item_option = body_item_option_map

			body_item_options = append(body_item_options, body_item_option)

		}
		body_item["options"] = body_item_options

		body_item["selectFirst"] = true

		body = append(body, body_item)
	}

	_, err = jsonutils.Set(&code_map, "$.body", body)

	uc.log.Infof("code_map: %v", code_map)

	if err != nil {
		return nil, err
	}

	code, err = structpb.NewStruct(code_map)

	if err != nil {
		return nil, err
	}

	return code, nil
}

// 获取装备详细规格数据
func (uc *WmsEquipmentUsecase) GetEquipmentFeatures(ctx context.Context, req *v1.GetEquipmentFeaturesRequest) (*v1.GetEquipmentFeaturesResponse, error) {
	features, err := uc.repo.GetWmsEquipmentFeaturesByIds(ctx, []string{req.GetId()})

	if err != nil {
		return nil, err
	}

	// 过滤掉条件筛选的详细规格
	if req.Feature != nil {
		feature_map := req.GetFeature().AsMap()

		features = arrayutil.Filter(features, func(item *v1.EquipmentFeature) bool {
			find := false

			for key, _ := range feature_map {
				if item.Id == key {
					find = true
					break
				}
			}

			return !find
		})
	}

	features, err = uc.materialRepo.GetFeatureCountByEquipmentIdAndRepositoryIdAndFeatures(ctx, req.GetId(), req.RepositoryId, req.Feature, features)

	if err != nil {
		return nil, err
	}

	return &v1.GetEquipmentFeaturesResponse{
		Items: features,
	}, nil
}

// 获取当前详细规格数
func (uc *WmsEquipmentUsecase) GetEquipmentFeatureCount(ctx context.Context, req *v1.GetEquipmentFeatureCountRequest) (*v1.GetEquipmentFeatureCountResponse, error) {
	features, err := uc.repo.GetWmsEquipmentFeaturesByIds(ctx, []string{req.GetId()})

	if err != nil {
		return nil, err
	}

	// 过滤掉条件筛选的详细规格
	if req.Feature != nil {
		feature_map := req.GetFeature().AsMap()

		features = arrayutil.Filter(features, func(item *v1.EquipmentFeature) bool {
			find := false

			for key, value := range feature_map {
				if item.Id == key && item.Value == value {
					find = true
					break
				}
			}

			return find
		})
	}

	features, err = uc.materialRepo.GetFeatureCountByEquipmentIdAndRepositoryIdAndFeatures(ctx, req.GetId(), req.RepositoryId, req.Feature, features)

	if err != nil {
		return nil, err
	}

	if len(features) == 0 {
		return &v1.GetEquipmentFeatureCountResponse{
			Count: 0,
		}, nil
	}

	return &v1.GetEquipmentFeatureCountResponse{
		Count: features[0].GetInventoryCount(),
	}, nil
}

// 获取未完成的订单装备数量
func (uc *WmsEquipmentUsecase) getUnfinishedOrderEquipmentNumMap(ctx context.Context) map[string]int32 {
	unfinished_order_equipment_num_map := make(map[string]int32)

	// 借用
	borrow_orders, err := uc.borrowOrderRepo.GetWmsBorrowOrdersByStatus(ctx, []string{
		BORROW_ORDER_STATUS_PROCESSING,
		BORROW_ORDER_STATUS_APPROVED,
	})

	if err != nil {
		return unfinished_order_equipment_num_map
	}

	borrow_order_ids := arrayutil.Map(borrow_orders, func(item *v1.WmsBorrowOrder) string {
		return item.GetId()
	})

	borrow_order_details, err := uc.borrowOrderDetailRepo.GetWmsBorrowOrderDetailsByOrderIds(ctx, borrow_order_ids)

	if err != nil {
		return unfinished_order_equipment_num_map
	}

	for _, borrow_order_detail := range borrow_order_details {
		unfinished_order_equipment_num_map[borrow_order_detail.GetEquipmentId()] += int32(borrow_order_detail.GetNum())
	}

	// 申领
	claim_orders, err := uc.claimOrderRepo.GetWmsClaimOrdersByStatus(ctx, []string{
		CLAIM_ORDER_STATUS_PROCESSING,
		CLAIM_ORDER_STATUS_APPROVED,
	})

	if err != nil {
		return unfinished_order_equipment_num_map
	}

	claim_order_ids := arrayutil.Map(claim_orders, func(item *v1.WmsClaimOrder) string {
		return item.GetId()
	})

	claim_order_details, err := uc.claimOrderDetailRepo.GetWmsClaimOrderDetailsByOrderIds(ctx, claim_order_ids)

	if err != nil {
		return unfinished_order_equipment_num_map
	}

	for _, claim_order_detail := range claim_order_details {
		unfinished_order_equipment_num_map[claim_order_detail.GetEquipmentId()] += int32(claim_order_detail.GetNum())
	}

	// 调拨
	transfer_orders, err := uc.transferOrderRepo.GetWmsTransferOrdersByStatus(ctx, []string{
		TRANSFER_ORDER_STATUS_PROCESSING,
		TRANSFER_ORDER_STATUS_APPROVED,
		TRANSFER_ORDER_STATUS_OUT_REPOSITORY,
		TRANSFER_ORDER_STATUS_ENTER_REPOSITORY,
	})

	if err != nil {
		return unfinished_order_equipment_num_map
	}

	transfer_order_ids := arrayutil.Map(transfer_orders, func(item *v1.WmsTransferOrder) string {
		return item.GetId()
	})

	transfer_order_details, err := uc.transferOrderDetailRepo.GetWmsTransferOrderDetailsByOrderIds(ctx, transfer_order_ids)

	if err != nil {
		return unfinished_order_equipment_num_map
	}

	for _, transfer_order_detail := range transfer_order_details {
		unfinished_order_equipment_num_map[transfer_order_detail.GetEquipmentId()] += int32(transfer_order_detail.GetNum())
	}

	return unfinished_order_equipment_num_map
}

// 获取装备的库位列表
func (uc *WmsEquipmentUsecase) GetEquipmentRepositoryPositionList(ctx context.Context, req *v1.GetEquipmentRepositoryPositionListRequest) (*v1.GetEquipmentRepositoryPositionListResponse, error) {
	materials, err := uc.materialRepo.GetMaterialsByCondition(ctx, req.GetEquipmentId(), req.GetRepositoryId(), req.GetFeature(), req.GetCode())

	if err != nil {
		return nil, err
	}

	repository_position_ids := arrayutil.Map(materials, func(item *v1.WmsMaterial) string {
		return item.GetRepositoryPositionId()
	})

	repository_positions, err := uc.repositoryPositionRepo.GetRepositoryPositionsByIds(ctx, repository_position_ids)
	if err != nil {
		return nil, err
	}

	res := arrayutil.Map(repository_positions, func(item *v1.WmsRepositoryPosition) *v1.EquipmentRepositoryPosition {
		return &v1.EquipmentRepositoryPosition{
			Id:   item.GetId(),
			Name: item.GetName(),
		}
	})

	return &v1.GetEquipmentRepositoryPositionListResponse{
		Items: res,
	}, nil
}
