package biz

import (
	"context"
	"fmt"
	"time"

	v1 "kratos-mono-demo/gen/api/wms/v1"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	"kratos-mono-demo/internal/pkg/go-utils/arrayutil"
	"kratos-mono-demo/internal/pkg/go-utils/trans"

	"github.com/go-kratos/kratos/v2/log"
)

const (
	CLAIM_ORDER_STATUS_CANCELLED  = "-1"  // 已取消
	CLAIM_ORDER_STATUS_REJECTED   = "-2"  // 已驳回
	CLAIM_ORDER_STATUS_PENDING    = "0"   // 待提交
	CLAIM_ORDER_STATUS_PROCESSING = "1"   // 审批中
	CLAIM_ORDER_STATUS_APPROVED   = "2"   // 已审批
	CLAIM_ORDER_STATUS_COMPLETED  = "100" // 已完成
)

type WmsClaimOrderRepo interface {
	ListWmsClaimOrder(ctx context.Context, req *v1.ListWmsClaimOrderRequest) (*v1.ListWmsClaimOrderResponse, error)
	GetWmsClaimOrder(ctx context.Context, id string) (*v1.WmsClaimOrder, error)
	CreateWmsClaimOrder(ctx context.Context, req *v1.CreateWmsClaimOrderRequest) (*v1.WmsClaimOrder, error)
	UpdateWmsClaimOrder(ctx context.Context, req *v1.UpdateWmsClaimOrderRequest) (*v1.WmsClaimOrder, error)
	DeleteWmsClaimOrder(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsClaimOrder(ctx context.Context, ids []string) (bool, error)

	// 获取领用单申请用户的组织名称
	GetWmsClaimOrderUserOrganizationNameMap(ctx context.Context, ids []string) (map[string]string, error)

	// 根据领用单ID查询领用单
	GetWmsClaimOrdersByIds(ctx context.Context, ids []string) ([]*v1.WmsClaimOrder, error)

	// 根据关联单号查询申领单
	GetWmsClaimOrderByOrderNo(ctx context.Context, orderNo string) (*v1.WmsClaimOrder, error)

	// 根据状态获取领用单
	GetWmsClaimOrdersByStatus(ctx context.Context, status []string) ([]*v1.WmsClaimOrder, error)
}

type WmsClaimOrderUsecase struct {
	log                          *log.Helper
	repo                         WmsClaimOrderRepo
	detailRepo                   WmsClaimOrderDetailRepo
	tm                           entutils.Transaction
	documentRepo                 WmsDocumentRepo
	logRepo                      WmsOperateLogRepo
	equipmentRepo                WmsEquipmentRepo
	outRepositoryOrderRepo       WmsOutRepositoryOrderRepo
	outRepositoryOrderDetailRepo WmsOutRepositoryOrderDetailRepo
}

func NewWmsClaimOrderUsecase(
	repo WmsClaimOrderRepo,
	detailRepo WmsClaimOrderDetailRepo,
	tm entutils.Transaction,
	logger log.Logger,
	documentRepo WmsDocumentRepo,
	logRepo WmsOperateLogRepo,
	equipmentRepo WmsEquipmentRepo,
	outRepositoryOrderRepo WmsOutRepositoryOrderRepo,
	outRepositoryOrderDetailRepo WmsOutRepositoryOrderDetailRepo,
) *WmsClaimOrderUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_claim_order/usecase"))
	return &WmsClaimOrderUsecase{
		log:                          l,
		repo:                         repo,
		detailRepo:                   detailRepo,
		tm:                           tm,
		documentRepo:                 documentRepo,
		logRepo:                      logRepo,
		equipmentRepo:                equipmentRepo,
		outRepositoryOrderRepo:       outRepositoryOrderRepo,
		outRepositoryOrderDetailRepo: outRepositoryOrderDetailRepo,
	}
}

func (uc *WmsClaimOrderUsecase) ListWmsClaimOrder(ctx context.Context, req *v1.ListWmsClaimOrderRequest) (*v1.ListWmsClaimOrderResponse, error) {

	// // 自定义加入查询条件
	// newQuery := entgo.AppendQueryKeys(req.GetQuery(), map[string]string{
	// 	"createdBy": userId,
	// })
	// req.Query = &newQuery

	res, err := uc.repo.ListWmsClaimOrder(ctx, req)
	if err != nil {
		return nil, err
	}

	uc.setUserInfo(ctx, res.Items) // 设置领用单用户信息
	uc.setDetails(ctx, res.Items)  // 设置领用单明细

	return res, nil
}

func (uc *WmsClaimOrderUsecase) GetWmsClaimOrder(ctx context.Context, id string) (*v1.WmsClaimOrder, error) {
	res, err := uc.repo.GetWmsClaimOrder(ctx, id)
	if err != nil {
		return nil, err
	}

	uc.setDetails(ctx, []*v1.WmsClaimOrder{res})  // 设置领用单明细
	uc.setUserInfo(ctx, []*v1.WmsClaimOrder{res}) // 设置领用单用户信息

	return res, nil
}

func (uc *WmsClaimOrderUsecase) CreateWmsClaimOrder(ctx context.Context, req *v1.CreateWmsClaimOrderRequest) (*v1.WmsClaimOrder, error) {
	var err error
	var order *v1.WmsClaimOrder

	userId, ok := entutils.GetUserID(ctx)
	if !ok {
		return nil, entutils.ErrUserNotLogin
	}

	req.ClaimUserId = &userId // 设置申领单申请人ID

	// 开启事务
	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		order, err = uc.repo.CreateWmsClaimOrder(ctx, req)
		if err != nil {
			return err
		}

		// 创建领用单明细
		if len(req.Details) > 0 {
			for _, detail := range req.Details {
				detail.OrderId = &order.Id
				detailRes, err := uc.detailRepo.CreateWmsClaimOrderDetail(ctx, detail)
				if err != nil {
					return err
				}

				order.Details = append(order.Details, detailRes)
			}
		}

		// 创建操作日志
		log := fmt.Sprintf("创建领用单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_CLAIM_ORDER, log)
		if err != nil {
			return err
		}

		// 创建系统文档
		source := fmt.Sprintf("领用单: %s", trans.StringValue(order.OrderNo))
		err = uc.documentRepo.BatchCreateUpdateSystemDocuments(ctx, source, DOCUMENT_CLAIM_ORDER_CATEGEORY, order.Id, req)
		if err != nil {
			return err
		}

		// 如果审批通过，处理领用
		if order.GetStatus() == CLAIM_ORDER_STATUS_APPROVED {
			// 创建出库单
			err = uc.handleClaim(ctx, order)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return order, nil
}

func (uc *WmsClaimOrderUsecase) UpdateWmsClaimOrder(ctx context.Context, req *v1.UpdateWmsClaimOrderRequest) (*v1.WmsClaimOrder, error) {
	var err error
	var order *v1.WmsClaimOrder

	// 开启事务
	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		order, err = uc.repo.UpdateWmsClaimOrder(ctx, req)
		if err != nil {
			return err
		}

		// 更新领用单明细
		if len(req.Details) > 0 {
			// 删除领用单明细
			_, err = uc.detailRepo.DeleteWmsClaimOrderDetailByOrderId(ctx, order.Id)
			if err != nil {
				return err
			}

			// 创建领用单明细
			for _, detail := range req.Details {
				detail.OrderId = &order.Id
				detailRes, err := uc.detailRepo.CreateWmsClaimOrderDetail(ctx, detail)
				if err != nil {
					return err
				}

				order.Details = append(order.Details, detailRes)
			}
		}

		// 创建操作日志
		log := fmt.Sprintf("更新领用单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_CLAIM_ORDER, log)
		if err != nil {
			return err
		}

		// 更新系统文档
		source := fmt.Sprintf("领用单: %s", trans.StringValue(order.OrderNo))
		err = uc.documentRepo.BatchCreateUpdateSystemDocuments(ctx, source, DOCUMENT_CLAIM_ORDER_CATEGEORY, order.Id, req)
		if err != nil {
			return err
		}

		// 如果审批通过，处理领用
		if order.GetStatus() == CLAIM_ORDER_STATUS_APPROVED {
			// 处理领用
			err = uc.handleClaim(ctx, order)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return order, nil
}

func (uc *WmsClaimOrderUsecase) DeleteWmsClaimOrder(ctx context.Context, id string) (bool, error) {
	var err error

	order, err := uc.repo.GetWmsClaimOrder(ctx, id)
	if err != nil {
		return false, err
	}

	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		_, err = uc.repo.DeleteWmsClaimOrder(ctx, id)
		if err != nil {
			return err
		}

		// 删除领用单明细
		_, err = uc.detailRepo.DeleteWmsClaimOrderDetailByOrderId(ctx, id)
		if err != nil {
			return err
		}

		// 创建操作日志
		log := fmt.Sprintf("删除领用单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, id, LOG_CATEGEORY_CLAIM_ORDER, log)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return false, err
	}

	return true, nil
}

func (uc *WmsClaimOrderUsecase) MultiDeleteWmsClaimOrder(ctx context.Context, ids []string) (bool, error) {
	var err error

	orders, err := uc.repo.GetWmsClaimOrdersByIds(ctx, ids)
	if err != nil {
		return false, err
	}

	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		_, err = uc.repo.MultiDeleteWmsClaimOrder(ctx, ids)
		if err != nil {
			return err
		}

		// 删除领用单明细
		for _, id := range ids {
			_, err = uc.detailRepo.DeleteWmsClaimOrderDetailByOrderId(ctx, id)
			if err != nil {
				return err
			}
		}

		// 创建操作日志
		for _, order := range orders {
			log := fmt.Sprintf("删除领用单 %s", trans.StringValue(order.OrderNo))
			_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_CLAIM_ORDER, log)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return false, err
	}

	return true, nil
}

// 设置领用单明细
func (uc *WmsClaimOrderUsecase) setDetails(ctx context.Context, orders []*v1.WmsClaimOrder) {
	orderIds := make([]string, 0)
	for _, order := range orders {
		orderIds = append(orderIds, order.Id)
	}

	details, err := uc.detailRepo.GetWmsClaimOrderDetailsByOrderIds(ctx, orderIds)
	if err != nil {
		return
	}

	// 设置借用单明细详细规格
	uc.setDetailsFeatureStr(ctx, details)

	detailsMap := make(map[string][]*v1.WmsClaimOrderDetail)
	for _, detail := range details {
		detailsMap[detail.GetOrderId()] = append(detailsMap[detail.GetOrderId()], detail)
	}

	for _, order := range orders {
		order.Details = detailsMap[order.Id]
	}
}

// 设置领用单用户信息
func (uc *WmsClaimOrderUsecase) setUserInfo(ctx context.Context, orders []*v1.WmsClaimOrder) {
	ids := arrayutil.Map(orders, func(order *v1.WmsClaimOrder) string {
		return order.Id
	})

	// 获取领用单申请用户的组织名称
	organization_name_map, err := uc.repo.GetWmsClaimOrderUserOrganizationNameMap(ctx, ids)
	if err != nil {
		return
	}

	for _, order := range orders {
		order.CreateByOrganizationName = trans.String(organization_name_map[order.Id])
	}
}

// 设置借用单明细详细规格
func (uc *WmsClaimOrderUsecase) setDetailsFeatureStr(ctx context.Context, details []*v1.WmsClaimOrderDetail) {
	equipmentIds := arrayutil.Map(details, func(item *v1.WmsClaimOrderDetail) string {
		return item.GetEquipmentId()
	})

	features, err := uc.equipmentRepo.GetWmsEquipmentFeaturesByIds(ctx, equipmentIds)

	if err != nil {
		uc.log.Errorf("get equipment features error: %v", err)
	}

	for _, detail := range details {
		feature := detail.GetFeature()

		if feature != nil {
			feature_str := uc.equipmentRepo.ConvertFeatureToFeatureStr(feature.AsMap(), features)
			detail.FeatureStr = trans.String(feature_str)
		}
	}
}

// 处理领用
func (uc *WmsClaimOrderUsecase) handleClaim(ctx context.Context, order *v1.WmsClaimOrder) error {
	details, err := uc.detailRepo.GetWmsClaimOrderDetailsByOrderIds(ctx, []string{order.Id})

	if err != nil {
		return err
	}

	equipmentIds := arrayutil.Map(details, func(detail *v1.WmsClaimOrderDetail) string {
		return detail.GetEquipmentId()
	})

	// 获取装备列表
	equipments, err := uc.equipmentRepo.GetWmsEquipmentsByIds(ctx, equipmentIds)
	if err != nil {
		uc.log.Errorf("get wms equipments failed: %s", err.Error())
		return err
	}

	// 创建出库单
	outRepositoryOrders := make([]*v1.CreateWmsOutRepositoryOrderRequest, 0)
	outRepositoryOrderDetailsMap := make(map[string][]*v1.CreateWmsOutRepositoryOrderDetailRequest)

	for _, detail := range details {
		findOutRepositoryOrder := arrayutil.Find(outRepositoryOrders, func(outRepositoryOrder *v1.CreateWmsOutRepositoryOrderRequest) bool {
			if detail.GetRepositoryId() == "" {
				detail.RepositoryId = trans.String("8fe84ed6-c5b0-4ded-8a89-6413ef007055") // 默认仓库
			}

			return outRepositoryOrder.GetRepositoryId() == detail.GetRepositoryId()
		})

		if findOutRepositoryOrder == nil {
			outRepositoryOrder := &v1.CreateWmsOutRepositoryOrderRequest{
				Type:            trans.String(OUTREPOSITORYTYPECLAIMED),
				RepositoryId:    detail.RepositoryId,
				RelationOrderNo: order.OrderNo,
				OutTime:         trans.TimeToStrPtr(time.Now()),
				Status:          trans.String(OUTREPOSITORYSTATUSPENDING), // OUTREPOSITORYSTATUSPENDING 待出库
			}

			outRepositoryOrders = append(outRepositoryOrders, outRepositoryOrder)

			findOutRepositoryOrder = outRepositoryOrder
		}

		findEquipment := arrayutil.Find(equipments, func(equipment *v1.WmsEquipment) bool {
			return equipment.Id == detail.GetEquipmentId()
		})

		if findEquipment == nil {
			continue
		}

		// 如果是一物一码， 则需要根据数量创建对应数量的出库明细
		if findEquipment.IsOneMaterialOneCode {
			for i := 0; i < int(detail.GetNum()); i++ {
				req := &v1.CreateWmsOutRepositoryOrderDetailRequest{
					EquipmentId:     &findEquipment.Id,
					EquipmentTypeId: &findEquipment.EquipmentTypeId,
					Num:             trans.Uint32(1),
					RepositoryId:    detail.RepositoryId,
					Name:            &findEquipment.Name,
					ModelNo:         &findEquipment.ModelNo,
					Feature:         detail.Feature,
				}

				outRepositoryOrderDetailsMap[findOutRepositoryOrder.GetRepositoryId()] = append(outRepositoryOrderDetailsMap[findOutRepositoryOrder.GetRepositoryId()], req)
			}
		} else {
			req := &v1.CreateWmsOutRepositoryOrderDetailRequest{
				EquipmentId:     &findEquipment.Id,
				EquipmentTypeId: &findEquipment.EquipmentTypeId,
				Num:             trans.Uint32(1),
				RepositoryId:    detail.RepositoryId,
				Name:            &findEquipment.Name,
				ModelNo:         &findEquipment.ModelNo,
				Feature:         detail.Feature,
			}

			outRepositoryOrderDetailsMap[findOutRepositoryOrder.GetRepositoryId()] = append(outRepositoryOrderDetailsMap[findOutRepositoryOrder.GetRepositoryId()], req)
		}
	}

	for _, outRepositoryOrder := range outRepositoryOrders {
		// 创建出库单
		outRepositoryOrderRes, err := uc.outRepositoryOrderRepo.CreateWmsOutRepositoryOrder(ctx, outRepositoryOrder)
		if err != nil {
			return err
		}

		// 创建出库单明细
		for _, detail := range outRepositoryOrderDetailsMap[outRepositoryOrder.GetRepositoryId()] {
			detail.OutRepositoryOrderId = &outRepositoryOrderRes.Id
			detail.OrderNo = outRepositoryOrderRes.OrderNo

			_, err = uc.outRepositoryOrderDetailRepo.CreateWmsOutRepositoryOrderDetail(ctx, detail)
			if err != nil {
				return err
			}
		}

		log := fmt.Sprintf("创建出库单: %s", trans.StringValue(outRepositoryOrderRes.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, outRepositoryOrderRes.Id, "out_repository_order", log)
		if err != nil {
			return err
		}
	}

	return nil
}
