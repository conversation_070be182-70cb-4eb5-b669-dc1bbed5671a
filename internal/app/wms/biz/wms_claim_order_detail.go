package biz

import (
	"context"

	v1 "kratos-mono-demo/gen/api/wms/v1"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"

	"github.com/go-kratos/kratos/v2/log"
)

type WmsClaimOrderDetailRepo interface {
	ListWmsClaimOrderDetail(ctx context.Context, req *v1.ListWmsClaimOrderDetailRequest) (*v1.ListWmsClaimOrderDetailResponse, error)
	GetWmsClaimOrderDetail(ctx context.Context, id string) (*v1.WmsClaimOrderDetail, error)
	CreateWmsClaimOrderDetail(ctx context.Context, req *v1.CreateWmsClaimOrderDetailRequest) (*v1.WmsClaimOrderDetail, error)
	UpdateWmsClaimOrderDetail(ctx context.Context, req *v1.UpdateWmsClaimOrderDetailRequest) (*v1.WmsClaimOrderDetail, error)
	DeleteWmsClaimOrderDetail(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsClaimOrderDetail(ctx context.Context, ids []string) (bool, error)

	// 根据领用单id查询明细
	GetWmsClaimOrderDetailsByOrderIds(ctx context.Context, orderIds []string) ([]*v1.WmsClaimOrderDetail, error)

	// 根据领用单ID删除领用单明细
	DeleteWmsClaimOrderDetailByOrderId(ctx context.Context, orderId string) (bool, error)
}

type WmsClaimOrderDetailUsecase struct {
	log  *log.Helper
	repo WmsClaimOrderDetailRepo
	tm   entutils.Transaction
}

func NewWmsClaimOrderDetailUsecase(repo WmsClaimOrderDetailRepo, tm entutils.Transaction, logger log.Logger) *WmsClaimOrderDetailUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_claim_order_detail/usecase"))
	return &WmsClaimOrderDetailUsecase{
		log:  l,
		repo: repo,
		tm:   tm,
	}
}

func (uc *WmsClaimOrderDetailUsecase) ListWmsClaimOrderDetail(ctx context.Context, req *v1.ListWmsClaimOrderDetailRequest) (*v1.ListWmsClaimOrderDetailResponse, error) {

	// // 自定义加入查询条件
	// newQuery := entgo.AppendQueryKeys(req.GetQuery(), map[string]string{
	// 	"createdBy": userId,
	// })
	// req.Query = &newQuery

	return uc.repo.ListWmsClaimOrderDetail(ctx, req)
}

func (uc *WmsClaimOrderDetailUsecase) GetWmsClaimOrderDetail(ctx context.Context, id string) (*v1.WmsClaimOrderDetail, error) {
	return uc.repo.GetWmsClaimOrderDetail(ctx, id)
}

func (uc *WmsClaimOrderDetailUsecase) CreateWmsClaimOrderDetail(ctx context.Context, req *v1.CreateWmsClaimOrderDetailRequest) (*v1.WmsClaimOrderDetail, error) {
	return uc.repo.CreateWmsClaimOrderDetail(ctx, req)
}

func (uc *WmsClaimOrderDetailUsecase) UpdateWmsClaimOrderDetail(ctx context.Context, req *v1.UpdateWmsClaimOrderDetailRequest) (*v1.WmsClaimOrderDetail, error) {
	return uc.repo.UpdateWmsClaimOrderDetail(ctx, req)
}

func (uc *WmsClaimOrderDetailUsecase) DeleteWmsClaimOrderDetail(ctx context.Context, id string) (bool, error) {
	return uc.repo.DeleteWmsClaimOrderDetail(ctx, id)
}

func (uc *WmsClaimOrderDetailUsecase) MultiDeleteWmsClaimOrderDetail(ctx context.Context, ids []string) (bool, error) {
	return uc.repo.MultiDeleteWmsClaimOrderDetail(ctx, ids)
}
