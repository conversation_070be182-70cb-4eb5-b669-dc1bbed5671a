package biz

import (
	"context"

	v1 "kratos-mono-demo/gen/api/wms/v1"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"

	"github.com/go-kratos/kratos/v2/log"
)

type WmsDiscardOrderDetailRepo interface {
	ListWmsDiscardOrderDetail(ctx context.Context, req *v1.ListWmsDiscardOrderDetailRequest) (*v1.ListWmsDiscardOrderDetailResponse, error)
	GetWmsDiscardOrderDetail(ctx context.Context, id string) (*v1.WmsDiscardOrderDetail, error)
	CreateWmsDiscardOrderDetail(ctx context.Context, req *v1.CreateWmsDiscardOrderDetailRequest) (*v1.WmsDiscardOrderDetail, error)
	UpdateWmsDiscardOrderDetail(ctx context.Context, req *v1.UpdateWmsDiscardOrderDetailRequest) (*v1.WmsDiscardOrderDetail, error)
	DeleteWmsDiscardOrderDetail(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsDiscardOrderDetail(ctx context.Context, ids []string) (bool, error)

	// 根据报废单ID删除报废单明细
	DeleteWmsDiscardOrderDetailByOrderId(ctx context.Context, orderId string) (bool, error)

	// 根据报废单ID查询报废单明细
	GetWmsDiscardOrderDetailsByOrderIds(ctx context.Context, orderIds []string) ([]*v1.WmsDiscardOrderDetail, error)

	// 根据报废单ID查询报废单明细
	GetWmsDiscardOrderDetailsByOrderId(ctx context.Context, orderId string) ([]*v1.WmsDiscardOrderDetail, error)
}

type WmsDiscardOrderDetailUsecase struct {
	log  *log.Helper
	repo WmsDiscardOrderDetailRepo
	tm   entutils.Transaction
}

func NewWmsDiscardOrderDetailUsecase(repo WmsDiscardOrderDetailRepo, tm entutils.Transaction, logger log.Logger) *WmsDiscardOrderDetailUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_discard_order_detail/usecase"))
	return &WmsDiscardOrderDetailUsecase{
		log:  l,
		repo: repo,
		tm:   tm,
	}
}

func (uc *WmsDiscardOrderDetailUsecase) ListWmsDiscardOrderDetail(ctx context.Context, req *v1.ListWmsDiscardOrderDetailRequest) (*v1.ListWmsDiscardOrderDetailResponse, error) {

	// // 自定义加入查询条件
	// newQuery := entgo.AppendQueryKeys(req.GetQuery(), map[string]string{
	// 	"createdBy": userId,
	// })
	// req.Query = &newQuery

	return uc.repo.ListWmsDiscardOrderDetail(ctx, req)
}

func (uc *WmsDiscardOrderDetailUsecase) GetWmsDiscardOrderDetail(ctx context.Context, id string) (*v1.WmsDiscardOrderDetail, error) {
	return uc.repo.GetWmsDiscardOrderDetail(ctx, id)
}

func (uc *WmsDiscardOrderDetailUsecase) CreateWmsDiscardOrderDetail(ctx context.Context, req *v1.CreateWmsDiscardOrderDetailRequest) (*v1.WmsDiscardOrderDetail, error) {
	return uc.repo.CreateWmsDiscardOrderDetail(ctx, req)
}

func (uc *WmsDiscardOrderDetailUsecase) UpdateWmsDiscardOrderDetail(ctx context.Context, req *v1.UpdateWmsDiscardOrderDetailRequest) (*v1.WmsDiscardOrderDetail, error) {
	return uc.repo.UpdateWmsDiscardOrderDetail(ctx, req)
}

func (uc *WmsDiscardOrderDetailUsecase) DeleteWmsDiscardOrderDetail(ctx context.Context, id string) (bool, error) {
	return uc.repo.DeleteWmsDiscardOrderDetail(ctx, id)
}

func (uc *WmsDiscardOrderDetailUsecase) MultiDeleteWmsDiscardOrderDetail(ctx context.Context, ids []string) (bool, error) {
	return uc.repo.MultiDeleteWmsDiscardOrderDetail(ctx, ids)
}
