package biz

import (
	"context"

	v1 "kratos-mono-demo/gen/api/wms/v1"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"

	"github.com/go-kratos/kratos/v2/log"
)

type WmsTransferOrderDetailRepo interface {
	ListWmsTransferOrderDetail(ctx context.Context, req *v1.ListWmsTransferOrderDetailRequest) (*v1.ListWmsTransferOrderDetailResponse, error)
	GetWmsTransferOrderDetail(ctx context.Context, id string) (*v1.WmsTransferOrderDetail, error)
	CreateWmsTransferOrderDetail(ctx context.Context, req *v1.CreateWmsTransferOrderDetailRequest) (*v1.WmsTransferOrderDetail, error)
	UpdateWmsTransferOrderDetail(ctx context.Context, req *v1.UpdateWmsTransferOrderDetailRequest) (*v1.WmsTransferOrderDetail, error)
	DeleteWmsTransferOrderDetail(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsTransferOrderDetail(ctx context.Context, ids []string) (bool, error)

	// 根据调拨单ID删除调拨单明细
	DeleteWmsTransferOrderDetailByOrderId(ctx context.Context, orderId string) (bool, error)

	// 根据调拨单ID查询调拨单明细
	GetWmsTransferOrderDetailsByOrderIds(ctx context.Context, orderIds []string) ([]*v1.WmsTransferOrderDetail, error)

	// 根据调拨单ID查询调拨单明细
	GetWmsTransferOrderDetailsByOrderId(ctx context.Context, orderId string) ([]*v1.WmsTransferOrderDetail, error)
}

type WmsTransferOrderDetailUsecase struct {
	log  *log.Helper
	repo WmsTransferOrderDetailRepo
	tm   entutils.Transaction
}

func NewWmsTransferOrderDetailUsecase(repo WmsTransferOrderDetailRepo, tm entutils.Transaction, logger log.Logger) *WmsTransferOrderDetailUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_transfer_order_detail/usecase"))
	return &WmsTransferOrderDetailUsecase{
		log:  l,
		repo: repo,
		tm:   tm,
	}
}

func (uc *WmsTransferOrderDetailUsecase) ListWmsTransferOrderDetail(ctx context.Context, req *v1.ListWmsTransferOrderDetailRequest) (*v1.ListWmsTransferOrderDetailResponse, error) {

	// // 自定义加入查询条件
	// newQuery := entgo.AppendQueryKeys(req.GetQuery(), map[string]string{
	// 	"createdBy": userId,
	// })
	// req.Query = &newQuery

	return uc.repo.ListWmsTransferOrderDetail(ctx, req)
}

func (uc *WmsTransferOrderDetailUsecase) GetWmsTransferOrderDetail(ctx context.Context, id string) (*v1.WmsTransferOrderDetail, error) {
	return uc.repo.GetWmsTransferOrderDetail(ctx, id)
}

func (uc *WmsTransferOrderDetailUsecase) CreateWmsTransferOrderDetail(ctx context.Context, req *v1.CreateWmsTransferOrderDetailRequest) (*v1.WmsTransferOrderDetail, error) {
	return uc.repo.CreateWmsTransferOrderDetail(ctx, req)
}

func (uc *WmsTransferOrderDetailUsecase) UpdateWmsTransferOrderDetail(ctx context.Context, req *v1.UpdateWmsTransferOrderDetailRequest) (*v1.WmsTransferOrderDetail, error) {
	return uc.repo.UpdateWmsTransferOrderDetail(ctx, req)
}

func (uc *WmsTransferOrderDetailUsecase) DeleteWmsTransferOrderDetail(ctx context.Context, id string) (bool, error) {
	return uc.repo.DeleteWmsTransferOrderDetail(ctx, id)
}

func (uc *WmsTransferOrderDetailUsecase) MultiDeleteWmsTransferOrderDetail(ctx context.Context, ids []string) (bool, error) {
	return uc.repo.MultiDeleteWmsTransferOrderDetail(ctx, ids)
}
