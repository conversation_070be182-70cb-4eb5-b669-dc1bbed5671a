package biz

import (
	"context"

	v1 "kratos-mono-demo/gen/api/wms/v1"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"

	"github.com/go-kratos/kratos/v2/log"
)

type WmsBorrowOrderDetailRepo interface {
	ListWmsBorrowOrderDetail(ctx context.Context, req *v1.ListWmsBorrowOrderDetailRequest) (*v1.ListWmsBorrowOrderDetailResponse, error)
	GetWmsBorrowOrderDetail(ctx context.Context, id string) (*v1.WmsBorrowOrderDetail, error)
	CreateWmsBorrowOrderDetail(ctx context.Context, req *v1.CreateWmsBorrowOrderDetailRequest) (*v1.WmsBorrowOrderDetail, error)
	UpdateWmsBorrowOrderDetail(ctx context.Context, req *v1.UpdateWmsBorrowOrderDetailRequest) (*v1.WmsBorrowOrderDetail, error)
	DeleteWmsBorrowOrderDetail(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsBorrowOrderDetail(ctx context.Context, ids []string) (bool, error)

	// 根据借用单ID删除借用单明细
	DeleteWmsBorrowOrderDetailByOrderId(ctx context.Context, orderId string) (bool, error)

	// 根据借用单ID查询借用单明细
	GetWmsBorrowOrderDetailsByOrderIds(ctx context.Context, orderIds []string) ([]*v1.WmsBorrowOrderDetail, error)
}

type WmsBorrowOrderDetailUsecase struct {
	log  *log.Helper
	repo WmsBorrowOrderDetailRepo
	tm   entutils.Transaction
}

func NewWmsBorrowOrderDetailUsecase(repo WmsBorrowOrderDetailRepo, tm entutils.Transaction, logger log.Logger) *WmsBorrowOrderDetailUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_borrow_order_detail/usecase"))
	return &WmsBorrowOrderDetailUsecase{
		log:  l,
		repo: repo,
		tm:   tm,
	}
}

func (uc *WmsBorrowOrderDetailUsecase) ListWmsBorrowOrderDetail(ctx context.Context, req *v1.ListWmsBorrowOrderDetailRequest) (*v1.ListWmsBorrowOrderDetailResponse, error) {

	// // 自定义加入查询条件
	// newQuery := entgo.AppendQueryKeys(req.GetQuery(), map[string]string{
	// 	"createdBy": userId,
	// })
	// req.Query = &newQuery

	return uc.repo.ListWmsBorrowOrderDetail(ctx, req)
}

func (uc *WmsBorrowOrderDetailUsecase) GetWmsBorrowOrderDetail(ctx context.Context, id string) (*v1.WmsBorrowOrderDetail, error) {
	return uc.repo.GetWmsBorrowOrderDetail(ctx, id)
}

func (uc *WmsBorrowOrderDetailUsecase) CreateWmsBorrowOrderDetail(ctx context.Context, req *v1.CreateWmsBorrowOrderDetailRequest) (*v1.WmsBorrowOrderDetail, error) {
	return uc.repo.CreateWmsBorrowOrderDetail(ctx, req)
}

func (uc *WmsBorrowOrderDetailUsecase) UpdateWmsBorrowOrderDetail(ctx context.Context, req *v1.UpdateWmsBorrowOrderDetailRequest) (*v1.WmsBorrowOrderDetail, error) {
	return uc.repo.UpdateWmsBorrowOrderDetail(ctx, req)
}

func (uc *WmsBorrowOrderDetailUsecase) DeleteWmsBorrowOrderDetail(ctx context.Context, id string) (bool, error) {
	return uc.repo.DeleteWmsBorrowOrderDetail(ctx, id)
}

func (uc *WmsBorrowOrderDetailUsecase) MultiDeleteWmsBorrowOrderDetail(ctx context.Context, ids []string) (bool, error) {
	return uc.repo.MultiDeleteWmsBorrowOrderDetail(ctx, ids)
}
