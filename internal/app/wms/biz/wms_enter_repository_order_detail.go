package biz

import (
	"context"
	"errors"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	"kratos-mono-demo/internal/pkg/go-utils/arrayutil"
	"kratos-mono-demo/internal/pkg/go-utils/trans"

	"github.com/go-kratos/kratos/v2/log"
)

type WmsEnterRepositoryOrderDetailRepo interface {
	ListWmsEnterRepositoryOrderDetail(ctx context.Context, req *v1.ListWmsEnterRepositoryOrderDetailRequest) (*v1.ListWmsEnterRepositoryOrderDetailResponse, error)
	GetWmsEnterRepositoryOrderDetail(ctx context.Context, id string) (*v1.WmsEnterRepositoryOrderDetail, error)
	CreateWmsEnterRepositoryOrderDetail(ctx context.Context, req *v1.CreateWmsEnterRepositoryOrderDetailRequest) (*v1.WmsEnterRepositoryOrderDetail, error)
	UpdateWmsEnterRepositoryOrderDetail(ctx context.Context, req *v1.UpdateWmsEnterRepositoryOrderDetailRequest) (*v1.WmsEnterRepositoryOrderDetail, error)
	DeleteWmsEnterRepositoryOrderDetail(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsEnterRepositoryOrderDetail(ctx context.Context, ids []string) (bool, error)

	// 根据入库单ID获取入库单明细
	ListWmsEnterRepositoryOrderDetailByOrderID(ctx context.Context, orderID string) (*v1.ListWmsEnterRepositoryOrderDetailResponse, error)

	// 根据入库单ID删除入库单明细
	DeleteWmsEnterRepositoryOrderDetailByOrderID(ctx context.Context, orderID string) error
}

type WmsEnterRepositoryOrderDetailUsecase struct {
	log           *log.Helper
	repo          WmsEnterRepositoryOrderDetailRepo
	orderRepo     WmsEnterRepositoryOrderRepo
	materialRepo  WmsMaterialRepo
	tm            entutils.Transaction
	equipmentRepo WmsEquipmentRepo
}

func NewWmsEnterRepositoryOrderDetailUsecase(
	repo WmsEnterRepositoryOrderDetailRepo,
	materialRepo WmsMaterialRepo,
	orderRepo WmsEnterRepositoryOrderRepo,
	tm entutils.Transaction,
	logger log.Logger,
	equipmentRepo WmsEquipmentRepo,
) *WmsEnterRepositoryOrderDetailUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_enter_repository_order_detail/usecase"))
	return &WmsEnterRepositoryOrderDetailUsecase{
		log:           l,
		repo:          repo,
		orderRepo:     orderRepo,
		materialRepo:  materialRepo,
		tm:            tm,
		equipmentRepo: equipmentRepo,
	}
}

func (uc *WmsEnterRepositoryOrderDetailUsecase) ListWmsEnterRepositoryOrderDetail(ctx context.Context, req *v1.ListWmsEnterRepositoryOrderDetailRequest) (*v1.ListWmsEnterRepositoryOrderDetailResponse, error) {
	res, err := uc.repo.ListWmsEnterRepositoryOrderDetail(ctx, req)

	if err != nil {
		return nil, err
	}

	// 转换详细规格为字符串
	equipmentIds := arrayutil.Map(res.GetItems(), func(item *v1.WmsEnterRepositoryOrderDetail) string {
		return item.GetEquipmentId()
	})

	features, err := uc.equipmentRepo.GetWmsEquipmentFeaturesByIds(ctx, equipmentIds)

	if err != nil {
		uc.log.Errorf("get equipment features error: %v", err)
	}

	for _, detail := range res.GetItems() {
		feature := detail.GetFeature()

		if feature != nil {
			feature_str := uc.equipmentRepo.ConvertFeatureToFeatureStr(feature.AsMap(), features)
			detail.FeatureStr = trans.String(feature_str)
		}
	}

	return res, nil
}

func (uc *WmsEnterRepositoryOrderDetailUsecase) GetWmsEnterRepositoryOrderDetail(ctx context.Context, id string) (*v1.WmsEnterRepositoryOrderDetail, error) {
	detail, err := uc.repo.GetWmsEnterRepositoryOrderDetail(ctx, id)

	if err != nil {
		uc.log.Errorf("get wms_enter_repository_detail error: %v", err)
	}

	// 转换详细规格为字符串
	features, err := uc.equipmentRepo.GetWmsEquipmentFeaturesByIds(ctx, []string{detail.GetEquipmentId()})

	if err != nil {
		uc.log.Errorf("get equipment features error: %v", err)
	}

	feature := detail.GetFeature()

	if feature != nil {
		feature_str := uc.equipmentRepo.ConvertFeatureToFeatureStr(feature.AsMap(), features)
		detail.FeatureStr = trans.String(feature_str)
	}

	return detail, err
}

// 检查RFID物料编码是否存在
func (uc *WmsEnterRepositoryOrderDetailUsecase) checkRFIDCode(ctx context.Context, code string) error {
	material, _ := uc.materialRepo.FindWmsMaterialByCode(ctx, code)
	if material != nil {
		return errors.New("RFID物料编码已存在")
	}
	return nil
}

// 更新入库单的数量
func (uc *WmsEnterRepositoryOrderDetailUsecase) updateOrderCount(ctx context.Context, orderID string) error {
	if orderID == "" {
		return nil
	}

	detailList, err := uc.repo.ListWmsEnterRepositoryOrderDetailByOrderID(ctx, orderID)
	if err != nil {
		return err
	}
	_, err = uc.orderRepo.UpdateWmsEnterRepositoryOrder(ctx, &v1.UpdateWmsEnterRepositoryOrderRequest{
		Id:           orderID,
		EquipmentNum: trans.Uint32(uint32(detailList.Total)),
	})

	return err
}

func (uc *WmsEnterRepositoryOrderDetailUsecase) CreateWmsEnterRepositoryOrderDetail(ctx context.Context, req *v1.CreateWmsEnterRepositoryOrderDetailRequest) (*v1.WmsEnterRepositoryOrderDetail, error) {
	var detail *v1.WmsEnterRepositoryOrderDetail
	var err error

	// /*
	// 	检查RFID物料编码是否存在
	// 	如果是退还装备,则不需要检查
	// 	目前判断是如果有FromRepositoryId，则不需要判断
	// */
	// if req.GetFromRepositoryId() == "" {
	// 	// err = uc.checkRFIDCode(ctx, req.GetCode())
	// 	err = uc.check
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// }

	// // 一个rfid对应一个物料，数量为1
	// req.Num = trans.Uint32(uint32(1))

	if req.GetCode() == "" {
		return nil, errors.New("RFID物料编码不能为空")
	}

	if req.GetToRepositoryId() == "" {
		order, err := uc.orderRepo.GetWmsEnterRepositoryOrder(ctx, req.GetEnterRepositoryOrderId())

		if err != nil {
			return nil, err
		}

		req.ToRepositoryId = order.RepositoryId
	}

	// 这里限制要去掉，否则有些类型无法入库
	// if req.GetRepositoryAreaId() == "" {
	// 	return nil, errors.New("入库库区不能为空")
	// }

	// if req.GetRepositoryPositionId() == "" {
	// 	return nil, errors.New("入库库位不能为空")
	// }

	// 一个编码只能绑定一种装备
	// material, err := uc.materialRepo.FindWmsMaterialByCode(ctx, req.GetCode())

	// if err != nil {
	// 	return nil, err
	// }

	// equipment, err := uc.equipmentRepo.GetWmsEquipment(ctx, req.GetEquipmentId())
	// if err != nil {
	// 	return nil, err
	// }

	// // 如果是一物一码，则检查物料是否已经绑定了装备
	// if equipment.IsOneMaterialOneCode && material != nil {
	// 	if material.EquipmentId != req.EquipmentId {
	// 		return nil, errors.New("RFID物料编码已绑定其他装备")
	// 	}
	// }

	txErr := uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		detail, err = uc.repo.CreateWmsEnterRepositoryOrderDetail(ctx, req)
		if err != nil {
			return err
		}

		// 更新入库单的数量
		err = uc.updateOrderCount(ctx, req.GetEnterRepositoryOrderId())
		if err != nil {
			return err
		}

		return nil
	})

	return detail, txErr
}

func (uc *WmsEnterRepositoryOrderDetailUsecase) UpdateWmsEnterRepositoryOrderDetail(ctx context.Context, req *v1.UpdateWmsEnterRepositoryOrderDetailRequest) (*v1.WmsEnterRepositoryOrderDetail, error) {

	var detail *v1.WmsEnterRepositoryOrderDetail
	var err error

	// /*
	// 	检查RFID物料编码是否存在
	// 	如果是退还装备,则不需要检查
	// 	目前判断是如果有FromRepositoryId，则不需要判断
	// */
	// if req.GetFromRepositoryId() == "" {
	// 	err = uc.checkRFIDCode(ctx, req.GetCode())
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// }

	// // 一个rfid对应一个物料，数量为1
	// req.Num = trans.Uint32(uint32(1))

	db_detail, err := uc.GetWmsEnterRepositoryOrderDetail(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	if req.GetCode() == "" {
		return nil, errors.New("RFID物料编码不能为空")
	}

	if req.GetToRepositoryId() == "" {
		order, err := uc.orderRepo.GetWmsEnterRepositoryOrder(ctx, db_detail.GetEnterRepositoryOrderId())

		if err != nil {
			return nil, err
		}

		req.ToRepositoryId = order.RepositoryId
	}

	// 这里限制要去掉，否则有些类型无法入库
	// if req.GetRepositoryAreaId() == "" {
	// 	return nil, errors.New("入库库区不能为空")
	// }

	// if req.GetRepositoryPositionId() == "" {
	// 	return nil, errors.New("入库库位不能为空")
	// }

	// // 一个编码只能绑定一种装备
	// material, err := uc.materialRepo.FindWmsMaterialByCode(ctx, req.GetCode())

	// if err != nil {
	// 	return nil, err
	// }

	// equipment, err := uc.equipmentRepo.GetWmsEquipment(ctx, req.GetEquipmentId())
	// if err != nil {
	// 	return nil, err
	// }

	// if equipment.IsOneMaterialOneCode && material != nil {
	// 	if material.EquipmentId != req.EquipmentId {
	// 		return nil, errors.New("RFID物料编码已绑定其他装备")
	// 	}
	// }

	txErr := uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		detail, err = uc.repo.UpdateWmsEnterRepositoryOrderDetail(ctx, req)
		if err != nil {
			return err
		}

		// 更新入库单的数量
		err = uc.updateOrderCount(ctx, req.GetEnterRepositoryOrderId())
		if err != nil {
			return err
		}

		return nil
	})

	return detail, txErr
}

func (uc *WmsEnterRepositoryOrderDetailUsecase) DeleteWmsEnterRepositoryOrderDetail(ctx context.Context, id string) (bool, error) {
	var ok bool
	var err error
	var detail *v1.WmsEnterRepositoryOrderDetail

	detail, err = uc.GetWmsEnterRepositoryOrderDetail(ctx, id)
	if err != nil {
		return false, err
	}

	txErr := uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		ok, err = uc.repo.DeleteWmsEnterRepositoryOrderDetail(ctx, id)
		if err != nil {
			return err
		}

		// 更新入库单的数量
		err = uc.updateOrderCount(ctx, detail.GetEnterRepositoryOrderId())
		if err != nil {
			return err
		}

		return nil
	})

	return ok, txErr
}

func (uc *WmsEnterRepositoryOrderDetailUsecase) MultiDeleteWmsEnterRepositoryOrderDetail(ctx context.Context, ids []string) (bool, error) {

	if len(ids) == 0 {
		return false, nil
	}

	var ok bool
	var err error
	var detail *v1.WmsEnterRepositoryOrderDetail

	detail, err = uc.GetWmsEnterRepositoryOrderDetail(ctx, ids[0])
	if err != nil {
		return false, err
	}

	txErr := uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		ok, err = uc.repo.MultiDeleteWmsEnterRepositoryOrderDetail(ctx, ids)
		if err != nil {
			return err
		}

		// 更新入库单的数量
		err = uc.updateOrderCount(ctx, detail.GetEnterRepositoryOrderId())
		if err != nil {
			return err
		}

		return nil
	})

	return ok, txErr
}
