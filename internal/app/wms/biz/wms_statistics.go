package biz

import (
	"context"
	"errors"
	"fmt"
	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	"kratos-mono-demo/internal/pkg/go-utils/arrayutil"
	"time"

	sysv1 "kratos-mono-demo/gen/api/system/v1"
	v1 "kratos-mono-demo/gen/api/wms/v1"

	sysBiz "kratos-mono-demo/internal/app/system/biz"

	"github.com/go-kratos/kratos/v2/log"
)

type WmsStatisticsUsecase struct {
	log *log.Helper
	tm  entutils.Transaction

	materialRepo        WmsMaterialRepo
	equipmentTypeRepo   WmsEquipmentTypeRepo
	repositoryRepo      WmsRepositoryRepo
	organizationRepo    sysBiz.SysOrganizationRepo
	enterRepositoryRepo WmsEnterRepositoryOrderRepo
	outRepositoryRepo   WmsOutRepositoryOrderRepo
	discardMeetingRepo  WmsDiscardMeetingRepo
	approvalTaskRepo    WmsApprovalTaskRepo
	auditPlanRepo       WmsAuditPlanRepo
	documentRepo        WmsDocumentRepo
	userRepo            sysBiz.SysUserRepo
}

func NewWmsStatisticsUsecase(
	tm entutils.Transaction,
	logger log.Logger,

	materialRepo WmsMaterialRepo,
	equipmentTypeRepo WmsEquipmentTypeRepo,
	repositoryRepo WmsRepositoryRepo,
	organizationRepo sysBiz.SysOrganizationRepo,
	enterRepositoryRepo WmsEnterRepositoryOrderRepo,
	outRepositoryRepo WmsOutRepositoryOrderRepo,
	discardMeetingRepo WmsDiscardMeetingRepo,
	approvalTaskRepo WmsApprovalTaskRepo,
	auditPlanRepo WmsAuditPlanRepo,
	documentRepo WmsDocumentRepo,
	userRepo sysBiz.SysUserRepo,
) *WmsStatisticsUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_staitstics/usecase"))
	return &WmsStatisticsUsecase{
		log: l,
		tm:  tm,

		materialRepo:        materialRepo,
		equipmentTypeRepo:   equipmentTypeRepo,
		repositoryRepo:      repositoryRepo,
		organizationRepo:    organizationRepo,
		enterRepositoryRepo: enterRepositoryRepo,
		outRepositoryRepo:   outRepositoryRepo,
		discardMeetingRepo:  discardMeetingRepo,
		approvalTaskRepo:    approvalTaskRepo,
		auditPlanRepo:       auditPlanRepo,
		documentRepo:        documentRepo,
		userRepo:            userRepo,
	}
}

// 获取资产统计
func (uc *WmsStatisticsUsecase) GetMaterialStatistics(ctx context.Context, req *v1.GetMaterialStatisticsRequest) (*v1.GetMaterialStatisticsResponse, error) {
	// 获取统计包括 总数、固资总数、物资总数、库存总数、在用总数、维修中固资总数、维修中物资总数、报废总数、固资报废总数、物资报废总数
	material_statistics, err := uc.materialRepo.GetMaterialStatistics(ctx, req)

	if err != nil {
		return nil, err
	}

	in_use_car_count, err := uc.materialRepo.GetCarUsingCount(ctx, req)

	if err != nil {
		return nil, err
	}

	material_statistics.InUseCarCount = int32(in_use_car_count)

	return material_statistics, nil
}

// 获取库存装备分类统计
func (uc *WmsStatisticsUsecase) GetMaterialEquipmentTypeStatistics(ctx context.Context) (*v1.GetMaterialEquipmentTypeStatisticsResponse, error) {
	// 获取统计数据
	statistics, err := uc.materialRepo.GetMaterialEquipmentTypeStatistics(ctx, MATERIAL_STATUS_AVAILABLE)

	if err != nil {
		return nil, err
	}

	// 获取装备类型数据
	equipment_type_ids := arrayutil.Map(statistics, func(item *v1.MaterialEquipmentTypeStatistics) string {
		return item.EquipmentTypeId
	})

	equipment_types, err := uc.equipmentTypeRepo.GetEquipmentTypeByIds(ctx, equipment_type_ids)

	if err != nil {
		return nil, err
	}

	equipment_type_map := arrayutil.ToMap(equipment_types, func(item *v1.WmsEquipmentType) (string, *v1.WmsEquipmentType) {
		return item.Id, item
	})

	// 合并装备类型名称到统计数据
	for _, item := range statistics {
		equipment_type := equipment_type_map[item.EquipmentTypeId]

		if equipment_type == nil {
			item.EquipmentTypeName = ""
		}

		item.EquipmentTypeName = equipment_type.GetName()
	}

	res := &v1.GetMaterialEquipmentTypeStatisticsResponse{
		Data: statistics,
	}

	return res, nil
}

// 获取申领装备分类统计
func (uc *WmsStatisticsUsecase) GetMaterialEquipmentTypeClaimedStatistics(ctx context.Context) (*v1.GetMaterialEquipmentTypeStatisticsResponse, error) {
	// 获取统计数据
	statistics, err := uc.materialRepo.GetMaterialEquipmentTypeStatistics(ctx, MATERIAL_STATUS_CLAIMED)

	if err != nil {
		return nil, err
	}

	// 获取装备类型数据
	equipment_type_ids := arrayutil.Map(statistics, func(item *v1.MaterialEquipmentTypeStatistics) string {
		return item.EquipmentTypeId
	})

	equipment_types, err := uc.equipmentTypeRepo.GetEquipmentTypeByIds(ctx, equipment_type_ids)

	if err != nil {
		return nil, err
	}

	equipment_type_map := arrayutil.ToMap(equipment_types, func(item *v1.WmsEquipmentType) (string, *v1.WmsEquipmentType) {
		return item.Id, item
	})

	// 合并装备类型名称到统计数据
	for _, item := range statistics {
		equipment_type := equipment_type_map[item.EquipmentTypeId]

		if equipment_type == nil {
			item.EquipmentTypeName = ""
		}

		item.EquipmentTypeName = equipment_type.GetName()
	}

	// 过滤掉统计为0的装备类型
	statistics = arrayutil.Filter(statistics, func(item *v1.MaterialEquipmentTypeStatistics) bool {
		return item.Count > 0
	})

	res := &v1.GetMaterialEquipmentTypeStatisticsResponse{
		Data: statistics,
	}

	return res, nil
}

// 获取库存仓库分类统计
func (uc *WmsStatisticsUsecase) GetMaterialRepositoryStatistics(ctx context.Context) (*v1.GetMaterialRepositoryStatisticsResponse, error) {
	// 获取统计数据
	statistics, err := uc.materialRepo.GetMaterialRepositoryStatistics(ctx)

	if err != nil {
		return nil, err
	}

	// 获取仓库数据
	repository_ids := arrayutil.Map(statistics, func(item *v1.MaterialRepositoryStatistics) string {
		return item.RepositoryId
	})

	repositories, err := uc.repositoryRepo.GetRepositoriesByIds(ctx, repository_ids)

	if err != nil {
		return nil, err
	}

	repository_map := arrayutil.ToMap(repositories, func(item *v1.WmsRepository) (string, *v1.WmsRepository) {
		return item.Id, item
	})

	// 合并仓库名称到统计数据
	for _, item := range statistics {
		repository := repository_map[item.RepositoryId]

		if repository == nil {
			item.RepositoryName = ""
		}

		item.RepositoryName = repository.GetName()
	}

	res := &v1.GetMaterialRepositoryStatisticsResponse{
		Data: statistics,
	}

	return res, nil
}

// 获取资产申领排行前十部门统计
func (uc *WmsStatisticsUsecase) GetMaterialOrganizationTop10Statistics(ctx context.Context, req *v1.GetMaterialOrganizationTop10StatisticsRequest) (*v1.GetMaterialOrganizationTop10StatisticsResponse, error) {
	// 最近12个月、今年、去年
	// 今年年初
	time_range_map := map[string]*TimeGroup{
		"12m": {
			Name:      "最近12个月",
			StartTime: time.Now().AddDate(0, -11, 0),
			EndTime:   time.Now(),
		},
		"y": {
			Name:      "今年",
			StartTime: time.Date(time.Now().Year(), 1, 1, 0, 0, 0, 0, time.Local),
			EndTime:   time.Now(),
		},
		"ly": {
			Name:      "去年",
			StartTime: time.Now().AddDate(-1, 0, 0),
			EndTime:   time.Date(time.Now().Year(), 1, 1, 0, 0, 0, 0, time.Local),
		},
	}

	if _, ok := time_range_map[req.TimeRangeKey]; !ok {
		return nil, errors.New("不支持的统计类型")
	}

	time_group := time_range_map[req.TimeRangeKey]

	startTime := time_group.StartTime
	endTime := time_group.EndTime

	if startTime.After(endTime) {
		return nil, errors.New("开始时间不能大于结束时间")
	}

	page := 1
	pageSize := 10
	// 获取统计数据
	statistics, err := uc.materialRepo.GetMaterialOrganizationStatistics(ctx, &startTime, &endTime, &page, &pageSize)

	if err != nil {
		return nil, err
	}

	// 获取组织数据
	organization_ids := arrayutil.Map(statistics, func(item *v1.MaterialOrganizationStatistics) string {
		return item.OrganizationId
	})

	organizations, err := uc.organizationRepo.GetOrganizationsByIds(ctx, organization_ids)

	if err != nil {
		return nil, err
	}

	organization_map := arrayutil.ToMap(organizations, func(item *sysv1.SysOrganization) (string, *sysv1.SysOrganization) {
		return item.Id, item
	})

	// 合并组织名称到统计数据
	for _, item := range statistics {
		organization := organization_map[item.OrganizationId]

		if organization == nil {
			item.OrganizationName = ""
		}

		item.OrganizationName = organization.GetName()
	}

	res := &v1.GetMaterialOrganizationTop10StatisticsResponse{
		Data: statistics,
	}

	return res, nil
}

// 获取资产部门申领统计
func (uc *WmsStatisticsUsecase) GetMaterialOrganizationStatistics(ctx context.Context) (*v1.GetMaterialOrganizationStatisticsResponse, error) {
	// 获取统计数据
	statistics, err := uc.materialRepo.GetMaterialOrganizationStatistics(ctx, nil, nil, nil, nil)

	if err != nil {
		return nil, err
	}

	// 获取组织数据
	organization_ids := arrayutil.Map(statistics, func(item *v1.MaterialOrganizationStatistics) string {
		return item.OrganizationId
	})

	organizations, err := uc.organizationRepo.GetOrganizationsByIds(ctx, organization_ids)

	if err != nil {
		return nil, err
	}

	organization_map := arrayutil.ToMap(organizations, func(item *sysv1.SysOrganization) (string, *sysv1.SysOrganization) {
		return item.Id, item
	})

	// 合并组织名称到统计数据
	for _, item := range statistics {
		organization := organization_map[item.OrganizationId]

		if organization == nil {
			item.OrganizationName = ""
		}

		item.OrganizationName = organization.GetName()
	}

	// 过滤掉统计为0的组织
	statistics = arrayutil.Filter(statistics, func(item *v1.MaterialOrganizationStatistics) bool {
		return item.Count > 0
	})

	res := &v1.GetMaterialOrganizationStatisticsResponse{
		Data: statistics,
	}

	return res, nil
}

// 获取资产服务年限统计
func (uc *WmsStatisticsUsecase) GetMaterialTimeStatistics(ctx context.Context, req *v1.GetMaterialTimeStatisticsRequest) (*v1.GetMaterialTimeStatisticsResponse, error) {
	time_groups := []*TimeGroup{
		{
			Name:      "1年内",
			StartTime: time.Now().AddDate(-1, 0, 0),
			EndTime:   time.Now(),
		},
		{
			Name:      "1-3年",
			StartTime: time.Now().AddDate(-3, 0, 0),
			EndTime:   time.Now().AddDate(-1, 0, 0),
		},
		{
			Name:      "3-5年",
			StartTime: time.Now().AddDate(-5, 0, 0),
			EndTime:   time.Now().AddDate(-3, 0, 0),
		},
		{
			Name:      "5-7年",
			StartTime: time.Now().AddDate(-7, 0, 0),
			EndTime:   time.Now().AddDate(-5, 0, 0),
		},
		{
			Name:      "7-10年",
			StartTime: time.Now().AddDate(-10, 0, 0),
			EndTime:   time.Now().AddDate(-7, 0, 0),
		},
		{
			Name:      "10-12年",
			StartTime: time.Now().AddDate(-12, 0, 0),
			EndTime:   time.Now().AddDate(-10, 0, 0),
		},
		{
			Name:      "12年以上",
			StartTime: time.Now().AddDate(-100, 0, 0),
			EndTime:   time.Now().AddDate(-12, 0, 0),
		},
	}

	// 获取统计数据
	statistics, err := uc.materialRepo.GetMaterialTimeStatistics(ctx, time_groups, req.GetOrganizationId())

	if err != nil {
		return nil, err
	}

	res := &v1.GetMaterialTimeStatisticsResponse{
		Data: statistics,
	}

	return res, nil
}

// 获取资产生命周期统计
func (uc *WmsStatisticsUsecase) GetMaterialLifetimeStatistics(ctx context.Context, req *v1.GetMaterialLifetimeStatisticsRequest) (*v1.GetMaterialLifetimeStatisticsResponse, error) {
	// 最近12个月、今年、去年
	// 今年年初
	time_range_map := map[string]*TimeGroup{
		"12m": {
			Name:      "最近12个月",
			StartTime: time.Now().AddDate(0, -11, 0),
			EndTime:   time.Now(),
		},
		"y": {
			Name:      "今年",
			StartTime: time.Date(time.Now().Year(), 1, 1, 0, 0, 0, 0, time.Local),
			EndTime:   time.Now(),
		},
		"ly": {
			Name:      "去年",
			StartTime: time.Now().AddDate(-1, 0, 0),
			EndTime:   time.Date(time.Now().Year(), 1, 1, 0, 0, 0, 0, time.Local),
		},
	}

	if _, ok := time_range_map[req.TimeRangeKey]; !ok {
		return nil, errors.New("不支持的统计类型")
	}

	time_range := time_range_map[req.TimeRangeKey]

	// 转换成月份数组
	time_groups := []*TimeGroup{}

	for group_time := time_range.StartTime; group_time.Before(time_range.EndTime); group_time = group_time.AddDate(0, 1, 0) {
		time_groups = append(time_groups, &TimeGroup{
			Name:      group_time.Format("2006-01"),
			StartTime: group_time,
			EndTime:   group_time.AddDate(0, 1, 0),
		})
	}

	// 获取入库统计数据
	enter_repository_statistics, err := uc.enterRepositoryRepo.GetEnterRepositoryTimeStatistics(ctx, time_groups)

	if err != nil {
		return nil, err
	}

	// 获取出库统计数据
	out_repository_statistics, err := uc.outRepositoryRepo.GetOutRepositoryTimeStatistics(ctx, time_groups)

	if err != nil {
		return nil, err
	}

	// 获取固资报废统计数据
	discard_static_statistics, err := uc.discardMeetingRepo.GetDiscardMeetingTimeStatistics(ctx, time_groups)

	if err != nil {
		return nil, err
	}

	// 获取物资报废统计数据
	discard_common_statistics, err := uc.approvalTaskRepo.GetDiscardTimeStatistics(ctx, time_groups)

	if err != nil {
		return nil, err
	}

	// 获取统计数据
	statistics := make([]*v1.MaterialLifetimeStatistics, 0)

	for _, group := range time_groups {
		enter_repository_count := 0
		out_repository_count := 0
		discard_count := 0

		for _, enter_repository := range enter_repository_statistics {
			if enter_repository.Name == group.Name {
				enter_repository_count = enter_repository.Count
				break
			}
		}

		for _, out_repository := range out_repository_statistics {
			if out_repository.Name == group.Name {
				out_repository_count = out_repository.Count
				break
			}
		}

		for _, discard_static := range discard_static_statistics {
			if discard_static.Name == group.Name {
				discard_count += discard_static.Count
				break
			}
		}

		for _, discard_common := range discard_common_statistics {
			if discard_common.Name == group.Name {
				discard_count += discard_common.Count
				break
			}
		}

		statistics = append(statistics, &v1.MaterialLifetimeStatistics{
			Name:                 group.Name,
			EnterRepositoryCount: int32(enter_repository_count),
			OutRepositoryCount:   int32(out_repository_count),
			DiscardCount:         int32(discard_count),
		})
	}

	res := &v1.GetMaterialLifetimeStatisticsResponse{
		Data: statistics,
	}

	return res, nil
}

// 获取车辆装备分类统计
func (uc *WmsStatisticsUsecase) GetCarEquipmentTypeStatistics(ctx context.Context) (*v1.GetCarEquipmentTypeStatisticsResponse, error) {
	// 获取统计数据
	statistics, err := uc.materialRepo.GetCarEquipmentTypeIdStatistics(ctx)

	if err != nil {
		return nil, err
	}

	// 获取装备类型数据
	equipment_type_ids := arrayutil.Map(statistics, func(item *v1.CarEquipmentTypeStatistics) string {
		return item.EquipmentTypeId
	})

	equipment_types, err := uc.equipmentTypeRepo.GetEquipmentTypeByIds(ctx, equipment_type_ids)

	if err != nil {
		return nil, err
	}

	equipment_type_map := arrayutil.ToMap(equipment_types, func(item *v1.WmsEquipmentType) (string, *v1.WmsEquipmentType) {
		return item.Id, item
	})

	// 合并装备类型名称到统计数据
	for _, item := range statistics {
		equipment_type := equipment_type_map[item.EquipmentTypeId]

		if equipment_type == nil {
			item.EquipmentTypeName = ""
		}

		item.EquipmentTypeName = equipment_type.GetName()
	}

	res := &v1.GetCarEquipmentTypeStatisticsResponse{
		Data: statistics,
	}

	return res, nil
}

// 获取车辆组织分类统计
func (uc *WmsStatisticsUsecase) GetCarOrganizationStatistics(ctx context.Context) (*v1.GetCarOrganizationStatisticsResponse, error) {
	// 获取统计数据
	statistics, err := uc.materialRepo.GetCarOrganizationIdStatistics(ctx)

	if err != nil {
		return nil, err
	}

	// 获取装备类型数据
	organization_ids := arrayutil.Map(statistics, func(item *v1.CarOrganizationStatistics) string {
		return item.OrganizationId
	})

	fmt.Println(organization_ids)

	fmt.Println(len(organization_ids))

	organizations, err := uc.organizationRepo.GetOrganizationsByIds(ctx, organization_ids)

	fmt.Println(len(organizations))

	if err != nil {
		return nil, err
	}

	organization_map := arrayutil.ToMap(organizations, func(item *sysv1.SysOrganization) (string, *sysv1.SysOrganization) {
		return item.Id, item
	})

	// 合并装备类型名称到统计数据
	for _, item := range statistics {
		organization := organization_map[item.OrganizationId]

		if organization == nil {
			item.OrganizationName = ""
		}

		item.OrganizationName = organization.GetName()
	}

	res := &v1.GetCarOrganizationStatisticsResponse{
		Data: statistics,
	}

	return res, nil
}

// 获取盘点资产覆盖统计
func (uc *WmsStatisticsUsecase) GetAuditMaterialCoverageStatistics(ctx context.Context, req *v1.GetAuditMaterialStatisticRequest) (*v1.GetAuditMaterialCoverageStatisticsResponse, error) {
	var start_time *time.Time
	var end_time *time.Time
	if req.GetYear() != "" {
		// 判断是否为年份
		year_time, err := time.Parse("2006", req.GetYear())

		if err != nil {
			return nil, errors.New("年份格式错误")
		}

		// 获取年份的开始时间和结束时间
		year_time_start := time.Date(year_time.Year(), 1, 1, 0, 0, 0, 0, time.Local)
		year_time_end := time.Date(year_time.Year(), 12, 31, 23, 59, 59, 0, time.Local)

		start_time = &year_time_start
		end_time = &year_time_end
	}

	// 获取统计数据
	distinct_audit_count, err := uc.auditPlanRepo.GetDistinctAuditCount(ctx, start_time, end_time)

	if err != nil {
		return nil, err
	}

	material_count, err := uc.materialRepo.GetMaterialCount(ctx, start_time, end_time)

	if err != nil {
		return nil, err
	}

	statistics := &v1.GetAuditMaterialCoverageStatisticsResponse{
		AuditCount:    int32(distinct_audit_count),
		MaterialCount: int32(material_count),
	}

	return statistics, nil
}

// 获取盘点资产统计
func (uc *WmsStatisticsUsecase) GetAuditMaterialStatistics(ctx context.Context, req *v1.GetAuditMaterialStatisticRequest) (*v1.GetAuditMaterialStatisticsResponse, error) {
	var start_time *time.Time
	var end_time *time.Time
	if req.GetYear() != "" {
		// 判断是否为年份
		year_time, err := time.Parse("2006", req.GetYear())

		if err != nil {
			return nil, errors.New("年份格式错误")
		}

		// 获取年份的开始时间和结束时间
		year_time_start := time.Date(year_time.Year(), 1, 1, 0, 0, 0, 0, time.Local)
		year_time_end := time.Date(year_time.Year(), 12, 31, 23, 59, 59, 0, time.Local)

		start_time = &year_time_start
		end_time = &year_time_end
	}

	// 获取统计数据
	statistics, err := uc.auditPlanRepo.GetAuditMaterialStatistics(ctx, start_time, end_time)

	if err != nil {
		return nil, err
	}

	return statistics, nil
}

// 获取盘点计划统计
func (uc *WmsStatisticsUsecase) GetAuditPlanStatistics(ctx context.Context, req *v1.GetAuditMaterialStatisticRequest) (*v1.GetAuditPlanStatisticsResponse, error) {
	var start_time *time.Time
	var end_time *time.Time
	if req.GetYear() != "" {
		// 判断是否为年份
		year_time, err := time.Parse("2006", req.GetYear())

		if err != nil {
			return nil, errors.New("年份格式错误")
		}

		// 获取年份的开始时间和结束时间
		year_time_start := time.Date(year_time.Year(), 1, 1, 0, 0, 0, 0, time.Local)
		year_time_end := time.Date(year_time.Year(), 12, 31, 23, 59, 59, 0, time.Local)

		start_time = &year_time_start
		end_time = &year_time_end
	}

	// 获取统计数据
	statistics, err := uc.auditPlanRepo.GetAuditPlanStatistics(ctx, start_time, end_time)

	if err != nil {
		return nil, err
	}

	// 获取用时统计
	use_time_statistics, err := uc.auditPlanRepo.GetAuditPlanUseTime(ctx, start_time, end_time)

	if err != nil {
		return nil, err
	}

	statistics.UseHour = use_time_statistics

	// 获取用户数统计
	user_count_statistics, err := uc.auditPlanRepo.GetAuditPlanUserCount(ctx, start_time, end_time)

	if err != nil {
		return nil, err
	}

	statistics.UserCount = user_count_statistics

	// 获取部门数统计
	organization_count_statistics, err := uc.auditPlanRepo.GetAuditPlanOrganizationCount(ctx, start_time, end_time)

	if err != nil {
		return nil, err
	}

	statistics.OrganizationCount = organization_count_statistics

	return statistics, nil
}

// 获取盘点计划时间分布统计
func (uc *WmsStatisticsUsecase) GetAuditPlanTimeStatistics(ctx context.Context, req *v1.GetAuditMaterialStatisticRequest) (*v1.GetAuditPlanTimeStatisticsResponse, error) {
	var start_time *time.Time
	var end_time *time.Time
	if req.GetYear() != "" {
		// 判断是否为年份
		year_time, err := time.Parse("2006", req.GetYear())

		if err != nil {
			return nil, errors.New("年份格式错误")
		}

		// 获取年份的开始时间和结束时间
		year_time_start := time.Date(year_time.Year(), 1, 1, 0, 0, 0, 0, time.Local)
		year_time_end := time.Date(year_time.Year(), 12, 31, 23, 59, 59, 0, time.Local)

		start_time = &year_time_start
		end_time = &year_time_end
	}

	fmt.Println(start_time, end_time)

	// 获取统计数据
	statistics, err := uc.auditPlanRepo.GetAuditPlanTimeStatistics(ctx, start_time, end_time)

	if err != nil {
		return nil, err
	}

	res := &v1.GetAuditPlanTimeStatisticsResponse{
		Data: statistics,
	}

	return res, nil

}

// 获取票据统计
func (uc *WmsStatisticsUsecase) GetDocumentStatistics(ctx context.Context, req *v1.GetDocumentStatisticsRequest) (*v1.GetDocumentStatisticsResponse, error) {
	// 获取统计数据
	statistics, err := uc.documentRepo.GetDocumentStatistics(ctx, req)

	if err != nil {
		return nil, err
	}

	return statistics, nil
}

// 获取票据用户统计
func (uc *WmsStatisticsUsecase) GetDocumentUserStatistics(ctx context.Context, req *v1.GetDocumentStatisticsRequest) (*v1.GetDocumentUserStatisticsResponse, error) {
	// 获取统计数据
	statistics, err := uc.documentRepo.GetDocumentUserStatistics(ctx, req)

	if err != nil {
		return nil, err
	}

	if len(statistics) == 0 {
		return &v1.GetDocumentUserStatisticsResponse{
			Data: []*v1.DocumentUserStatistics{},
		}, nil

	}

	user_ids := arrayutil.Map(statistics, func(item *v1.DocumentUserStatistics) string {
		return item.UserId
	})

	users, err := uc.userRepo.GetUsersByIds(ctx, user_ids)

	if err != nil {
		return nil, err
	}

	if len(users) == 0 {
		return &v1.GetDocumentUserStatisticsResponse{
			Data: []*v1.DocumentUserStatistics{},
		}, nil
	}

	user_map := arrayutil.ToMap(users, func(item *sysv1.SysUser) (string, *sysv1.SysUser) {
		return item.Id, item
	})

	// 合并用户名称到统计数据
	for _, item := range statistics {
		user := user_map[item.UserId]

		if user == nil {
			item.UserName = ""
		}

		if user.GetNickname() == "" {
			item.UserName = user.GetUsername()
		} else {
			item.UserName = user.GetNickname()
		}
	}

	res := &v1.GetDocumentUserStatisticsResponse{
		Data: statistics,
	}

	return res, nil
}

// 获取票据时间统计
func (uc *WmsStatisticsUsecase) GetDocumentTimeStatistics(ctx context.Context, req *v1.GetDocumentStatisticsRequest) (*v1.GetDocumentTimeStatisticsResponse, error) {
	// 获取统计数据
	statistics, err := uc.documentRepo.GetDocumentTimeStatistics(ctx, req)

	if err != nil {
		return nil, err
	}

	var res = &v1.GetDocumentTimeStatisticsResponse{
		Data: statistics,
	}

	return res, nil

}

type TimeGroup struct {
	Name      string
	StartTime time.Time
	EndTime   time.Time
}

type TimeStatistics struct {
	Name  string
	Count int
}
