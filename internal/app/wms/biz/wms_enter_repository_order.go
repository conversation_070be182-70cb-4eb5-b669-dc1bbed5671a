package biz

import (
	"context"
	"errors"
	"fmt"
	"kratos-mono-demo/internal/app/wms/biz/event_manager"
	"kratos-mono-demo/internal/pkg/go-utils/arrayutil"
	"kratos-mono-demo/internal/pkg/go-utils/event"
	"kratos-mono-demo/internal/pkg/go-utils/stringutil"
	"strings"
	"time"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	"kratos-mono-demo/internal/pkg/go-utils/trans"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cast"
)

const (
	ENTER_REPOSITORY_TYPE_PURCHASE   = "purchase"   // 采购入库
	ENTER_REPOSITORY_TYPE_TRANSFER   = "transfer"   // 调拨入库
	ENTER_REPOSITORY_TYPE_RETURN     = "return"     // 退还入库
	ENTER_REPOSITORY_TYPE_AUDIT      = "audit"      // 盘点入库
	ENTER_REPOSITORY_TYPE_UP_TO_DOWN = "up_to_down" // 下发入库
	ENTER_REPOSITORY_TYPE_OTHER      = "other"      // 其他入库

	ENTER_REPOSITORY_STATUS_WAITING = "1"  // 待入库
	ENTER_REPOSITORY_STATUS_CANCEL  = "-1" // 撤回入库
	ENTER_REPOSITORY_STATUS_CONFIRM = "3"  // 验收入库
)

type WmsEnterRepositoryOrderRepo interface {
	ListWmsEnterRepositoryOrder(ctx context.Context, req *v1.ListWmsEnterRepositoryOrderRequest) (*v1.ListWmsEnterRepositoryOrderResponse, error)
	GetWmsEnterRepositoryOrder(ctx context.Context, id string) (*v1.WmsEnterRepositoryOrder, error)
	CreateWmsEnterRepositoryOrder(ctx context.Context, req *v1.CreateWmsEnterRepositoryOrderRequest) (*v1.WmsEnterRepositoryOrder, error)
	UpdateWmsEnterRepositoryOrder(ctx context.Context, req *v1.UpdateWmsEnterRepositoryOrderRequest) (*v1.WmsEnterRepositoryOrder, error)
	DeleteWmsEnterRepositoryOrder(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsEnterRepositoryOrder(ctx context.Context, ids []string) (bool, error)

	// 根据关联单号查询入库单
	ListWmsEnterRepositoryOrderByRelationOrderNo(ctx context.Context, relationOrderNo string) (*v1.ListWmsEnterRepositoryOrderResponse, error)

	// 入库统计数据
	GetEnterRepositoryTimeStatistics(ctx context.Context, time_group []*TimeGroup) ([]*TimeStatistics, error)
}

type WmsEnterRepositoryOrderUsecase struct {
	log          *log.Helper
	eventManager *event_manager.EventManager
	repo         WmsEnterRepositoryOrderRepo
	tm           entutils.Transaction
	logRepo      WmsOperateLogRepo
	documentRepo WmsDocumentRepo
	// 资产
	materialRepo            WmsMaterialRepo
	orderDetailRepo         WmsEnterRepositoryOrderDetailRepo
	purchaseOrderRepo       WmsPurchaseOrderRepo
	purchaseOrderDetailRepo WmsPurchaseOrderDetailRepo
	equipmentRepo           WmsEquipmentRepo
	materialLogRepo         WmsMaterialLogRepo
	transferOrderRepo       WmsTransferOrderRepo
	returnOrderRepo         WmsReturnOrderRepo
}

func NewWmsEnterRepositoryOrderUsecase(
	eventManager *event_manager.EventManager,
	repo WmsEnterRepositoryOrderRepo,
	logRepo WmsOperateLogRepo,
	documentRepo WmsDocumentRepo,
	materialRepo WmsMaterialRepo,
	orderDetailRepo WmsEnterRepositoryOrderDetailRepo,
	purchaseOrderRepo WmsPurchaseOrderRepo,
	purchaseOrderDetailRepo WmsPurchaseOrderDetailRepo,
	equipmentRepo WmsEquipmentRepo,
	logger log.Logger,
	tm entutils.Transaction,
	materialLogRepo WmsMaterialLogRepo,
	transferOrderRepo WmsTransferOrderRepo,
	returnOrderRepo WmsReturnOrderRepo,
) *WmsEnterRepositoryOrderUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_enter_repository_order/usecase"))

	uc := &WmsEnterRepositoryOrderUsecase{
		log:                     l,
		eventManager:            eventManager,
		repo:                    repo,
		tm:                      tm,
		logRepo:                 logRepo,
		documentRepo:            documentRepo,
		materialRepo:            materialRepo,
		orderDetailRepo:         orderDetailRepo,
		purchaseOrderRepo:       purchaseOrderRepo,
		purchaseOrderDetailRepo: purchaseOrderDetailRepo,
		equipmentRepo:           equipmentRepo,
		materialLogRepo:         materialLogRepo,
		transferOrderRepo:       transferOrderRepo,
		returnOrderRepo:         returnOrderRepo,
	}

	// 监听采购单事件
	eventManager.PurchaseOrder.RegisterListener(event_manager.EventPurchaseOrder, uc.HandlePurchaseOrderEvent)

	return uc
}

func (uc *WmsEnterRepositoryOrderUsecase) ListWmsEnterRepositoryOrder(ctx context.Context, req *v1.ListWmsEnterRepositoryOrderRequest) (*v1.ListWmsEnterRepositoryOrderResponse, error) {
	return uc.repo.ListWmsEnterRepositoryOrder(ctx, req)
}

func (uc *WmsEnterRepositoryOrderUsecase) GetWmsEnterRepositoryOrder(ctx context.Context, id string) (*v1.WmsEnterRepositoryOrder, error) {
	return uc.repo.GetWmsEnterRepositoryOrder(ctx, id)
}

func (uc *WmsEnterRepositoryOrderUsecase) CreateWmsEnterRepositoryOrder(ctx context.Context, req *v1.CreateWmsEnterRepositoryOrderRequest) (*v1.WmsEnterRepositoryOrder, error) {
	var order *v1.WmsEnterRepositoryOrder
	var err error
	_err := uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		order, err = uc.repo.CreateWmsEnterRepositoryOrder(ctx, req)
		if err != nil {
			return err
		}

		log := fmt.Sprintf("创建入库单: %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_ENTER_REPOSITORY_ORDER, log)
		if err != nil {
			return err
		}

		source := fmt.Sprintf("入库单: %s", trans.StringValue(order.OrderNo))
		err = uc.documentRepo.BatchCreateUpdateSystemDocuments(ctx, source, DOCUMENT_ENTER_REPOSITORY_ORDER_CATEGEORY, order.Id, req)

		return err
	})
	return order, _err
}

func (uc *WmsEnterRepositoryOrderUsecase) checkOrderStatus(ctx context.Context, id string) error {
	order, err := uc.repo.GetWmsEnterRepositoryOrder(ctx, id)
	if err != nil {
		return err
	}

	// 入库状态为验收入库时，不允许修改
	if trans.StringValue(order.Status) == ENTER_REPOSITORY_STATUS_CONFIRM {
		return fmt.Errorf("入库单已验收入库，请勿重复操作")
	}

	return nil
}

// 添加入库单明细到资产
func (uc *WmsEnterRepositoryOrderUsecase) appendOrderDetailsToMaterial(ctx context.Context, order *v1.WmsEnterRepositoryOrder) error {
	var err error

	detailRes, err := uc.orderDetailRepo.ListWmsEnterRepositoryOrderDetailByOrderID(ctx, order.Id)
	if err != nil {
		return err
	}

	detail_items := detailRes.Items

	// 如果是退还入库, 合并数据到原有资产
	// 如果是采购入库、下发入库、盘点入库、调拨入库、其他入库，创建合并资产
	switch trans.StringValue(order.Type) {
	case ENTER_REPOSITORY_TYPE_TRANSFER:
		err = uc.transferEnterRepository(ctx, order, detail_items)
	case ENTER_REPOSITORY_TYPE_RETURN:
		err = uc.returnEnterRepository(ctx, order, detail_items)
	default:
		err = uc.defaultEnterRepository(ctx, detail_items)
	}

	if err != nil {
		return err
	}

	// 更新资产日志
	err = uc.CreateMaterialLogs(ctx, order, detail_items)

	return err
}

func (uc *WmsEnterRepositoryOrderUsecase) UpdateWmsEnterRepositoryOrder(ctx context.Context, req *v1.UpdateWmsEnterRepositoryOrderRequest) (*v1.WmsEnterRepositoryOrder, error) {
	var order *v1.WmsEnterRepositoryOrder
	var err error

	err = uc.checkOrderStatus(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	_err := uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		order, err = uc.repo.UpdateWmsEnterRepositoryOrder(ctx, req)
		if err != nil {
			return err
		}

		log := fmt.Sprintf("更新入库单: %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_ENTER_REPOSITORY_ORDER, log)
		if err != nil {
			return err
		}

		source := fmt.Sprintf("入库单: %s", trans.StringValue(order.OrderNo))
		err = uc.documentRepo.BatchCreateUpdateSystemDocuments(ctx, source, DOCUMENT_ENTER_REPOSITORY_ORDER_CATEGEORY, order.Id, req)

		// 入库状态为验收入库时
		if req.GetStatus() == ENTER_REPOSITORY_STATUS_CONFIRM {
			// 检查订单详情信息
			err = uc.checkDetails(ctx, order.Id)
			if err != nil {
				return err
			}

			// 添加入库单明细到资产
			err = uc.appendOrderDetailsToMaterial(ctx, order)
			if err != nil {
				return err
			}

			// 发送事件
			status := cast.ToInt(trans.StringValue(req.Status))
			err = uc.eventManager.EnterRepositoryOrder.Dispatch(ctx, event_manager.EventEnterRepositoryOrder, event_manager.EnterRepositoryOrderEventPayload{
				ID:              req.Id,
				Type:            event_manager.EnterRepositoryOrderType(trans.StringValue(order.Type)),
				RelationOrderNo: trans.StringValue(order.RelationOrderNo),
				Status:          event_manager.EnterRepositoryOrderStatus(status),
			})

			if err != nil {
				return err
			}
		}

		return err
	})
	return order, _err
}

func (uc *WmsEnterRepositoryOrderUsecase) DeleteWmsEnterRepositoryOrder(ctx context.Context, id string) (bool, error) {
	err := uc.checkOrderStatus(ctx, id)
	if err != nil {
		return false, err
	}

	order, err := uc.repo.GetWmsEnterRepositoryOrder(ctx, id)
	if err != nil {
		return false, err
	}

	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		if order.GetRelationOrderNo() != "" {
			// 更新关联单号状态
			err = uc.updateRelationOrderStatusCancel(ctx, order)
			if err != nil {
				return err
			}
		}

		// 删除入库单
		_, err = uc.repo.DeleteWmsEnterRepositoryOrder(ctx, id)
		if err != nil {
			return err
		}

		// 删除入库单明细
		err = uc.orderDetailRepo.DeleteWmsEnterRepositoryOrderDetailByOrderID(ctx, id)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return false, err
	}

	return true, nil
}

func (uc *WmsEnterRepositoryOrderUsecase) MultiDeleteWmsEnterRepositoryOrder(ctx context.Context, ids []string) (bool, error) {
	return false, nil
}

func (uc *WmsEnterRepositoryOrderUsecase) GetRepositoryMap(details []*v1.WmsPurchaseOrderDetail) map[string][]*v1.WmsPurchaseOrderDetail {
	repositoryMap := make(map[string][]*v1.WmsPurchaseOrderDetail)
	// 先获取有几个仓库
	for _, detail := range details {
		if detail.Equipment == nil {
			continue
		}

		if stringutil.IsEmpty(detail.RepositoryId) {
			continue
		}

		repositoryId := trans.StringValue(detail.RepositoryId)
		if repositoryMap[repositoryId] == nil {
			repositoryMap[repositoryId] = []*v1.WmsPurchaseOrderDetail{}
		}

		repositoryMap[repositoryId] = append(repositoryMap[repositoryId], detail)
	}
	return repositoryMap
}

// 更新入库单的数量
func (uc *WmsEnterRepositoryOrderUsecase) updateOrderCount(ctx context.Context, orderID string) error {
	if orderID == "" {
		return nil
	}

	detailList, err := uc.orderDetailRepo.ListWmsEnterRepositoryOrderDetailByOrderID(ctx, orderID)
	if err != nil {
		return err
	}
	_, err = uc.repo.UpdateWmsEnterRepositoryOrder(ctx, &v1.UpdateWmsEnterRepositoryOrderRequest{
		Id:           orderID,
		EquipmentNum: trans.Uint32(uint32(detailList.Total)),
	})

	return err
}

func (uc *WmsEnterRepositoryOrderUsecase) CheckRFIDCode(Items []*v1.WmsEnterRepositoryOrderDetail) error {
	strbuidler := strings.Builder{}
	for _, item := range Items {

		if stringutil.IsEmpty(item.Code) {
			strbuidler.WriteString(fmt.Sprintf("设备名称: %s, 设备型号: %s\n", item.GetName(), item.GetModelNo()))
		}
	}

	if strbuidler.Len() > 0 {
		return fmt.Errorf("以下设备没有RFID编码: \n%s", strbuidler.String())
	}

	return nil
}

func (uc *WmsEnterRepositoryOrderUsecase) HandlePurchaseOrderEvent(ctx context.Context, eventType event.EventType, payload event_manager.PurchaseOrderEventPayload) error {
	// 如果是入库状态
	if payload.Status == event_manager.PurchaseOrderStatusInbound {
		order, err := uc.purchaseOrderRepo.GetWmsPurchaseOrder(ctx, payload.ID)
		if err != nil {
			return err
		}

		details, err := uc.purchaseOrderDetailRepo.ListWmsPurchaseOrderDetailByOrderID(ctx, order.Id)
		if err != nil {
			return err
		}

		equipment_ids := arrayutil.Map(details.Items, func(detail *v1.WmsPurchaseOrderDetail) string {
			return detail.GetEquipmentId()
		})

		equipments, err := uc.equipmentRepo.GetWmsEquipmentsByIds(ctx, equipment_ids)

		if err != nil {
			return err
		}

		// 先按仓库分组，不同的仓库需要创建不同的入库单
		repositoryMap := uc.GetRepositoryMap(details.Items)

		// 创建入库单
		err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {

			for repositoryId, details := range repositoryMap {
				enterOrder, err := uc.repo.CreateWmsEnterRepositoryOrder(ctx, &v1.CreateWmsEnterRepositoryOrderRequest{
					ContractUrls:    order.ContractUrls,
					InvoiceUrls:     order.InvoiceUrls,
					AuditUrls:       order.AuditUrls,
					OtherUrls:       order.OtherUrls,
					Type:            trans.String(ENTER_REPOSITORY_TYPE_PURCHASE),  // 采购入库
					RelationOrderNo: order.OrderNo,                                 // 关联采购单号
					EnterTime:       trans.TimeToStrPtr(time.Now()),                // 入库时间，先按当前时间
					RepositoryId:    &repositoryId,                                 // 仓库ID
					Status:          trans.String(ENTER_REPOSITORY_STATUS_WAITING), // 待入库
					Remark:          trans.StringValue(order.Remark),
				})
				if err != nil {
					return err
				}

				// 创建明细
				for _, detail := range details {
					find_equipment := arrayutil.Find(equipments, func(equipment *v1.WmsEquipment) bool {
						return equipment.Id == detail.GetEquipmentId()
					})

					if find_equipment == nil {
						return errors.New(fmt.Sprintf("设备不存在: %s", detail.GetName()))
					}

					if find_equipment.IsOneMaterialOneCode {
						// 如果是一个物资一个编码，则需要分拆, 否则不拆分
						for i := 0; i < int(detail.GetNum()); i++ {
							_, err := uc.orderDetailRepo.CreateWmsEnterRepositoryOrderDetail(ctx, &v1.CreateWmsEnterRepositoryOrderDetailRequest{
								EnterRepositoryOrderId: trans.String(enterOrder.Id),
								EquipmentTypeId:        detail.EquipmentTypeId,
								EquipmentId:            detail.EquipmentId,
								Name:                   &detail.Equipment.Name,
								ModelNo:                &detail.Equipment.ModelNo,
								MeasureUnitId:          detail.MeasureUnitId,
								ProviderId:             detail.ProviderId,
								Num:                    trans.Uint32(1),
								Price:                  detail.Price,
								ToRepositoryId:         &repositoryId,
								OrderNo:                enterOrder.OrderNo,
								Feature:                detail.Feature,
							})
							if err != nil {
								return err
							}
						}
					} else {
						_, err := uc.orderDetailRepo.CreateWmsEnterRepositoryOrderDetail(ctx, &v1.CreateWmsEnterRepositoryOrderDetailRequest{
							EnterRepositoryOrderId: trans.String(enterOrder.Id),
							EquipmentTypeId:        detail.EquipmentTypeId,
							EquipmentId:            detail.EquipmentId,
							Name:                   &detail.Equipment.Name,
							ModelNo:                &detail.Equipment.ModelNo,
							MeasureUnitId:          detail.MeasureUnitId,
							ProviderId:             detail.ProviderId,
							Num:                    detail.Num,
							Price:                  detail.Price,
							ToRepositoryId:         &repositoryId,
							OrderNo:                enterOrder.OrderNo,
							Feature:                detail.Feature,
						})
						if err != nil {
							return err
						}
					}
				}

				err = uc.updateOrderCount(ctx, enterOrder.Id)
				if err != nil {
					return err
				}
			}

			return nil
		})
		if err != nil {
			return err
		}
	}
	return nil
}

// 默认入库方式 采购入库、下发入库、盘点入库、其他入库
func (uc *WmsEnterRepositoryOrderUsecase) defaultEnterRepository(ctx context.Context, details []*v1.WmsEnterRepositoryOrderDetail) error {
	// 获取在库的物资，如果存在则更新资产数量， 不存在则创建
	keys := arrayutil.Map(details, func(detail *v1.WmsEnterRepositoryOrderDetail) string {
		return uc.materialRepo.GenerateMaterialKey(
			detail.GetToRepositoryId(),
			detail.GetRepositoryAreaId(),
			detail.GetRepositoryPositionId(),
			detail.GetCode(),
			detail.GetFeature(),
			"",
		)
	})

	db_materials, err := uc.materialRepo.GetMaterialsByKeysAndStatusAndEquipmentStatus(ctx, keys, MATERIAL_STATUS_AVAILABLE, MATERIAL_MAINTAIN_STATUS_NORMAL)

	if err != nil {
		return err
	}

	for _, item := range details {
		key := uc.materialRepo.GenerateMaterialKey(
			item.GetToRepositoryId(),
			item.GetRepositoryAreaId(),
			item.GetRepositoryPositionId(),
			item.GetCode(),
			item.GetFeature(),
			"",
		)

		find_material := arrayutil.Find(db_materials, func(material *v1.WmsMaterial) bool {
			return key == material.GetKey()
		})

		if find_material != nil {
			// 如果存在且是一物一码，则不允许重复入库
			if find_material.Equipment != nil && find_material.Equipment.IsOneMaterialOneCode {
				return fmt.Errorf("设备名称: %s, 设备型号: %s, 一物一码, 不能重复入库", item.GetName(), item.GetModelNo())
			}

			// 如果存在不是一物一码，则更新数量
			_, err := uc.materialRepo.UpdateWmsMaterial(ctx, &v1.UpdateWmsMaterialRequest{
				Id:  find_material.Id,
				Key: key,
				Num: trans.Uint32(trans.Uint32Value(find_material.Num) + trans.Uint32Value(item.Num)),
			})

			if err != nil {
				return err
			}
		} else {
			_, err := uc.materialRepo.CreateWmsMaterial(ctx, &v1.CreateWmsMaterialRequest{
				Code:                 item.Code,
				Key:                  key,
				Name:                 item.Name,
				ModelNo:              item.ModelNo,
				EquipmentId:          item.EquipmentId,
				EquipmentTypeId:      item.EquipmentTypeId,
				RepositoryId:         item.ToRepositoryId,
				RepositoryAreaId:     item.RepositoryAreaId,
				RepositoryPositionId: item.RepositoryPositionId,
				ProviderId:           item.ProviderId,
				MeasureUnitId:        item.MeasureUnitId,
				Num:                  item.Num,
				Price:                item.Price,
				ExpireTime:           item.ExpireTime,
				Status:               MATERIAL_STATUS_AVAILABLE,
				Feature:              item.Feature,
			})

			if err != nil {
				return err
			}
		}
	}

	return err
}

// 调拨入库
func (uc *WmsEnterRepositoryOrderUsecase) transferEnterRepository(ctx context.Context, order *v1.WmsEnterRepositoryOrder, details []*v1.WmsEnterRepositoryOrderDetail) error {
	// clogger.PrintlnWithJSON("调拨入库-order", order)
	// clogger.PrintlnWithJSON("调拨入库-details", details)
	// keys := arrayutil.Map(details, func(detail *v1.WmsEnterRepositoryOrderDetail) string {
	// 	return uc.materialRepo.GenerateMaterialKey(
	// 		detail.GetFromRepositoryId(),
	// 		detail.GetRepositoryAreaId(),
	// 		detail.GetRepositoryPositionId(),
	// 		detail.GetCode(),
	// 		detail.GetFeature(),
	// 		"",
	// })

	var err error

	transferOrder, err := uc.transferOrderRepo.GetWmsTransferOrderByOrderNo(ctx, order.GetRelationOrderNo())
	if err != nil {
		return err
	}

	// 判断是否已出库
	if transferOrder.GetStatus() != TRANSFER_ORDER_STATUS_OUT_REPOSITORY {
		return fmt.Errorf("调拨单未出库，无法入库")
	}

	for _, item := range details {
		_, err = uc.materialRepo.SplitMaterial(ctx,
			item.GetMaterialId(),
			item.GetRepositoryPositionId(),
			nil,
			trans.Int32(MATERIAL_STATUS_AVAILABLE),
			trans.Int32(MATERIAL_MAINTAIN_STATUS_NORMAL),
			int32(item.GetNum()),
			nil,
		)

		if err != nil {
			return err
		}
	}

	// 设置调拨单为已完成
	err = uc.updateTransferOrderStatus(ctx, transferOrder.GetOrderNo(), TRANSFER_ORDER_STATUS_COMPLETED)
	if err != nil {
		return err
	}

	return err
}

// 退还入库
func (uc *WmsEnterRepositoryOrderUsecase) returnEnterRepository(ctx context.Context, order *v1.WmsEnterRepositoryOrder, details []*v1.WmsEnterRepositoryOrderDetail) error {
	var err error

	// 获取退还单
	return_order, err := uc.returnOrderRepo.GetWmsReturnOrderByOrderNo(ctx, order.GetRelationOrderNo())
	if err != nil {
		return err
	}

	for _, item := range details {
		_, err = uc.materialRepo.SplitMaterial(ctx,
			item.GetMaterialId(),
			item.GetRepositoryPositionId(),
			trans.String(""),
			trans.Int32(MATERIAL_STATUS_AVAILABLE),
			trans.Int32(MATERIAL_MAINTAIN_STATUS_NORMAL),
			int32(item.GetNum()),
			nil,
		)

		if err != nil {
			return err
		}
	}

	// 更新退还单状态
	err = uc.updateReturnOrderStatus(ctx, return_order.GetOrderNo(), RETURN_ORDER_STATUS_COMPLETED)
	if err != nil {
		return err
	}

	return err
}

// 检查订单详情信息
func (uc *WmsEnterRepositoryOrderUsecase) checkDetails(ctx context.Context, orderID string) error {
	detailList, err := uc.orderDetailRepo.ListWmsEnterRepositoryOrderDetailByOrderID(ctx, orderID)
	if err != nil {
		return err
	}

	for _, detail := range detailList.Items {
		if stringutil.IsEmpty(detail.Code) {
			return fmt.Errorf("设备名称: %s, 编码不能为空", detail.GetName())
		}

		if detail.GetNum() == 0 {
			return fmt.Errorf("设备名称: %s, 数量不能为0", detail.GetName())
		}

		if detail.GetRepositoryAreaId() == "" {
			return fmt.Errorf("设备名称: %s, 库区不能为空", detail.GetName())
		}

		if detail.GetRepositoryPositionId() == "" {
			return fmt.Errorf("设备名称: %s, 库位不能为空", detail.GetName())
		}
	}

	equipment_ids := arrayutil.Map(detailList.Items, func(detail *v1.WmsEnterRepositoryOrderDetail) string {
		return detail.GetEquipmentId()
	})

	equipments, err := uc.equipmentRepo.GetWmsEquipmentsByIds(ctx, equipment_ids)

	if err != nil {
		return err
	}

	for _, detail := range detailList.Items {
		find_equipment := arrayutil.Find(equipments, func(equipment *v1.WmsEquipment) bool {
			return equipment.Id == detail.GetEquipmentId()
		})

		if find_equipment == nil {
			return fmt.Errorf("设备名称: %s, 设备型号: %s, 设备不存在", detail.GetName(), detail.GetModelNo())
		}

		if find_equipment.IsOneMaterialOneCode && detail.GetNum() != 1 {
			return fmt.Errorf("设备名称: %s, 设备型号: %s, 一物一码, 数量必须为1", detail.GetName(), detail.GetModelNo())
		}
	}

	return nil
}

// 创建资产日志
func (uc *WmsEnterRepositoryOrderUsecase) CreateMaterialLogs(ctx context.Context, order *v1.WmsEnterRepositoryOrder, details []*v1.WmsEnterRepositoryOrderDetail) error {
	behavior := ""

	switch order.GetType() {
	case ENTER_REPOSITORY_TYPE_PURCHASE:
		behavior = MATERIAL_LOG_BEHAVIOR_PURCHASE_ENTER_REPOSITORY
	case ENTER_REPOSITORY_TYPE_TRANSFER:
		behavior = MATERIAL_LOG_BEHAVIOR_TRANSFER_ENTER_REPOSITORY
	case ENTER_REPOSITORY_TYPE_RETURN:
		behavior = MATERIAL_LOG_BEHAVIOR_RETURN_ENTER_REPOSITORY
	case ENTER_REPOSITORY_TYPE_AUDIT:
		behavior = MATERIAL_LOG_BEHAVIOR_AUDIT_ENTER_REPOSITORY
	case ENTER_REPOSITORY_TYPE_UP_TO_DOWN:
		behavior = MATERIAL_LOG_BEHAVIOR_UPTODOWN_ENTER_REPOSITORY
	case ENTER_REPOSITORY_TYPE_OTHER:
		behavior = MATERIAL_LOG_BEHAVIOR_OTHER_ENTER_REPOSITORY
	}

	if behavior == "" {
		return nil
	}

	// 如果是采购入库，还要记录采购日志
	if behavior == MATERIAL_LOG_BEHAVIOR_PURCHASE_ENTER_REPOSITORY && order.GetRelationOrderNo() != "" {
		purchaseOrder, err := uc.purchaseOrderRepo.GetWmsPurchaseOrderByOrderNo(ctx, order.GetRelationOrderNo())
		if err != nil {
			return err
		}

		purchaseTime := purchaseOrder.CreatedAt
		purchaseUser := purchaseOrder.CreatedBy
		purchaseBehavior := MATERIAL_LOG_BEHAVIOR_PURCHASE

		for _, detail := range details {
			_, err := uc.materialLogRepo.CreateWmsMaterialLog(ctx, &v1.CreateWmsMaterialLogRequest{
				Code:                 detail.Code,
				CodeType:             detail.CodeType,
				EquipmentTypeId:      detail.EquipmentTypeId,
				EquipmentId:          detail.EquipmentId,
				Feature:              detail.Feature,
				RepositoryId:         order.RepositoryId,
				RepositoryAreaId:     detail.RepositoryAreaId,
				RepositoryPositionId: detail.RepositoryPositionId,
				Num:                  detail.Num,
				Name:                 detail.Name,
				Price:                detail.Price,
				MeasureUnitId:        detail.MeasureUnitId,
				ModelNo:              detail.ModelNo,
				Remark:               order.GetRemark(),
				ProviderId:           detail.ProviderId,
				ExpireTime:           detail.ExpireTime,
				Behavior:             trans.String(purchaseBehavior),
				CreatedBy:            purchaseUser,
				CreatedAt:            purchaseTime,
			})

			if err != nil {
				return err
			}
		}
	}

	for _, detail := range details {
		_, err := uc.materialLogRepo.CreateWmsMaterialLog(ctx, &v1.CreateWmsMaterialLogRequest{
			Code:                 detail.Code,
			CodeType:             detail.CodeType,
			EquipmentTypeId:      detail.EquipmentTypeId,
			EquipmentId:          detail.EquipmentId,
			Feature:              detail.Feature,
			RepositoryId:         order.RepositoryId,
			RepositoryAreaId:     detail.RepositoryAreaId,
			RepositoryPositionId: detail.RepositoryPositionId,
			Num:                  detail.Num,
			Name:                 detail.Name,
			Price:                detail.Price,
			MeasureUnitId:        detail.MeasureUnitId,
			ModelNo:              detail.ModelNo,
			Remark:               order.GetRemark(),
			ProviderId:           detail.ProviderId,
			ExpireTime:           detail.ExpireTime,
			Behavior:             trans.String(behavior),
			CreatedBy:            order.CreatedBy,
			CreatedAt:            order.CreatedAt,
		})

		if err != nil {
			return err
		}
	}

	return nil
}

// 更新关联单号状态
func (uc *WmsEnterRepositoryOrderUsecase) updateRelationOrderStatusCancel(ctx context.Context, order *v1.WmsEnterRepositoryOrder) error {
	relation_order_no := order.GetRelationOrderNo()

	if relation_order_no == "" {
		return nil
	}

	switch order.GetType() {
	case ENTER_REPOSITORY_TYPE_TRANSFER: // 调拨出库
		return uc.updateTransferOrderStatus(ctx, relation_order_no, TRANSFER_ORDER_STATUS_CANCELLED)
	case ENTER_REPOSITORY_TYPE_RETURN: // 退还出库
		return uc.updateReturnOrderStatus(ctx, relation_order_no, RETURN_ORDER_STATUS_CANCELLED)
	default:
		return nil
	}
}

// 更新调拨单状态
func (uc *WmsEnterRepositoryOrderUsecase) updateTransferOrderStatus(ctx context.Context, orderNo string, status string) error {
	transferOrder, err := uc.transferOrderRepo.GetWmsTransferOrderByOrderNo(ctx, orderNo)
	if err != nil {
		return err
	}

	if transferOrder.GetStatus() == TRANSFER_ORDER_STATUS_COMPLETED {
		return nil
	}

	_, err = uc.transferOrderRepo.UpdateWmsTransferOrder(ctx, &v1.UpdateWmsTransferOrderRequest{
		Id:     transferOrder.GetId(),
		Status: trans.String(status),
	})

	return err
}

// 更新退还单状态
func (uc *WmsEnterRepositoryOrderUsecase) updateReturnOrderStatus(ctx context.Context, orderNo string, status string) error {
	return_order, err := uc.returnOrderRepo.GetWmsReturnOrderByOrderNo(ctx, orderNo)
	if err != nil {
		return err
	}

	if return_order.GetStatus() == RETURN_ORDER_STATUS_COMPLETED {
		return nil
	}

	_, err = uc.returnOrderRepo.UpdateWmsReturnOrder(ctx, &v1.UpdateWmsReturnOrderRequest{
		Id:     return_order.GetId(),
		Status: trans.String(status),
	})

	return err
}
