package biz

import (
	"context"
	"fmt"
	"time"

	v1 "kratos-mono-demo/gen/api/wms/v1"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	"kratos-mono-demo/internal/pkg/go-utils/arrayutil"
	"kratos-mono-demo/internal/pkg/go-utils/trans"

	"github.com/go-kratos/kratos/v2/log"
)

const (
	BORROW_ORDER_STATUS_CANCELLED  = "-1"  // 已取消
	BORROW_ORDER_STATUS_REJECTED   = "-2"  // 已驳回
	BORROW_ORDER_STATUS_PENDING    = "0"   // 待提交
	BORROW_ORDER_STATUS_PROCESSING = "1"   // 审批中
	BORROW_ORDER_STATUS_APPROVED   = "2"   // 已审批
	BORROW_ORDER_STATUS_COMPLETED  = "100" // 已完成
)

type WmsBorrowOrderRepo interface {
	ListWmsBorrowOrder(ctx context.Context, req *v1.ListWmsBorrowOrderRequest) (*v1.ListWmsBorrowOrderResponse, error)
	GetWmsBorrowOrder(ctx context.Context, id string) (*v1.WmsBorrowOrder, error)
	CreateWmsBorrowOrder(ctx context.Context, req *v1.CreateWmsBorrowOrderRequest) (*v1.WmsBorrowOrder, error)
	UpdateWmsBorrowOrder(ctx context.Context, req *v1.UpdateWmsBorrowOrderRequest) (*v1.WmsBorrowOrder, error)
	DeleteWmsBorrowOrder(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsBorrowOrder(ctx context.Context, ids []string) (bool, error)
	GetWmsBorrowOrdersByIds(ctx context.Context, ids []string) ([]*v1.WmsBorrowOrder, error)

	// 根据关联流程单号查询借用单
	GetWmsBorrowOrderByOrderNo(ctx context.Context, orderNo string) (*v1.WmsBorrowOrder, error)

	// 获取借用单申请用户的组织名称
	GetWmsBorrowOrderUserOrganizationNameMap(ctx context.Context, ids []string) (map[string]string, error)

	// 根据状态获取借用单
	GetWmsBorrowOrdersByStatus(ctx context.Context, status []string) ([]*v1.WmsBorrowOrder, error)
}

type WmsBorrowOrderUsecase struct {
	log                          *log.Helper
	repo                         WmsBorrowOrderRepo
	detailRepo                   WmsBorrowOrderDetailRepo
	tm                           entutils.Transaction
	documentRepo                 WmsDocumentRepo
	logRepo                      WmsOperateLogRepo
	equipmentRepo                WmsEquipmentRepo
	outRepositoryOrderRepo       WmsOutRepositoryOrderRepo
	outRepositoryOrderDetailRepo WmsOutRepositoryOrderDetailRepo
}

func NewWmsBorrowOrderUsecase(
	repo WmsBorrowOrderRepo,
	detailRepo WmsBorrowOrderDetailRepo,
	tm entutils.Transaction,
	logger log.Logger,
	documentRepo WmsDocumentRepo,
	logRepo WmsOperateLogRepo,
	equipmentRepo WmsEquipmentRepo,
	outRepositoryOrderRepo WmsOutRepositoryOrderRepo,
	outRepositoryOrderDetailRepo WmsOutRepositoryOrderDetailRepo,
) *WmsBorrowOrderUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_borrow_order/usecase"))
	return &WmsBorrowOrderUsecase{
		log:                          l,
		repo:                         repo,
		detailRepo:                   detailRepo,
		tm:                           tm,
		documentRepo:                 documentRepo,
		logRepo:                      logRepo,
		equipmentRepo:                equipmentRepo,
		outRepositoryOrderRepo:       outRepositoryOrderRepo,
		outRepositoryOrderDetailRepo: outRepositoryOrderDetailRepo,
	}
}

func (uc *WmsBorrowOrderUsecase) ListWmsBorrowOrder(ctx context.Context, req *v1.ListWmsBorrowOrderRequest) (*v1.ListWmsBorrowOrderResponse, error) {

	// // 自定义加入查询条件
	// newQuery := entgo.AppendQueryKeys(req.GetQuery(), map[string]string{
	// 	"createdBy": userId,
	// })
	// req.Query = &newQuery

	res, err := uc.repo.ListWmsBorrowOrder(ctx, req)
	if err != nil {
		return nil, err
	}

	items := res.Items

	uc.setDetails(ctx, items)  // 设置借用单明细
	uc.setUserInfo(ctx, items) // 设置借用单用户信息

	return res, nil
}

func (uc *WmsBorrowOrderUsecase) GetWmsBorrowOrder(ctx context.Context, id string) (*v1.WmsBorrowOrder, error) {
	res, err := uc.repo.GetWmsBorrowOrder(ctx, id)
	if err != nil {
		return nil, err
	}

	uc.setDetails(ctx, []*v1.WmsBorrowOrder{res})  // 设置借用单明细
	uc.setUserInfo(ctx, []*v1.WmsBorrowOrder{res}) // 设置借用单用户信息

	return res, nil
}

func (uc *WmsBorrowOrderUsecase) CreateWmsBorrowOrder(ctx context.Context, req *v1.CreateWmsBorrowOrderRequest) (*v1.WmsBorrowOrder, error) {
	var err error
	var order *v1.WmsBorrowOrder

	userId, ok := entutils.GetUserID(ctx)
	if !ok {
		return nil, entutils.ErrUserNotLogin
	}
	req.BorrowerId = &userId // 设置借用单申请人ID

	// 开启事务
	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		order, err = uc.repo.CreateWmsBorrowOrder(ctx, req)
		if err != nil {
			return err
		}

		// 创建借用单明细
		if len(req.Details) > 0 {
			for _, detail := range req.Details {
				detail.OrderId = &order.Id
				detailRes, err := uc.detailRepo.CreateWmsBorrowOrderDetail(ctx, detail)
				if err != nil {
					return err
				}

				order.Details = append(order.Details, detailRes)
			}
		}

		// 创建操作日志
		log := fmt.Sprintf("创建借用单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_BORROW_ORDER, log)
		if err != nil {
			return err
		}

		// 创建系统文档
		source := fmt.Sprintf("借用单: %s", trans.StringValue(order.OrderNo))
		err = uc.documentRepo.BatchCreateUpdateSystemDocuments(ctx, source, DOCUMENT_BORROW_ORDER_CATEGEORY, order.Id, req)
		if err != nil {
			return err
		}

		// 如果审批通过，处理借用
		if order.GetStatus() == BORROW_ORDER_STATUS_APPROVED {
			// 处理借用
			err = uc.handleBorrow(ctx, order)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return order, nil
}

func (uc *WmsBorrowOrderUsecase) UpdateWmsBorrowOrder(ctx context.Context, req *v1.UpdateWmsBorrowOrderRequest) (*v1.WmsBorrowOrder, error) {
	var err error
	var order *v1.WmsBorrowOrder

	// 开启事务
	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		order, err = uc.repo.UpdateWmsBorrowOrder(ctx, req)
		if err != nil {
			return err
		}

		// 更新借用单明细
		if len(req.Details) > 0 {
			// 删除借用单明细
			_, err = uc.detailRepo.DeleteWmsBorrowOrderDetailByOrderId(ctx, order.Id)
			if err != nil {
				return err
			}

			// 创建借用单明细
			for _, detail := range req.Details {
				detail.OrderId = &order.Id
				detailRes, err := uc.detailRepo.CreateWmsBorrowOrderDetail(ctx, detail)
				if err != nil {
					return err
				}

				order.Details = append(order.Details, detailRes)
			}
		}

		// 创建操作日志
		log := fmt.Sprintf("更新借用单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_BORROW_ORDER, log)
		if err != nil {
			return err
		}

		// 更新系统文档
		source := fmt.Sprintf("借用单: %s", trans.StringValue(order.OrderNo))
		err = uc.documentRepo.BatchCreateUpdateSystemDocuments(ctx, source, DOCUMENT_BORROW_ORDER_CATEGEORY, order.Id, req)
		if err != nil {
			return err
		}

		// 如果审批通过，处理借用
		if order.GetStatus() == BORROW_ORDER_STATUS_APPROVED {
			// 处理借用
			err = uc.handleBorrow(ctx, order)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return order, nil
}

func (uc *WmsBorrowOrderUsecase) DeleteWmsBorrowOrder(ctx context.Context, id string) (bool, error) {
	var err error

	order, err := uc.repo.GetWmsBorrowOrder(ctx, id)
	if err != nil {
		return false, err
	}

	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		_, err = uc.repo.DeleteWmsBorrowOrder(ctx, id)
		if err != nil {
			return err
		}

		// 删除借用单明细
		_, err = uc.detailRepo.DeleteWmsBorrowOrderDetailByOrderId(ctx, id)
		if err != nil {
			return err
		}

		// 创建操作日志
		log := fmt.Sprintf("删除借用单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, id, LOG_CATEGEORY_BORROW_ORDER, log)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return false, err
	}

	return true, nil
}

func (uc *WmsBorrowOrderUsecase) MultiDeleteWmsBorrowOrder(ctx context.Context, ids []string) (bool, error) {
	var err error

	orders, err := uc.repo.GetWmsBorrowOrdersByIds(ctx, ids)
	if err != nil {
		return false, err
	}

	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		_, err = uc.repo.MultiDeleteWmsBorrowOrder(ctx, ids)
		if err != nil {
			return err
		}

		// 删除借用单明细
		for _, id := range ids {
			_, err = uc.detailRepo.DeleteWmsBorrowOrderDetailByOrderId(ctx, id)
			if err != nil {
				return err
			}
		}

		// 创建操作日志
		for _, order := range orders {
			log := fmt.Sprintf("删除借用单 %s", trans.StringValue(order.OrderNo))
			_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_BORROW_ORDER, log)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return false, err
	}

	return true, nil
}

// 设置借用单明细
func (uc *WmsBorrowOrderUsecase) setDetails(ctx context.Context, orders []*v1.WmsBorrowOrder) {
	orderIds := make([]string, 0)
	for _, order := range orders {
		orderIds = append(orderIds, order.Id)
	}

	details, err := uc.detailRepo.GetWmsBorrowOrderDetailsByOrderIds(ctx, orderIds)
	if err != nil {
		return
	}

	// 设置借用单明细详细规格
	uc.setDetailsFeatureStr(ctx, details)

	detailsMap := make(map[string][]*v1.WmsBorrowOrderDetail)
	for _, detail := range details {
		detailsMap[detail.GetOrderId()] = append(detailsMap[detail.GetOrderId()], detail)
	}

	for _, order := range orders {
		order.Details = detailsMap[order.Id]
	}
}

// 设置借用单用户信息
func (uc *WmsBorrowOrderUsecase) setUserInfo(ctx context.Context, orders []*v1.WmsBorrowOrder) {
	ids := arrayutil.Map(orders, func(order *v1.WmsBorrowOrder) string {
		return order.Id
	})

	// 获取借用单申请用户的组织名称
	organization_name_map, err := uc.repo.GetWmsBorrowOrderUserOrganizationNameMap(ctx, ids)
	if err != nil {
		return
	}

	for _, order := range orders {
		order.CreateByOrganizationName = trans.String(organization_name_map[order.Id])
	}
}

// 设置借用单明细详细规格
func (uc *WmsBorrowOrderUsecase) setDetailsFeatureStr(ctx context.Context, details []*v1.WmsBorrowOrderDetail) {
	equipmentIds := arrayutil.Map(details, func(item *v1.WmsBorrowOrderDetail) string {
		return item.GetEquipmentId()
	})

	features, err := uc.equipmentRepo.GetWmsEquipmentFeaturesByIds(ctx, equipmentIds)

	if err != nil {
		uc.log.Errorf("get equipment features error: %v", err)
	}

	for _, detail := range details {
		feature := detail.GetFeature()

		if feature != nil {
			feature_str := uc.equipmentRepo.ConvertFeatureToFeatureStr(feature.AsMap(), features)
			detail.FeatureStr = trans.String(feature_str)
		}
	}
}

// 处理借用
func (uc *WmsBorrowOrderUsecase) handleBorrow(ctx context.Context, order *v1.WmsBorrowOrder) error {
	details, err := uc.detailRepo.GetWmsBorrowOrderDetailsByOrderIds(ctx, []string{order.Id})

	if err != nil {
		return err
	}

	equipmentIds := arrayutil.Map(details, func(detail *v1.WmsBorrowOrderDetail) string {
		return detail.GetEquipmentId()
	})

	// 获取装备列表
	equipments, err := uc.equipmentRepo.GetWmsEquipmentsByIds(ctx, equipmentIds)
	if err != nil {
		uc.log.Errorf("get wms equipments failed: %s", err.Error())
		return err
	}

	outRepositoryOrders := make([]*v1.CreateWmsOutRepositoryOrderRequest, 0)
	outRepositoryOrderDetailsMap := make(map[string][]*v1.CreateWmsOutRepositoryOrderDetailRequest)

	for _, detail := range details {
		findOutRepositoryOrder := arrayutil.Find(outRepositoryOrders, func(outRepositoryOrder *v1.CreateWmsOutRepositoryOrderRequest) bool {
			if detail.GetRepositoryId() == "" {
				detail.RepositoryId = trans.String("8fe84ed6-c5b0-4ded-8a89-6413ef007055") // 默认仓库
			}

			return outRepositoryOrder.GetRepositoryId() == detail.GetRepositoryId()
		})

		if findOutRepositoryOrder == nil {
			outRepositoryOrder := &v1.CreateWmsOutRepositoryOrderRequest{
				Type:            trans.String(OUTREPOSITORYTYPEBORROWED),
				RepositoryId:    detail.RepositoryId,
				RelationOrderNo: order.OrderNo,
				OutTime:         trans.TimeToStrPtr(time.Now()),
				Status:          trans.String(OUTREPOSITORYSTATUSPENDING), // OUTREPOSITORYSTATUSPENDING 待出库
			}

			outRepositoryOrders = append(outRepositoryOrders, outRepositoryOrder)

			findOutRepositoryOrder = outRepositoryOrder
		}

		findEquipment := arrayutil.Find(equipments, func(equipment *v1.WmsEquipment) bool {
			return equipment.Id == detail.GetEquipmentId()
		})

		if findEquipment == nil {
			continue
		}

		// 如果是一物一码， 则需要根据数量创建对应数量的出库明细
		if findEquipment.IsOneMaterialOneCode {
			for i := 0; i < int(detail.GetNum()); i++ {
				req := &v1.CreateWmsOutRepositoryOrderDetailRequest{
					EquipmentId:     &findEquipment.Id,
					EquipmentTypeId: &findEquipment.EquipmentTypeId,
					Num:             trans.Uint32(1),
					RepositoryId:    detail.RepositoryId,
					Name:            &findEquipment.Name,
					ModelNo:         &findEquipment.ModelNo,
					Feature:         detail.Feature,
				}

				outRepositoryOrderDetailsMap[findOutRepositoryOrder.GetRepositoryId()] = append(outRepositoryOrderDetailsMap[findOutRepositoryOrder.GetRepositoryId()], req)
			}
		} else {
			req := &v1.CreateWmsOutRepositoryOrderDetailRequest{
				EquipmentId:     &findEquipment.Id,
				EquipmentTypeId: &findEquipment.EquipmentTypeId,
				Num:             trans.Uint32(1),
				RepositoryId:    detail.RepositoryId,
				Name:            &findEquipment.Name,
				ModelNo:         &findEquipment.ModelNo,
				Feature:         detail.Feature,
			}

			outRepositoryOrderDetailsMap[findOutRepositoryOrder.GetRepositoryId()] = append(outRepositoryOrderDetailsMap[findOutRepositoryOrder.GetRepositoryId()], req)
		}
	}

	for _, outRepositoryOrder := range outRepositoryOrders {
		// 创建出库单
		outRepositoryOrderRes, err := uc.outRepositoryOrderRepo.CreateWmsOutRepositoryOrder(ctx, outRepositoryOrder)
		if err != nil {
			return err
		}

		// 创建出库单明细
		for _, detail := range outRepositoryOrderDetailsMap[outRepositoryOrder.GetRepositoryId()] {
			detail.OutRepositoryOrderId = &outRepositoryOrderRes.Id
			detail.OrderNo = outRepositoryOrderRes.OrderNo

			_, err = uc.outRepositoryOrderDetailRepo.CreateWmsOutRepositoryOrderDetail(ctx, detail)
			if err != nil {
				return err
			}
		}

		log := fmt.Sprintf("创建出库单: %s", trans.StringValue(outRepositoryOrderRes.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, outRepositoryOrderRes.Id, "out_repository_order", log)
		if err != nil {
			return err
		}
	}

	return nil
}
