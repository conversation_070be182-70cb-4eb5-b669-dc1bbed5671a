package biz

import (
	"context"
	"fmt"
	"time"

	v1 "kratos-mono-demo/gen/api/wms/v1"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	"kratos-mono-demo/internal/pkg/go-utils/arrayutil"
	"kratos-mono-demo/internal/pkg/go-utils/trans"

	"github.com/go-kratos/kratos/v2/log"
)

const (
	RETURN_ORDER_STATUS_CANCELLED  = "-1"  // 已取消
	RETURN_ORDER_STATUS_REJECTED   = "-2"  // 已驳回
	RETURN_ORDER_STATUS_PENDING    = "0"   // 待提交
	RETURN_ORDER_STATUS_PROCESSING = "1"   // 审批中
	RETURN_ORDER_STATUS_APPROVED   = "2"   // 已审批
	RETURN_ORDER_STATUS_COMPLETED  = "100" // 已完成
)

type WmsReturnOrderRepo interface {
	ListWmsReturnOrder(ctx context.Context, req *v1.ListWmsReturnOrderRequest) (*v1.ListWmsReturnOrderResponse, error)
	GetWmsReturnOrder(ctx context.Context, id string) (*v1.WmsReturnOrder, error)
	CreateWmsReturnOrder(ctx context.Context, req *v1.CreateWmsReturnOrderRequest) (*v1.WmsReturnOrder, error)
	UpdateWmsReturnOrder(ctx context.Context, req *v1.UpdateWmsReturnOrderRequest) (*v1.WmsReturnOrder, error)
	DeleteWmsReturnOrder(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsReturnOrder(ctx context.Context, ids []string) (bool, error)

	GetWmsReturnOrdersByIds(ctx context.Context, ids []string) ([]*v1.WmsReturnOrder, error)
	GetWmsReturnOrderUserOrganizationNameMap(ctx context.Context, ids []string) (map[string]string, error)

	// 根据关联单号查询退还单
	GetWmsReturnOrderByOrderNo(ctx context.Context, orderNo string) (*v1.WmsReturnOrder, error)
}

type WmsReturnOrderUsecase struct {
	log                            *log.Helper
	repo                           WmsReturnOrderRepo
	tm                             entutils.Transaction
	detailRepo                     WmsReturnOrderDetailRepo
	logRepo                        WmsOperateLogRepo
	documentRepo                   WmsDocumentRepo
	equipmentRepo                  WmsEquipmentRepo
	enterRepositoryOrderRepo       WmsEnterRepositoryOrderRepo
	enterRepositoryOrderDetailRepo WmsEnterRepositoryOrderDetailRepo
}

func NewWmsReturnOrderUsecase(
	repo WmsReturnOrderRepo,
	detailRepo WmsReturnOrderDetailRepo,
	tm entutils.Transaction,
	logger log.Logger,
	logRepo WmsOperateLogRepo,
	documentRepo WmsDocumentRepo,
	equipmentRepo WmsEquipmentRepo,
	enterRepositoryOrderRepo WmsEnterRepositoryOrderRepo,
	enterRepositoryOrderDetailRepo WmsEnterRepositoryOrderDetailRepo,
) *WmsReturnOrderUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_return_order/usecase"))
	return &WmsReturnOrderUsecase{
		log:                            l,
		repo:                           repo,
		detailRepo:                     detailRepo,
		tm:                             tm,
		logRepo:                        logRepo,
		documentRepo:                   documentRepo,
		equipmentRepo:                  equipmentRepo,
		enterRepositoryOrderRepo:       enterRepositoryOrderRepo,
		enterRepositoryOrderDetailRepo: enterRepositoryOrderDetailRepo,
	}
}

func (uc *WmsReturnOrderUsecase) ListWmsReturnOrder(ctx context.Context, req *v1.ListWmsReturnOrderRequest) (*v1.ListWmsReturnOrderResponse, error) {

	// // 自定义加入查询条件
	// newQuery := entgo.AppendQueryKeys(req.GetQuery(), map[string]string{
	// 	"createdBy": userId,
	// })
	// req.Query = &newQuery

	res, err := uc.repo.ListWmsReturnOrder(ctx, req)
	if err != nil {
		return nil, err
	}

	items := res.Items

	uc.setDetails(ctx, items)  // 设置明细
	uc.setUserInfo(ctx, items) // 设置用户信息

	return res, nil
}

func (uc *WmsReturnOrderUsecase) GetWmsReturnOrder(ctx context.Context, id string) (*v1.WmsReturnOrder, error) {
	res, err := uc.repo.GetWmsReturnOrder(ctx, id)
	if err != nil {
		return nil, err
	}

	uc.setDetails(ctx, []*v1.WmsReturnOrder{res})  // 设置明细
	uc.setUserInfo(ctx, []*v1.WmsReturnOrder{res}) // 设置用户信息

	return res, nil
}

func (uc *WmsReturnOrderUsecase) CreateWmsReturnOrder(ctx context.Context, req *v1.CreateWmsReturnOrderRequest) (*v1.WmsReturnOrder, error) {
	var err error
	var order *v1.WmsReturnOrder

	// 开启事务
	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		order, err = uc.repo.CreateWmsReturnOrder(ctx, req)
		if err != nil {
			return err
		}

		// 创建明细
		if len(req.Details) > 0 {
			for _, detail := range req.Details {
				detail.OrderId = &order.Id
				detailRes, err := uc.detailRepo.CreateWmsReturnOrderDetail(ctx, detail)
				if err != nil {
					return err
				}

				order.Details = append(order.Details, detailRes)
			}
		}

		// 创建操作日志
		log := fmt.Sprintf("创建退还单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_RETURN_ORDER, log)
		if err != nil {
			return err
		}

		// 创建系统文档
		source := fmt.Sprintf("退还单: %s", trans.StringValue(order.OrderNo))
		err = uc.documentRepo.BatchCreateUpdateSystemDocuments(ctx, source, DOCUMENT_RETURN_ORDER_CATEGEORY, order.Id, req)
		if err != nil {
			return err
		}

		// 如果审批通过，处理退还
		if order.GetStatus() == RETURN_ORDER_STATUS_APPROVED {
			// 处理退还
			err = uc.handleReturn(ctx, order)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return order, nil
}

func (uc *WmsReturnOrderUsecase) UpdateWmsReturnOrder(ctx context.Context, req *v1.UpdateWmsReturnOrderRequest) (*v1.WmsReturnOrder, error) {
	var err error
	var order *v1.WmsReturnOrder

	// 开启事务
	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		order, err = uc.repo.UpdateWmsReturnOrder(ctx, req)
		if err != nil {
			return err
		}

		// 更新明细
		if len(req.Details) > 0 {
			// 删除明细
			_, err = uc.detailRepo.DeleteWmsReturnOrderDetailByOrderId(ctx, order.Id)
			if err != nil {
				return err
			}

			// 创建明细
			for _, detail := range req.Details {
				detail.OrderId = &order.Id
				detailRes, err := uc.detailRepo.CreateWmsReturnOrderDetail(ctx, detail)
				if err != nil {
					return err
				}

				order.Details = append(order.Details, detailRes)
			}
		}

		// 创建操作日志
		log := fmt.Sprintf("更新退还单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_RETURN_ORDER, log)
		if err != nil {
			return err
		}

		// 更新系统文档
		source := fmt.Sprintf("退还单: %s", trans.StringValue(order.OrderNo))
		err = uc.documentRepo.BatchCreateUpdateSystemDocuments(ctx, source, DOCUMENT_RETURN_ORDER_CATEGEORY, order.Id, req)
		if err != nil {
			return err
		}

		// 如果审批通过，创建入库单
		if order.GetStatus() == RETURN_ORDER_STATUS_APPROVED {
			// 处理退还
			err = uc.handleReturn(ctx, order)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return order, nil
}

func (uc *WmsReturnOrderUsecase) DeleteWmsReturnOrder(ctx context.Context, id string) (bool, error) {
	var err error

	order, err := uc.repo.GetWmsReturnOrder(ctx, id)
	if err != nil {
		return false, err
	}

	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		_, err = uc.repo.DeleteWmsReturnOrder(ctx, id)
		if err != nil {
			return err
		}

		// 删除明细
		_, err = uc.detailRepo.DeleteWmsReturnOrderDetailByOrderId(ctx, id)
		if err != nil {
			return err
		}

		// 创建操作日志
		log := fmt.Sprintf("删除退还单 %s", trans.StringValue(order.OrderNo))
		_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, id, LOG_CATEGEORY_RETURN_ORDER, log)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return false, err
	}

	return true, nil
}

func (uc *WmsReturnOrderUsecase) MultiDeleteWmsReturnOrder(ctx context.Context, ids []string) (bool, error) {
	var err error

	orders, err := uc.repo.GetWmsReturnOrdersByIds(ctx, ids)
	if err != nil {
		return false, err
	}

	err = uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		_, err = uc.repo.MultiDeleteWmsReturnOrder(ctx, ids)
		if err != nil {
			return err
		}

		// 删除明细
		for _, id := range ids {
			_, err = uc.detailRepo.DeleteWmsReturnOrderDetailByOrderId(ctx, id)
			if err != nil {
				return err
			}
		}

		// 创建操作日志
		for _, order := range orders {
			log := fmt.Sprintf("删除退还单 %s", trans.StringValue(order.OrderNo))
			_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, order.Id, LOG_CATEGEORY_RETURN_ORDER, log)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return false, err
	}

	return true, nil
}

// 设置退还单明细
func (uc *WmsReturnOrderUsecase) setDetails(ctx context.Context, orders []*v1.WmsReturnOrder) {
	orderIds := make([]string, 0)
	for _, order := range orders {
		orderIds = append(orderIds, order.Id)
	}

	details, err := uc.detailRepo.GetWmsReturnOrderDetailsByOrderIds(ctx, orderIds)
	if err != nil {
		return
	}

	// 设置退还单明细详细规格
	uc.setDetailsFeatureStr(ctx, details)

	detailsMap := make(map[string][]*v1.WmsReturnOrderDetail)
	for _, detail := range details {
		detailsMap[detail.GetOrderId()] = append(detailsMap[detail.GetOrderId()], detail)
	}

	for _, order := range orders {
		order.Details = detailsMap[order.Id]
	}
}

// 设置退还单用户信息
func (uc *WmsReturnOrderUsecase) setUserInfo(ctx context.Context, orders []*v1.WmsReturnOrder) {
	ids := arrayutil.Map(orders, func(order *v1.WmsReturnOrder) string {
		return order.Id
	})

	// 获取借用单申请用户的组织名称
	organization_name_map, err := uc.repo.GetWmsReturnOrderUserOrganizationNameMap(ctx, ids)
	if err != nil {
		return
	}

	for _, order := range orders {
		order.CreateByOrganizationName = trans.String(organization_name_map[order.Id])
	}
}

// 设置退还单明细详细规格
func (uc *WmsReturnOrderUsecase) setDetailsFeatureStr(ctx context.Context, details []*v1.WmsReturnOrderDetail) {
	equipmentIds := arrayutil.Map(details, func(item *v1.WmsReturnOrderDetail) string {
		return item.GetEquipmentId()
	})

	features, err := uc.equipmentRepo.GetWmsEquipmentFeaturesByIds(ctx, equipmentIds)

	if err != nil {
		uc.log.Errorf("get equipment features error: %v", err)
	}

	for _, detail := range details {
		feature := detail.GetFeature()

		if feature != nil {
			feature_str := uc.equipmentRepo.ConvertFeatureToFeatureStr(feature.AsMap(), features)
			detail.FeatureStr = trans.String(feature_str)
		}
	}
}

func (uc *WmsReturnOrderUsecase) handleReturn(ctx context.Context, order *v1.WmsReturnOrder) error {
	// 获取退还单详情
	details, err := uc.detailRepo.GetWmsReturnOrderDetailsByOrderIds(ctx, []string{order.Id})
	if err != nil {
		return err
	}

	// 创建入库单
	enterRepositoryOrderRes, err := uc.enterRepositoryOrderRepo.CreateWmsEnterRepositoryOrder(ctx, &v1.CreateWmsEnterRepositoryOrderRequest{
		Type:            trans.String(ENTER_REPOSITORY_TYPE_RETURN),
		RepositoryId:    order.RepositoryId,
		RelationOrderNo: order.OrderNo,
		EnterTime:       trans.TimeToStrPtr(time.Now()),
		Status:          trans.String(ENTER_REPOSITORY_STATUS_WAITING),
	})
	if err != nil {
		return err
	}

	// 创建入库单明细
	for _, detail := range details {
		_, err = uc.enterRepositoryOrderDetailRepo.CreateWmsEnterRepositoryOrderDetail(ctx, &v1.CreateWmsEnterRepositoryOrderDetailRequest{
			MaterialId:             detail.MaterialId,
			EnterRepositoryOrderId: trans.String(enterRepositoryOrderRes.Id),
			ToRepositoryId:         order.RepositoryId,
			EquipmentTypeId:        detail.EquipmentTypeId,
			EquipmentId:            detail.EquipmentId,
			Code:                   detail.Code,
			MeasureUnitId:          detail.MeasureUnitId,
			Num:                    detail.Num,
			Name:                   detail.MaterialName,
			ModelNo:                detail.ModelNo,
			Feature:                detail.Feature,
		})
		if err != nil {
			return err
		}
	}

	// 创建操作日志
	log := fmt.Sprintf("创建入库单: %s", trans.StringValue(enterRepositoryOrderRes.OrderNo))
	_, err = uc.logRepo.CreateWmsOperateLogEx(ctx, enterRepositoryOrderRes.Id, LOG_CATEGEORY_ENTER_REPOSITORY_ORDER, log)
	if err != nil {
		return err
	}

	return nil
}
