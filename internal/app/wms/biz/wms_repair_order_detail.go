package biz

import (
	"context"

	v1 "kratos-mono-demo/gen/api/wms/v1"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"

	"github.com/go-kratos/kratos/v2/log"
)

type WmsRepairOrderDetailRepo interface {
	ListWmsRepairOrderDetail(ctx context.Context, req *v1.ListWmsRepairOrderDetailRequest) (*v1.ListWmsRepairOrderDetailResponse, error)
	GetWmsRepairOrderDetail(ctx context.Context, id string) (*v1.WmsRepairOrderDetail, error)
	CreateWmsRepairOrderDetail(ctx context.Context, req *v1.CreateWmsRepairOrderDetailRequest) (*v1.WmsRepairOrderDetail, error)
	UpdateWmsRepairOrderDetail(ctx context.Context, req *v1.UpdateWmsRepairOrderDetailRequest) (*v1.WmsRepairOrderDetail, error)
	DeleteWmsRepairOrderDetail(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsRepairOrderDetail(ctx context.Context, ids []string) (bool, error)

	DeleteWmsRepairOrderDetailByOrderId(ctx context.Context, orderId string) (bool, error)
	GetWmsRepairOrderDetailsByOrderIds(ctx context.Context, orderIds []string) ([]*v1.WmsRepairOrderDetail, error)
}

type WmsRepairOrderDetailUsecase struct {
	log  *log.Helper
	repo WmsRepairOrderDetailRepo
	tm   entutils.Transaction
}

func NewWmsRepairOrderDetailUsecase(repo WmsRepairOrderDetailRepo, tm entutils.Transaction, logger log.Logger) *WmsRepairOrderDetailUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_repair_order_detail/usecase"))
	return &WmsRepairOrderDetailUsecase{
		log:  l,
		repo: repo,
		tm:   tm,
	}
}

func (uc *WmsRepairOrderDetailUsecase) ListWmsRepairOrderDetail(ctx context.Context, req *v1.ListWmsRepairOrderDetailRequest) (*v1.ListWmsRepairOrderDetailResponse, error) {

	// // 自定义加入查询条件
	// newQuery := entgo.AppendQueryKeys(req.GetQuery(), map[string]string{
	// 	"createdBy": userId,
	// })
	// req.Query = &newQuery

	return uc.repo.ListWmsRepairOrderDetail(ctx, req)
}

func (uc *WmsRepairOrderDetailUsecase) GetWmsRepairOrderDetail(ctx context.Context, id string) (*v1.WmsRepairOrderDetail, error) {
	return uc.repo.GetWmsRepairOrderDetail(ctx, id)
}

func (uc *WmsRepairOrderDetailUsecase) CreateWmsRepairOrderDetail(ctx context.Context, req *v1.CreateWmsRepairOrderDetailRequest) (*v1.WmsRepairOrderDetail, error) {
	return uc.repo.CreateWmsRepairOrderDetail(ctx, req)
}

func (uc *WmsRepairOrderDetailUsecase) UpdateWmsRepairOrderDetail(ctx context.Context, req *v1.UpdateWmsRepairOrderDetailRequest) (*v1.WmsRepairOrderDetail, error) {
	return uc.repo.UpdateWmsRepairOrderDetail(ctx, req)
}

func (uc *WmsRepairOrderDetailUsecase) DeleteWmsRepairOrderDetail(ctx context.Context, id string) (bool, error) {
	return uc.repo.DeleteWmsRepairOrderDetail(ctx, id)
}

func (uc *WmsRepairOrderDetailUsecase) MultiDeleteWmsRepairOrderDetail(ctx context.Context, ids []string) (bool, error) {
	return uc.repo.MultiDeleteWmsRepairOrderDetail(ctx, ids)
}
