package biz

import (
	"context"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	systemBiz "kratos-mono-demo/internal/app/system/biz"
	entutils "kratos-mono-demo/internal/pkg/ent-utils"

	"github.com/go-kratos/kratos/v2/log"
)

type WmsRepositoryRepo interface {
	ListWmsRepository(ctx context.Context, req *v1.ListWmsRepositoryRequest) (*v1.ListWmsRepositoryResponse, error)
	GetWmsRepository(ctx context.Context, id string) (*v1.WmsRepository, error)
	CreateWmsRepository(ctx context.Context, req *v1.CreateWmsRepositoryRequest) (*v1.WmsRepository, error)
	UpdateWmsRepository(ctx context.Context, req *v1.UpdateWmsRepositoryRequest) (*v1.WmsRepository, error)
	DeleteWmsRepository(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsRepository(ctx context.Context, ids []string) (bool, error)
	// 获取仓库树，包含仓库下的库区、库位，用于快捷查询
	ListWmsRepositoryTree(ctx context.Context, req *v1.ListWmsRepositoryRequest) (*v1.ListWmsRepositoryTreeResponse, error)

	// 获取仓库数据根据ids
	GetRepositoriesByIds(ctx context.Context, ids []string) ([]*v1.WmsRepository, error)

	// 根据组织ids获取仓库列表
	ListWmsRepositoryByOrganizationIds(ctx context.Context, req *v1.ListWmsRepositoryRequest, organizationIds []string) (*v1.ListWmsRepositoryResponse, error)
}

type WmsRepositoryUsecase struct {
	log  *log.Helper
	repo WmsRepositoryRepo

	userOrganizationRepo systemBiz.SysUserOrganizationRepo
	organizationRepo     systemBiz.SysOrganizationRepo
}

func NewWmsRepositoryUsecase(
	repo WmsRepositoryRepo,
	logger log.Logger,

	userOrganizationRepo systemBiz.SysUserOrganizationRepo,
	organizationRepo systemBiz.SysOrganizationRepo,
) *WmsRepositoryUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_repository/usecase"))
	return &WmsRepositoryUsecase{
		log:                  l,
		repo:                 repo,
		userOrganizationRepo: userOrganizationRepo,
		organizationRepo:     organizationRepo,
	}
}

func (uc *WmsRepositoryUsecase) ListWmsRepository(ctx context.Context, req *v1.ListWmsRepositoryRequest) (*v1.ListWmsRepositoryResponse, error) {
	return uc.repo.ListWmsRepository(ctx, req)
}

func (uc *WmsRepositoryUsecase) GetWmsRepository(ctx context.Context, id string) (*v1.WmsRepository, error) {
	return uc.repo.GetWmsRepository(ctx, id)
}

func (uc *WmsRepositoryUsecase) CreateWmsRepository(ctx context.Context, req *v1.CreateWmsRepositoryRequest) (*v1.WmsRepository, error) {
	return uc.repo.CreateWmsRepository(ctx, req)
}

func (uc *WmsRepositoryUsecase) UpdateWmsRepository(ctx context.Context, req *v1.UpdateWmsRepositoryRequest) (*v1.WmsRepository, error) {
	return uc.repo.UpdateWmsRepository(ctx, req)
}

func (uc *WmsRepositoryUsecase) DeleteWmsRepository(ctx context.Context, id string) (bool, error) {
	return uc.repo.DeleteWmsRepository(ctx, id)
}

func (uc *WmsRepositoryUsecase) MultiDeleteWmsRepository(ctx context.Context, ids []string) (bool, error) {
	return uc.repo.MultiDeleteWmsRepository(ctx, ids)
}

// 获取仓库树，包含仓库下的库区、库位，用于快捷查询
func (uc *WmsRepositoryUsecase) ListWmsRepositoryTree(ctx context.Context, req *v1.ListWmsRepositoryRequest) (*v1.ListWmsRepositoryTreeResponse, error) {
	return uc.repo.ListWmsRepositoryTree(ctx, req)
}

// 获取我的仓库
func (uc *WmsRepositoryUsecase) ListMyWmsRepository(ctx context.Context, req *v1.ListWmsRepositoryRequest) (*v1.ListWmsRepositoryResponse, error) {
	userId, ok := entutils.GetUserID(ctx)
	if !ok {
		return nil, entutils.ErrUserNotLogin
	}

	// 获取用户的组织
	organizationIds, err := uc.userOrganizationRepo.GetOrganizationIdsByUserId(ctx, userId)
	if err != nil {
		return nil, err
	}

	// 获取组织的父组织，也就是继承
	parentOrganizationIds, err := uc.organizationRepo.GetParentIds(ctx, organizationIds)

	if err != nil {
		return nil, err
	}

	// 合并组织ids和父组织ids
	organizationIds = append(organizationIds, parentOrganizationIds...)

	// 根据组织ids获取仓库
	repositories, err := uc.repo.ListWmsRepositoryByOrganizationIds(ctx, req, organizationIds)

	return repositories, err
}
