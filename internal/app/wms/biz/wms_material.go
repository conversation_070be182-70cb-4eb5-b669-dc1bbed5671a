package biz

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	sysv1 "kratos-mono-demo/gen/api/system/v1"
	v1 "kratos-mono-demo/gen/api/wms/v1"

	"kratos-mono-demo/internal/app/wms/biz/event_manager"
	"kratos-mono-demo/internal/data/ent"
	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	"kratos-mono-demo/internal/pkg/go-utils/arrayutil"
	entgo "kratos-mono-demo/internal/pkg/go-utils/entgo/query"
	"kratos-mono-demo/internal/pkg/go-utils/event"
	"kratos-mono-demo/internal/pkg/go-utils/trans"
	jsonutils "kratos-mono-demo/internal/pkg/json-utils"

	sysBiz "kratos-mono-demo/internal/app/system/biz"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/structpb"
)

const (
	// 在库，可用状态
	MATERIAL_STATUS_AVAILABLE = 1
	// 已报废，不可用状态
	MATERIAL_STATUS_UNAVAILABLE = 0
	// 已领用，不可用状态
	MATERIAL_STATUS_CLAIMED = -1
	// 借用中，不可用状态
	MATERIAL_STATUS_BORROWED = -2
	// 维修中，不可用状态
	// MATERIAL_STATUS_REPAIRING = -3
	// 保养中，不可用状态
	// MATERIAL_STATUS_MAINTAINING = -4
	// 调拨中，不可用状态
	MATERIAL_STATUS_TRANSFERING = -5
	// 退还中，不可用状态
	MATERIAL_STATUS_RETURNING = -6

	// 维保状态
	// 正常（无维保）
	MATERIAL_MAINTAIN_STATUS_NORMAL = 1
	// 维修中
	MATERIAL_MAINTAIN_STATUS_REPAIRING = -1
	// 保养中
	MATERIAL_MAINTAIN_STATUS_MAINTAINING = -2

	// 物料类型, 固资
	MATERIAL_TYPE_STATIC = "static"
	// 物料类型, 物资
	MATERIAL_TYPE_COMMON = "common"

	// 资产报废方式, 审核报废
	MATERIAL_DISCARD_METHOD_APPROVE = 1
	// 资产报废方式, 上会报废
	MATERIAL_DISCARD_METHOD_MEETING = 2
	// 资产报废方式, 出库报废
	MATERIAL_DISCARD_METHOD_OUT_REPOSITORY = 3
)

type SplitOptions struct {
	ForceSplit bool    // 强制分裂
	OrderNo    *string // 关联单号
}

// 资产
type WmsMaterialRepo interface {
	ListWmsMaterial(ctx context.Context, req *v1.ListWmsMaterialRequest) (*v1.ListWmsMaterialResponse, error)
	GetWmsMaterial(ctx context.Context, id string) (*v1.WmsMaterial, error)
	CreateWmsMaterial(ctx context.Context, req *v1.CreateWmsMaterialRequest) (*v1.WmsMaterial, error)
	UpdateWmsMaterial(ctx context.Context, req *v1.UpdateWmsMaterialRequest) (*v1.WmsMaterial, error)
	DeleteWmsMaterial(ctx context.Context, id string) (bool, error)
	MultiDeleteWmsMaterial(ctx context.Context, ids []string) (bool, error)

	// FindWmsMaterialByCode 根据编码查找物料
	FindWmsMaterialByCode(ctx context.Context, code string) (*v1.WmsMaterial, error)
	// 批量创建资产
	BatchCreateWmsMaterial(ctx context.Context, reqs []*v1.CreateWmsMaterialRequest) ([]*v1.WmsMaterial, error)
	// 更新资产状态
	UpdateMaterialStatus(ctx context.Context, id string, status int32) error
	// 更新资产持有人
	UpdateMaterialOwner(ctx context.Context, id string, ownerId string) error
	// 根据编码列表查找物料数据
	QueryMaterialsByCodes(ctx context.Context, codes []string) ([]*v1.WmsMaterial, error)

	// 获取可用资产数量
	GetAvailableMaterialIds(ctx context.Context, userIds, repositoryIds, equipmentTypeIds string) ([]string, error)
	// 获取资产
	ListMaterialWithIds(ctx context.Context, ids []string) ([]*v1.WmsMaterial, error)
	ListMaterialWithCodes(ctx context.Context, codes []string) ([]*v1.WmsMaterial, error)

	GetProtoListFromEnt(userMap map[string]string, results []*ent.WmsMaterial, fieldMask []string) ([]*v1.WmsMaterial, error)

	// UpdateWmsMaterialApproveStatus 批量更新物资的审批状态
	UpdateWmsMaterialApproveStatus(ctx context.Context, ids []string, isApproving bool) error
	// GetAllMaterialIdByWmsApprovalId 通过wmsApprovalId 获取全部物资 ID
	GetAllMaterialIdByWmsApprovalId(ctx context.Context, wmsApprovalId string) ([]string, error)

	// RevertWmsMaterialStatus 回滚物资状态
	RevertWmsMaterialStatus(ctx context.Context, ids []string, newEquipmentStatus *int32) error

	// ListMaterialByIds 通过ids 获取物资
	ListMaterialByIds(ctx context.Context, ids []string) ([]*v1.WmsMaterial, error)

	// 根据关联单号获取物资
	GetWmsMaterialByRelationOrderNo(ctx context.Context, relationOrderNo string) ([]*v1.WmsMaterial, error)

	// 根据keys和物资状态、保养状态获取物资数据
	GetMaterialsByKeysAndStatusAndEquipmentStatus(ctx context.Context, keys []string, status int32, equipment_status int32) ([]*v1.WmsMaterial, error)

	// 根据ids和物资状态、保养状态获取物资数据
	GetMaterialsByIdsAndStatusAndEquipmentStatus(ctx context.Context, ids []string, status int32, equipment_status int32) ([]*v1.WmsMaterial, error)

	// 根据key和物资状态、保养状态获取物资数据
	GetMaterialsByKeyAndStatusAndEquipmentStatus(ctx context.Context, key string, status int32, equipment_status int32) (*v1.WmsMaterial, error)

	// 合并资产
	ConcatMaterial(ctx context.Context, fromId string, toId string, num int32) error

	// 分裂资产给仓库 带指针的参数，如果不需要更新，传nil
	SplitMaterial(ctx context.Context, fromId string, repositoryPositionId string, ownerId *string, status *int32, equipmentStatus *int32, num int32, options *SplitOptions) (*v1.WmsMaterial, error)

	// 根据 repository_id repository_area_id repository_position_id code feature  生成hash值, 用于比较确定物资唯一性
	GenerateMaterialKey(repository_id string, repository_area_id string, repository_position_id string, code string, feature *structpb.Struct, owner_id string) string

	// 根据设备id、仓库id、feature、 features获取库存数量
	GetFeatureCountByEquipmentIdAndRepositoryIdAndFeatures(ctx context.Context, equipmentId string, repositoryId *string, feature *structpb.Struct, features []*v1.EquipmentFeature) ([]*v1.EquipmentFeature, error)

	// 获取资产总数
	GetMaterialCount(ctx context.Context, startTime *time.Time, endTime *time.Time) (int, error)

	// 获取固资总数
	GetMaterialStaticsCount(ctx context.Context, repositoryId *string, repositoryAreaIds []string) (int, error)

	// 获取物资总数
	GetMaterialCommonCount(ctx context.Context, repositoryId *string, repositoryAreaIds []string) (int, error)

	// 获取库存总数
	GetMaterialAvailableCount(ctx context.Context) (int, error)

	// 获取在用总数
	GetMaterialUsingCount(ctx context.Context) (int, error)

	// 获取在用车辆总数
	GetCarUsingCount(ctx context.Context, req *v1.GetMaterialStatisticsRequest) (int, error)

	//获取固资维修中总数
	GetMaterialStaticsRepairingCount(ctx context.Context) (int, error)

	//获取物资维修中总数
	GetMaterialCommonRepairingCount(ctx context.Context) (int, error)

	// 获取报废资产总数
	GetMaterialUnavailableCount(ctx context.Context) (int, error)

	// 获取固资报废总数
	GetMaterialStaticsUnavailableCount(ctx context.Context) (int, error)

	// 获取物资报废总数
	GetMaterialCommonUnavailableCount(ctx context.Context) (int, error)

	// 获取库存装备分类统计
	GetMaterialEquipmentTypeStatistics(ctx context.Context, status int) ([]*v1.MaterialEquipmentTypeStatistics, error)

	// 获取库存仓库分类统计
	GetMaterialRepositoryStatistics(ctx context.Context) ([]*v1.MaterialRepositoryStatistics, error)

	// 获取资产申领组织分类统计
	GetMaterialOrganizationStatistics(ctx context.Context, startTime *time.Time, endTime *time.Time, page *int, pageSize *int) ([]*v1.MaterialOrganizationStatistics, error)

	// 获取资产服役年限统计
	GetMaterialTimeStatistics(ctx context.Context, time_group []*TimeGroup, organization_id string) ([]*v1.MaterialTimeStatistics, error)

	// 获取资产统计
	GetMaterialStatistics(ctx context.Context, req *v1.GetMaterialStatisticsRequest) (*v1.GetMaterialStatisticsResponse, error)

	// 获取车辆分类统计
	GetCarEquipmentTypeIdStatistics(ctx context.Context) ([]*v1.CarEquipmentTypeStatistics, error)

	// 获取车辆分类统计
	GetCarOrganizationIdStatistics(ctx context.Context) ([]*v1.CarOrganizationStatistics, error)

	// 获取仓库装备分类统计
	GetRepositoryEquipmentTypeStatistics(ctx context.Context, repositoryId string, repositoryAreaIds []string, equipmentTypeIds []string, status int, equipmentStatus int) ([]*EquipmentTypeGroup, error)

	// 获取仓库装备统计
	GetRepositoryEquipmentStatistics(ctx context.Context, repositoryId string, repositoryAreaId []string, equipmentTypeIds []string, status int, equipmentStatus int) ([]*RepositoryEquipmentGroup, error)

	// 获取装备列表资产的第一条数据
	GetFirstMaterialsByEquipmentIds(ctx context.Context, equipmentIds []string) ([]*v1.WmsMaterial, error)

	// 获取资产装备分类
	GetEquipmentTypeIds(ctx context.Context, repositoryId string, repositoryAreaIds []string, status int, equipmentStatus int) ([]string, error)

	// 统计资产库存按照仓库、装备、详细规格、计量单位分组
	GetMaterialStatisticsByRepositoryPositionAndEquipmentAndFeatureAndMeasureUnit(ctx context.Context, repositoryId string, repositoryAreaIds []string, equipmentTypeIds []string) ([]*MaterialStatisticsGroupRepositoryPositionAndEquipmentAndFeatureAndMeasureUnit, error)

	// 根据条件获取资产
	GetMaterialsByCondition(ctx context.Context, equipmentId string, repositoryId string, feature *structpb.Struct, code string) ([]*v1.WmsMaterial, error)
}

type WmsMaterialUsecase struct {
	log                       *log.Helper
	repo                      WmsMaterialRepo
	userRepo                  sysBiz.SysUserRepo
	tm                        entutils.Transaction
	eventManager              *event_manager.EventManager
	equipmentRepo             WmsEquipmentRepo
	sysPageCodeRepo           sysBiz.SysPageCodeRepo
	wmsApprovalTaskDetailRepo WmsApprovalTaskDetailRepo
}

type EquipmentTypeGroup struct {
	EquipmentTypeId string
	Count           uint32
}

type RepositoryEquipmentGroup struct {
	EquipmentId          string
	RepositoryId         string
	RepositoryAreaId     string
	RepositoryPositionId string
	Count                uint32
}

func NewWmsMaterialUsecase(
	repo WmsMaterialRepo,
	userRepo sysBiz.SysUserRepo,
	tm entutils.Transaction,
	eventManager *event_manager.EventManager,
	logger log.Logger,
	equipmentRepo WmsEquipmentRepo,
	sysPageCodeRepo sysBiz.SysPageCodeRepo,
) *WmsMaterialUsecase {
	l := log.NewHelper(log.With(logger, "module", "wms_material/usecase"))
	uc := &WmsMaterialUsecase{
		log:             l,
		repo:            repo,
		userRepo:        userRepo,
		tm:              tm,
		eventManager:    eventManager,
		equipmentRepo:   equipmentRepo,
		sysPageCodeRepo: sysPageCodeRepo,
	}

	// 注册事件， 关注入库单事件
	uc.eventManager.ApprovalTask.RegisterListener(event_manager.EventApprovalTask, uc.HandleApprovalTaskEvent)

	return uc
}

func (uc *WmsMaterialUsecase) ListWmsMaterial(ctx context.Context, req *v1.ListWmsMaterialRequest) (*v1.ListWmsMaterialResponse, error) {
	// 自定义加入查询条件
	newQuery := entgo.AppendQueryKeys(req.GetQuery(), map[string]string{
		"num__not": "0",
	})
	req.Query = &newQuery

	res, err := uc.repo.ListWmsMaterial(ctx, req)

	if err != nil {
		return nil, err
	}

	ownerIds := make([]string, 0)

	for _, item := range res.Items {
		if item.GetOwnerId() != "" {
			ownerIds = append(ownerIds, item.GetOwnerId())
		}
	}

	// 获取用户信息
	users, err := uc.userRepo.GetUsersByIds(ctx, ownerIds)

	if err != nil {
		return nil, err
	}

	user_map := arrayutil.ToMap(users, func(item *sysv1.SysUser) (string, *sysv1.SysUser) {
		return item.Id, item
	})

	for _, item := range res.Items {

		if item.GetOwnerId() != "" {
			user := user_map[item.GetOwnerId()]

			if user == nil {
				item.OwnerId = trans.String("")
			}

			if user.GetNickname() == "" {
				item.OwnerId = trans.String(user.GetUsername())
			} else {
				item.OwnerId = trans.String(user.GetNickname())
			}
		}
	}

	// 转换详细规格为字符串
	equipmentIds := arrayutil.Map(res.GetItems(), func(item *v1.WmsMaterial) string {
		return item.GetEquipmentId()
	})

	features, err := uc.equipmentRepo.GetWmsEquipmentFeaturesByIds(ctx, equipmentIds)

	if err != nil {
		uc.log.Errorf("get equipment features error: %v", err)
	}

	for _, detail := range res.GetItems() {
		feature := detail.GetFeature()

		if feature != nil {
			feature_str := uc.equipmentRepo.ConvertFeatureToFeatureStr(feature.AsMap(), features)
			detail.FeatureStr = trans.String(feature_str)
		}
	}

	return res, nil
}

func (uc *WmsMaterialUsecase) GetWmsMaterial(ctx context.Context, id string) (*v1.WmsMaterial, error) {
	res, err := uc.repo.GetWmsMaterial(ctx, id)

	if err != nil {
		return nil, err
	}

	// 转换详细规格为字符串
	features, err := uc.equipmentRepo.GetWmsEquipmentFeaturesByIds(ctx, []string{res.GetEquipmentId()})

	if err != nil {
		uc.log.Errorf("get equipment features error: %v", err)
	}

	feature := res.GetFeature()

	if feature != nil {
		feature_str := uc.equipmentRepo.ConvertFeatureToFeatureStr(feature.AsMap(), features)
		res.FeatureStr = trans.String(feature_str)
	}

	return res, err
}

func (uc *WmsMaterialUsecase) CreateWmsMaterial(ctx context.Context, req *v1.CreateWmsMaterialRequest) (*v1.WmsMaterial, error) {
	return uc.repo.CreateWmsMaterial(ctx, req)
}

func (uc *WmsMaterialUsecase) UpdateWmsMaterial(ctx context.Context, req *v1.UpdateWmsMaterialRequest) (*v1.WmsMaterial, error) {
	return uc.repo.UpdateWmsMaterial(ctx, req)
}

func (uc *WmsMaterialUsecase) DeleteWmsMaterial(ctx context.Context, id string) (bool, error) {
	return uc.repo.DeleteWmsMaterial(ctx, id)
}

func (uc *WmsMaterialUsecase) MultiDeleteWmsMaterial(ctx context.Context, ids []string) (bool, error) {
	return uc.repo.MultiDeleteWmsMaterial(ctx, ids)
}

func (uc *WmsMaterialUsecase) GetWmsMaterialByCode(ctx context.Context, code string) (*v1.WmsMaterial, error) {
	return uc.repo.FindWmsMaterialByCode(ctx, code)
}

func (uc *WmsMaterialUsecase) HandleApprovalTaskEvent(ctx context.Context, eventType event.EventType, payload event_manager.ApprovalTaskEventPayload) error {
	// 创建入库单
	err := uc.tm.EntInTx(ctx, func(ctx context.Context) error {
		if payload.Status == event_manager.ApprovalTaskStatusCancelled || payload.Status == event_manager.ApprovalTaskStatusApproved || payload.Status == event_manager.ApprovalTaskStatusRejected {
			// 审批流程结束，设置物资状态为未审批
			err := uc.repo.UpdateWmsMaterialApproveStatus(ctx, payload.LinkedMaterialIDs, false)
			if err != nil {
				return err
			}

			return nil
		}

		if len(payload.LinkedMaterialIDs) > 0 {
			// 关联了物资，设置物资状态为审批中
			err := uc.repo.UpdateWmsMaterialApproveStatus(ctx, payload.LinkedMaterialIDs, true)
			if err != nil {
				return err
			}
		}

		if len(payload.UnLinkedMaterialIDs) > 0 {
			// 取消关联了物资，设置物资状态为未审批
			err := uc.repo.UpdateWmsMaterialApproveStatus(ctx, payload.UnLinkedMaterialIDs, false)
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (uc *WmsMaterialUsecase) ListMyWmsMaterial(ctx context.Context, req *v1.ListWmsMaterialRequest) (*v1.ListWmsMaterialResponse, error) {
	userId, ok := entutils.GetUserID(ctx)
	if !ok {
		return nil, entutils.ErrUserNotLogin
	}

	// 自定义加入查询条件
	newQuery := entgo.AppendQueryKeys(req.GetQuery(), map[string]string{
		"ownerId": userId,
	})
	req.Query = &newQuery

	return uc.ListWmsMaterial(ctx, req)
}

// 获取物料详情，通过code、feature、repository_id、repository_area_id、repository_position_id
func (uc *WmsMaterialUsecase) GetWmsMaterialByCodeFeatureRepository(ctx context.Context, req *v1.GetWmsMaterialByCodeFeatureRepositoryRequest) (*v1.WmsMaterial, error) {
	query_map := map[string]string{}

	code := strings.TrimSpace(req.GetCode())
	if code != "" {
		query_map["code"] = code
	}

	if req.GetRepositoryId() != "" {
		query_map["repositoryId"] = req.GetRepositoryId()
	}

	if req.GetRepositoryAreaId() != "" {
		query_map["repositoryAreaId"] = req.GetRepositoryAreaId()
	}

	if req.GetRepositoryPositionId() != "" {
		query_map["repositoryPositionId"] = req.GetRepositoryPositionId()
	}

	if req.GetFeature() != nil {
		feature_map := req.GetFeature().AsMap()

		for key, value := range feature_map {
			query_map[fmt.Sprintf("feature__%v", key)] = fmt.Sprintf("%v", value)
		}
	}

	query_map["status"] = fmt.Sprintf("%d", MATERIAL_STATUS_AVAILABLE)
	query_map["equipmentStatus"] = fmt.Sprintf("%d", MATERIAL_MAINTAIN_STATUS_NORMAL)

	newQuery := entgo.AppendQueryKeys("", query_map)

	materials_res, err := uc.repo.ListWmsMaterial(ctx, &v1.ListWmsMaterialRequest{
		Query:    &newQuery,
		NoPaging: trans.Bool(true),
	})

	if err != nil {
		return nil, err
	}

	if len(materials_res.Items) == 0 {
		return nil, errors.New("未找到此装备")
	}

	material := materials_res.Items[0]

	return material, nil
}

// 获取页面代码
func (uc *WmsMaterialUsecase) GetWmsMaterialPageCode(ctx context.Context, req *v1.GetWmsMaterialPageCodeRequest) (*structpb.Struct, error) {
	code, err := uc.sysPageCodeRepo.GetSysPageCode(ctx, req.GetName())
	if err != nil {
		return nil, err
	}

	code_map := code.AsMap()

	materials, err := uc.repo.QueryMaterialsByCodes(ctx, []string{req.GetCode()})

	if err != nil {
		return nil, err
	}

	if len(materials) == 0 {
		return nil, nil
	}

	equipmentIds := arrayutil.Map(materials, func(item *v1.WmsMaterial) string {
		return item.GetEquipmentId()
	})

	features, err := uc.equipmentRepo.GetWmsEquipmentFeaturesByIds(ctx, equipmentIds)

	if err != nil {
		uc.log.Errorf("get equipment features error: %v", err)
	}

	material_features := arrayutil.Map(materials, func(item *v1.WmsMaterial) *structpb.Struct {
		return item.GetFeature()
	})

	filter_feature := arrayutil.Filter(features, func(item *v1.EquipmentFeature) bool {
		find_material_feature := arrayutil.Find(material_features, func(material_feature *structpb.Struct) bool {
			material_feature_map := material_feature.AsMap()

			for key, value := range material_feature_map {
				if item.Id == key && item.Value == value {
					return true
				}
			}

			return false
		})

		return find_material_feature != nil
	})

	feature_group := arrayutil.GroupBy(filter_feature, func(item *v1.EquipmentFeature) string {
		return item.Id
	})

	body := make([]interface{}, 0)

	for key, value := range feature_group {
		property_feature := arrayutil.Find(filter_feature, func(item *v1.EquipmentFeature) bool {
			return item.Id == key
		})

		equipment_type_name := property_feature.Name
		equipment_type_id := property_feature.Id

		body_item := make(map[string]interface{})

		body_item["id"] = "feature." + equipment_type_id
		body_item["name"] = "feature." + equipment_type_id
		body_item["type"] = "radios"
		body_item["label"] = equipment_type_name

		body_item_options := make([]interface{}, 0)

		items := value

		for _, value := range items {
			var body_item_option interface{}
			body_item_option_map := make(map[string]interface{})

			body_item_option_map["label"] = value.GetLabel()
			body_item_option_map["value"] = value.GetValue()

			body_item_option = body_item_option_map

			body_item_options = append(body_item_options, body_item_option)

		}
		body_item["options"] = body_item_options

		body_item["selectFirst"] = true

		body = append(body, body_item)
	}

	_, err = jsonutils.Set(&code_map, "$.body", body)

	uc.log.Infof("code_map: %v", code_map)

	if err != nil {
		return nil, err
	}

	code, err = structpb.NewStruct(code_map)

	if err != nil {
		return nil, err
	}

	return code, nil
}
