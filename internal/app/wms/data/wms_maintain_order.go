package data

import (
	"context"
	"kratos-mono-demo/internal/app/wms/biz"
	"kratos-mono-demo/internal/data"
	"time"

	conf "kratos-mono-demo/gen/config/conf/v1"

	"kratos-mono-demo/internal/data/ent"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/data/ent/sysorganization"
	"kratos-mono-demo/internal/data/ent/sysuser"
	"kratos-mono-demo/internal/data/ent/sysuserorganization"
	"kratos-mono-demo/internal/data/ent/wmsmaintainorder"

	"github.com/go-kratos/kratos/v2/log"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	"kratos-mono-demo/internal/pkg/go-utils/arrayutil"
	entgo "kratos-mono-demo/internal/pkg/go-utils/entgo/query"
	"kratos-mono-demo/internal/pkg/go-utils/orderutil"
	"kratos-mono-demo/internal/pkg/go-utils/stringutil"
	"kratos-mono-demo/internal/pkg/go-utils/trans"
)

var _ biz.WmsMaintainOrderRepo = (*WmsMaintainOrderRepo)(nil)

type WmsMaintainOrderRepo struct {
	data *data.Data
	log  *log.Helper
}

func NewWmsMaintainOrderRepo(c *conf.Config, logger log.Logger, data *data.Data) biz.WmsMaintainOrderRepo {
	l := log.NewHelper(log.With(logger, "module", "wms_maintain_order/data"))
	return &WmsMaintainOrderRepo{
		data: data,
		log:  l,
	}
}

func (r *WmsMaintainOrderRepo) getEntClient(ctx context.Context) *ent.Client {
	tx := ent.TxFromContext(ctx)
	if tx != nil {
		return tx.Client()
	}
	return r.data.EntClient.Client()
}

func (r *WmsMaintainOrderRepo) convertEntToProto(in *ent.WmsMaintainOrder, userMap map[string]string, fieldMask []string) *v1.WmsMaintainOrder {
	if in == nil {
		return nil
	}

	isAllField := len(fieldMask) == 0
	fieldMaskMap := map[string]bool{}
	for _, f := range fieldMask {
		fieldMaskMap[f] = true
	}

	item := &v1.WmsMaintainOrder{
		Id: in.ID,
	}
	if isAllField || fieldMaskMap["createdAt"] {
		item.CreatedAt = trans.TimeToStrPtr(in.CreatedAt)
	}
	if isAllField || fieldMaskMap["updatedAt"] {
		item.UpdatedAt = trans.TimeToStrPtr(in.UpdatedAt)
	}
	if isAllField || fieldMaskMap["createdBy"] {
		item.CreatedBy = trans.String(in.CreatedBy)
		if stringutil.IsNotEmpty(item.CreatedBy) {
			item.CreatedBy = trans.String(userMap[*item.CreatedBy])
		}
	}
	if isAllField || fieldMaskMap["updatedBy"] {
		item.UpdatedBy = in.UpdatedBy
		if stringutil.IsNotEmpty(item.UpdatedBy) {
			item.UpdatedBy = trans.String(userMap[*item.UpdatedBy])
		}
	}
	if isAllField || fieldMaskMap["remark"] {
		item.Remark = in.Remark
	}
	if isAllField || fieldMaskMap["contractUrls"] {
		item.ContractUrls = trans.String(in.ContractUrls)
	}
	if isAllField || fieldMaskMap["invoiceUrls"] {
		item.InvoiceUrls = trans.String(in.InvoiceUrls)
	}
	if isAllField || fieldMaskMap["auditUrls"] {
		item.AuditUrls = trans.String(in.AuditUrls)
	}
	if isAllField || fieldMaskMap["otherUrls"] {
		item.OtherUrls = trans.String(in.OtherUrls)
	}
	if isAllField || fieldMaskMap["orderNo"] {
		item.OrderNo = trans.String(in.OrderNo)
	}
	if isAllField || fieldMaskMap["equipmentNum"] {
		item.EquipmentNum = trans.Uint32(in.EquipmentNum)
	}
	if isAllField || fieldMaskMap["status"] {
		item.Status = trans.String(in.Status)
	}
	if isAllField || fieldMaskMap["maintainTime"] {
		item.MaintainTime = trans.TimePtrToStrPtr(in.MaintainTime)
	}

	return item
}

func (r *WmsMaintainOrderRepo) getUserInfoMap(ctx context.Context, results []*ent.WmsMaintainOrder) (map[string]string, error) {
	userIdInfo := map[string]string{}
	for _, m := range results {
		if m.CreatedBy != "" {
			userIdInfo[m.CreatedBy] = ""
		}
		if m.UpdatedBy != nil && *m.UpdatedBy != "" {
			userIdInfo[*m.UpdatedBy] = ""
		}
	}

	// 获取所有用户id
	userIds := make([]string, 0, len(userIdInfo))
	for k := range userIdInfo {
		userIds = append(userIds, k)
	}

	// 获取用户信息
	userInfo, err := r.getEntClient(ctx).SysUser.
		Query().
		Where(sysuser.IDIn(userIds...)).
		Select(sysuser.FieldUsername, sysuser.FieldNickname).
		All(ctx)

	if err != nil {
		r.log.Errorf("query user info failed: %s", err.Error())
		return nil, err
	}

	for _, u := range userInfo {
		if u.Nickname != "" {
			userIdInfo[u.ID] = u.Nickname
		} else {
			userIdInfo[u.ID] = u.Username
		}
	}

	return userIdInfo, nil
}

func (r *WmsMaintainOrderRepo) convertEntListToProto(
	userMap map[string]string,
	results []*ent.WmsMaintainOrder,
	fieldMask []string,
) ([]*v1.WmsMaintainOrder, error) {
	items := make([]*v1.WmsMaintainOrder, 0, len(results))
	for _, m := range results {
		item := r.convertEntToProto(m, userMap, fieldMask)
		items = append(items, item)
	}
	return items, nil
}

func (r *WmsMaintainOrderRepo) ListWmsMaintainOrder(ctx context.Context, req *v1.ListWmsMaintainOrderRequest) (*v1.ListWmsMaintainOrderResponse, error) {
	builder := r.getEntClient(ctx).WmsMaintainOrder.Query()
	countBuilder := r.getEntClient(ctx).WmsMaintainOrder.Query()

	// 从查询中获取值并且移除
	valuesMap := map[string]string{}
	query := entgo.RemoveQueryKeys(req.GetQuery(), []string{}, valuesMap)
	req.Query = &query

	fieldMask := req.GetFieldMask().GetPaths()
	err, whereSelectors, querySelectors := entgo.BuildQuerySelector(
		req.GetQuery(), req.GetOrQuery(),
		req.GetPage(), req.GetPageSize(), req.GetNoPaging(),
		req.GetOrderBy(), wmsmaintainorder.FieldCreatedAt,
		fieldMask,
	)
	if err != nil {
		r.log.Errorf("build query select faild: %s", err.Error())
		return nil, err
	}

	if querySelectors != nil {
		builder.Modify(querySelectors...)
	}

	if whereSelectors != nil {
		countBuilder.Modify(whereSelectors...)
	}

	results, err := builder.All(ctx)
	if err != nil {
		return nil, err
	}

	// 获取用户信息
	userMap, err := r.getUserInfoMap(ctx, results)
	if err != nil {
		return nil, err
	}

	// 转换成proto
	items, err := r.convertEntListToProto(userMap, results, fieldMask)
	if err != nil {
		return nil, err
	}

	count, err := countBuilder.Count(ctx)
	if err != nil {
		return nil, err
	}

	ret := v1.ListWmsMaintainOrderResponse{
		Total: int32(count),
		Items: items,
	}

	return &ret, err
}

func (r *WmsMaintainOrderRepo) GetWmsMaintainOrder(ctx context.Context, id string) (*v1.WmsMaintainOrder, error) {
	ret, err := r.getEntClient(ctx).WmsMaintainOrder.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	userMap, err := r.getUserInfoMap(ctx, []*ent.WmsMaintainOrder{ret})

	return r.convertEntToProto(ret, userMap, nil), err
}

func (r *WmsMaintainOrderRepo) CreateWmsMaintainOrder(ctx context.Context, req *v1.CreateWmsMaintainOrderRequest) (*v1.WmsMaintainOrder, error) {
	// 生成保养单号
	orderNo, err := orderutil.GenerateOrderNo("BY")
	if err != nil {
		return nil, err
	}

	builder := r.getEntClient(ctx).WmsMaintainOrder.Create().
		SetRemark(req.Remark).
		SetNillableContractUrls(req.ContractUrls).
		SetNillableInvoiceUrls(req.InvoiceUrls).
		SetNillableAuditUrls(req.AuditUrls).
		SetNillableOtherUrls(req.OtherUrls).
		SetOrderNo(orderNo).
		SetNillableEquipmentNum(req.EquipmentNum).
		SetNillableStatus(req.Status).
		SetNillableMaintainTime(trans.StrToTime(req.MaintainTime)).
		SetCreatedAt(time.Now())

	entutils.SetCreatedBy(ctx, builder.Mutation())

	ret, err := builder.Save(ctx)
	if err != nil {
		r.log.Errorf("insert one data failed: %s", err.Error())
		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

func (r *WmsMaintainOrderRepo) UpdateWmsMaintainOrder(ctx context.Context, req *v1.UpdateWmsMaintainOrderRequest) (*v1.WmsMaintainOrder, error) {

	builder := r.getEntClient(ctx).WmsMaintainOrder.UpdateOneID(req.Id).
		SetNillableRemark(req.Remark).
		SetNillableContractUrls(req.ContractUrls).
		SetNillableInvoiceUrls(req.InvoiceUrls).
		SetNillableAuditUrls(req.AuditUrls).
		SetNillableOtherUrls(req.OtherUrls).
		SetNillableOrderNo(req.OrderNo).
		SetNillableEquipmentNum(req.EquipmentNum).
		SetNillableStatus(req.Status).
		SetNillableMaintainTime(trans.StrToTime(req.MaintainTime)).
		SetUpdatedAt(time.Now())

	entutils.SetUpdatedBy(ctx, builder.Mutation())

	ret, err := builder.Save(ctx)
	if err != nil {
		r.log.Errorf("update one data failed: %s", err.Error())
		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

func (r *WmsMaintainOrderRepo) DeleteWmsMaintainOrder(ctx context.Context, id string) (bool, error) {
	err := r.getEntClient(ctx).WmsMaintainOrder.
		DeleteOneID(id).
		Exec(ctx)
	return err != nil, err
}

func (r *WmsMaintainOrderRepo) MultiDeleteWmsMaintainOrder(ctx context.Context, ids []string) (bool, error) {
	_, err := r.getEntClient(ctx).WmsMaintainOrder.
		Delete().
		Where(wmsmaintainorder.IDIn(ids...)).
		Exec(ctx)
	return err != nil, err
}

// 根据关联流程单号查询保养单
func (r *WmsMaintainOrderRepo) GetWmsMaintainOrderByOrderNo(ctx context.Context, orderNo string) (*v1.WmsMaintainOrder, error) {
	ret, err := r.getEntClient(ctx).WmsMaintainOrder.Query().Where(wmsmaintainorder.OrderNo(orderNo)).First(ctx)
	if err != nil && !ent.IsNotFound(err) {
		return nil, err
	}
	return r.convertEntToProto(ret, nil, nil), err
}

// 获取保养单申请用户的组织名称
func (r *WmsMaintainOrderRepo) GetWmsMaintainOrderUserOrganizationNameMap(ctx context.Context, ids []string) (map[string]string, error) {
	results, err := r.getEntClient(ctx).WmsMaintainOrder.Query().Where(wmsmaintainorder.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}

	user_ids := arrayutil.Map(results, func(result *ent.WmsMaintainOrder) string {
		return result.CreatedBy
	})

	user_organizations, err := r.getEntClient(ctx).SysUserOrganization.Query().Where(sysuserorganization.UserIDIn(user_ids...)).All(ctx)
	if err != nil {
		return nil, err
	}

	organization_ids := arrayutil.Map(user_organizations, func(organization *ent.SysUserOrganization) string {
		return organization.OrganizationID
	})

	organizations, err := r.getEntClient(ctx).SysOrganization.Query().Where(sysorganization.IDIn(organization_ids...)).All(ctx)
	if err != nil {
		return nil, err
	}

	result := map[string]string{}

	for _, item := range results {
		item_user_id := item.CreatedBy
		find_item_organization := arrayutil.Find(user_organizations, func(organization *ent.SysUserOrganization) bool {
			return organization.UserID == item_user_id
		})

		if find_item_organization == nil {
			continue
		}

		find_organization := arrayutil.Find(organizations, func(organization *ent.SysOrganization) bool {
			return organization.ID == find_item_organization.OrganizationID
		})

		if find_organization == nil {
			continue
		}

		result[item.ID] = find_organization.Name
	}

	return result, nil
}

// 根据ID列表获取保养单
func (r *WmsMaintainOrderRepo) GetWmsMaintainOrdersByIds(ctx context.Context, ids []string) ([]*v1.WmsMaintainOrder, error) {
	results, err := r.getEntClient(ctx).WmsMaintainOrder.Query().Where(wmsmaintainorder.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}

	userMap, err := r.getUserInfoMap(ctx, results)
	if err != nil {
		return nil, err
	}

	return r.convertEntListToProto(userMap, results, nil)
}
