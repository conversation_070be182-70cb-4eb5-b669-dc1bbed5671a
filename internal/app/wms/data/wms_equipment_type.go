package data

import (
	"context"
	"kratos-mono-demo/internal/app/wms/biz"
	"kratos-mono-demo/internal/data"
	"strings"
	"time"

	conf "kratos-mono-demo/gen/config/conf/v1"

	"kratos-mono-demo/internal/data/ent"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/data/ent/sysuser"
	"kratos-mono-demo/internal/data/ent/wmsequipmenttype"
	"kratos-mono-demo/internal/data/ent/wmsequipmenttypeproperty"
	"kratos-mono-demo/internal/data/ent/wmsequipmenttypepropertygroup"
	"kratos-mono-demo/internal/data/ent/wmsequipmenttypepropertyoption"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"errors"
	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	"kratos-mono-demo/internal/pkg/go-utils/arrayutil"
	entgo "kratos-mono-demo/internal/pkg/go-utils/entgo/query"
	"kratos-mono-demo/internal/pkg/go-utils/stringutil"
	"kratos-mono-demo/internal/pkg/go-utils/trans"
)

var _ biz.WmsEquipmentTypeRepo = (*WmsEquipmentTypeRepo)(nil)

type WmsEquipmentTypeRepo struct {
	data *data.Data
	log  *log.Helper
}

func NewWmsEquipmentTypeRepo(c *conf.Config, logger log.Logger, data *data.Data) biz.WmsEquipmentTypeRepo {
	l := log.NewHelper(log.With(logger, "module", "wms_equipment_type/data"))
	return &WmsEquipmentTypeRepo{
		data: data,
		log:  l,
	}
}

func (r *WmsEquipmentTypeRepo) getEntClient() *ent.Client {
	return r.data.EntClient.Client()
}

func (r *WmsEquipmentTypeRepo) convertEntToProto(in *ent.WmsEquipmentType) *v1.WmsEquipmentType {
	if in == nil {
		return nil
	}

	property_groups := make([]*v1.WmsEquipmentTypePropertyGroup, 0, len(in.Edges.PropertyGroups))

	for _, m := range in.Edges.PropertyGroups {
		property_groups = append(property_groups, &v1.WmsEquipmentTypePropertyGroup{
			Id:                m.ID,
			Name:              trans.String(m.Name),
			PropertyGroupType: trans.String(m.PropertyGroupType),
			EquipmentTypeId:   trans.String(m.EquipmentTypeID),
			Sort:              trans.Int32(m.Sort),
		})
	}

	properties := make([]*v1.WmsEquipmentTypeProperty, 0, len(in.Edges.Properties))

	for _, m := range in.Edges.Properties {
		var property_options []*v1.WmsEquipmentTypePropertyOption
		if m.Edges.Options != nil {
			for _, option := range m.Edges.Options {
				property_options = append(property_options, &v1.WmsEquipmentTypePropertyOption{
					Id:    option.ID,
					Code:  option.Code,
					Label: option.Label,
				})
			}
		}

		properties = append(properties, &v1.WmsEquipmentTypeProperty{
			Id:              m.ID,
			Name:            trans.String(m.Name),
			EquipmentTypeId: trans.String(m.EquipmentTypeID),
			GroupId:         trans.String(m.GroupID),
			FieldType:       trans.String(m.FieldType),
			IsRequired:      trans.String(m.IsRequired),
			PropertyType:    trans.String(m.PropertyType),
			Remark:          m.Remark,
			Sort:            trans.Int32(m.Sort),
			Options:         property_options,
		})
	}

	return &v1.WmsEquipmentType{
		Id:                 in.ID,
		CreatedAt:          trans.TimeToStrPtr(in.CreatedAt),
		UpdatedAt:          trans.TimeToStrPtr(in.UpdatedAt),
		CreatedBy:          trans.String(in.CreatedBy),
		UpdatedBy:          in.UpdatedBy,
		Remark:             in.Remark,
		Code:               trans.String(in.Code),
		Name:               trans.String(in.Name),
		ParentId:           in.ParentID,
		FeatureDescription: in.FeatureDescription,
		MeasureUnit:        in.MeasureUnit,
		Sort:               trans.Int32(in.Sort),

		PropertyGroups: property_groups,
		Properties:     properties,
	}
}
func (r *WmsEquipmentTypeRepo) travelChild(node *v1.WmsEquipmentType, nodesMap map[string][]*v1.WmsEquipmentType, level int) ([]*v1.WmsEquipmentType, error) {
	if level > 50 {
		return nil, errors.New("reached maximum recursion depth, possible circular reference")
	}

	children, exist := nodesMap[node.Id]
	if !exist {
		// 没有子节点，直接返回
		return nil, nil
	}

	for _, child := range children {
		traveledChildren, err := r.travelChild(child, nodesMap, level+1)
		if err != nil {
			return nil, err
		}
		child.Children = traveledChildren
	}

	return children, nil
}
func (r *WmsEquipmentTypeRepo) getHasChildrenMap(ctx context.Context, results []*ent.WmsEquipmentType) (map[string]bool, error) {
	var ids []string
	for _, m := range results {
		ids = append(ids, m.ID)
	}
	children, err := r.getEntClient().WmsEquipmentType.Query().
		Where(wmsequipmenttype.ParentIDIn(ids...)).
		Select(wmsequipmenttype.FieldParentID).
		All(ctx)
	if err != nil {
		return nil, err
	}
	hasChildren := make(map[string]bool)
	for _, child := range children {
		hasChildren[*child.ParentID] = true
	}
	return hasChildren, nil
}

func (r *WmsEquipmentTypeRepo) Count(ctx context.Context, whereCond []func(s *sql.Selector), onlyRoot bool) (int, error) {
	builder := r.getEntClient().WmsEquipmentType.Query()
	if onlyRoot {
		builder.Where(
			wmsequipmenttype.Or(
				wmsequipmenttype.ParentIDIsNil(),
				wmsequipmenttype.ParentID(""),
			),
		)
	}

	if len(whereCond) != 0 {
		builder.Modify(whereCond...)
	}

	count, err := builder.Count(ctx)
	if err != nil {
		r.log.Errorf("query count failed: %s", err.Error())
	}

	return count, err
}

func (r *WmsEquipmentTypeRepo) getUserInfoMap(ctx context.Context, results []*ent.WmsEquipmentType) (map[string]string, error) {
	userIdInfo := map[string]string{}
	for _, m := range results {
		if m.CreatedBy != "" {
			userIdInfo[m.CreatedBy] = ""
		}
		if m.UpdatedBy != nil && *m.UpdatedBy != "" {
			userIdInfo[*m.UpdatedBy] = ""
		}
	}

	// 获取所有用户id
	userIds := make([]string, 0, len(userIdInfo))
	for k := range userIdInfo {
		userIds = append(userIds, k)
	}

	// 获取用户信息
	userInfo, err := r.getEntClient().SysUser.
		Query().
		Where(sysuser.IDIn(userIds...)).
		Select(sysuser.FieldUsername, sysuser.FieldNickname).
		All(ctx)

	if err != nil {
		r.log.Errorf("query user info failed: %s", err.Error())
		return nil, err
	}

	for _, u := range userInfo {
		if u.Nickname != "" {
			userIdInfo[u.ID] = u.Nickname
		} else {
			userIdInfo[u.ID] = u.Username
		}
	}

	return userIdInfo, nil
}

// 创建索引
func (r *WmsEquipmentTypeRepo) createIndex(resources []*ent.WmsEquipmentType) map[string]*ent.WmsEquipmentType {
	index := make(map[string]*ent.WmsEquipmentType)
	for _, res := range resources {
		index[res.ID] = res
	}
	return index
}

// 是不是空的父节点
func (r *WmsEquipmentTypeRepo) isEmptyParentId(index map[string]*ent.WmsEquipmentType, parentId *string) bool {
	if stringutil.IsEmpty(parentId) {
		return true
	}

	if _, ok := index[*parentId]; !ok {
		return true
	}

	return false
}

func (r *WmsEquipmentTypeRepo) convertEntListToProto(
	ctx context.Context,
	noChildren bool,
	userMap map[string]string,
	results []*ent.WmsEquipmentType,
) ([]*v1.WmsEquipmentType, error) {
	items := make([]*v1.WmsEquipmentType, 0, len(results))
	index := r.createIndex(results)
	// 遍历子节点
	if !noChildren {
		root_items := make([]*v1.WmsEquipmentType, 0, len(results))
		nodesMap := make(map[string][]*v1.WmsEquipmentType)
		for _, m := range results {
			// 如果是空的父节点，则跳过
			if r.isEmptyParentId(index, m.ParentID) {
				continue
			}

			item := r.convertEntToProto(m)
			if userMap != nil {
				if stringutil.IsNotEmpty(item.CreatedBy) {
					item.CreatedBy = trans.String(userMap[*item.CreatedBy])
				}
				if stringutil.IsNotEmpty(item.UpdatedBy) {
					item.UpdatedBy = trans.String(userMap[*item.UpdatedBy])
				}
			}

			parentId := trans.StringValue(item.ParentId)
			nodesMap[parentId] = append(nodesMap[parentId], item)
		}

		for _, m := range results {
			if !r.isEmptyParentId(index, m.ParentID) {
				continue
			}

			item := r.convertEntToProto(m)
			if userMap != nil {
				if stringutil.IsNotEmpty(item.CreatedBy) {
					item.CreatedBy = trans.String(userMap[*item.CreatedBy])
				}
				if stringutil.IsNotEmpty(item.UpdatedBy) {
					item.UpdatedBy = trans.String(userMap[*item.UpdatedBy])
				}
			}
			item_children, err := r.travelChild(item, nodesMap, 1)
			if err != nil {
				return nil, err
			}
			item.Children = item_children
			root_items = append(root_items, item)
		}

		return root_items, nil
	}

	hasChildren, err := r.getHasChildrenMap(ctx, results)
	if err != nil {
		return nil, err
	}

	// 不遍历子节点
	for _, m := range results {
		item := r.convertEntToProto(m)
		if userMap != nil {
			if stringutil.IsNotEmpty(item.CreatedBy) {
				item.CreatedBy = trans.String(userMap[*item.CreatedBy])
			}
			if stringutil.IsNotEmpty(item.UpdatedBy) {
				item.UpdatedBy = trans.String(userMap[*item.UpdatedBy])
			}
		}
		item.HasChildren = trans.Bool(hasChildren[item.Id])
		items = append(items, item)
	}
	return items, nil
}

func (r *WmsEquipmentTypeRepo) ListWmsEquipmentType(ctx context.Context, req *v1.ListWmsEquipmentTypeRequest) (*v1.ListWmsEquipmentTypeResponse, error) {
	builder := r.getEntClient().WmsEquipmentType.Query()
	if req.GetOnlyRoot() {
		builder.Where(
			wmsequipmenttype.Or(
				wmsequipmenttype.ParentIDIsNil(),
				wmsequipmenttype.ParentID(""),
			),
		)
	}

	value_map := make(map[string]string)

	query := entgo.RemoveQueryKeys(req.GetQuery(), []string{"noChildren"}, value_map)

	req.Query = &query

	err, whereSelectors, querySelectors := entgo.BuildQuerySelector(
		req.GetQuery(), req.GetOrQuery(),
		req.GetPage(), req.GetPageSize(), req.GetNoPaging(),
		req.GetOrderBy(), wmsequipmenttype.FieldCreatedAt,
		req.GetFieldMask().GetPaths(),
	)
	if err != nil {
		r.log.Errorf("build query select faild: %s", err.Error())
		return nil, err
	}

	if querySelectors != nil {
		builder.Modify(querySelectors...)
	}

	results, err := builder.All(ctx)
	if err != nil {
		return nil, err
	}

	r.log.Infof("all data %+v", results)
	// 获取用户信息
	userMap, err := r.getUserInfoMap(ctx, results)
	if err != nil {
		return nil, err
	}

	nochildren := value_map["noChildren"] == "true"

	// 转换成proto
	items, err := r.convertEntListToProto(ctx, nochildren, userMap, results)
	if err != nil {
		return nil, err
	}

	count, err := r.Count(ctx, whereSelectors, req.GetOnlyRoot())
	if err != nil {
		return nil, err
	}

	ret := v1.ListWmsEquipmentTypeResponse{
		Total: int32(count),
		Items: items,
	}

	return &ret, err
}

func (r *WmsEquipmentTypeRepo) GetWmsEquipmentType(ctx context.Context, id string) (*v1.WmsEquipmentType, error) {
	ret, err := r.getEntClient().WmsEquipmentType.Query().
		Where(wmsequipmenttype.ID(id)).
		WithPropertyGroups(func(wetpgq *ent.WmsEquipmentTypePropertyGroupQuery) {
			wetpgq.Order(ent.Desc(wmsequipmenttypepropertygroup.FieldSort))

		}).
		WithProperties(func(wetpq *ent.WmsEquipmentTypePropertyQuery) {
			wetpq.Order(ent.Desc(wmsequipmenttypeproperty.FieldSort))
			wetpq.WithOptions(func(wetpoq *ent.WmsEquipmentTypePropertyOptionQuery) {
				wetpoq.Order(ent.Desc(wmsequipmenttypepropertyoption.FieldSort))
			})
		}).
		First(ctx)

	if err != nil {
		return nil, err
	}

	common_equipment_property_groups, err := r.getEntClient().WmsEquipmentTypePropertyGroup.Query().
		Where(wmsequipmenttypepropertygroup.PropertyGroupType("common")).
		Order(ent.Desc(wmsequipmenttypepropertygroup.FieldSort)).
		All(ctx)

	if err != nil {
		return nil, err
	}

	// common_equipment_properties, err := r.getEntClient().WmsEquipmentTypeProperty.Query().
	// 	Where(wmsequipmenttypeproperty.PropertyType("common")).
	// 	Order(ent.Desc(wmsequipmenttypeproperty.FieldSort)).
	// 	WithOptions(func(wetpoq *ent.WmsEquipmentTypePropertyOptionQuery) {
	// 		wetpoq.Order(ent.Desc(wmsequipmenttypepropertyoption.FieldSort))
	// 	}).
	// 	All(ctx)

	// if err != nil {
	// 	return nil, err
	// }

	if ret.Edges.PropertyGroups == nil {
		ret.Edges.PropertyGroups = make([]*ent.WmsEquipmentTypePropertyGroup, 0)
	}

	// if ret.Edges.Properties == nil {
	// 	ret.Edges.Properties = make([]*ent.WmsEquipmentTypeProperty, 0)
	// }

	ret.Edges.PropertyGroups = append(common_equipment_property_groups, ret.Edges.PropertyGroups...)
	// ret.Edges.Properties = append(common_equipment_properties, ret.Edges.Properties...)

	ret.Edges.PropertyGroups = arrayutil.Sort(ret.Edges.PropertyGroups, func(next *ent.WmsEquipmentTypePropertyGroup, prev *ent.WmsEquipmentTypePropertyGroup) bool {
		return prev.Sort > next.Sort
	})

	// ret.Edges.Properties = arrayutil.Sort(ret.Edges.Properties, func(next *ent.WmsEquipmentTypeProperty, prev *ent.WmsEquipmentTypeProperty) bool {
	// 	return prev.Sort > next.Sort
	// })

	return r.convertEntToProto(ret), err
}

func (r *WmsEquipmentTypeRepo) CreateWmsEquipmentType(ctx context.Context, req *v1.CreateWmsEquipmentTypeRequest) (*v1.WmsEquipmentType, error) {
	tx, err := r.getEntClient().Tx(ctx)

	if err != nil {
		return nil, err
	}

	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	builder := tx.WmsEquipmentType.Create().
		SetRemark(req.Remark).
		SetCode(req.Code).
		SetName(req.Name).
		SetNillableParentID(req.ParentId).
		SetNillableFeatureDescription(req.FeatureDescription).
		SetNillableMeasureUnit(req.MeasureUnit).
		SetNillableSort(req.Sort).
		SetCreatedAt(time.Now())

	entutils.SetCreatedBy(ctx, builder.Mutation())

	ret, err := builder.Save(ctx)
	if err != nil {
		r.log.Errorf("insert one data failed: %s", err.Error())
		tx.Rollback()

		return nil, err
	}

	if req.PropertyGroups != nil {
		property_group_type := "custom" // 只能添加自定义属性组

		var create_property_groups []*ent.WmsEquipmentTypePropertyGroupCreate

		sort_index := r.getDefaultGroupSort()
		for _, propertyGroup := range req.PropertyGroups {
			create_property_groups = append(create_property_groups, tx.WmsEquipmentTypePropertyGroup.Create().
				SetName(propertyGroup.Name).
				SetNillableEquipmentTypeID(&ret.ID).
				SetPropertyGroupType(property_group_type).
				SetSort(sort_index).
				SetCreatedAt(time.Now()))

			// 设置排序
			sort_index--
		}

		_, err = tx.WmsEquipmentTypePropertyGroup.
			CreateBulk(create_property_groups...).
			Save(ctx)

		if err != nil {
			r.log.Errorf("insert one data failed: %s", err.Error())
			tx.Rollback()

			return nil, err
		}
	}

	if err := tx.Commit(); err != nil {
		r.log.Errorf("insert one data failed: %s", err.Error())
		tx.Rollback()

		return nil, err
	}

	return r.convertEntToProto(ret), err
}

func (r *WmsEquipmentTypeRepo) UpdateWmsEquipmentType(ctx context.Context, req *v1.UpdateWmsEquipmentTypeRequest) (*v1.WmsEquipmentType, error) {
	property_group_type := "custom"      // 只能添加自定义属性组
	property_type := property_group_type // 只能添加自定义属性

	db_property_groups, err := r.getEntClient().WmsEquipmentTypePropertyGroup.Query().
		Where(wmsequipmenttypepropertygroup.EquipmentTypeID(req.Id)).
		All(ctx)

	if err != nil {
		return nil, err
	}

	tx, err := r.getEntClient().Tx(ctx)

	if err != nil {
		return nil, err
	}

	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	builder := tx.WmsEquipmentType.UpdateOneID(req.Id).
		SetNillableRemark(req.Remark).
		SetNillableCode(req.Code).
		SetNillableName(req.Name).
		SetNillableParentID(req.ParentId).
		SetNillableFeatureDescription(req.FeatureDescription).
		SetNillableMeasureUnit(req.MeasureUnit).
		SetNillableSort(req.Sort).
		SetUpdatedAt(time.Now())

	entutils.SetUpdatedBy(ctx, builder.Mutation())

	ret, err := builder.Save(ctx)
	if err != nil {
		r.log.Errorf("update one data failed: %s", err.Error())
		tx.Rollback()

		return nil, err
	}

	// 获取要删除的属性组，获取要添加的属性组
	var delete_property_group_ids []string
	var create_property_groups []*ent.WmsEquipmentTypePropertyGroupCreate
	var update_property_groups []*ent.WmsEquipmentTypePropertyGroupUpdate

	for _, db_property_group := range db_property_groups {
		found := false
		for _, property_group := range req.PropertyGroups {
			if db_property_group.ID == property_group.Id {
				found = true
				break
			}
		}

		if !found {
			delete_property_group_ids = append(delete_property_group_ids, db_property_group.ID)
		}
	}

	sort_index := r.getDefaultGroupSort()
	for _, property_group := range req.PropertyGroups {
		find_db_property_group := arrayutil.Find(db_property_groups, func(item *ent.WmsEquipmentTypePropertyGroup) bool {
			return item.ID == property_group.Id
		})

		if find_db_property_group == nil {
			create_property_groups = append(create_property_groups, tx.WmsEquipmentTypePropertyGroup.Create().
				SetName(property_group.GetName()).
				SetNillableEquipmentTypeID(&ret.ID).
				SetPropertyGroupType(property_group_type).
				SetSort(sort_index).
				SetCreatedAt(time.Now()))
		} else {
			update_property_groups = append(update_property_groups, tx.WmsEquipmentTypePropertyGroup.Update().
				Where(wmsequipmenttypepropertygroup.IDEQ(find_db_property_group.ID)).
				SetName(property_group.GetName()).
				SetSort(sort_index).
				SetUpdatedAt(time.Now()))
		}

		// 设置排序
		sort_index--
	}

	// 删除属性组
	_, err = tx.WmsEquipmentTypePropertyGroup.
		Delete().
		Where(wmsequipmenttypepropertygroup.IDIn(delete_property_group_ids...), wmsequipmenttypepropertygroup.PropertyGroupType(property_group_type)).
		Exec(ctx)

	if err != nil {
		r.log.Errorf("delete one data failed: %s", err.Error())
		tx.Rollback()

		return nil, err
	}

	// 删除属性
	_, err = tx.WmsEquipmentTypeProperty.
		Delete().
		Where(wmsequipmenttypeproperty.GroupIDIn(delete_property_group_ids...), wmsequipmenttypeproperty.PropertyType(property_type)).
		Exec(ctx)

	if err != nil {
		r.log.Errorf("delete one data failed: %s", err.Error())
		tx.Rollback()

		return nil, err
	}

	// 添加属性组
	_, err = tx.WmsEquipmentTypePropertyGroup.
		CreateBulk(create_property_groups...).
		Save(ctx)

	if err != nil {
		r.log.Errorf("insert one data failed: %s", err.Error())
		tx.Rollback()

		return nil, err
	}

	// 更新属性组
	for _, update_property_group := range update_property_groups {
		_, err = update_property_group.Save(ctx)

		if err != nil {
			r.log.Errorf("update one data failed: %s", err.Error())
			tx.Rollback()

			return nil, err
		}
	}

	if err := tx.Commit(); err != nil {
		r.log.Errorf("update one data failed: %s", err.Error())
		tx.Rollback()

		return nil, err
	}

	return r.convertEntToProto(ret), err
}
func (r *WmsEquipmentTypeRepo) DeleteWmsEquipmentType(ctx context.Context, id string) (bool, error) {
	menus, err := r.getEntClient().WmsEquipmentType.Query().
		Select(wmsequipmenttype.FieldID, wmsequipmenttype.FieldParentID).
		All(ctx)

	if err != nil {
		return false, err
	}

	ids := []string{id}
	// 递归获取所有子节点
	children_ids, err := r.getChildren(ctx, ids, menus, []string{})

	if err != nil {
		return false, err
	}

	ids = append(ids, children_ids...)

	// 去重
	ids = arrayutil.Distinct(ids)

	tx, err := r.getEntClient().Tx(ctx)

	if err != nil {
		return false, err
	}

	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	_, err = tx.WmsEquipmentType.
		Delete().
		Where(wmsequipmenttype.IDIn(ids...)).
		Exec(ctx)

	if err != nil {
		r.log.Errorf("delete one data failed: %s", err.Error())
		tx.Rollback()
	}

	// 删除属性组
	_, err = tx.WmsEquipmentTypePropertyGroup.
		Delete().
		Where(wmsequipmenttypepropertygroup.EquipmentTypeID(id)).
		Exec(ctx)

	if err != nil {
		r.log.Errorf("delete one data failed: %s", err.Error())
		tx.Rollback()
	}

	// 删除属性
	_, err = tx.WmsEquipmentTypeProperty.
		Delete().
		Where(wmsequipmenttypeproperty.EquipmentTypeID(id)).
		Exec(ctx)

	if err != nil {
		r.log.Errorf("delete one data failed: %s", err.Error())
		tx.Rollback()
	}

	if err := tx.Commit(); err != nil {
		r.log.Errorf("delete one data failed: %s", err.Error())
		tx.Rollback()
	}

	return err != nil, err
}

func (r *WmsEquipmentTypeRepo) MultiDeleteWmsEquipmentType(ctx context.Context, ids []string) (bool, error) {
	items, err := r.getEntClient().WmsEquipmentType.Query().
		Select(wmsequipmenttype.FieldID, wmsequipmenttype.FieldParentID).
		All(ctx)

	if err != nil {
		return false, err
	}

	// 递归获取所有子节点
	children_ids, err := r.getChildren(ctx, ids, items, []string{})

	if err != nil {
		return false, err
	}

	ids = append(ids, children_ids...)

	// 去重
	ids = arrayutil.Distinct(ids)

	tx, err := r.getEntClient().Tx(ctx)

	if err != nil {
		return false, err
	}

	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	_, err = tx.WmsEquipmentType.
		Delete().
		Where(wmsequipmenttype.IDIn(ids...)).
		Exec(ctx)

	if err != nil {
		r.log.Errorf("delete one data failed: %s", err.Error())
		tx.Rollback()
	}

	// 删除属性组
	_, err = tx.WmsEquipmentTypePropertyGroup.
		Delete().
		Where(wmsequipmenttypepropertygroup.EquipmentTypeIDIn(ids...)).
		Exec(ctx)

	if err != nil {
		r.log.Errorf("delete one data failed: %s", err.Error())
		tx.Rollback()
	}

	// 删除属性
	_, err = tx.WmsEquipmentTypeProperty.
		Delete().
		Where(wmsequipmenttypeproperty.EquipmentTypeIDIn(ids...)).
		Exec(ctx)

	if err != nil {
		r.log.Errorf("delete one data failed: %s", err.Error())
		tx.Rollback()
	}

	if err := tx.Commit(); err != nil {
		r.log.Errorf("delete one data failed: %s", err.Error())
		tx.Rollback()
	}

	return err != nil, err
}

// 递归获取所有子节点
// ids: 要获取子节点的节点id
// items: 所有节点
// ancestors: 祖先节点, 用于判断是否有环
func (r *WmsEquipmentTypeRepo) getChildren(ctx context.Context, ids []string, items []*ent.WmsEquipmentType, ancestors []string) ([]string, error) {
	var children_ids []string
	for _, m := range items {
		if stringutil.IsNotEmpty(m.ParentID) && arrayutil.IsIn(*m.ParentID, ids) {
			children_ids = append(children_ids, m.ID)

			// 如果有祖先节点，则判断是否有环
			if len(ancestors) > 0 {
				// 如果祖先节点中有当前节点，则有环
				if arrayutil.IsIn(m.ID, ancestors) {
					return nil, errors.New("menu has circle")
				}
			}

			ancestors = append(ancestors, m.ID)

			children, err := r.getChildren(ctx, []string{m.ID}, items, ancestors)
			if err != nil {
				return nil, err
			}
			children_ids = append(children_ids, children...)
		}
	}

	// 去重
	children_ids = arrayutil.Distinct(children_ids)

	return children_ids, nil
}

// 获取默认属性组排序值
func (r *WmsEquipmentTypeRepo) getDefaultGroupSort() int32 {
	return 100
}

func (r *WmsEquipmentTypeRepo) GetChildrenIds(ctx context.Context, ids []string) ([]string, error) {
	if len(ids) == 0 {
		return nil, nil
	}

	items, err := r.getEntClient().WmsEquipmentType.Query().
		Select(wmsequipmenttype.FieldID, wmsequipmenttype.FieldParentID).
		All(ctx)

	if err != nil {
		return nil, err
	}

	if err != nil {
		return nil, err
	}

	// 递归获取所有子节点
	children_ids, err := r.getChildren(ctx, ids, items, []string{})

	if err != nil {
		return nil, err
	}

	// 聚合并去重
	children_ids = append(ids, children_ids...)
	children_ids = arrayutil.Distinct(children_ids)

	return children_ids, nil
}

// 获取设备类型数据根据ids
func (r *WmsEquipmentTypeRepo) GetEquipmentTypeByIds(ctx context.Context, ids []string) ([]*v1.WmsEquipmentType, error) {
	results, err := r.getEntClient().WmsEquipmentType.Query().
		Where(wmsequipmenttype.IDIn(ids...)).
		All(ctx)

	if err != nil {
		return nil, err
	}

	// 获取用户信息
	userMap, err := r.getUserInfoMap(ctx, results)
	if err != nil {
		return nil, err
	}

	// 转换成proto
	items, err := r.convertEntListToProto(ctx, true, userMap, results)
	if err != nil {
		return nil, err
	}

	return items, nil
}

// 获取设备类型子节点map
func (r *WmsEquipmentTypeRepo) GetChildrenMap(ctx context.Context, ids []string) (map[string][]string, error) {
	items, err := r.getEntClient().WmsEquipmentType.Query().
		Select(wmsequipmenttype.FieldID, wmsequipmenttype.FieldParentID).
		All(ctx)

	if err != nil {
		return nil, err
	}

	childrenMap := make(map[string][]string)
	for _, id := range ids {
		children, err := r.getChildren(ctx, []string{id}, items, []string{})
		if err != nil {
			return nil, err
		}
		childrenMap[id] = children
	}

	return childrenMap, nil
}

// 获取类型的一级子类型id数据
func (r *WmsEquipmentTypeRepo) GetChildrenIdsByParentIds(ctx context.Context, parentIds []string) ([]string, error) {
	items, err := r.getEntClient().WmsEquipmentType.Query().
		Select(wmsequipmenttype.FieldID).
		Where(wmsequipmenttype.ParentIDIn(parentIds...)).
		All(ctx)

	if err != nil {
		return nil, err
	}

	ids := arrayutil.Map(items, func(item *ent.WmsEquipmentType) string {
		return item.ID
	})

	return ids, nil
}

// 获取类型的完整名称 父级名称/父级名称/当前名称
func (r *WmsEquipmentTypeRepo) GetEquipmentTypeFullName(ctx context.Context, ids []string) ([]*biz.EquipmentTypeFullName, error) {
	ids = arrayutil.Distinct(ids)
	parentMap, err := r.GetParentMap(ctx, ids)

	if err != nil {
		return nil, err
	}

	parentIds := make([]string, 0)

	for key, parents := range parentMap {
		parentIds = append(parentIds, key)
		parentIds = append(parentIds, parents...)
	}

	items, err := r.getEntClient().WmsEquipmentType.Query().
		Select(wmsequipmenttype.FieldID, wmsequipmenttype.FieldParentID, wmsequipmenttype.FieldName).
		Where(wmsequipmenttype.IDIn(parentIds...)).
		All(ctx)

	if err != nil {
		return nil, err
	}

	fullNames := make([]*biz.EquipmentTypeFullName, 0)

	for _, id := range ids {
		parents, exist := parentMap[id]

		if !exist {
			continue
		}

		fullName := &biz.EquipmentTypeFullName{
			Id:       id,
			FullName: "",
		}

		name_arr := make([]string, 0)

		for _, parentId := range parents {
			find_item := arrayutil.Find(items, func(item *ent.WmsEquipmentType) bool {
				return item.ID == parentId
			})

			if find_item == nil {
				continue
			}

			name_arr = append(name_arr, find_item.Name)
		}

		name_arr = arrayutil.Reverse(name_arr)

		fullName.FullName = strings.Join(name_arr, "/")

		fullNames = append(fullNames, fullName)
	}

	return fullNames, nil
}

// 获取父级Ids
func (r *WmsEquipmentTypeRepo) GetParentIds(ctx context.Context, ids []string) ([]string, error) {
	allItems, err := r.getEntClient().WmsEquipmentType.Query().
		Select(wmsequipmenttype.FieldID, wmsequipmenttype.FieldParentID).
		All(ctx)

	if err != nil {
		return nil, err
	}

	parent_ids := make([]string, 0)

	for _, id := range ids {
		find_item := arrayutil.Find(allItems, func(item *ent.WmsEquipmentType) bool {
			return item.ID == id
		})

		if find_item == nil {
			continue
		}

		// 环数组
		cycle_arr := make([]string, 0)

		parentId := find_item.ParentID

		for {
			if parentId == nil {
				break
			}

			if arrayutil.IsIn(*parentId, cycle_arr) {
				return nil, errors.New("装备类型存在循环引用")
			}

			parent_ids = append(parent_ids, *parentId)

			find_item = arrayutil.Find(allItems, func(item *ent.WmsEquipmentType) bool {
				return item.ID == *parentId
			})

			if find_item == nil {
				break
			}

			parentId = find_item.ParentID
		}
	}

	parent_ids = arrayutil.Distinct(parent_ids)

	return parent_ids, nil
}

// 获取父级map
func (r *WmsEquipmentTypeRepo) GetParentMap(ctx context.Context, ids []string) (map[string][]string, error) {
	allItems, err := r.getEntClient().WmsEquipmentType.Query().
		Select(wmsequipmenttype.FieldID, wmsequipmenttype.FieldParentID).
		All(ctx)

	if err != nil {
		return nil, err
	}

	parentMap := make(map[string][]string)

	for _, id := range ids {
		find_item := arrayutil.Find(allItems, func(item *ent.WmsEquipmentType) bool {
			return item.ID == id
		})

		if find_item == nil {
			continue
		}

		// 环数组
		cycle_arr := make([]string, 0)

		parentId := find_item.ParentID

		for {
			if parentId == nil {
				break
			}

			if arrayutil.IsIn(*parentId, cycle_arr) {
				return nil, errors.New("装备类型存在循环引用")
			}

			parentMap[id] = append(parentMap[id], *parentId)

			find_item = arrayutil.Find(allItems, func(item *ent.WmsEquipmentType) bool {
				return item.ID == *parentId
			})

			if find_item == nil {
				break
			}

			parentId = find_item.ParentID
		}
	}

	return parentMap, nil
}

// 获取父级根ids
func (r *WmsEquipmentTypeRepo) GetParentRootIds(ctx context.Context, ids []string) ([]string, error) {
	allItems, err := r.getEntClient().WmsEquipmentType.Query().
		Select(wmsequipmenttype.FieldID, wmsequipmenttype.FieldParentID).
		All(ctx)

	if err != nil {
		return nil, err
	}

	rootIds := make([]string, 0)

	for _, id := range ids {
		find_item := arrayutil.Find(allItems, func(item *ent.WmsEquipmentType) bool {
			return item.ID == id
		})

		if find_item == nil {
			continue
		}

		// 环数组
		cycle_arr := make([]string, 0)

		parentId := find_item.ParentID

		rootId := id

		for {
			if parentId == nil || *parentId == "" {
				rootIds = append(rootIds, rootId)
				break
			}

			if arrayutil.IsIn(*parentId, cycle_arr) {
				return nil, errors.New("装备类型存在循环引用")
			}

			find_item = arrayutil.Find(allItems, func(item *ent.WmsEquipmentType) bool {
				return item.ID == *parentId
			})

			if find_item == nil {
				break
			}

			parentId = find_item.ParentID
			rootId = find_item.ID
		}
	}

	rootIds = arrayutil.Distinct(rootIds)

	return rootIds, nil
}
