package data

import (
	"context"
	"kratos-mono-demo/internal/app/wms/biz"
	"kratos-mono-demo/internal/data"
	"time"

	conf "kratos-mono-demo/gen/config/conf/v1"

	"kratos-mono-demo/internal/data/ent"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/data/ent/predicate"
	"kratos-mono-demo/internal/data/ent/sysuser"
	"kratos-mono-demo/internal/data/ent/wmsauditplan"
	"kratos-mono-demo/internal/data/ent/wmsauditplandetail"
	"kratos-mono-demo/internal/data/ent/wmsmaterial"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/structpb"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	entgo "kratos-mono-demo/internal/pkg/go-utils/entgo/query"
	"kratos-mono-demo/internal/pkg/go-utils/stringutil"
	"kratos-mono-demo/internal/pkg/go-utils/trans"
)

var _ biz.WmsAuditPlanDetailRepo = (*WmsAuditPlanDetailRepo)(nil)

type WmsAuditPlanDetailRepo struct {
	data *data.Data
	log  *log.Helper
}

func NewWmsAuditPlanDetailRepo(c *conf.Config, logger log.Logger, data *data.Data) biz.WmsAuditPlanDetailRepo {
	l := log.NewHelper(log.With(logger, "module", "wms_audit_plan_detail/data"))
	return &WmsAuditPlanDetailRepo{
		data: data,
		log:  l,
	}
}

func (r *WmsAuditPlanDetailRepo) getEntClient(ctx context.Context) *ent.Client {
	tx := ent.TxFromContext(ctx)
	if tx != nil {
		return tx.Client()
	}
	return r.data.EntClient.Client()
}

func (r *WmsAuditPlanDetailRepo) convertEntToProto(in *ent.WmsAuditPlanDetail, userMap map[string]string, fieldMask []string) *v1.WmsAuditPlanDetail {
	if in == nil {
		return nil
	}

	isAllField := len(fieldMask) == 0
	fieldMaskMap := map[string]bool{}
	for _, f := range fieldMask {
		fieldMaskMap[f] = true
	}

	item := &v1.WmsAuditPlanDetail{
		Id: in.ID,
	}
	if isAllField || fieldMaskMap["createdAt"] {
		item.CreatedAt = trans.TimeToStrPtr(in.CreatedAt)
	}
	if isAllField || fieldMaskMap["updatedAt"] {
		item.UpdatedAt = trans.TimeToStrPtr(in.UpdatedAt)
	}
	if isAllField || fieldMaskMap["createdBy"] {
		item.CreatedBy = trans.String(in.CreatedBy)
		if stringutil.IsNotEmpty(item.CreatedBy) {
			item.CreatedBy = trans.String(userMap[*item.CreatedBy])
		}
	}
	if isAllField || fieldMaskMap["updatedBy"] {
		item.UpdatedBy = in.UpdatedBy
		if stringutil.IsNotEmpty(item.UpdatedBy) {
			item.UpdatedBy = trans.String(userMap[*item.UpdatedBy])
		}
	}
	if isAllField || fieldMaskMap["remark"] {
		item.Remark = in.Remark
	}
	if isAllField || fieldMaskMap["status"] {
		item.Status = trans.Int32(in.Status)
	}
	if isAllField || fieldMaskMap["auditPlanId"] {
		item.AuditPlanId = trans.String(in.AuditPlanID)
	}
	if isAllField || fieldMaskMap["materialId"] {
		item.MaterialId = trans.String(in.MaterialID)
	}
	if isAllField || fieldMaskMap["scope"] {
		item.Scope = trans.String(in.Scope)
	}
	if isAllField || fieldMaskMap["state"] {
		item.State = trans.String(in.State.String())
	}
	if isAllField || fieldMaskMap["isAudit"] {
		item.IsAudit = trans.Bool(in.IsAudit)
	}
	if isAllField || fieldMaskMap["isReader"] {
		item.IsReader = trans.Bool(in.IsReader)
	}
	if isAllField || fieldMaskMap["source"] {
		item.Source = trans.String(in.Source)
	}
	if isAllField || fieldMaskMap["fireStationId"] {
		item.FireStationId = trans.String(in.FireStationID)
	}
	if isAllField || fieldMaskMap["materialType"] {
		item.MaterialType = trans.String(in.MaterialType)
	}
	if isAllField || fieldMaskMap["equipmentTypeId"] {
		item.EquipmentTypeId = trans.String(in.EquipmentTypeID)
	}
	if isAllField || fieldMaskMap["equipmentId"] {
		item.EquipmentId = trans.String(in.EquipmentID)
	}
	if isAllField || fieldMaskMap["code"] {
		item.Code = trans.String(in.Code)
	}
	if isAllField || fieldMaskMap["codeType"] {
		item.CodeType = in.CodeType
	}
	if isAllField || fieldMaskMap["name"] {
		item.Name = in.Name
	}
	if isAllField || fieldMaskMap["modelNo"] {
		item.ModelNo = trans.String(in.ModelNo)
	}
	if isAllField || fieldMaskMap["providerId"] {
		item.ProviderId = trans.String(in.ProviderID)
	}
	if isAllField || fieldMaskMap["num"] {
		item.Num = trans.Uint32(in.Num)
	}
	if isAllField || fieldMaskMap["price"] {
		item.Price = trans.Uint64MoneyValueToYuanFloat64(in.Price)
	}
	if isAllField || fieldMaskMap["repositoryId"] {
		item.RepositoryId = trans.String(in.RepositoryID)
	}
	if isAllField || fieldMaskMap["repositoryAreaId"] {
		item.RepositoryAreaId = trans.String(in.RepositoryAreaID)
	}
	if isAllField || fieldMaskMap["repositoryPositionId"] {
		item.RepositoryPositionId = trans.String(in.RepositoryPositionID)
	}
	if isAllField || fieldMaskMap["measureUnitId"] {
		item.MeasureUnitId = trans.String(in.MeasureUnitID)
	}
	if isAllField || fieldMaskMap["expireTime"] {
		item.ExpireTime = trans.TimeToStrPtr(in.ExpireTime)
	}
	if isAllField || fieldMaskMap["equipmentStatus"] {
		item.EquipmentStatus = trans.Int32(in.EquipmentStatus)
	}
	if isAllField || fieldMaskMap["ownerId"] {
		item.OwnerId = trans.String(in.OwnerID)
	}

	if isAllField || fieldMaskMap["feature"] {
		item.Feature, _ = structpb.NewStruct(in.Feature)
	}

	if isAllField || fieldMaskMap["materialId"] {
		item.MaterialId = trans.String(in.MaterialID)
	}

	if in.Edges.Equipment != nil {
		item.MaterialType = trans.String(in.Edges.Equipment.Type)
		item.ModelNo = trans.String(in.Edges.Equipment.ModelNo)
		item.ProviderName = trans.String(in.Edges.Equipment.ProviderName)
	}
	if in.Edges.Owner != nil {
		userName := in.Edges.Owner.Nickname
		if userName == "" {
			userName = in.Edges.Owner.Username
		}
		item.OwnerName = trans.String(userName)
	} else {
		// 没有owner，才去获取仓库信息
		if in.Edges.Repository != nil {
			item.RepositoryName = trans.String(in.Edges.Repository.Name)
		}
		if in.Edges.RepositoryArea != nil {
			item.RepositoryAreaName = trans.String(in.Edges.RepositoryArea.Name)
		}
		if in.Edges.RepositoryPosition != nil {
			item.RepositoryPositionName = trans.String(in.Edges.RepositoryPosition.Name)
		}
	}
	if in.Edges.EquipmentType != nil {
		item.EquipmentTypeName = trans.String(in.Edges.EquipmentType.Name)
	}
	if in.Edges.MeasureUnit != nil {
		item.MeasureUnitName = trans.String(in.Edges.MeasureUnit.Name)
	}

	return item
}

func (r *WmsAuditPlanDetailRepo) getUserInfoMap(ctx context.Context, results []*ent.WmsAuditPlanDetail) (map[string]string, error) {
	userIdInfo := map[string]string{}
	for _, m := range results {
		if m.CreatedBy != "" {
			userIdInfo[m.CreatedBy] = ""
		}
		if m.UpdatedBy != nil && *m.UpdatedBy != "" {
			userIdInfo[*m.UpdatedBy] = ""
		}
	}

	// 获取所有用户id
	userIds := make([]string, 0, len(userIdInfo))
	for k := range userIdInfo {
		userIds = append(userIds, k)
	}

	// 获取用户信息
	userInfo, err := r.getEntClient(ctx).SysUser.
		Query().
		Where(sysuser.IDIn(userIds...)).
		Select(sysuser.FieldUsername, sysuser.FieldNickname).
		All(ctx)

	if err != nil {
		r.log.Errorf("query user info failed: %s", err.Error())
		return nil, err
	}

	for _, u := range userInfo {
		if u.Nickname != "" {
			userIdInfo[u.ID] = u.Nickname
		} else {
			userIdInfo[u.ID] = u.Username
		}
	}

	return userIdInfo, nil
}

func (r *WmsAuditPlanDetailRepo) convertEntListToProto(
	userMap map[string]string,
	results []*ent.WmsAuditPlanDetail,
	fieldMask []string,
) ([]*v1.WmsAuditPlanDetail, error) {
	items := make([]*v1.WmsAuditPlanDetail, 0, len(results))
	for _, m := range results {
		item := r.convertEntToProto(m, userMap, fieldMask)
		items = append(items, item)
	}
	return items, nil
}

func (r *WmsAuditPlanDetailRepo) ListWmsAuditPlanDetail(ctx context.Context, req *v1.ListWmsAuditPlanDetailRequest) (*v1.ListWmsAuditPlanDetailResponse, error) {
	builder := r.getEntClient(ctx).WmsAuditPlanDetail.Query()
	countBuilder := r.getEntClient(ctx).WmsAuditPlanDetail.Query()

	// 从查询中获取值并且移除
	valuesMap := map[string]string{}
	query := entgo.RemoveQueryKeys(req.GetQuery(), []string{}, valuesMap)
	req.Query = &query

	fieldMask := req.GetFieldMask().GetPaths()
	err, whereSelectors, querySelectors := entgo.BuildQuerySelector(
		req.GetQuery(), req.GetOrQuery(),
		req.GetPage(), req.GetPageSize(), req.GetNoPaging(),
		req.GetOrderBy(), wmsauditplandetail.FieldCreatedAt,
		fieldMask,
	)
	if err != nil {
		r.log.Errorf("build query select faild: %s", err.Error())
		return nil, err
	}

	if querySelectors != nil {
		builder.Modify(querySelectors...)
	}

	if whereSelectors != nil {
		countBuilder.Modify(whereSelectors...)
	}

	builder.
		WithMaterial().
		WithEquipment().
		WithEquipmentType().
		WithRepository().
		WithRepositoryArea().
		WithRepositoryPosition().
		WithMeasureUnit().
		WithOwner()

	results, err := builder.All(ctx)
	if err != nil {
		return nil, err
	}

	// 获取用户信息
	userMap, err := r.getUserInfoMap(ctx, results)
	if err != nil {
		return nil, err
	}

	// 转换成proto
	items, err := r.convertEntListToProto(userMap, results, fieldMask)
	if err != nil {
		return nil, err
	}

	count, err := countBuilder.Count(ctx)
	if err != nil {
		return nil, err
	}

	ret := v1.ListWmsAuditPlanDetailResponse{
		Total: int32(count),
		Items: items,
	}

	return &ret, err
}

func (r *WmsAuditPlanDetailRepo) GetWmsAuditPlanDetail(ctx context.Context, id string) (*v1.WmsAuditPlanDetail, error) {
	ret, err := r.getEntClient(ctx).WmsAuditPlanDetail.Get(ctx, id)
	if err != nil && !ent.IsNotFound(err) {
		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

func (r *WmsAuditPlanDetailRepo) CreateWmsAuditPlanDetail(ctx context.Context, req *v1.CreateWmsAuditPlanDetailRequest) (*v1.WmsAuditPlanDetail, error) {
	var feature_map map[string]interface{}

	if req.Feature != nil {
		feature_map = req.Feature.AsMap()
	}

	builder := r.getEntClient(ctx).WmsAuditPlanDetail.Create().
		SetRemark(req.Remark).
		SetStatus(req.Status).
		SetAuditPlanID(trans.StringValue(req.AuditPlanId)).
		SetMaterialID(trans.StringValue(req.MaterialId)).
		SetScope(trans.StringValue(req.Scope)).
		SetState(wmsauditplandetail.State(*req.State)).
		SetIsAudit(trans.BoolValue(req.IsAudit)).
		SetIsReader(trans.BoolValue(req.IsReader)).
		SetSource(trans.StringValue(req.Source)).
		SetNillableFireStationID(req.FireStationId).
		SetMaterialType(trans.StringValue(req.MaterialType)).
		SetNillableEquipmentTypeID(req.EquipmentTypeId).
		SetNillableEquipmentID(req.EquipmentId).
		SetCode(trans.StringValue(req.Code)).
		SetCodeType(trans.StringValue(req.CodeType)).
		SetName(trans.StringValue(req.Name)).
		SetModelNo(trans.StringValue(req.ModelNo)).
		SetProviderID(trans.StringValue(req.ProviderId)).
		SetNum(trans.Uint32Value(req.Num)).
		SetPrice(trans.Float64MoneyToFenUInt64Value(req.Price)).
		SetNillableRepositoryID(req.RepositoryId).
		SetNillableRepositoryAreaID(req.RepositoryAreaId).
		SetNillableRepositoryPositionID(req.RepositoryPositionId).
		SetNillableMeasureUnitID(req.MeasureUnitId).
		SetNillableExpireTime(trans.StrToTime(req.ExpireTime)).
		SetNillableEquipmentStatus(req.EquipmentStatus).
		SetNillableOwnerID(req.OwnerId).
		SetFeature(feature_map).
		SetCreatedAt(time.Now())

	entutils.SetCreatedBy(ctx, builder.Mutation())

	ret, err := builder.Save(ctx)
	if err != nil {
		r.log.Errorf("insert one data failed: %s", err.Error())
		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

func (r *WmsAuditPlanDetailRepo) UpdateWmsAuditPlanDetail(ctx context.Context, req *v1.UpdateWmsAuditPlanDetailRequest) (*v1.WmsAuditPlanDetail, error) {
	var feature_map map[string]interface{}

	if req.Feature != nil {
		feature_map = req.Feature.AsMap()
	}

	builder := r.getEntClient(ctx).WmsAuditPlanDetail.UpdateOneID(req.Id).
		SetNillableRemark(req.Remark).
		SetNillableStatus(req.Status).
		SetNillableAuditPlanID(req.AuditPlanId).
		SetNillableMaterialID(req.MaterialId).
		SetNillableScope(req.Scope).
		SetNillableState((*wmsauditplandetail.State)(req.State)).
		SetNillableIsAudit(req.IsAudit).
		SetNillableIsReader(req.IsReader).
		SetNillableSource(req.Source).
		SetNillableFireStationID(req.FireStationId).
		SetNillableMaterialType(req.MaterialType).
		SetNillableEquipmentTypeID(req.EquipmentTypeId).
		SetNillableEquipmentID(req.EquipmentId).
		SetNillableCode(req.Code).
		SetNillableCodeType(req.CodeType).
		SetNillableName(req.Name).
		SetNillableModelNo(req.ModelNo).
		SetNillableProviderID(req.ProviderId).
		SetNillableNum(req.Num).
		SetNillablePrice(trans.Float64MoneyToFenUInt64(req.Price)).
		SetNillableRepositoryID(req.RepositoryId).
		SetNillableRepositoryAreaID(req.RepositoryAreaId).
		SetNillableRepositoryPositionID(req.RepositoryPositionId).
		SetNillableMeasureUnitID(req.MeasureUnitId).
		SetNillableExpireTime(trans.StrToTime(req.ExpireTime)).
		SetNillableEquipmentStatus(req.EquipmentStatus).
		SetNillableOwnerID(req.OwnerId).
		SetUpdatedAt(time.Now())

	if feature_map != nil {
		builder.SetFeature(feature_map)
	}

	entutils.SetUpdatedBy(ctx, builder.Mutation())

	ret, err := builder.Save(ctx)
	if err != nil {
		r.log.Errorf("update one data failed: %s", err.Error())
		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

func (r *WmsAuditPlanDetailRepo) DeleteWmsAuditPlanDetail(ctx context.Context, id string) (bool, error) {
	err := r.getEntClient(ctx).WmsAuditPlanDetail.
		DeleteOneID(id).
		Exec(ctx)
	return err != nil, err
}

func (r *WmsAuditPlanDetailRepo) MultiDeleteWmsAuditPlanDetail(ctx context.Context, ids []string) (bool, error) {
	_, err := r.getEntClient(ctx).WmsAuditPlanDetail.
		Delete().
		Where(wmsauditplandetail.IDIn(ids...)).
		Exec(ctx)
	return err != nil, err
}

func (r *WmsAuditPlanDetailRepo) BatchCreateWmsAuditPlanDetail(ctx context.Context, reqs []*v1.CreateWmsAuditPlanDetailRequest) error {
	builder := r.getEntClient(ctx).WmsAuditPlanDetail

	var creates []*ent.WmsAuditPlanDetailCreate
	for _, req := range reqs {
		var feature_map map[string]interface{}

		if req.Feature != nil {
			feature_map = req.Feature.AsMap()
		}

		create := builder.Create().
			SetRemark(req.Remark).
			SetStatus(req.Status).
			SetAuditPlanID(trans.StringValue(req.AuditPlanId)).
			SetMaterialID(trans.StringValue(req.MaterialId)).
			SetScope(trans.StringValue(req.Scope)).
			SetState(wmsauditplandetail.State(*req.State)).
			SetIsAudit(trans.BoolValue(req.IsAudit)).
			SetSource(trans.StringValue(req.Source)).
			SetNillableFireStationID(req.FireStationId).
			SetMaterialType(trans.StringValue(req.MaterialType)).
			SetNillableEquipmentTypeID(req.EquipmentTypeId).
			SetNillableEquipmentID(req.EquipmentId).
			SetCode(trans.StringValue(req.Code)).
			SetCodeType(trans.StringValue(req.CodeType)).
			SetName(trans.StringValue(req.Name)).
			SetModelNo(trans.StringValue(req.ModelNo)).
			SetProviderID(trans.StringValue(req.ProviderId)).
			SetNum(trans.Uint32Value(req.Num)).
			SetPrice(trans.Float64MoneyToFenUInt64Value(req.Price)).
			SetNillableRepositoryID(req.RepositoryId).
			SetNillableRepositoryAreaID(req.RepositoryAreaId).
			SetNillableRepositoryPositionID(req.RepositoryPositionId).
			SetNillableMeasureUnitID(req.MeasureUnitId).
			SetNillableExpireTime(trans.StrToTime(req.ExpireTime)).
			SetNillableEquipmentStatus(req.EquipmentStatus).
			SetNillableOwnerID(req.OwnerId).
			SetFeature(feature_map).
			SetCreatedAt(time.Now())
		//SetPrice(trans.StringMoneyValueToFenUInt64Value(req.Price))
		//SetPrice(trans.Float64MoneyToFenUInt64Value(req.Price)).

		creates = append(creates, create)
	}

	return builder.CreateBulk(creates...).Exec(ctx)
}

func (r *WmsAuditPlanDetailRepo) FoundRfidCode(ctx context.Context, rfidCode string) error {
	if rfidCode == "" {
		return nil
	}

	builder := r.getEntClient(ctx).WmsAuditPlanDetail
	_, err := builder.Update().
		Where(
			wmsauditplandetail.Code(rfidCode),
			wmsauditplandetail.HasAuditPlanWith(wmsauditplan.StatusEQ(1)), // 正在盘点中
		).
		SetIsAudit(true).
		SetStatus(1).
		SetIsReader(true).
		Save(ctx)

	return err
}

func (r *WmsAuditPlanDetailRepo) MultiUpdateWmsAuditPlanDetail(ctx context.Context, req *v1.MultiUpdateWmsAuditPlanDetailRequest) error {
	builder := r.getEntClient(ctx).WmsAuditPlanDetail
	_, err := builder.Update().
		Where(wmsauditplandetail.IDIn(req.GetIds()...)).
		SetIsAudit(true).
		SetStatus(req.GetStatus()).
		SetRemark(req.GetRemark()).
		Save(ctx)

	return err
}

// 获取我的装备盘点计划详情列表
func (r *WmsAuditPlanDetailRepo) ListWmsAuditPlanDetailByUserId(ctx context.Context, userId string, auditPlanId string, isAudit *bool) (*v1.ListWmsAuditPlanDetailResponse, error) {
	where_arr := make([]predicate.WmsAuditPlanDetail, 0)

	where_arr = append(where_arr, wmsauditplandetail.HasAuditPlanWith(wmsauditplan.ID(auditPlanId)))
	where_arr = append(where_arr, wmsauditplandetail.HasAuditPlanWith(wmsauditplan.StatusEQ(1)))
	where_arr = append(where_arr, wmsauditplandetail.HasMaterialWith(wmsmaterial.HasOwnerWith(sysuser.IDEQ(userId))))

	if isAudit != nil {
		where_arr = append(where_arr, wmsauditplandetail.IsAuditEQ(*isAudit))
	}

	builder := r.getEntClient(ctx).WmsAuditPlanDetail.Query().
		Where(where_arr...).
		WithMaterial().
		WithEquipment().
		WithEquipmentType().
		WithRepository().
		WithRepositoryArea().
		WithRepositoryPosition().
		WithMeasureUnit().
		WithOwner()

	results, err := builder.All(ctx)
	if err != nil {
		return nil, err
	}

	// 获取用户信息
	userMap, err := r.getUserInfoMap(ctx, results)
	if err != nil {
		return nil, err
	}

	// 转换成proto
	items, err := r.convertEntListToProto(userMap, results, nil)
	if err != nil {
		return nil, err
	}

	ret := v1.ListWmsAuditPlanDetailResponse{
		Total: int32(len(items)),
		Items: items,
	}

	return &ret, err
}

// 更新我的装备盘点计划详情
func (r *WmsAuditPlanDetailRepo) UpdateWmsAuditPlanDetailByUserId(ctx context.Context, userId string, req *v1.PostMyWmsAuditPlanDetailRequest) error {
	builder := r.getEntClient(ctx).WmsAuditPlanDetail
	_, err := builder.Update().
		Where(
			wmsauditplandetail.ID(req.Id),
			wmsauditplandetail.HasAuditPlanWith(wmsauditplan.StatusEQ(1)),                      // 正在盘点中
			wmsauditplandetail.HasMaterialWith(wmsmaterial.HasOwnerWith(sysuser.IDEQ(userId))), // 只能修改自己的
		).
		SetIsAudit(true).
		SetStatus(req.GetStatus()).
		SetRemark(req.GetRemark()).
		Save(ctx)

	if err != nil {
		r.log.Errorf("update one data failed: %s", err.Error())
		return err
	}

	return nil
}
