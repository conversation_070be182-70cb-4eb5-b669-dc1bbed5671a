package data

import (
	"context"
	"kratos-mono-demo/internal/app/wms/biz"
	"kratos-mono-demo/internal/data"
	"time"

	conf "kratos-mono-demo/gen/config/conf/v1"

	"kratos-mono-demo/internal/data/ent"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/data/ent/sysuser"
	"kratos-mono-demo/internal/data/ent/wmsrepairorderdetail"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/structpb"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	entgo "kratos-mono-demo/internal/pkg/go-utils/entgo/query"
	"kratos-mono-demo/internal/pkg/go-utils/stringutil"
	"kratos-mono-demo/internal/pkg/go-utils/trans"
)

var _ biz.WmsRepairOrderDetailRepo = (*WmsRepairOrderDetailRepo)(nil)

type WmsRepairOrderDetailRepo struct {
	data *data.Data
	log  *log.Helper
}

func NewWmsRepairOrderDetailRepo(c *conf.Config, logger log.Logger, data *data.Data) biz.WmsRepairOrderDetailRepo {
	l := log.NewHelper(log.With(logger, "module", "wms_repair_order_detail/data"))
	return &WmsRepairOrderDetailRepo{
		data: data,
		log:  l,
	}
}

func (r *WmsRepairOrderDetailRepo) getEntClient(ctx context.Context) *ent.Client {
	tx := ent.TxFromContext(ctx)
	if tx != nil {
		return tx.Client()
	}
	return r.data.EntClient.Client()
}

func (r *WmsRepairOrderDetailRepo) convertEntToProto(in *ent.WmsRepairOrderDetail, userMap map[string]string, fieldMask []string) *v1.WmsRepairOrderDetail {
	if in == nil {
		return nil
	}

	isAllField := len(fieldMask) == 0
	fieldMaskMap := map[string]bool{}
	for _, f := range fieldMask {
		fieldMaskMap[f] = true
	}

	item := &v1.WmsRepairOrderDetail{
		Id: in.ID,
	}
	if isAllField || fieldMaskMap["createdAt"] {
		item.CreatedAt = trans.TimeToStrPtr(in.CreatedAt)
	}
	if isAllField || fieldMaskMap["updatedAt"] {
		item.UpdatedAt = trans.TimeToStrPtr(in.UpdatedAt)
	}
	if isAllField || fieldMaskMap["createdBy"] {
		item.CreatedBy = trans.String(in.CreatedBy)
		if stringutil.IsNotEmpty(item.CreatedBy) {
			item.CreatedBy = trans.String(userMap[*item.CreatedBy])
		}
	}
	if isAllField || fieldMaskMap["updatedBy"] {
		item.UpdatedBy = in.UpdatedBy
		if stringutil.IsNotEmpty(item.UpdatedBy) {
			item.UpdatedBy = trans.String(userMap[*item.UpdatedBy])
		}
	}
	if isAllField || fieldMaskMap["remark"] {
		item.Remark = in.Remark
	}
	if isAllField || fieldMaskMap["orderId"] {
		item.OrderId = trans.String(in.OrderID)
	}
	if isAllField || fieldMaskMap["materialId"] {
		item.MaterialId = trans.String(in.MaterialID)
	}
	if isAllField || fieldMaskMap["materialName"] {
		item.MaterialName = trans.String(in.MaterialName)
	}
	if isAllField || fieldMaskMap["code"] {
		item.Code = trans.String(in.Code)
	}
	if isAllField || fieldMaskMap["equipmentId"] {
		item.EquipmentId = trans.String(in.EquipmentID)
	}
	if isAllField || fieldMaskMap["equipmentTypeId"] {
		item.EquipmentTypeId = trans.String(in.EquipmentTypeID)
	}
	if isAllField || fieldMaskMap["feature"] {
		if in.Feature != nil {
			item.Feature, _ = structpb.NewStruct(in.Feature)
		}
	}
	if isAllField || fieldMaskMap["repositoryId"] {
		item.RepositoryId = in.RepositoryID
	}
	if isAllField || fieldMaskMap["repositoryAreaId"] {
		item.RepositoryAreaId = in.RepositoryAreaID
	}
	if isAllField || fieldMaskMap["repositoryPositionId"] {
		item.RepositoryPositionId = in.RepositoryPositionID
	}
	if isAllField || fieldMaskMap["ownerId"] {
		item.OwnerId = in.OwnerID
	}
	if isAllField || fieldMaskMap["ModelNo"] {
		item.ModelNo = trans.String(in.ModelNo)
	}
	if isAllField || fieldMaskMap["measureUnitId"] {
		item.MeasureUnitId = trans.String(in.MeasureUnitID)
	}
	if isAllField || fieldMaskMap["num"] {
		item.Num = trans.Uint32(in.Num)
	}
	if isAllField || fieldMaskMap["repairType"] {
		item.RepairType = in.RepairType
	}
	if isAllField || fieldMaskMap["repairReason"] {
		item.RepairReason = in.RepairReason
	}
	if isAllField || fieldMaskMap["imageUrls"] {
		item.ImageUrls = in.ImageUrls
	}
	if isAllField || fieldMaskMap["repairTime"] {
		item.RepairTime = trans.TimePtrToStrPtr(in.RepairTime)
	}

	return item
}

func (r *WmsRepairOrderDetailRepo) getUserInfoMap(ctx context.Context, results []*ent.WmsRepairOrderDetail) (map[string]string, error) {
	userIdInfo := map[string]string{}
	for _, m := range results {
		if m.CreatedBy != "" {
			userIdInfo[m.CreatedBy] = ""
		}
		if m.UpdatedBy != nil && *m.UpdatedBy != "" {
			userIdInfo[*m.UpdatedBy] = ""
		}
	}

	// 获取所有用户id
	userIds := make([]string, 0, len(userIdInfo))
	for k := range userIdInfo {
		userIds = append(userIds, k)
	}

	// 获取用户信息
	userInfo, err := r.getEntClient(ctx).SysUser.
		Query().
		Where(sysuser.IDIn(userIds...)).
		Select(sysuser.FieldUsername, sysuser.FieldNickname).
		All(ctx)

	if err != nil {
		r.log.Errorf("query user info failed: %s", err.Error())
		return nil, err
	}

	for _, u := range userInfo {
		if u.Nickname != "" {
			userIdInfo[u.ID] = u.Nickname
		} else {
			userIdInfo[u.ID] = u.Username
		}
	}

	return userIdInfo, nil
}

func (r *WmsRepairOrderDetailRepo) convertEntListToProto(
	userMap map[string]string,
	results []*ent.WmsRepairOrderDetail,
	fieldMask []string,
) ([]*v1.WmsRepairOrderDetail, error) {
	items := make([]*v1.WmsRepairOrderDetail, 0, len(results))
	for _, m := range results {
		item := r.convertEntToProto(m, userMap, fieldMask)
		items = append(items, item)
	}
	return items, nil
}

func (r *WmsRepairOrderDetailRepo) ListWmsRepairOrderDetail(ctx context.Context, req *v1.ListWmsRepairOrderDetailRequest) (*v1.ListWmsRepairOrderDetailResponse, error) {
	builder := r.getEntClient(ctx).WmsRepairOrderDetail.Query()
	countBuilder := r.getEntClient(ctx).WmsRepairOrderDetail.Query()

	// 从查询中获取值并且移除
	valuesMap := map[string]string{}
	query := entgo.RemoveQueryKeys(req.GetQuery(), []string{}, valuesMap)
	req.Query = &query

	fieldMask := req.GetFieldMask().GetPaths()
	err, whereSelectors, querySelectors := entgo.BuildQuerySelector(
		req.GetQuery(), req.GetOrQuery(),
		req.GetPage(), req.GetPageSize(), req.GetNoPaging(),
		req.GetOrderBy(), wmsrepairorderdetail.FieldCreatedAt,
		fieldMask,
	)
	if err != nil {
		r.log.Errorf("build query select faild: %s", err.Error())
		return nil, err
	}

	if querySelectors != nil {
		builder.Modify(querySelectors...)
	}

	if whereSelectors != nil {
		countBuilder.Modify(whereSelectors...)
	}

	results, err := builder.All(ctx)
	if err != nil {
		return nil, err
	}

	// 获取用户信息
	userMap, err := r.getUserInfoMap(ctx, results)
	if err != nil {
		return nil, err
	}

	// 转换成proto
	items, err := r.convertEntListToProto(userMap, results, fieldMask)
	if err != nil {
		return nil, err
	}

	count, err := countBuilder.Count(ctx)
	if err != nil {
		return nil, err
	}

	ret := v1.ListWmsRepairOrderDetailResponse{
		Total: int32(count),
		Items: items,
	}

	return &ret, err
}

func (r *WmsRepairOrderDetailRepo) GetWmsRepairOrderDetail(ctx context.Context, id string) (*v1.WmsRepairOrderDetail, error) {
	ret, err := r.getEntClient(ctx).WmsRepairOrderDetail.Get(ctx, id)
	if err != nil && !ent.IsNotFound(err) {
		return nil, err
	}

	userMap, err := r.getUserInfoMap(ctx, []*ent.WmsRepairOrderDetail{ret})
	if err != nil {
		return nil, err
	}

	return r.convertEntToProto(ret, userMap, nil), err
}

func (r *WmsRepairOrderDetailRepo) CreateWmsRepairOrderDetail(ctx context.Context, req *v1.CreateWmsRepairOrderDetailRequest) (*v1.WmsRepairOrderDetail, error) {
	var feature_map map[string]interface{}

	if req.Feature != nil {
		feature_map = req.Feature.AsMap()
	}

	builder := r.getEntClient(ctx).WmsRepairOrderDetail.Create().
		SetRemark(req.Remark).
		SetNillableOrderID(req.OrderId).
		SetNillableMaterialID(req.MaterialId).
		SetNillableMaterialName(req.MaterialName).
		SetCode(trans.StringValue(req.Code)).
		SetNillableEquipmentID(req.EquipmentId).
		SetNillableEquipmentTypeID(req.EquipmentTypeId).
		SetFeature(feature_map).
		SetNillableRepositoryID(req.RepositoryId).
		SetNillableRepositoryAreaID(req.RepositoryAreaId).
		SetNillableRepositoryPositionID(req.RepositoryPositionId).
		SetNillableOwnerID(req.OwnerId).
		SetModelNo(trans.StringValue(req.ModelNo)).
		SetNillableMeasureUnitID(req.MeasureUnitId).
		SetNum(trans.Uint32Value(req.Num)).
		SetNillableRepairType(req.RepairType).
		SetNillableRepairReason(req.RepairReason).
		SetNillableImageUrls(req.ImageUrls).
		SetNillableRepairTime(trans.StrToTime(req.RepairTime)).
		SetCreatedAt(time.Now())

	entutils.SetCreatedBy(ctx, builder.Mutation())

	ret, err := builder.Save(ctx)
	if err != nil {
		r.log.Errorf("insert one data failed: %s", err.Error())
		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

func (r *WmsRepairOrderDetailRepo) UpdateWmsRepairOrderDetail(ctx context.Context, req *v1.UpdateWmsRepairOrderDetailRequest) (*v1.WmsRepairOrderDetail, error) {
	var feature_map map[string]interface{}

	if req.Feature != nil {
		feature_map = req.Feature.AsMap()
	}

	builder := r.getEntClient(ctx).WmsRepairOrderDetail.UpdateOneID(req.Id).
		SetNillableRemark(req.Remark).
		SetNillableOrderID(req.OrderId).
		SetNillableMaterialID(req.MaterialId).
		SetNillableMaterialName(req.MaterialName).
		SetNillableCode(req.Code).
		SetNillableEquipmentID(req.EquipmentId).
		SetNillableEquipmentTypeID(req.EquipmentTypeId).
		SetFeature(feature_map).
		SetNillableRepositoryID(req.RepositoryId).
		SetNillableRepositoryAreaID(req.RepositoryAreaId).
		SetNillableRepositoryPositionID(req.RepositoryPositionId).
		SetNillableOwnerID(req.OwnerId).
		SetNillableModelNo(req.ModelNo).
		SetNillableMeasureUnitID(req.MeasureUnitId).
		SetNillableNum(req.Num).
		SetNillableRepairType(req.RepairType).
		SetNillableRepairReason(req.RepairReason).
		SetNillableImageUrls(req.ImageUrls).
		SetNillableRepairTime(trans.StrToTime(req.RepairTime)).
		SetUpdatedAt(time.Now())

	entutils.SetUpdatedBy(ctx, builder.Mutation())

	ret, err := builder.Save(ctx)
	if err != nil {
		r.log.Errorf("update one data failed: %s", err.Error())
		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

func (r *WmsRepairOrderDetailRepo) DeleteWmsRepairOrderDetail(ctx context.Context, id string) (bool, error) {
	err := r.getEntClient(ctx).WmsRepairOrderDetail.
		DeleteOneID(id).
		Exec(ctx)
	return err != nil, err
}

func (r *WmsRepairOrderDetailRepo) MultiDeleteWmsRepairOrderDetail(ctx context.Context, ids []string) (bool, error) {
	_, err := r.getEntClient(ctx).WmsRepairOrderDetail.
		Delete().
		Where(wmsrepairorderdetail.IDIn(ids...)).
		Exec(ctx)
	return err != nil, err
}

func (r *WmsRepairOrderDetailRepo) DeleteWmsRepairOrderDetailByOrderId(ctx context.Context, orderId string) (bool, error) {
	_, err := r.getEntClient(ctx).WmsRepairOrderDetail.
		Delete().
		Where(wmsrepairorderdetail.OrderID(orderId)).
		Exec(ctx)
	return err != nil, err
}

func (r *WmsRepairOrderDetailRepo) GetWmsRepairOrderDetailsByOrderIds(ctx context.Context, orderIds []string) ([]*v1.WmsRepairOrderDetail, error) {
	results, err := r.getEntClient(ctx).WmsRepairOrderDetail.
		Query().
		Where(wmsrepairorderdetail.OrderIDIn(orderIds...)).
		All(ctx)

	if err != nil {
		return nil, err
	}

	userMap, err := r.getUserInfoMap(ctx, results)
	if err != nil {
		return nil, err
	}

	items, err := r.convertEntListToProto(userMap, results, nil)
	if err != nil {
		return nil, err
	}

	return items, nil
}
