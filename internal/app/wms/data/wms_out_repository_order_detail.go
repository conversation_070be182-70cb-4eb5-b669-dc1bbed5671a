package data

import (
	"context"
	"fmt"
	"kratos-mono-demo/internal/app/wms/biz"
	"kratos-mono-demo/internal/data"
	"time"

	conf "kratos-mono-demo/gen/config/conf/v1"

	"kratos-mono-demo/internal/data/ent"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/data/ent/sysuser"
	"kratos-mono-demo/internal/data/ent/wmsoutrepositoryorderdetail"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/structpb"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	entgo "kratos-mono-demo/internal/pkg/go-utils/entgo/query"
	"kratos-mono-demo/internal/pkg/go-utils/stringutil"
	"kratos-mono-demo/internal/pkg/go-utils/trans"
)

var _ biz.WmsOutRepositoryOrderDetailRepo = (*WmsOutRepositoryOrderDetailRepo)(nil)

type WmsOutRepositoryOrderDetailRepo struct {
	data *data.Data
	log  *log.Helper
}

func NewWmsOutRepositoryOrderDetailRepo(c *conf.Config, logger log.Logger, data *data.Data) biz.WmsOutRepositoryOrderDetailRepo {
	l := log.NewHelper(log.With(logger, "module", "wms_out_repository_order_detail/data"))
	return &WmsOutRepositoryOrderDetailRepo{
		data: data,
		log:  l,
	}
}

func (r *WmsOutRepositoryOrderDetailRepo) getEntClient(ctx context.Context) *ent.Client {
	tx := ent.TxFromContext(ctx)
	if tx != nil {
		return tx.Client()
	}
	return r.data.EntClient.Client()
}

func (r *WmsOutRepositoryOrderDetailRepo) convertEntToProto(in *ent.WmsOutRepositoryOrderDetail, userMap map[string]string, fieldMask []string) *v1.WmsOutRepositoryOrderDetail {
	if in == nil {
		return nil
	}

	isAllField := len(fieldMask) == 0
	fieldMaskMap := map[string]bool{}
	for _, f := range fieldMask {
		fieldMaskMap[f] = true
	}

	item := &v1.WmsOutRepositoryOrderDetail{
		Id: in.ID,
	}
	if isAllField || fieldMaskMap["createdAt"] {
		item.CreatedAt = trans.TimeToStrPtr(in.CreatedAt)
	}
	if isAllField || fieldMaskMap["updatedAt"] {
		item.UpdatedAt = trans.TimeToStrPtr(in.UpdatedAt)
	}
	if isAllField || fieldMaskMap["createdBy"] {
		item.CreatedBy = trans.String(in.CreatedBy)
		if stringutil.IsNotEmpty(item.CreatedBy) {
			item.CreatedBy = trans.String(userMap[*item.CreatedBy])
		}
	}
	if isAllField || fieldMaskMap["updatedBy"] {
		item.UpdatedBy = in.UpdatedBy
		if stringutil.IsNotEmpty(item.UpdatedBy) {
			item.UpdatedBy = trans.String(userMap[*item.UpdatedBy])
		}
	}
	if isAllField || fieldMaskMap["remark"] {
		item.Remark = in.Remark
	}
	if isAllField || fieldMaskMap["outRepositoryOrderId"] {
		item.OutRepositoryOrderId = trans.String(in.OutRepositoryOrderID)
	}
	if isAllField || fieldMaskMap["equipmentTypeId"] {
		item.EquipmentTypeId = trans.String(in.EquipmentTypeID)
	}
	if isAllField || fieldMaskMap["equipmentId"] {
		item.EquipmentId = trans.String(in.EquipmentID)
	}
	if isAllField || fieldMaskMap["name"] {
		item.Name = trans.String(in.Name)
	}
	if isAllField || fieldMaskMap["code"] {
		item.Code = trans.String(in.Code)
	}
	if isAllField || fieldMaskMap["codeType"] {
		item.CodeType = trans.String(in.CodeType)
	}
	if isAllField || fieldMaskMap["modelNo"] {
		item.ModelNo = trans.String(in.ModelNo)
	}
	if isAllField || fieldMaskMap["measureUnitId"] {
		item.MeasureUnitId = trans.String(in.MeasureUnitID)
	}
	if isAllField || fieldMaskMap["providerId"] {
		item.ProviderId = trans.String(in.ProviderID)
	}
	if isAllField || fieldMaskMap["num"] {
		item.Num = trans.Uint32(in.Num)
	}
	if isAllField || fieldMaskMap["price"] {
		item.Price = trans.Uint64MoneyValueToYuanFloat64(in.Price)
	}
	if isAllField || fieldMaskMap["repositoryId"] {
		item.RepositoryId = trans.String(in.RepositoryID)
	}
	if isAllField || fieldMaskMap["repositoryAreaId"] {
		item.RepositoryAreaId = trans.String(in.RepositoryAreaID)
	}
	if isAllField || fieldMaskMap["repositoryPositionId"] {
		item.RepositoryPositionId = trans.String(in.RepositoryPositionID)
	}
	if isAllField || fieldMaskMap["orderNo"] {
		item.OrderNo = trans.String(in.OrderNo)
	}
	if isAllField || fieldMaskMap["feature"] {
		item.Feature, _ = structpb.NewStruct(in.Feature)
	}

	if isAllField || fieldMaskMap["materialId"] {
		item.MaterialId = in.MaterialID
	}

	if in.Edges.Equipment != nil {
		item.Name = trans.String(in.Edges.Equipment.Name)
		item.ModelNo = trans.String(in.Edges.Equipment.ModelNo)
	}

	if in.Edges.EquipmentType != nil {
		item.EquipmentTypeName = trans.String(in.Edges.EquipmentType.Name)
	}

	if in.Edges.Repository != nil && in.Edges.RepositoryArea != nil && in.Edges.RepositoryPosition != nil {
		item.RepositoryDisplayName = trans.String(fmt.Sprintf("%s-%s-%s", in.Edges.Repository.Name, in.Edges.RepositoryArea.Name, in.Edges.RepositoryPosition.Name))
	}

	return item
}

func (r *WmsOutRepositoryOrderDetailRepo) getUserInfoMap(ctx context.Context, results []*ent.WmsOutRepositoryOrderDetail) (map[string]string, error) {
	userIdInfo := map[string]string{}
	for _, m := range results {
		if m.CreatedBy != "" {
			userIdInfo[m.CreatedBy] = ""
		}
		if m.UpdatedBy != nil && *m.UpdatedBy != "" {
			userIdInfo[*m.UpdatedBy] = ""
		}
	}

	// 获取所有用户id
	userIds := make([]string, 0, len(userIdInfo))
	for k := range userIdInfo {
		userIds = append(userIds, k)
	}

	// 获取用户信息
	userInfo, err := r.getEntClient(ctx).SysUser.
		Query().
		Where(sysuser.IDIn(userIds...)).
		Select(sysuser.FieldUsername, sysuser.FieldNickname).
		All(ctx)

	if err != nil {
		r.log.Errorf("query user info failed: %s", err.Error())
		return nil, err
	}

	for _, u := range userInfo {
		if u.Nickname != "" {
			userIdInfo[u.ID] = u.Nickname
		} else {
			userIdInfo[u.ID] = u.Username
		}
	}

	return userIdInfo, nil
}

func (r *WmsOutRepositoryOrderDetailRepo) convertEntListToProto(
	userMap map[string]string,
	results []*ent.WmsOutRepositoryOrderDetail,
	fieldMask []string,
) ([]*v1.WmsOutRepositoryOrderDetail, error) {
	items := make([]*v1.WmsOutRepositoryOrderDetail, 0, len(results))
	for _, m := range results {
		item := r.convertEntToProto(m, userMap, fieldMask)
		items = append(items, item)
	}
	return items, nil
}

func (r *WmsOutRepositoryOrderDetailRepo) ListWmsOutRepositoryOrderDetail(ctx context.Context, req *v1.ListWmsOutRepositoryOrderDetailRequest) (*v1.ListWmsOutRepositoryOrderDetailResponse, error) {
	builder := r.getEntClient(ctx).WmsOutRepositoryOrderDetail.Query()
	countBuilder := r.getEntClient(ctx).WmsOutRepositoryOrderDetail.Query()

	// 从查询中获取值并且移除
	valuesMap := map[string]string{}
	query := entgo.RemoveQueryKeys(req.GetQuery(), []string{}, valuesMap)
	req.Query = &query

	fieldMask := req.GetFieldMask().GetPaths()
	err, whereSelectors, querySelectors := entgo.BuildQuerySelector(
		req.GetQuery(), req.GetOrQuery(),
		req.GetPage(), req.GetPageSize(), req.GetNoPaging(),
		req.GetOrderBy(), wmsoutrepositoryorderdetail.FieldCreatedAt,
		fieldMask,
	)
	if err != nil {
		r.log.Errorf("build query select faild: %s", err.Error())
		return nil, err
	}

	if querySelectors != nil {
		builder.Modify(querySelectors...)
	}

	if whereSelectors != nil {
		countBuilder.Modify(whereSelectors...)
	}

	builder.WithEquipment()
	builder.WithEquipmentType()
	builder.WithRepository()
	builder.WithRepositoryArea()
	builder.WithRepositoryPosition()

	results, err := builder.All(ctx)
	if err != nil {
		return nil, err
	}

	// 获取用户信息
	userMap, err := r.getUserInfoMap(ctx, results)
	if err != nil {
		return nil, err
	}

	// 转换成proto
	items, err := r.convertEntListToProto(userMap, results, fieldMask)
	if err != nil {
		return nil, err
	}

	count, err := countBuilder.Count(ctx)
	if err != nil {
		return nil, err
	}

	ret := v1.ListWmsOutRepositoryOrderDetailResponse{
		Total: int32(count),
		Items: items,
	}

	return &ret, err
}

func (r *WmsOutRepositoryOrderDetailRepo) GetWmsOutRepositoryOrderDetail(ctx context.Context, id string) (*v1.WmsOutRepositoryOrderDetail, error) {
	ret, err := r.getEntClient(ctx).WmsOutRepositoryOrderDetail.Get(ctx, id)
	if err != nil && !ent.IsNotFound(err) {
		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

func (r *WmsOutRepositoryOrderDetailRepo) CreateWmsOutRepositoryOrderDetail(ctx context.Context, req *v1.CreateWmsOutRepositoryOrderDetailRequest) (*v1.WmsOutRepositoryOrderDetail, error) {
	var feature_map map[string]interface{}

	if req.Feature != nil {
		feature_map = req.Feature.AsMap()
	}

	builder := r.getEntClient(ctx).WmsOutRepositoryOrderDetail.Create().
		SetRemark(req.Remark).
		SetNillableOutRepositoryOrderID(req.OutRepositoryOrderId).
		SetNillableEquipmentTypeID(req.EquipmentTypeId).
		SetNillableEquipmentID(req.EquipmentId).
		SetNillableName(req.Name).
		// SetCode(trans.StringValue(req.Code)).
		SetNillableCode(req.Code).
		SetNillableCodeType(req.CodeType).
		SetModelNo(trans.StringValue(req.ModelNo)).
		SetNillableMeasureUnitID(req.MeasureUnitId).
		SetProviderID(trans.StringValue(req.ProviderId)).
		SetNum(trans.Uint32Value(req.Num)).
		SetPrice(trans.Float64MoneyToFenUInt64Value(req.Price)).
		SetNillableRepositoryID(req.RepositoryId).
		SetNillableRepositoryAreaID(req.RepositoryAreaId).
		SetNillableRepositoryPositionID(req.RepositoryPositionId).
		SetNillableOrderNo(req.OrderNo).
		SetFeature(feature_map).
		SetMaterialID(req.MaterialId).
		SetCreatedAt(time.Now())

	entutils.SetCreatedBy(ctx, builder.Mutation())

	ret, err := builder.Save(ctx)
	if err != nil {
		r.log.Errorf("insert one data failed: %s", err.Error())
		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

func (r *WmsOutRepositoryOrderDetailRepo) UpdateWmsOutRepositoryOrderDetail(ctx context.Context, req *v1.UpdateWmsOutRepositoryOrderDetailRequest) (*v1.WmsOutRepositoryOrderDetail, error) {
	var feature_map map[string]interface{}

	if req.Feature != nil {
		feature_map = req.Feature.AsMap()
	}

	builder := r.getEntClient(ctx).WmsOutRepositoryOrderDetail.UpdateOneID(req.Id).
		SetNillableRemark(req.Remark).
		SetNillableOutRepositoryOrderID(req.OutRepositoryOrderId).
		SetNillableEquipmentTypeID(req.EquipmentTypeId).
		SetNillableEquipmentID(req.EquipmentId).
		SetNillableName(req.Name).
		SetNillableCode(req.Code).
		SetNillableCodeType(req.CodeType).
		SetNillableModelNo(req.ModelNo).
		SetNillableMeasureUnitID(req.MeasureUnitId).
		SetNillableProviderID(req.ProviderId).
		SetNillableNum(req.Num).
		SetNillablePrice(trans.Float64MoneyToFenUInt64(req.Price)).
		SetNillableRepositoryID(req.RepositoryId).
		SetNillableRepositoryAreaID(req.RepositoryAreaId).
		SetNillableRepositoryPositionID(req.RepositoryPositionId).
		SetNillableOrderNo(req.OrderNo).
		SetMaterialID(req.MaterialId).
		SetUpdatedAt(time.Now())

	if feature_map != nil {
		builder.SetFeature(feature_map)
	}

	entutils.SetUpdatedBy(ctx, builder.Mutation())

	ret, err := builder.Save(ctx)
	if err != nil {
		r.log.Errorf("update one data failed: %s", err.Error())
		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

func (r *WmsOutRepositoryOrderDetailRepo) DeleteWmsOutRepositoryOrderDetail(ctx context.Context, id string) (bool, error) {
	err := r.getEntClient(ctx).WmsOutRepositoryOrderDetail.
		DeleteOneID(id).
		Exec(ctx)
	return err != nil, err
}

func (r *WmsOutRepositoryOrderDetailRepo) MultiDeleteWmsOutRepositoryOrderDetail(ctx context.Context, ids []string) (bool, error) {
	_, err := r.getEntClient(ctx).WmsOutRepositoryOrderDetail.
		Delete().
		Where(wmsoutrepositoryorderdetail.IDIn(ids...)).
		Exec(ctx)
	return err != nil, err
}

// 根据出库单ID查询出库单明细
func (r *WmsOutRepositoryOrderDetailRepo) ListWmsOutRepositoryOrderDetailByOrderID(ctx context.Context, orderID string) (*v1.ListWmsOutRepositoryOrderDetailResponse, error) {
	newQuery := entgo.AppendQueryKeys("", map[string]string{
		"outRepositoryOrderId": orderID,
	})

	return r.ListWmsOutRepositoryOrderDetail(ctx, &v1.ListWmsOutRepositoryOrderDetailRequest{
		Query:    &newQuery,
		NoPaging: trans.Bool(true),
	})
}

// 根据出库单ID删除出库单明细
func (r *WmsOutRepositoryOrderDetailRepo) DeleteWmsOutRepositoryOrderDetailByOrderID(ctx context.Context, orderID string) (bool, error) {
	_, err := r.getEntClient(ctx).WmsOutRepositoryOrderDetail.
		Delete().
		Where(wmsoutrepositoryorderdetail.OutRepositoryOrderID(orderID)).
		Exec(ctx)
	return err != nil, err
}
