package data

import (
	"context"
	"kratos-mono-demo/internal/app/wms/biz"
	"kratos-mono-demo/internal/data"
	"time"

	conf "kratos-mono-demo/gen/config/conf/v1"

	"kratos-mono-demo/internal/data/ent"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/data/ent/sysuser"
	"kratos-mono-demo/internal/data/ent/wmsclaimorderdetail"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/structpb"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	entgo "kratos-mono-demo/internal/pkg/go-utils/entgo/query"
	"kratos-mono-demo/internal/pkg/go-utils/stringutil"
	"kratos-mono-demo/internal/pkg/go-utils/trans"
)

var _ biz.WmsClaimOrderDetailRepo = (*WmsClaimOrderDetailRepo)(nil)

type WmsClaimOrderDetailRepo struct {
	data *data.Data
	log  *log.Helper
}

func NewWmsClaimOrderDetailRepo(c *conf.Config, logger log.Logger, data *data.Data) biz.WmsClaimOrderDetailRepo {
	l := log.NewHelper(log.With(logger, "module", "wms_claim_order_detail/data"))
	return &WmsClaimOrderDetailRepo{
		data: data,
		log:  l,
	}
}

func (r *WmsClaimOrderDetailRepo) getEntClient(ctx context.Context) *ent.Client {
	tx := ent.TxFromContext(ctx)
	if tx != nil {
		return tx.Client()
	}
	return r.data.EntClient.Client()
}

func (r *WmsClaimOrderDetailRepo) convertEntToProto(in *ent.WmsClaimOrderDetail, userMap map[string]string, fieldMask []string) *v1.WmsClaimOrderDetail {
	if in == nil {
		return nil
	}

	isAllField := len(fieldMask) == 0
	fieldMaskMap := map[string]bool{}
	for _, f := range fieldMask {
		fieldMaskMap[f] = true
	}

	item := &v1.WmsClaimOrderDetail{
		Id: in.ID,
	}
	if isAllField || fieldMaskMap["createdAt"] {
		item.CreatedAt = trans.TimeToStrPtr(in.CreatedAt)
	}
	if isAllField || fieldMaskMap["updatedAt"] {
		item.UpdatedAt = trans.TimeToStrPtr(in.UpdatedAt)
	}
	if isAllField || fieldMaskMap["createdBy"] {
		item.CreatedBy = trans.String(in.CreatedBy)
		if stringutil.IsNotEmpty(item.CreatedBy) {
			item.CreatedBy = trans.String(userMap[*item.CreatedBy])
		}
	}
	if isAllField || fieldMaskMap["updatedBy"] {
		item.UpdatedBy = in.UpdatedBy
		if stringutil.IsNotEmpty(item.UpdatedBy) {
			item.UpdatedBy = trans.String(userMap[*item.UpdatedBy])
		}
	}
	if isAllField || fieldMaskMap["remark"] {
		item.Remark = in.Remark
	}
	if isAllField || fieldMaskMap["orderId"] {
		item.OrderId = trans.String(in.OrderID)
	}
	if isAllField || fieldMaskMap["equipmentTypeId"] {
		item.EquipmentTypeId = trans.String(in.EquipmentTypeID)
	}
	if isAllField || fieldMaskMap["equipmentId"] {
		item.EquipmentId = trans.String(in.EquipmentID)
	}
	if isAllField || fieldMaskMap["name"] {
		item.Name = trans.String(in.Name)
	}
	if isAllField || fieldMaskMap["modelNo"] {
		item.ModelNo = trans.String(in.ModelNo)
	}
	if isAllField || fieldMaskMap["measureUnitId"] {
		item.MeasureUnitId = in.MeasureUnitID
	}
	if isAllField || fieldMaskMap["num"] {
		item.Num = trans.Uint32(in.Num)
	}
	if isAllField || fieldMaskMap["repositoryId"] {
		item.RepositoryId = trans.String(in.RepositoryID)
	}
	if isAllField || fieldMaskMap["feature"] {
		if in.Feature != nil {
			item.Feature, _ = structpb.NewStruct(in.Feature)
		}
	}

	return item
}

func (r *WmsClaimOrderDetailRepo) getUserInfoMap(ctx context.Context, results []*ent.WmsClaimOrderDetail) (map[string]string, error) {
	userIdInfo := map[string]string{}
	for _, m := range results {
		if m.CreatedBy != "" {
			userIdInfo[m.CreatedBy] = ""
		}
		if m.UpdatedBy != nil && *m.UpdatedBy != "" {
			userIdInfo[*m.UpdatedBy] = ""
		}
	}

	// 获取所有用户id
	userIds := make([]string, 0, len(userIdInfo))
	for k := range userIdInfo {
		userIds = append(userIds, k)
	}

	// 获取用户信息
	userInfo, err := r.getEntClient(ctx).SysUser.
		Query().
		Where(sysuser.IDIn(userIds...)).
		Select(sysuser.FieldUsername, sysuser.FieldNickname).
		All(ctx)

	if err != nil {
		r.log.Errorf("query user info failed: %s", err.Error())
		return nil, err
	}

	for _, u := range userInfo {
		if u.Nickname != "" {
			userIdInfo[u.ID] = u.Nickname
		} else {
			userIdInfo[u.ID] = u.Username
		}
	}

	return userIdInfo, nil
}

func (r *WmsClaimOrderDetailRepo) convertEntListToProto(
	userMap map[string]string,
	results []*ent.WmsClaimOrderDetail,
	fieldMask []string,
) ([]*v1.WmsClaimOrderDetail, error) {
	items := make([]*v1.WmsClaimOrderDetail, 0, len(results))
	for _, m := range results {
		item := r.convertEntToProto(m, userMap, fieldMask)
		items = append(items, item)
	}
	return items, nil
}

func (r *WmsClaimOrderDetailRepo) ListWmsClaimOrderDetail(ctx context.Context, req *v1.ListWmsClaimOrderDetailRequest) (*v1.ListWmsClaimOrderDetailResponse, error) {
	builder := r.getEntClient(ctx).WmsClaimOrderDetail.Query()
	countBuilder := r.getEntClient(ctx).WmsClaimOrderDetail.Query()

	// 从查询中获取值并且移除
	valuesMap := map[string]string{}
	query := entgo.RemoveQueryKeys(req.GetQuery(), []string{}, valuesMap)
	req.Query = &query

	fieldMask := req.GetFieldMask().GetPaths()
	err, whereSelectors, querySelectors := entgo.BuildQuerySelector(
		req.GetQuery(), req.GetOrQuery(),
		req.GetPage(), req.GetPageSize(), req.GetNoPaging(),
		req.GetOrderBy(), wmsclaimorderdetail.FieldCreatedAt,
		fieldMask,
	)
	if err != nil {
		r.log.Errorf("build query select faild: %s", err.Error())
		return nil, err
	}

	if querySelectors != nil {
		builder.Modify(querySelectors...)
	}

	if whereSelectors != nil {
		countBuilder.Modify(whereSelectors...)
	}

	results, err := builder.All(ctx)
	if err != nil {
		return nil, err
	}

	// 获取用户信息
	userMap, err := r.getUserInfoMap(ctx, results)
	if err != nil {
		return nil, err
	}

	// 转换成proto
	items, err := r.convertEntListToProto(userMap, results, fieldMask)
	if err != nil {
		return nil, err
	}

	count, err := countBuilder.Count(ctx)
	if err != nil {
		return nil, err
	}

	ret := v1.ListWmsClaimOrderDetailResponse{
		Total: int32(count),
		Items: items,
	}

	return &ret, err
}

func (r *WmsClaimOrderDetailRepo) GetWmsClaimOrderDetail(ctx context.Context, id string) (*v1.WmsClaimOrderDetail, error) {
	ret, err := r.getEntClient(ctx).WmsClaimOrderDetail.Get(ctx, id)
	if err != nil && !ent.IsNotFound(err) {
		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

func (r *WmsClaimOrderDetailRepo) CreateWmsClaimOrderDetail(ctx context.Context, req *v1.CreateWmsClaimOrderDetailRequest) (*v1.WmsClaimOrderDetail, error) {
	var feature_map map[string]interface{}
	if req.Feature != nil {
		feature_map = req.Feature.AsMap()
	}

	builder := r.getEntClient(ctx).WmsClaimOrderDetail.Create().
		SetRemark(req.Remark).
		SetNillableOrderID(req.OrderId).
		SetNillableEquipmentTypeID(req.EquipmentTypeId).
		SetNillableEquipmentID(req.EquipmentId).
		SetNillableName(req.Name).
		SetModelNo(trans.StringValue(req.ModelNo)).
		SetNillableMeasureUnitID(req.MeasureUnitId).
		SetNum(trans.Uint32Value(req.Num)).
		SetNillableRepositoryID(req.RepositoryId).
		SetFeature(feature_map).
		SetCreatedAt(time.Now())

	entutils.SetCreatedBy(ctx, builder.Mutation())

	ret, err := builder.Save(ctx)
	if err != nil {
		r.log.Errorf("insert one data failed: %s", err.Error())
		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

func (r *WmsClaimOrderDetailRepo) UpdateWmsClaimOrderDetail(ctx context.Context, req *v1.UpdateWmsClaimOrderDetailRequest) (*v1.WmsClaimOrderDetail, error) {
	var feature_map map[string]interface{}

	if req.Feature != nil {
		feature_map = req.Feature.AsMap()
	}

	builder := r.getEntClient(ctx).WmsClaimOrderDetail.UpdateOneID(req.Id).
		SetNillableRemark(req.Remark).
		SetNillableOrderID(req.OrderId).
		SetNillableEquipmentTypeID(req.EquipmentTypeId).
		SetNillableEquipmentID(req.EquipmentId).
		SetNillableName(req.Name).
		SetNillableModelNo(req.ModelNo).
		SetNillableMeasureUnitID(req.MeasureUnitId).
		SetNillableNum(req.Num).
		SetNillableRepositoryID(req.RepositoryId).
		SetFeature(feature_map).
		SetUpdatedAt(time.Now())

	entutils.SetUpdatedBy(ctx, builder.Mutation())

	ret, err := builder.Save(ctx)
	if err != nil {
		r.log.Errorf("update one data failed: %s", err.Error())
		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

func (r *WmsClaimOrderDetailRepo) DeleteWmsClaimOrderDetail(ctx context.Context, id string) (bool, error) {
	err := r.getEntClient(ctx).WmsClaimOrderDetail.
		DeleteOneID(id).
		Exec(ctx)
	return err != nil, err
}

func (r *WmsClaimOrderDetailRepo) MultiDeleteWmsClaimOrderDetail(ctx context.Context, ids []string) (bool, error) {
	_, err := r.getEntClient(ctx).WmsClaimOrderDetail.
		Delete().
		Where(wmsclaimorderdetail.IDIn(ids...)).
		Exec(ctx)
	return err != nil, err
}

// 根据领用单id查询明细
func (r *WmsClaimOrderDetailRepo) GetWmsClaimOrderDetailsByOrderIds(ctx context.Context, orderIds []string) ([]*v1.WmsClaimOrderDetail, error) {
	results, err := r.getEntClient(ctx).WmsClaimOrderDetail.Query().Where(wmsclaimorderdetail.OrderIDIn(orderIds...)).All(ctx)
	if err != nil {
		return nil, err
	}

	return r.convertEntListToProto(nil, results, nil)
}

// 根据领用单ID删除领用单明细
func (r *WmsClaimOrderDetailRepo) DeleteWmsClaimOrderDetailByOrderId(ctx context.Context, orderId string) (bool, error) {
	_, err := r.getEntClient(ctx).WmsClaimOrderDetail.
		Delete().
		Where(wmsclaimorderdetail.OrderID(orderId)).
		Exec(ctx)
	return err != nil, err
}
