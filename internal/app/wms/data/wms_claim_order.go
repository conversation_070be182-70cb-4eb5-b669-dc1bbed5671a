package data

import (
	"context"
	"kratos-mono-demo/internal/app/wms/biz"
	"kratos-mono-demo/internal/data"
	"time"

	conf "kratos-mono-demo/gen/config/conf/v1"

	"kratos-mono-demo/internal/data/ent"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/data/ent/sysorganization"
	"kratos-mono-demo/internal/data/ent/sysuser"
	"kratos-mono-demo/internal/data/ent/sysuserorganization"
	"kratos-mono-demo/internal/data/ent/wmsclaimorder"

	"github.com/go-kratos/kratos/v2/log"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	"kratos-mono-demo/internal/pkg/go-utils/arrayutil"
	entgo "kratos-mono-demo/internal/pkg/go-utils/entgo/query"
	"kratos-mono-demo/internal/pkg/go-utils/orderutil"
	"kratos-mono-demo/internal/pkg/go-utils/stringutil"
	"kratos-mono-demo/internal/pkg/go-utils/trans"
)

var _ biz.WmsClaimOrderRepo = (*WmsClaimOrderRepo)(nil)

type WmsClaimOrderRepo struct {
	data *data.Data
	log  *log.Helper
}

func NewWmsClaimOrderRepo(c *conf.Config, logger log.Logger, data *data.Data) biz.WmsClaimOrderRepo {
	l := log.NewHelper(log.With(logger, "module", "wms_claim_order/data"))
	return &WmsClaimOrderRepo{
		data: data,
		log:  l,
	}
}

func (r *WmsClaimOrderRepo) getEntClient(ctx context.Context) *ent.Client {
	tx := ent.TxFromContext(ctx)
	if tx != nil {
		return tx.Client()
	}
	return r.data.EntClient.Client()
}

func (r *WmsClaimOrderRepo) convertEntToProto(in *ent.WmsClaimOrder, userMap map[string]string, fieldMask []string) *v1.WmsClaimOrder {
	if in == nil {
		return nil
	}

	isAllField := len(fieldMask) == 0
	fieldMaskMap := map[string]bool{}
	for _, f := range fieldMask {
		fieldMaskMap[f] = true
	}

	item := &v1.WmsClaimOrder{
		Id: in.ID,
	}
	if isAllField || fieldMaskMap["createdAt"] {
		item.CreatedAt = trans.TimeToStrPtr(in.CreatedAt)
	}
	if isAllField || fieldMaskMap["updatedAt"] {
		item.UpdatedAt = trans.TimeToStrPtr(in.UpdatedAt)
	}
	if isAllField || fieldMaskMap["createdBy"] {
		item.CreatedBy = trans.String(in.CreatedBy)
		if stringutil.IsNotEmpty(item.CreatedBy) {
			item.CreatedBy = trans.String(userMap[*item.CreatedBy])
		}
	}
	if isAllField || fieldMaskMap["updatedBy"] {
		item.UpdatedBy = in.UpdatedBy
		if stringutil.IsNotEmpty(item.UpdatedBy) {
			item.UpdatedBy = trans.String(userMap[*item.UpdatedBy])
		}
	}
	if isAllField || fieldMaskMap["remark"] {
		item.Remark = in.Remark
	}
	if isAllField || fieldMaskMap["contractUrls"] {
		item.ContractUrls = trans.String(in.ContractUrls)
	}
	if isAllField || fieldMaskMap["invoiceUrls"] {
		item.InvoiceUrls = trans.String(in.InvoiceUrls)
	}
	if isAllField || fieldMaskMap["auditUrls"] {
		item.AuditUrls = trans.String(in.AuditUrls)
	}
	if isAllField || fieldMaskMap["otherUrls"] {
		item.OtherUrls = trans.String(in.OtherUrls)
	}
	if isAllField || fieldMaskMap["orderNo"] {
		item.OrderNo = trans.String(in.OrderNo)
	}
	if isAllField || fieldMaskMap["repositoryId"] {
		item.RepositoryId = trans.String(in.RepositoryID)
	}
	if isAllField || fieldMaskMap["claimTime"] {
		item.ClaimTime = trans.TimeToStrPtr(in.ClaimTime)
	}
	if isAllField || fieldMaskMap["claimUserId"] {
		item.ClaimUserId = trans.String(in.ClaimUserID)
	}
	if isAllField || fieldMaskMap["status"] {
		item.Status = trans.String(in.Status)
	}
	if isAllField || fieldMaskMap["equipmentNum"] {
		item.EquipmentNum = trans.Uint32(in.EquipmentNum)
	}
	if isAllField || fieldMaskMap["reason"] {
		item.Reason = in.Reason
	}

	return item
}

func (r *WmsClaimOrderRepo) getUserInfoMap(ctx context.Context, results []*ent.WmsClaimOrder) (map[string]string, error) {
	userIdInfo := map[string]string{}
	for _, m := range results {
		if m.CreatedBy != "" {
			userIdInfo[m.CreatedBy] = ""
		}
		if m.UpdatedBy != nil && *m.UpdatedBy != "" {
			userIdInfo[*m.UpdatedBy] = ""
		}
	}

	// 获取所有用户id
	userIds := make([]string, 0, len(userIdInfo))
	for k := range userIdInfo {
		userIds = append(userIds, k)
	}

	// 获取用户信息
	userInfo, err := r.getEntClient(ctx).SysUser.
		Query().
		Where(sysuser.IDIn(userIds...)).
		Select(sysuser.FieldUsername, sysuser.FieldNickname).
		All(ctx)

	if err != nil {
		r.log.Errorf("query user info failed: %s", err.Error())
		return nil, err
	}

	for _, u := range userInfo {
		if u.Nickname != "" {
			userIdInfo[u.ID] = u.Nickname
		} else {
			userIdInfo[u.ID] = u.Username
		}
	}

	return userIdInfo, nil
}

func (r *WmsClaimOrderRepo) convertEntListToProto(
	userMap map[string]string,
	results []*ent.WmsClaimOrder,
	fieldMask []string,
) ([]*v1.WmsClaimOrder, error) {
	items := make([]*v1.WmsClaimOrder, 0, len(results))
	for _, m := range results {
		item := r.convertEntToProto(m, userMap, fieldMask)
		items = append(items, item)
	}
	return items, nil
}

func (r *WmsClaimOrderRepo) ListWmsClaimOrder(ctx context.Context, req *v1.ListWmsClaimOrderRequest) (*v1.ListWmsClaimOrderResponse, error) {
	builder := r.getEntClient(ctx).WmsClaimOrder.Query()
	countBuilder := r.getEntClient(ctx).WmsClaimOrder.Query()

	// 从查询中获取值并且移除
	valuesMap := map[string]string{}
	query := entgo.RemoveQueryKeys(req.GetQuery(), []string{}, valuesMap)
	req.Query = &query

	fieldMask := req.GetFieldMask().GetPaths()
	err, whereSelectors, querySelectors := entgo.BuildQuerySelector(
		req.GetQuery(), req.GetOrQuery(),
		req.GetPage(), req.GetPageSize(), req.GetNoPaging(),
		req.GetOrderBy(), wmsclaimorder.FieldCreatedAt,
		fieldMask,
	)
	if err != nil {
		r.log.Errorf("build query select faild: %s", err.Error())
		return nil, err
	}

	if querySelectors != nil {
		builder.Modify(querySelectors...)
	}

	if whereSelectors != nil {
		countBuilder.Modify(whereSelectors...)
	}

	results, err := builder.All(ctx)
	if err != nil {
		return nil, err
	}

	// 获取用户信息
	userMap, err := r.getUserInfoMap(ctx, results)
	if err != nil {
		return nil, err
	}

	// 转换成proto
	items, err := r.convertEntListToProto(userMap, results, fieldMask)
	if err != nil {
		return nil, err
	}

	count, err := countBuilder.Count(ctx)
	if err != nil {
		return nil, err
	}

	ret := v1.ListWmsClaimOrderResponse{
		Total: int32(count),
		Items: items,
	}

	return &ret, err
}

func (r *WmsClaimOrderRepo) GetWmsClaimOrder(ctx context.Context, id string) (*v1.WmsClaimOrder, error) {
	ret, err := r.getEntClient(ctx).WmsClaimOrder.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取用户信息
	userMap, err := r.getUserInfoMap(ctx, []*ent.WmsClaimOrder{ret})
	if err != nil {
		return nil, err
	}

	return r.convertEntToProto(ret, userMap, nil), err
}

func (r *WmsClaimOrderRepo) CreateWmsClaimOrder(ctx context.Context, req *v1.CreateWmsClaimOrderRequest) (*v1.WmsClaimOrder, error) {
	// 生成领用单号
	orderNo, err := orderutil.GenerateOrderNo("SL")
	if err != nil {
		return nil, err
	}

	builder := r.getEntClient(ctx).WmsClaimOrder.Create().
		SetRemark(req.Remark).
		SetNillableContractUrls(req.ContractUrls).
		SetNillableInvoiceUrls(req.InvoiceUrls).
		SetNillableAuditUrls(req.AuditUrls).
		SetNillableOtherUrls(req.OtherUrls).
		SetOrderNo(orderNo).
		SetNillableRepositoryID(req.RepositoryId).
		SetClaimTime(trans.StrToTimeValue(req.ClaimTime)).
		SetClaimUserID(trans.StringValue(req.ClaimUserId)).
		SetStatus(trans.StringValue(req.Status)).
		SetNillableEquipmentNum(req.EquipmentNum).
		SetNillableReason(req.Reason).
		SetCreatedAt(time.Now())

	entutils.SetCreatedBy(ctx, builder.Mutation())

	ret, err := builder.Save(ctx)
	if err != nil {
		r.log.Errorf("insert one data failed: %s", err.Error())
		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

func (r *WmsClaimOrderRepo) UpdateWmsClaimOrder(ctx context.Context, req *v1.UpdateWmsClaimOrderRequest) (*v1.WmsClaimOrder, error) {

	builder := r.getEntClient(ctx).WmsClaimOrder.UpdateOneID(req.Id).
		SetNillableRemark(req.Remark).
		SetNillableContractUrls(req.ContractUrls).
		SetNillableInvoiceUrls(req.InvoiceUrls).
		SetNillableAuditUrls(req.AuditUrls).
		SetNillableOtherUrls(req.OtherUrls).
		SetNillableOrderNo(req.OrderNo).
		SetNillableRepositoryID(req.RepositoryId).
		SetNillableClaimTime(trans.StrToTime(req.ClaimTime)).
		SetNillableClaimUserID(req.ClaimUserId).
		SetNillableStatus(req.Status).
		SetNillableEquipmentNum(req.EquipmentNum).
		SetNillableReason(req.Reason).
		SetUpdatedAt(time.Now())

	entutils.SetUpdatedBy(ctx, builder.Mutation())

	ret, err := builder.Save(ctx)
	if err != nil {
		r.log.Errorf("update one data failed: %s", err.Error())
		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

func (r *WmsClaimOrderRepo) DeleteWmsClaimOrder(ctx context.Context, id string) (bool, error) {
	err := r.getEntClient(ctx).WmsClaimOrder.
		DeleteOneID(id).
		Exec(ctx)
	return err != nil, err
}

func (r *WmsClaimOrderRepo) MultiDeleteWmsClaimOrder(ctx context.Context, ids []string) (bool, error) {
	_, err := r.getEntClient(ctx).WmsClaimOrder.
		Delete().
		Where(wmsclaimorder.IDIn(ids...)).
		Exec(ctx)
	return err != nil, err
}

// 获取领用单申请用户的组织名称
func (r *WmsClaimOrderRepo) GetWmsClaimOrderUserOrganizationNameMap(ctx context.Context, ids []string) (map[string]string, error) {
	results, err := r.getEntClient(ctx).WmsClaimOrder.Query().Where(wmsclaimorder.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}

	user_ids := arrayutil.Map(results, func(result *ent.WmsClaimOrder) string {
		return result.CreatedBy
	})

	user_organizations, err := r.getEntClient(ctx).SysUserOrganization.Query().Where(sysuserorganization.UserIDIn(user_ids...)).All(ctx)
	if err != nil {
		return nil, err
	}

	organization_ids := arrayutil.Map(user_organizations, func(organization *ent.SysUserOrganization) string {
		return organization.OrganizationID
	})

	organizations, err := r.getEntClient(ctx).SysOrganization.Query().Where(sysorganization.IDIn(organization_ids...)).All(ctx)
	if err != nil {
		return nil, err
	}

	result := map[string]string{}

	for _, item := range results {
		item_user_id := item.CreatedBy
		find_item_organization := arrayutil.Find(user_organizations, func(organization *ent.SysUserOrganization) bool {
			return organization.UserID == item_user_id
		})

		if find_item_organization == nil {
			continue
		}

		find_organization := arrayutil.Find(organizations, func(organization *ent.SysOrganization) bool {
			return organization.ID == find_item_organization.OrganizationID
		})

		if find_organization == nil {
			continue
		}

		result[item.ID] = find_organization.Name
	}

	return result, nil
}

// 根据领用单ID查询领用单
func (r *WmsClaimOrderRepo) GetWmsClaimOrdersByIds(ctx context.Context, ids []string) ([]*v1.WmsClaimOrder, error) {
	results, err := r.getEntClient(ctx).WmsClaimOrder.Query().Where(wmsclaimorder.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}

	return r.convertEntListToProto(nil, results, nil)
}

// 根据关联单号查询申领单
func (r *WmsClaimOrderRepo) GetWmsClaimOrderByOrderNo(ctx context.Context, orderNo string) (*v1.WmsClaimOrder, error) {
	result, err := r.getEntClient(ctx).WmsClaimOrder.Query().Where(wmsclaimorder.OrderNo(orderNo)).First(ctx)
	if err != nil {
		return nil, err
	}

	return r.convertEntToProto(result, nil, nil), nil
}

// 根据状态获取领用单
func (r *WmsClaimOrderRepo) GetWmsClaimOrdersByStatus(ctx context.Context, status []string) ([]*v1.WmsClaimOrder, error) {
	results, err := r.getEntClient(ctx).WmsClaimOrder.Query().Where(wmsclaimorder.StatusIn(status...)).All(ctx)
	if err != nil {
		return nil, err
	}

	return r.convertEntListToProto(nil, results, nil)
}
