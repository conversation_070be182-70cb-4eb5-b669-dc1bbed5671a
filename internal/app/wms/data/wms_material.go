package data

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"kratos-mono-demo/internal/app/wms/biz"
	"kratos-mono-demo/internal/data"
	"sort"
	"strings"
	"time"

	conf "kratos-mono-demo/gen/config/conf/v1"

	"kratos-mono-demo/internal/data/ent"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/data/ent/predicate"
	"kratos-mono-demo/internal/data/ent/sysuser"
	"kratos-mono-demo/internal/data/ent/sysuserorganization"
	"kratos-mono-demo/internal/data/ent/wmsapprovaltask"
	"kratos-mono-demo/internal/data/ent/wmsequipment"
	"kratos-mono-demo/internal/data/ent/wmsequipmenttype"
	"kratos-mono-demo/internal/data/ent/wmsfirestation"
	"kratos-mono-demo/internal/data/ent/wmsmaterial"
	"kratos-mono-demo/internal/data/ent/wmsmeasureunit"
	"kratos-mono-demo/internal/data/ent/wmsrepository"
	"kratos-mono-demo/internal/data/ent/wmsrepositoryarea"
	"kratos-mono-demo/internal/data/ent/wmsrepositoryposition"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/structpb"

	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	entgo "kratos-mono-demo/internal/pkg/go-utils/entgo/query"
	"kratos-mono-demo/internal/pkg/go-utils/stringutil"
	"kratos-mono-demo/internal/pkg/go-utils/trans"
)

var _ biz.WmsMaterialRepo = (*WmsMaterialRepo)(nil)

type WmsMaterialRepo struct {
	data *data.Data
	log  *log.Helper
}

func NewWmsMaterialRepo(c *conf.Config, logger log.Logger, data *data.Data) biz.WmsMaterialRepo {
	l := log.NewHelper(log.With(logger, "module", "wms_material/data"))
	return &WmsMaterialRepo{
		data: data,
		log:  l,
	}
}

func (r *WmsMaterialRepo) getEntClient(ctx context.Context) *ent.Client {
	tx := ent.TxFromContext(ctx)
	if tx != nil {
		return tx.Client()
	}
	return r.data.EntClient.Client()
}

func (r *WmsMaterialRepo) convertEntToProto(in *ent.WmsMaterial, userMap map[string]string, fieldMask []string) *v1.WmsMaterial {
	if in == nil {
		return nil
	}

	isAllField := len(fieldMask) == 0
	fieldMaskMap := map[string]bool{}
	for _, f := range fieldMask {
		fieldMaskMap[f] = true
	}

	item := &v1.WmsMaterial{
		Id: in.ID,
	}
	if isAllField || fieldMaskMap["createdAt"] {
		item.CreatedAt = trans.TimeToStrPtr(in.CreatedAt)
	}
	if isAllField || fieldMaskMap["updatedAt"] {
		item.UpdatedAt = trans.TimeToStrPtr(in.UpdatedAt)
	}
	if isAllField || fieldMaskMap["createdBy"] {
		item.CreatedBy = trans.String(in.CreatedBy)
		if stringutil.IsNotEmpty(item.CreatedBy) {
			item.CreatedBy = trans.String(userMap[*item.CreatedBy])
		}
	}
	if isAllField || fieldMaskMap["updatedBy"] {
		item.UpdatedBy = in.UpdatedBy
		if stringutil.IsNotEmpty(item.UpdatedBy) {
			item.UpdatedBy = trans.String(userMap[*item.UpdatedBy])
		}
	}
	if isAllField || fieldMaskMap["status"] {
		item.Status = trans.Int32(in.Status)
	}
	if isAllField || fieldMaskMap["remark"] {
		item.Remark = in.Remark
	}
	if isAllField || fieldMaskMap["fireStationId"] {
		item.FireStationId = trans.String(in.FireStationID)
	}
	if isAllField || fieldMaskMap["materialType"] {
		item.MaterialType = trans.String(in.MaterialType)

		if in.Edges.Equipment != nil {
			item.MaterialType = trans.String(in.Edges.Equipment.Type)
		}
	}
	if isAllField || fieldMaskMap["equipmentTypeId"] {
		item.EquipmentTypeId = trans.String(in.EquipmentTypeID)

		if in.Edges.EquipmentType != nil {
			item.EquipmentTypeName = trans.String(in.Edges.EquipmentType.Name)
		}
	}
	if isAllField || fieldMaskMap["equipmentId"] {
		item.EquipmentId = trans.String(in.EquipmentID)
	}
	if isAllField || fieldMaskMap["code"] {
		item.Code = trans.String(in.Code)
	}
	if isAllField || fieldMaskMap["codeType"] {
		item.CodeType = in.CodeType
	}
	if isAllField || fieldMaskMap["name"] {
		item.Name = in.Name

		if in.Edges.Equipment != nil {
			item.Name = trans.String(in.Edges.Equipment.Name)
		}
	}
	if isAllField || fieldMaskMap["modelNo"] {
		item.ModelNo = trans.String(in.ModelNo)

		if in.Edges.Equipment != nil {
			item.ModelNo = trans.String(in.Edges.Equipment.ModelNo)
		}
	}
	if isAllField || fieldMaskMap["providerId"] {
		item.ProviderId = trans.String(in.ProviderID)
	}
	if isAllField || fieldMaskMap["num"] {
		item.Num = trans.Uint32(in.Num)
	}
	if isAllField || fieldMaskMap["price"] {
		item.Price = trans.Uint64MoneyValueToYuanFloat64(in.Price)
	}
	if isAllField || fieldMaskMap["repositoryId"] {
		item.RepositoryId = trans.String(in.RepositoryID)

		if in.Edges.Repository != nil {
			item.RepositoryName = trans.String(in.Edges.Repository.Name)
		}
	}
	if isAllField || fieldMaskMap["repositoryAreaId"] {
		item.RepositoryAreaId = trans.String(in.RepositoryAreaID)

		if in.Edges.RepositoryArea != nil {
			item.RepositoryAreaName = trans.String(in.Edges.RepositoryArea.Name)
		}
	}
	if isAllField || fieldMaskMap["repositoryPositionId"] {
		item.RepositoryPositionId = trans.String(in.RepositoryPositionID)

		if in.Edges.RepositoryPosition != nil {
			item.RepositoryPositionName = trans.String(in.Edges.RepositoryPosition.Name)
		}
	}
	if isAllField || fieldMaskMap["measureUnitId"] {
		item.MeasureUnitId = trans.String(in.MeasureUnitID)

		if in.Edges.MeasureUnit != nil {
			item.MeasureUnitName = trans.String(in.Edges.MeasureUnit.Name)
		}
	}
	if isAllField || fieldMaskMap["expireTime"] {
		item.ExpireTime = trans.TimeToStrPtr(in.ExpireTime)
	}
	if isAllField || fieldMaskMap["equipmentStatus"] {
		item.EquipmentStatus = trans.Int32(in.EquipmentStatus)
	}
	if isAllField || fieldMaskMap["ownerId"] {
		item.OwnerId = trans.String(in.OwnerID)
	}

	if isAllField || fieldMaskMap["feature"] {
		item.Feature, _ = structpb.NewStruct(in.Feature)
	}

	if isAllField || fieldMaskMap["originalId"] {
		item.OriginalId = in.OriginalID
	}

	if isAllField || fieldMaskMap["key"] {
		item.Key = trans.String(in.Key)
	}

	if isAllField || fieldMaskMap["financeSystemNo"] {
		item.FinanceSystemNo = in.FinanceSystemNo
	}

	if isAllField || fieldMaskMap["useStartTime"] {
		item.UseStartTime = trans.DatePtrToStrPtr(in.UseStartTime)
	}

	if in.Edges.FireStation != nil {
		item.FireStationName = trans.String(in.Edges.FireStation.Name)
	}
	if in.Edges.EquipmentType != nil {
		item.EquipmentTypeName = trans.String(in.Edges.EquipmentType.Name)
	}

	if in.Edges.Equipment != nil {
		item.Equipment = &v1.WmsEquipment{
			Name:                 in.Edges.Equipment.Name,
			Images:               in.Edges.Equipment.Images,
			Type:                 trans.String(in.Edges.Equipment.Type),
			IsOneMaterialOneCode: in.Edges.Equipment.IsOneMaterialOneCode,
			DiscardMethod:        in.Edges.Equipment.DiscardMethod,
		}

		if in.Edges.EquipmentType != nil {
			item.Equipment.EquipmentType = &v1.WmsEquipmentType{
				Name: &in.Edges.EquipmentType.Name,
			}
		}
	}

	return item
}

func (r *WmsMaterialRepo) getUserInfoMap(ctx context.Context, results []*ent.WmsMaterial) (map[string]string, error) {
	userIdInfo := map[string]string{}
	for _, m := range results {
		if m.CreatedBy != "" {
			userIdInfo[m.CreatedBy] = ""
		}
		if m.UpdatedBy != nil && *m.UpdatedBy != "" {
			userIdInfo[*m.UpdatedBy] = ""
		}
	}

	// 获取所有用户id
	userIds := make([]string, 0, len(userIdInfo))
	for k := range userIdInfo {
		userIds = append(userIds, k)
	}

	// 获取用户信息
	userInfo, err := r.getEntClient(ctx).SysUser.
		Query().
		Where(sysuser.IDIn(userIds...)).
		Select(sysuser.FieldUsername, sysuser.FieldNickname).
		All(ctx)

	if err != nil {
		r.log.Errorf("query user info failed: %s", err.Error())
		return nil, err
	}

	for _, u := range userInfo {
		if u.Nickname != "" {
			userIdInfo[u.ID] = u.Nickname
		} else {
			userIdInfo[u.ID] = u.Username
		}
	}

	return userIdInfo, nil
}

func (r *WmsMaterialRepo) GetProtoListFromEnt(
	userMap map[string]string,
	results []*ent.WmsMaterial,
	fieldMask []string,
) ([]*v1.WmsMaterial, error) {
	return r.convertEntListToProto(userMap, results, fieldMask)
}

func (r *WmsMaterialRepo) convertEntListToProto(
	userMap map[string]string,
	results []*ent.WmsMaterial,
	fieldMask []string,
) ([]*v1.WmsMaterial, error) {
	items := make([]*v1.WmsMaterial, 0, len(results))
	for _, m := range results {
		item := r.convertEntToProto(m, userMap, fieldMask)
		items = append(items, item)
	}
	return items, nil
}

func (r *WmsMaterialRepo) ListWmsMaterial(ctx context.Context, req *v1.ListWmsMaterialRequest) (*v1.ListWmsMaterialResponse, error) {
	builder := r.getEntClient(ctx).WmsMaterial.Query()
	countBuilder := r.getEntClient(ctx).WmsMaterial.Query()

	// 从查询中获取值并且移除
	valuesMap := map[string]string{}
	query := entgo.RemoveQueryKeys(req.GetQuery(), []string{"materialType", "keyword", "createdBy"}, valuesMap)
	feature_map := map[string]string{}
	query = entgo.RemoveQueryKeysWithPrefix(query, []string{"feature"}, feature_map)
	req.Query = &query

	fieldMask := req.GetFieldMask().GetPaths()
	err, whereSelectors, querySelectors := entgo.BuildQuerySelector(
		req.GetQuery(), req.GetOrQuery(),
		req.GetPage(), req.GetPageSize(), req.GetNoPaging(),
		req.GetOrderBy(), wmsmaterial.FieldCreatedAt,
		fieldMask,
	)
	if err != nil {
		r.log.Errorf("build query select faild: %s", err.Error())
		return nil, err
	}

	if querySelectors != nil {
		builder.Modify(querySelectors...)
	}

	if whereSelectors != nil {
		countBuilder.Modify(whereSelectors...)
	}

	keyword := valuesMap["keyword"]
	if keyword != "" {
		keywordWhere := wmsmaterial.Or(
			wmsmaterial.CodeEQ(keyword),
			wmsmaterial.HasEquipmentWith(wmsequipment.NameContains(keyword)),
		)
		builder.Where(keywordWhere)
		countBuilder.Where(keywordWhere)
	}

	materialType := valuesMap["materialType"]
	if materialType != "" {
		materialTypeWhere := wmsmaterial.HasEquipmentWith(wmsequipment.TypeEQ(materialType))
		builder.Where(materialTypeWhere)
		countBuilder.Where(materialTypeWhere)
	}

	createdBy := valuesMap["createdBy"]
	if createdBy != "" {
		builder.Where(wmsmaterial.HasCreatedUserWith(
			sysuser.Or(
				sysuser.NicknameContains(createdBy),
				sysuser.UsernameContains(createdBy),
			),
		))
		countBuilder.Where(wmsmaterial.HasCreatedUserWith(
			sysuser.Or(
				sysuser.NicknameContains(createdBy),
				sysuser.UsernameContains(createdBy),
			),
		))
	}

	// 支持详细规格查询
	if len(feature_map) > 0 {
		for k, v := range feature_map {
			remove_prefix_key := strings.TrimPrefix(k, "feature__")
			// 不允许有特殊字符
			if !stringutil.IsLetterOrDigit(remove_prefix_key) {
				continue
			}

			if !stringutil.IsLetterOrDigit(v) {
				continue
			}

			var feature_where_sql = fmt.Sprintf("feature->>'%s' = '%s'", remove_prefix_key, v)

			builder.Where(func(s *sql.Selector) {
				s.Where(sql.ExprP(feature_where_sql))
			})
			countBuilder.Where(func(s *sql.Selector) {
				s.Where(sql.ExprP(feature_where_sql))
			})
		}
	}

	builder.
		WithEquipment(func(weq *ent.WmsEquipmentQuery) {
			weq.Select(wmsequipment.FieldName, wmsequipment.FieldModelNo, wmsequipment.FieldType, wmsequipment.FieldImages)
		}).
		WithEquipmentType(func(wetq *ent.WmsEquipmentTypeQuery) {
			wetq.Select(wmsequipmenttype.FieldName)
		}).
		WithRepository(func(wrq *ent.WmsRepositoryQuery) {
			wrq.Select(wmsrepository.FieldName)
		}).
		WithRepositoryArea(func(wraq *ent.WmsRepositoryAreaQuery) {
			wraq.Select(wmsrepositoryarea.FieldName)
		}).
		WithRepositoryPosition(func(wrpq *ent.WmsRepositoryPositionQuery) {
			wrpq.Select(wmsrepositoryposition.FieldName)
		}).
		WithMeasureUnit(func(wmuq *ent.WmsMeasureUnitQuery) {
			wmuq.Select(wmsmeasureunit.FieldName)
		}).
		WithFireStation(func(wfsq *ent.WmsFireStationQuery) {
			wfsq.Select(wmsfirestation.FieldName)
		})

	results, err := builder.All(ctx)
	if err != nil {
		return nil, err
	}

	// 获取用户信息
	userMap, err := r.getUserInfoMap(ctx, results)
	if err != nil {
		return nil, err
	}

	// 转换成proto
	items, err := r.convertEntListToProto(userMap, results, fieldMask)
	if err != nil {
		return nil, err
	}

	count, err := countBuilder.Count(ctx)
	if err != nil {
		return nil, err
	}

	ret := v1.ListWmsMaterialResponse{
		Total: int32(count),
		Items: items,
	}

	return &ret, err
}

func (r *WmsMaterialRepo) GetWmsMaterial(ctx context.Context, id string) (*v1.WmsMaterial, error) {
	ret, err := r.getEntClient(ctx).WmsMaterial.Get(ctx, id)
	if err != nil && !ent.IsNotFound(err) {
		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

func (r *WmsMaterialRepo) CreateWmsMaterial(ctx context.Context, req *v1.CreateWmsMaterialRequest) (*v1.WmsMaterial, error) {
	if stringutil.IsEmpty(req.Code) {
		req.Code = nil
	}

	var feature_map map[string]interface{}

	if req.Feature != nil {
		feature_map = req.Feature.AsMap()
	}

	builder := r.getEntClient(ctx).WmsMaterial.Create().
		SetStatus(req.Status).
		SetRemark(req.Remark).
		SetNillableFireStationID(req.FireStationId).
		SetMaterialType(trans.StringValue(req.MaterialType)).
		SetNillableEquipmentTypeID(req.EquipmentTypeId).
		SetNillableEquipmentID(req.EquipmentId).
		SetNillableCode(req.Code).
		SetCodeType(trans.StringValue(req.CodeType)).
		SetName(trans.StringValue(req.Name)).
		SetModelNo(trans.StringValue(req.ModelNo)).
		SetProviderID(trans.StringValue(req.ProviderId)).
		SetNum(trans.Uint32Value(req.Num)).
		SetPrice(trans.Float64MoneyToFenUInt64Value(req.Price)).
		SetNillableRepositoryID(req.RepositoryId).
		SetNillableRepositoryAreaID(req.RepositoryAreaId).
		SetNillableRepositoryPositionID(req.RepositoryPositionId).
		SetNillableMeasureUnitID(req.MeasureUnitId).
		SetNillableExpireTime(trans.StrToTime(req.ExpireTime)).
		SetEquipmentStatus(1). // 默认正常
		SetNillableOwnerID(req.OwnerId).
		SetFeature(feature_map).
		SetNillableOriginalID(req.OriginalId).
		SetKey(req.GetKey()).
		SetNillableFinanceSystemNo(req.FinanceSystemNo).
		SetNillableUseStartTime(trans.StrToDate(req.UseStartTime)).
		SetCreatedAt(time.Now())

	entutils.SetCreatedBy(ctx, builder.Mutation())

	ret, err := builder.Save(ctx)
	if err != nil {
		r.log.Errorf("insert one data failed: %s", err.Error())
		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

func (r *WmsMaterialRepo) UpdateWmsMaterial(ctx context.Context, req *v1.UpdateWmsMaterialRequest) (*v1.WmsMaterial, error) {
	if stringutil.IsEmpty(req.Code) {
		req.Code = nil
	}
	var feature_map map[string]interface{}

	if req.Feature != nil {
		feature_map = req.Feature.AsMap()
	}

	builder := r.getEntClient(ctx).WmsMaterial.UpdateOneID(req.Id).
		SetNillableStatus(req.Status).
		SetNillableRemark(req.Remark).
		SetNillableFireStationID(req.FireStationId).
		SetNillableMaterialType(req.MaterialType).
		SetNillableEquipmentTypeID(req.EquipmentTypeId).
		SetNillableEquipmentID(req.EquipmentId).
		SetNillableCode(req.Code).
		SetNillableCodeType(req.CodeType).
		SetNillableName(req.Name).
		SetNillableModelNo(req.ModelNo).
		SetNillableProviderID(req.ProviderId).
		SetNillableNum(req.Num).
		SetNillablePrice(trans.Float64MoneyToFenUInt64(req.Price)).
		SetNillableRepositoryID(req.RepositoryId).
		SetNillableRepositoryAreaID(req.RepositoryAreaId).
		SetNillableRepositoryPositionID(req.RepositoryPositionId).
		SetNillableMeasureUnitID(req.MeasureUnitId).
		SetNillableExpireTime(trans.StrToTime(req.ExpireTime)).
		SetNillableOwnerID(req.OwnerId).
		SetNillableOriginalID(req.OriginalId).
		SetNillableFinanceSystemNo(req.FinanceSystemNo).
		SetNillableUseStartTime(trans.StrToDate(req.UseStartTime)).
		SetUpdatedAt(time.Now())

	if req.GetKey() != "" {
		builder.SetKey(req.GetKey())
	}

	if feature_map != nil {
		builder.SetFeature(feature_map)
	}

	entutils.SetUpdatedBy(ctx, builder.Mutation())

	ret, err := builder.Save(ctx)
	if err != nil {
		r.log.Errorf("update one data failed: %s", err.Error())
		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

func (r *WmsMaterialRepo) DeleteWmsMaterial(ctx context.Context, id string) (bool, error) {
	err := r.getEntClient(ctx).WmsMaterial.
		DeleteOneID(id).
		Exec(ctx)
	return err != nil, err
}

func (r *WmsMaterialRepo) MultiDeleteWmsMaterial(ctx context.Context, ids []string) (bool, error) {
	_, err := r.getEntClient(ctx).WmsMaterial.
		Delete().
		Where(wmsmaterial.IDIn(ids...)).
		Exec(ctx)
	return err != nil, err
}

func (r *WmsMaterialRepo) FindWmsMaterialByCode(ctx context.Context, code string) (*v1.WmsMaterial, error) {
	if code == "" {
		return nil, errors.New("code is empty")
	}

	ret, err := r.getEntClient(ctx).WmsMaterial.Query().
		Where(
			wmsmaterial.CodeEQ(code),
			wmsmaterial.NumGT(0),
		).
		WithEquipment().
		First(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, nil
		}
		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

func (r *WmsMaterialRepo) BatchCreateWmsMaterial(ctx context.Context, reqs []*v1.CreateWmsMaterialRequest) ([]*v1.WmsMaterial, error) {
	if len(reqs) == 0 {
		return nil, nil
	}

	var creates []*ent.WmsMaterialCreate
	for _, req := range reqs {
		if stringutil.IsEmpty(req.Code) {
			req.Code = nil
		}

		var feature_map map[string]interface{}

		if req.Feature != nil {
			feature_map = req.Feature.AsMap()
		}

		create := r.getEntClient(ctx).WmsMaterial.Create().
			SetStatus(req.Status).
			SetRemark(req.Remark).
			SetNillableFireStationID(req.FireStationId).
			SetMaterialType(trans.StringValue(req.MaterialType)).
			SetNillableEquipmentTypeID(req.EquipmentTypeId).
			SetNillableEquipmentID(req.EquipmentId).
			SetNillableCode(req.Code).
			SetCodeType(trans.StringValue(req.CodeType)).
			SetName(trans.StringValue(req.Name)).
			SetModelNo(trans.StringValue(req.ModelNo)).
			SetProviderID(trans.StringValue(req.ProviderId)).
			SetNum(trans.Uint32Value(req.Num)).
			SetPrice(trans.Float64MoneyToFenUInt64Value(req.Price)).
			SetNillableRepositoryID(req.RepositoryId).
			SetNillableRepositoryAreaID(req.RepositoryAreaId).
			SetNillableRepositoryPositionID(req.RepositoryPositionId).
			SetNillableMeasureUnitID(req.MeasureUnitId).
			SetNillableExpireTime(trans.StrToTime(req.ExpireTime)).
			SetNillableOwnerID(req.OwnerId).
			SetFeature(feature_map).
			SetNillableOriginalID(req.OriginalId).
			SetKey(req.GetKey()).
			SetCreatedAt(time.Now())

		entutils.SetCreatedBy(ctx, create.Mutation())
		creates = append(creates, create)
	}

	list, err := r.getEntClient(ctx).WmsMaterial.CreateBulk(creates...).Save(ctx)
	if err != nil {
		return nil, err
	}

	return r.convertEntListToProto(nil, list, nil)
}

// 更新物料库存状态
func (r *WmsMaterialRepo) UpdateMaterialStatus(ctx context.Context, code string, status int32) error {
	_, err := r.getEntClient(ctx).WmsMaterial.Update().
		Where(wmsmaterial.CodeEQ(code)).
		SetStatus(status).
		Save(ctx)

	return err
}

// 根据编码列表查找物料数据
func (r *WmsMaterialRepo) QueryMaterialsByCodes(ctx context.Context, codes []string) ([]*v1.WmsMaterial, error) {
	if len(codes) == 0 {
		return nil, nil
	}

	ret, err := r.getEntClient(ctx).WmsMaterial.Query().
		Where(wmsmaterial.CodeIn(codes...)).
		All(ctx)
	if err != nil {
		return nil, err
	}

	return r.convertEntListToProto(nil, ret, nil)
}

// 更新资产持有人
func (r *WmsMaterialRepo) UpdateMaterialOwner(ctx context.Context, code string, ownerID string) error {
	_, err := r.getEntClient(ctx).WmsMaterial.Update().
		Where(wmsmaterial.CodeEQ(code)).
		SetOwnerID(ownerID).
		Save(ctx)

	return err
}

// 获取资产数量
func (r *WmsMaterialRepo) GetAvailableMaterialIds(ctx context.Context, userIds, repositoryIds, equipmentTypeIds string) ([]string, error) {
	builder := r.getEntClient(ctx).WmsMaterial.Query()

	orWhere := make([]predicate.WmsMaterial, 0)

	if userIds != "" {
		orWhere = append(orWhere, wmsmaterial.OwnerIDIn(stringutil.Split(userIds, ",")...))
	}
	if repositoryIds != "" {
		orWhere = append(orWhere, wmsmaterial.RepositoryIDIn(stringutil.Split(repositoryIds, ",")...))
	}
	if equipmentTypeIds != "" {
		orWhere = append(orWhere, wmsmaterial.EquipmentTypeIDIn(stringutil.Split(equipmentTypeIds, ",")...))
	}

	if len(orWhere) == 0 {
		return nil, nil
	}

	builder.Where(
		wmsmaterial.Or(orWhere...),
		wmsmaterial.StatusNEQ(0),
		wmsmaterial.EquipmentStatusNEQ(0),
		wmsmaterial.NumGT(0),
	)

	ret, err := builder.Select(wmsmaterial.FieldID).All(ctx)
	if err != nil {
		return nil, err
	}

	ids := make([]string, 0, len(ret))
	for _, r := range ret {
		ids = append(ids, r.ID)
	}

	return ids, nil
}

func (r *WmsMaterialRepo) ListMaterialWithIds(ctx context.Context, ids []string) ([]*v1.WmsMaterial, error) {
	results, err := r.getEntClient(ctx).WmsMaterial.Query().
		Where(wmsmaterial.IDIn(ids...)).
		WithEquipment().
		WithEquipmentType().
		WithRepository().
		WithRepositoryArea().
		WithRepositoryPosition().
		WithMeasureUnit().
		All(ctx)

	if err != nil {
		return nil, err
	}

	return r.convertEntListToProto(nil, results, nil)
}

func (r *WmsMaterialRepo) ListMaterialByIds(ctx context.Context, ids []string) ([]*v1.WmsMaterial, error) {
	results, err := r.getEntClient(ctx).WmsMaterial.Query().
		WithEquipment().
		Where(wmsmaterial.IDIn(ids...)).
		All(ctx)

	if err != nil {
		return nil, err
	}

	return r.convertEntListToProto(nil, results, nil)
}

func (r *WmsMaterialRepo) ListMaterialWithCodes(ctx context.Context, codes []string) ([]*v1.WmsMaterial, error) {
	results, err := r.getEntClient(ctx).WmsMaterial.Query().
		Where(wmsmaterial.CodeIn(codes...)).
		WithEquipment().
		All(ctx)

	if err != nil {
		return nil, err
	}

	return r.convertEntListToProto(nil, results, nil)
}

func (r *WmsMaterialRepo) UpdateWmsMaterialApproveStatus(ctx context.Context, ids []string, isApproving bool) error {
	builder := r.getEntClient(ctx).WmsMaterial.Update().
		Where(wmsmaterial.IDIn(ids...)).
		SetIsApproving(isApproving)
		// SetUpdatedAt(time.Now())

	// entutils.SetUpdatedBy(ctx, builder.Mutation())

	_, err := builder.Save(ctx)
	if err != nil {
		r.log.Errorf("update one data failed: %s", err.Error())
		return err
	}

	return nil
}

// RevertWmsMaterialStatus 撤销物料状态更新, 如果 newEquipmentStatus 不为空，则同时更新设备状态更新
func (r *WmsMaterialRepo) RevertWmsMaterialStatus(ctx context.Context, ids []string, newEquipmentStatus *int32) error {
	// 获取需要撤销更新的物料的原始状态
	materials, err := r.getEntClient(ctx).WmsMaterial.Query().
		Where(wmsmaterial.IDIn(ids...)).
		Select(wmsmaterial.FieldID, wmsmaterial.FieldOriginalStatus).
		All(ctx)
	if err != nil {
		r.log.Errorf("query materials failed: %s", err.Error())
		return err
	}

	// 遍历每个物料进行状态恢复
	for _, m := range materials {
		if m.OriginalStatus != nil { // 确保原始状态有效
			update := r.getEntClient(ctx).WmsMaterial.UpdateOneID(m.ID)
			update.SetStatus(*m.OriginalStatus) // 恢复到原始状态
			update.SetNillableEquipmentStatus(newEquipmentStatus)
			update.ClearOriginalStatus() // 清除原始状态字段

			_, err := update.Save(ctx)
			if err != nil {
				r.log.Errorf("revert material status failed: %s", err.Error())
				return err
			}
		}
	}

	return nil
}

func (r *WmsMaterialRepo) GetAllMaterialIdByWmsApprovalId(ctx context.Context, wmsApprovalId string) ([]string, error) {
	builder := r.getEntClient(ctx).WmsApprovalTask.Query().Where(wmsapprovaltask.IDEQ(wmsApprovalId)).
		QueryDetails().
		QueryMaterial()

	rets, err := builder.All(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return []string{}, nil
		}
		r.log.Errorf("update one data failed: %s", err.Error())
		return []string{}, err
	}

	ids := make([]string, 0)
	for _, ret := range rets {
		ids = append(ids, ret.Code)
	}

	return ids, nil
}

// 根据repository_id repository_area_id repository_position_id code feature owner_id 生成hash值, 用于比较是否是同意物资同一规格
func (r *WmsMaterialRepo) GenerateMaterialKey(repository_id string, repository_area_id string, repository_position_id string, code string, feature *structpb.Struct, owner_id string) string {
	feature_map := make(map[string]interface{})

	if feature != nil {
		feature_map = feature.AsMap()
	}

	str_arr := make([]string, 0)

	str_arr = append(str_arr, code)

	// feature_map 排序
	keys := make([]string, 0, len(feature_map))

	for k := range feature_map {
		keys = append(keys, k)
	}

	sort.Strings(keys)

	// 拼接字符串
	for _, k := range keys {
		str_arr = append(str_arr, fmt.Sprintf("%s_%v", k, feature_map[k]))
	}

	str_arr = append(str_arr, repository_id)
	str_arr = append(str_arr, repository_area_id)
	str_arr = append(str_arr, repository_position_id)
	str_arr = append(str_arr, owner_id)

	str := strings.Join(str_arr, "_")

	// 生成md5
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// 根据keys和物资状态、保养状态获取物资数据
func (r *WmsMaterialRepo) GetMaterialsByKeysAndStatusAndEquipmentStatus(ctx context.Context, keys []string, status int32, equipment_status int32) ([]*v1.WmsMaterial, error) {
	builder := r.getEntClient(ctx).WmsMaterial.Query()

	builder.Where(
		wmsmaterial.KeyIn(keys...),
		wmsmaterial.EquipmentStatusEQ(equipment_status),
		wmsmaterial.StatusEQ(status),
	)

	builder.WithEquipment()

	results, err := builder.All(ctx)
	if err != nil {
		return nil, err
	}

	return r.convertEntListToProto(nil, results, nil)
}

// 根据key和物资状态、保养状态获取物资数据
func (r *WmsMaterialRepo) GetMaterialsByKeyAndStatusAndEquipmentStatus(ctx context.Context, key string, status int32, equipment_status int32) (*v1.WmsMaterial, error) {
	builder := r.getEntClient(ctx).WmsMaterial.Query()

	builder.Where(
		wmsmaterial.KeyEQ(key),
		wmsmaterial.EquipmentStatusEQ(equipment_status),
		wmsmaterial.StatusEQ(status),
	)

	builder.WithEquipment()

	ret, err := builder.First(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, nil
		}

		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

// 根据ids和物资状态、保养状态获取物资数据
func (r *WmsMaterialRepo) GetMaterialsByIdsAndStatusAndEquipmentStatus(ctx context.Context, ids []string, status int32, equipment_status int32) ([]*v1.WmsMaterial, error) {
	builder := r.getEntClient(ctx).WmsMaterial.Query()

	builder.Where(
		wmsmaterial.IDIn(ids...),
		wmsmaterial.EquipmentStatusEQ(equipment_status),
		wmsmaterial.StatusEQ(status),
	)

	builder.WithEquipment()

	results, err := builder.All(ctx)
	if err != nil {
		return nil, err
	}

	return r.convertEntListToProto(nil, results, nil)
}

// 合并资产
func (r *WmsMaterialRepo) ConcatMaterial(ctx context.Context, fromId string, toId string, num int32) error {
	// 获取被合并的资产
	from_material, err := r.getEntClient(ctx).WmsMaterial.Get(ctx, fromId)

	if err != nil {
		return err
	}

	if from_material.Num < uint32(num) {
		return errors.New("concat num is greater than from material num")
	}

	from_material.Num = from_material.Num - uint32(num)

	_, err = r.getEntClient(ctx).WmsMaterial.UpdateOneID(fromId).
		SetNum(from_material.Num).
		Save(ctx)

	if err != nil {
		return err
	}

	// 如果被合并的资产数量为0, 则删除
	if from_material.Num == 0 {
		err = r.getEntClient(ctx).WmsMaterial.DeleteOneID(fromId).Exec(ctx)
		if err != nil {
			return err
		}

	}

	// 更新资产数量
	_, err = r.getEntClient(ctx).WmsMaterial.UpdateOneID(toId).
		AddNum(num).
		Save(ctx)

	if err != nil {
		return err
	}

	return nil
}

// 分裂资产给仓库
func (r *WmsMaterialRepo) SplitMaterial(ctx context.Context, fromId string, repositoryPositionId string, ownerId *string, status *int32, equipmentStatus *int32, num int32, options *biz.SplitOptions) (*v1.WmsMaterial, error) {
	if options == nil {
		options = &biz.SplitOptions{}
	}

	from_material, err := r.getEntClient(ctx).WmsMaterial.Get(ctx, fromId)

	if err != nil {
		return nil, err
	}

	uint32_num := uint32(num)

	if uint32_num > from_material.Num {
		return nil, errors.New("split num is greater than from material num")
	}

	update_num := from_material.Num - uint32_num

	// 更新资产数量
	_, err = r.getEntClient(ctx).WmsMaterial.UpdateOneID(fromId).
		SetNum(update_num).
		Save(ctx)

	if err != nil {
		return nil, err
	}

	if repositoryPositionId != "" {
		position, err := r.getEntClient(ctx).WmsRepositoryPosition.Query().
			Where(wmsrepositoryposition.IDEQ(repositoryPositionId)).
			First(ctx)

		if err != nil {
			return nil, err
		}

		from_material.RepositoryID = position.RepositoryID
		from_material.RepositoryAreaID = position.RepositoryAreaID
		from_material.RepositoryPositionID = repositoryPositionId
	}

	if ownerId != nil {
		from_material.OwnerID = *ownerId
	}

	if equipmentStatus != nil {
		from_material.EquipmentStatus = *equipmentStatus
	}

	if status != nil {
		from_material.Status = *status
	}

	feature, err := structpb.NewStruct(from_material.Feature)

	if err != nil {
		return nil, err
	}

	key := r.GenerateMaterialKey(from_material.RepositoryID, from_material.RepositoryAreaID, from_material.RepositoryPositionID, from_material.Code, feature, from_material.OwnerID)

	// 如果不强制拆分, 则查询资产是否已经存在
	if !options.ForceSplit {
		exist_material := r.getEntClient(ctx).WmsMaterial.Query().
			Where(
				wmsmaterial.KeyEQ(key),
				wmsmaterial.StatusEQ(from_material.Status),
				wmsmaterial.EquipmentStatusEQ(from_material.EquipmentStatus),
			).
			FirstX(ctx)

		// 查询资产是否已经存在
		if exist_material != nil {
			_, err = r.getEntClient(ctx).WmsMaterial.UpdateOneID(exist_material.ID).
				SetNum(exist_material.Num + uint32_num).
				Save(ctx)

			if err != nil {
				return nil, err
			}
			return r.convertEntToProto(exist_material, nil, nil), nil
		}
	}

	// 创建新的资产
	builder := r.getEntClient(ctx).WmsMaterial.Create().
		SetStatus(from_material.Status).
		SetNillableRemark(from_material.Remark).
		SetFireStationID(from_material.FireStationID).
		SetMaterialType(from_material.MaterialType).
		SetEquipmentTypeID(from_material.EquipmentTypeID).
		SetEquipmentID(from_material.EquipmentID).
		SetCode(from_material.Code).
		SetCodeType(trans.StringValue(from_material.CodeType)).
		SetName(trans.StringValue(from_material.Name)).
		SetModelNo(from_material.ModelNo).
		SetProviderID(from_material.ProviderID).
		SetNum(uint32_num).
		SetPrice(from_material.Price).
		SetRepositoryID(from_material.RepositoryID).
		SetRepositoryAreaID(from_material.RepositoryAreaID).
		SetRepositoryPositionID(from_material.RepositoryPositionID).
		SetMeasureUnitID(from_material.MeasureUnitID).
		SetExpireTime(from_material.ExpireTime).
		SetEquipmentStatus(from_material.EquipmentStatus).
		SetOwnerID(from_material.OwnerID).
		SetFeature(from_material.Feature).
		SetOriginalID(from_material.ID).
		SetNillableOrderNo(options.OrderNo).
		SetKey(key).
		SetCreatedAt(time.Now())

	entutils.SetCreatedBy(ctx, builder.Mutation())

	ret, err := builder.Save(ctx)

	if err != nil {
		return nil, err
	}

	return r.convertEntToProto(ret, nil, nil), err
}

// 根据设备id、仓库id、 feature features获取库存数量
func (r *WmsMaterialRepo) GetFeatureCountByEquipmentIdAndRepositoryIdAndFeatures(ctx context.Context, equipmentId string, repositoryId *string, feature *structpb.Struct, features []*v1.EquipmentFeature) ([]*v1.EquipmentFeature, error) {
	where_str_arr := make([]string, 0)
	where_params_arr := make([]interface{}, 0)

	where_str_arr = append(where_str_arr, "equipment_id = ?")
	where_params_arr = append(where_params_arr, equipmentId)

	where_str_arr = append(where_str_arr, "status = ?")
	where_params_arr = append(where_params_arr, biz.MATERIAL_STATUS_AVAILABLE)

	where_str_arr = append(where_str_arr, "equipment_status = ?")
	where_params_arr = append(where_params_arr, biz.MATERIAL_MAINTAIN_STATUS_NORMAL)

	if repositoryId != nil {
		where_str_arr = append(where_str_arr, "repository_id = ?")
		where_params_arr = append(where_params_arr, *repositoryId)
	}

	if feature != nil {
		for k, v := range feature.AsMap() {
			where_str_arr = append(where_str_arr, fmt.Sprintf("feature->>'%s' = ?", k))
			where_params_arr = append(where_params_arr, v)
		}
	}

	select_str_arr := make([]string, 0)

	for _, feature_item := range features {
		feature_str := fmt.Sprintf("SUM(case when feature->>'%s' = '%s' then num else 0 end) as %s", feature_item.Id, feature_item.Value, feature_item.Id+"_"+feature_item.Value)
		select_str_arr = append(select_str_arr, feature_str)
	}

	select_str := strings.Join(select_str_arr, ", ")

	where_str := ""
	if len(where_str_arr) > 0 {
		where_str = "WHERE " + strings.Join(where_str_arr, " AND ")
	}

	var res map[string]interface{}

	tx := r.data.GormClient.Raw(`
		SELECT 
		`+select_str+`
		FROM wms_materials
		`+where_str+`
	`, where_params_arr...).Scan(&res)

	if tx.Error != nil {
		return nil, tx.Error
	}

	for _, feature := range features {
		if val, ok := res[feature.Id+"_"+feature.Value]; ok {
			if val == nil {
				feature.InventoryCount = trans.Int32(0)
			} else {
				feature.InventoryCount = trans.Int32(int32(val.(float64)))
			}
		}
	}

	return features, nil
}

// 获取资产总数
func (r *WmsMaterialRepo) GetMaterialCount(ctx context.Context, startTime *time.Time, endTime *time.Time) (int, error) {
	where_arr := make([]string, 0)
	where_params := make([]interface{}, 0)

	// 报废状态的资产不计算
	where_arr = append(where_arr, "status != ?")
	where_params = append(where_params, biz.MATERIAL_STATUS_UNAVAILABLE)

	if startTime != nil && endTime != nil {
		where_arr = append(where_arr, "created_at >= ? AND created_at <= ?")
		where_params = append(where_params, startTime, endTime)
	}

	where_str := ""

	if len(where_arr) > 0 {
		where_str = " WHERE " + strings.Join(where_arr, " AND ")
	}

	var statistic int32

	tx := r.data.GormClient.Raw(`
		SELECT SUM(num) as num
		FROM wms_materials
		`+where_str+`
	`, where_params...).Scan(&statistic)

	if tx.Error != nil {
		return 0, tx.Error
	}

	return int(statistic), nil
}

// 获取固资总量
func (r *WmsMaterialRepo) GetMaterialStaticsCount(ctx context.Context, repositoryId *string, repositoryAreaIds []string) (int, error) {
	where := make([]predicate.WmsMaterial, 0)

	where = append(where, wmsmaterial.HasEquipmentWith(wmsequipment.TypeEQ(biz.MATERIAL_TYPE_STATIC)))

	if repositoryId != nil {
		where = append(where, wmsmaterial.RepositoryIDEQ(*repositoryId))
	}

	if len(repositoryAreaIds) > 0 {
		where = append(where, wmsmaterial.RepositoryAreaIDIn(repositoryAreaIds...))
	}

	return r.getEntClient(ctx).WmsMaterial.Query().
		Where(where...).
		Count(ctx)
}

// 获取物资总量
func (r *WmsMaterialRepo) GetMaterialCommonCount(ctx context.Context, repositoryId *string, repositoryAreaIds []string) (int, error) {
	where := make([]predicate.WmsMaterial, 0)

	where = append(where, wmsmaterial.HasEquipmentWith(wmsequipment.TypeEQ((biz.MATERIAL_TYPE_COMMON))))

	if repositoryId != nil {
		where = append(where, wmsmaterial.RepositoryIDEQ(*repositoryId))
	}

	if len(repositoryAreaIds) > 0 {
		where = append(where, wmsmaterial.RepositoryAreaIDIn(repositoryAreaIds...))
	}

	return r.getEntClient(ctx).WmsMaterial.Query().
		Where(where...).
		Count(ctx)
}

// 获取库存总量
func (r *WmsMaterialRepo) GetMaterialAvailableCount(ctx context.Context) (int, error) {
	return r.getEntClient(ctx).WmsMaterial.Query().
		Where(wmsmaterial.StatusEQ(biz.MATERIAL_STATUS_AVAILABLE)).
		Count(ctx)
}

// 获取在用总量 排除在库和报废的
func (r *WmsMaterialRepo) GetMaterialUsingCount(ctx context.Context) (int, error) {
	return r.getEntClient(ctx).WmsMaterial.Query().
		Where(wmsmaterial.StatusNotIn(biz.MATERIAL_STATUS_AVAILABLE, biz.MATERIAL_STATUS_UNAVAILABLE)).
		Count(ctx)
}

// 获取报废数据总量
func (r *WmsMaterialRepo) GetMaterialUnavailableCount(ctx context.Context) (int, error) {
	return r.getEntClient(ctx).WmsMaterial.Query().
		Where(wmsmaterial.StatusEQ(biz.MATERIAL_STATUS_UNAVAILABLE)).
		Count(ctx)
}

// 获取固资报废数据总量
func (r *WmsMaterialRepo) GetMaterialStaticsUnavailableCount(ctx context.Context) (int, error) {
	return r.getEntClient(ctx).WmsMaterial.Query().
		Where(
			wmsmaterial.MaterialTypeEQ(biz.MATERIAL_TYPE_STATIC),
			wmsmaterial.StatusEQ(biz.MATERIAL_STATUS_UNAVAILABLE),
		).
		Count(ctx)
}

// 获取物资报废数据总量
func (r *WmsMaterialRepo) GetMaterialCommonUnavailableCount(ctx context.Context) (int, error) {
	return r.getEntClient(ctx).WmsMaterial.Query().
		Where(
			wmsmaterial.MaterialTypeEQ(biz.MATERIAL_TYPE_COMMON),
			wmsmaterial.StatusEQ(biz.MATERIAL_STATUS_UNAVAILABLE),
		).
		Count(ctx)
}

// 获取库存统计(按分类)
func (r *WmsMaterialRepo) GetMaterialEquipmentTypeStatistics(ctx context.Context, status int) ([]*v1.MaterialEquipmentTypeStatistics, error) {
	var res []*v1.MaterialEquipmentTypeStatistics

	tx := r.data.GormClient.Raw(`
		select equipment_type_id, sum(num) as count
		from wms_materials
		where status = ?
		group by equipment_type_id
	`, status).Scan(&res)

	if tx.Error != nil {
		return nil, tx.Error
	}

	return res, nil
}

// 获取库存统计(按仓库)
func (r *WmsMaterialRepo) GetMaterialRepositoryStatistics(ctx context.Context) ([]*v1.MaterialRepositoryStatistics, error) {
	var res []*v1.MaterialRepositoryStatistics

	tx := r.data.GormClient.Raw(`
		select material.repository_id, sum(material.num) as count,
		SUM(case when equipment.type = '`+biz.MATERIAL_TYPE_STATIC+`' then material.num else 0 end) as static_count,
		SUM(case when equipment.type = '`+biz.MATERIAL_TYPE_COMMON+`' then material.num else 0 end) as common_count
		from wms_materials as material
		left join wms_equipments as equipment on material.equipment_id = equipment.id
		where material.status = ?
		group by material.repository_id order by count desc
	`, biz.MATERIAL_STATUS_AVAILABLE).Scan(&res)

	if tx.Error != nil {
		return nil, tx.Error
	}

	return res, nil
}

// 获取部门申领统计(按时间、分页)
func (r *WmsMaterialRepo) GetMaterialOrganizationStatistics(ctx context.Context, startTime *time.Time, endTime *time.Time, page *int, pageSize *int) ([]*v1.MaterialOrganizationStatistics, error) {
	var res []*v1.MaterialOrganizationStatistics

	var sql_params []interface{}

	where_str := ""
	where_str = "where material.status = ?"
	sql_params = append(sql_params, biz.MATERIAL_STATUS_CLAIMED)

	if startTime != nil && endTime != nil {
		where_str = "and material.created_at >= ? and material.created_at <= ?"
		sql_params = append(sql_params, startTime, endTime)
	}

	page_str := ""

	if page != nil && pageSize != nil {
		page_value := *page
		pagesize_value := *pageSize

		offset := (page_value - 1) * pagesize_value
		page_str = fmt.Sprintf("limit %d offset %d", pagesize_value, offset)
	}

	tx := r.data.GormClient.Raw(`
		select organization.id as organization_id, sum(material.num) as count
		from sys_organization as organization
		left join sys_user_organization as user_organization on organization.id = user_organization.organization_id and user_organization.is_main = true
		left join wms_materials as material on user_organization.user_id = material.owner_id `+where_str+`
		group by organization.id `+page_str+`
	`, sql_params...).Scan(&res)

	if tx.Error != nil {
		return nil, tx.Error
	}

	return res, nil
}

// 获取未报废资产总量(按时间查询)
func (r *WmsMaterialRepo) GetMaterialTotalNotUnavailable(ctx context.Context, startTime time.Time, endTime time.Time) (int, error) {
	count, err := r.getEntClient(ctx).WmsMaterial.Query().
		Where(
			wmsmaterial.CreatedAtGTE(startTime),
			wmsmaterial.CreatedAtLTE(endTime),
			wmsmaterial.StatusNEQ(biz.MATERIAL_STATUS_UNAVAILABLE),
		).
		Count(ctx)

	return count, err
}

// 获取未报废资产总量(按时间、部门查询)
func (r *WmsMaterialRepo) GetMaterialTotalNotUnavailableByOrganizationId(ctx context.Context, startTime time.Time, endTime time.Time, organizationId string) (int, error) {
	count, err := r.getEntClient(ctx).WmsMaterial.Query().
		Where(
			wmsmaterial.CreatedAtGTE(startTime),
			wmsmaterial.CreatedAtLTE(endTime),
			wmsmaterial.StatusNEQ(biz.MATERIAL_STATUS_UNAVAILABLE),
			wmsmaterial.HasOwnerWith(sysuser.HasOrganizationsWith(sysuserorganization.OrganizationIDEQ(organizationId))),
		).
		Count(ctx)

	return count, err
}

// 获取固资维修中的数量
func (r *WmsMaterialRepo) GetMaterialStaticsRepairingCount(ctx context.Context) (int, error) {
	count, err := r.getEntClient(ctx).WmsMaterial.Query().
		Where(
			wmsmaterial.EquipmentStatusEQ(biz.MATERIAL_MAINTAIN_STATUS_REPAIRING),
			wmsmaterial.MaterialTypeEQ(biz.MATERIAL_TYPE_STATIC),
		).
		Count(ctx)

	return count, err
}

// 获取物资维修中的数量
func (r *WmsMaterialRepo) GetMaterialCommonRepairingCount(ctx context.Context) (int, error) {
	count, err := r.getEntClient(ctx).WmsMaterial.Query().
		Where(
			wmsmaterial.EquipmentStatusEQ(biz.MATERIAL_MAINTAIN_STATUS_REPAIRING),
			wmsmaterial.MaterialTypeEQ(biz.MATERIAL_TYPE_COMMON),
		).
		Count(ctx)

	return count, err
}

// 获取在用车辆数量 排除在库和报废的
func (r *WmsMaterialRepo) GetCarUsingCount(ctx context.Context, req *v1.GetMaterialStatisticsRequest) (int, error) {
	where_arr := make([]string, 0)
	where_params := make([]interface{}, 0)

	where_arr = append(where_arr, fmt.Sprintf("material.status NOT IN (%d, %d)", biz.MATERIAL_STATUS_AVAILABLE, biz.MATERIAL_STATUS_UNAVAILABLE))

	if req.GetEquipmentTypeId() != "" {
		where_arr = append(where_arr, "material.equipment_type_id = ?")
		where_params = append(where_params, req.GetEquipmentTypeId())
	}

	if req.GetRepositoryId() != "" {
		where_arr = append(where_arr, "material.repository_id = ?")
		where_params = append(where_params, req.GetRepositoryId())
	}

	where_str := ""

	if len(where_arr) > 0 {
		where_str = " WHERE " + strings.Join(where_arr, " AND ")
	}

	var statistic *int32

	tx := r.data.GormClient.Raw(`
		SELECT SUM(material.num)
		FROM wms_materials as material
		INNER JOIN wms_cars as car ON material.id = car.wms_material_wms_car
		`+where_str+`
	`, where_params...).Scan(&statistic)

	if tx.Error != nil {
		return 0, tx.Error
	}

	if statistic == nil {
		return 0, nil
	}

	return int(*statistic), nil
}

// 获取车辆资产统计(按分类)
func (r *WmsMaterialRepo) GetCarEquipmentTypeIdStatistics(ctx context.Context) ([]*v1.CarEquipmentTypeStatistics, error) {
	var res []*v1.CarEquipmentTypeStatistics
	tx := r.data.GormClient.Raw(`
		SELECT material.equipment_type_id as equipment_type_id, SUM(material.num) as count
		FROM wms_cars as car
		LEFT JOIN wms_materials as material ON car.wms_material_wms_car = material.id
		group by material.equipment_type_id
	`).Scan(&res)

	if tx.Error != nil {
		return nil, tx.Error
	}

	return res, nil
}

// 获取车辆资产统计(按组织)
func (r *WmsMaterialRepo) GetCarOrganizationIdStatistics(ctx context.Context) ([]*v1.CarOrganizationStatistics, error) {
	var res []*v1.CarOrganizationStatistics
	tx := r.data.GormClient.Raw(`
		select organization.id as organization_id, count(material.num) as count
		from sys_organization as organization
		left join wms_cars as car on organization.id = car.organization_id
		left join wms_materials as material on car.wms_material_wms_car = material.id
		group by organization.id
	`).Scan(&res)

	if tx.Error != nil {
		return nil, tx.Error
	}

	return res, nil
}

// 获取资产服役年限统计
func (r *WmsMaterialRepo) GetMaterialTimeStatistics(ctx context.Context, time_group []*biz.TimeGroup, organization_id string) ([]*v1.MaterialTimeStatistics, error) {
	select_arr := make([]string, 0)

	for _, v := range time_group {
		start_time := v.StartTime.Format("2006-01-02 15:04:05")
		end_time := v.EndTime.Format("2006-01-02 15:04:05")
		item_str := fmt.Sprintf(`SUM(CASE WHEN material.created_at >= '%s' AND material.created_at <= '%s' THEN material.num ELSE 0 END) as "%s"`, start_time, end_time, v.Name)
		select_arr = append(select_arr, item_str)
	}

	select_str := strings.Join(select_arr, ",")

	var statistic_map map[string]interface{}

	where_arr := make([]string, 0)

	// 只统计非报废的资产
	where_arr = append(where_arr, fmt.Sprintf("material.status != %d", biz.MATERIAL_STATUS_UNAVAILABLE))

	if organization_id != "" {
		where_arr = append(where_arr, fmt.Sprintf("organization.id = '%s'", organization_id))
	}

	where_str := strings.Join(where_arr, " AND ")

	tx := r.data.GormClient.Raw(`
		SELECT ` + select_str + `
		FROM wms_materials as material
		LEFT JOIN sys_users as "user" ON material.owner_id = "user".id
		LEFT JOIN sys_user_organization as user_organization ON "user".id = user_organization.user_id
		LEFT JOIN sys_organization as organization ON user_organization.organization_id = organization.id
		WHERE ` + where_str + `
	`).Scan(&statistic_map)

	if tx.Error != nil {
		return nil, tx.Error
	}

	var statistics []*v1.MaterialTimeStatistics

	for _, v := range time_group {
		if v == nil {
			v = &biz.TimeGroup{}
		}

		if statistic_map[v.Name] == nil {
			statistic_map[v.Name] = int64(0)
		}

		statistics = append(statistics, &v1.MaterialTimeStatistics{
			Name:  v.Name,
			Count: int32(statistic_map[v.Name].(int64)),
		})
	}

	return statistics, nil
}

// 获取资产统计 总数、固资总数、物资总数、库存总数、在用总数、维修中固资总数、维修中物资总数、报废总数、固资报废总数、物资报废总数、保养总数、被申领总数、借用总数
func (r *WmsMaterialRepo) GetMaterialStatistics(ctx context.Context, req *v1.GetMaterialStatisticsRequest) (*v1.GetMaterialStatisticsResponse, error) {
	total_count_select := "SUM(num) as total_count"
	static_type_count := fmt.Sprintf(`
		SUM(CASE WHEN equipment.type = '%s' THEN num ELSE 0 END) as static_type_count
	`, biz.MATERIAL_TYPE_STATIC)
	common_type_count := fmt.Sprintf(`
		SUM(CASE WHEN equipment.type = '%s' THEN num ELSE 0 END) as common_type_count
	`, biz.MATERIAL_TYPE_COMMON)
	available_count := fmt.Sprintf(`
		SUM(CASE WHEN material.status = %d THEN num ELSE 0 END) as available_count
	`, biz.MATERIAL_STATUS_AVAILABLE)
	in_use_count := fmt.Sprintf(`
		SUM(CASE WHEN material.status != %d AND equipment_status != %d THEN num ELSE 0 END) as in_use_count
	`, biz.MATERIAL_STATUS_AVAILABLE, biz.MATERIAL_STATUS_UNAVAILABLE)
	repairing_static_type_count := fmt.Sprintf(`
		SUM(CASE WHEN material.equipment_status = %d AND equipment.type = '%s' THEN num ELSE 0 END) as repairing_static_type_count
	`, biz.MATERIAL_MAINTAIN_STATUS_REPAIRING, biz.MATERIAL_TYPE_STATIC)
	repairing_common_type_count := fmt.Sprintf(`
		SUM(CASE WHEN material.equipment_status = %d AND equipment.type = '%s' THEN num ELSE 0 END) as repairing_common_type_count
	`, biz.MATERIAL_MAINTAIN_STATUS_REPAIRING, biz.MATERIAL_TYPE_COMMON)
	unavailable_count := fmt.Sprintf(`
		SUM(CASE WHEN material.status = %d THEN num ELSE 0 END) as unavailable_count
	`, biz.MATERIAL_STATUS_UNAVAILABLE)
	unavailable_static_type_count := fmt.Sprintf(`
		SUM(CASE WHEN material.status = %d AND equipment.type = '%s' THEN num ELSE 0 END) as unavailable_static_type_count
	`, biz.MATERIAL_STATUS_UNAVAILABLE, biz.MATERIAL_TYPE_STATIC)
	unavailable_common_type_count := fmt.Sprintf(`
		SUM(CASE WHEN material.status = %d AND equipment.type = '%s' THEN num ELSE 0 END) as unavailable_common_type_count
	`, biz.MATERIAL_STATUS_UNAVAILABLE, biz.MATERIAL_TYPE_COMMON)
	claimed_count := fmt.Sprintf(`
		SUM(CASE WHEN material.status = %d THEN num ELSE 0 END) as claimed_count
	`, biz.MATERIAL_STATUS_CLAIMED)
	borrowed_count := fmt.Sprintf(`
		SUM(CASE WHEN material.status = %d THEN num ELSE 0 END) as borrowed_count
	`, biz.MATERIAL_STATUS_BORROWED)
	maintaining_count := fmt.Sprintf(`
		SUM(CASE WHEN material.equipment_status = %d THEN num ELSE 0 END) as maintaining_count
	`, biz.MATERIAL_MAINTAIN_STATUS_MAINTAINING)

	select_arr := []string{
		total_count_select,
		static_type_count,
		common_type_count,
		available_count,
		in_use_count,
		repairing_static_type_count,
		repairing_common_type_count,
		unavailable_count,
		unavailable_static_type_count,
		unavailable_common_type_count,
		claimed_count,
		borrowed_count,
		maintaining_count,
	}

	select_str := strings.Join(select_arr, ",")

	where_arr := make([]string, 0)
	where_params := make([]interface{}, 0)

	if req.GetEquipmentTypeId() != "" {
		where_arr = append(where_arr, "material.equipment_type_id = ?")
		where_params = append(where_params, req.GetEquipmentTypeId())
	}

	if req.GetRepositoryId() != "" {
		where_arr = append(where_arr, "material.repository_id = ?")
		where_params = append(where_params, req.GetRepositoryId())
	}

	where_str := ""

	if len(where_arr) > 0 {
		where_str = "WHERE " + strings.Join(where_arr, " AND ")
	}

	var statistic v1.GetMaterialStatisticsResponse

	tx := r.data.GormClient.Raw(`
		SELECT `+select_str+`
		FROM wms_materials as material
		LEFT JOIN wms_equipments as equipment ON material.equipment_id = equipment.id
		`+where_str+`
	`, where_params...).Scan(&statistic)

	if tx.Error != nil {
		return nil, tx.Error
	}

	return &statistic, nil
}

// 获取仓库装备分类统计
func (r *WmsMaterialRepo) GetRepositoryEquipmentTypeStatistics(ctx context.Context,
	repositoryId string,
	repositoryAreaIds []string,
	equipmentTypeIds []string,
	status int,
	equipmentStatus int,
) ([]*biz.EquipmentTypeGroup, error) {

	where_arr := make([]string, 0)
	where_params := make([]interface{}, 0)

	where_arr = append(where_arr, "status = ?")
	where_params = append(where_params, status)

	where_arr = append(where_arr, "equipment_status = ?")
	where_params = append(where_params, equipmentStatus)

	if repositoryId != "" {
		where_arr = append(where_arr, "repository_id = ?")
		where_params = append(where_params, repositoryId)
	}

	if len(repositoryAreaIds) > 0 {

		where_arr = append(where_arr, "repository_area_id IN (?)")
		where_params = append(where_params, repositoryAreaIds)
	}

	if len(equipmentTypeIds) > 0 {
		where_arr = append(where_arr, "equipment_type_id IN (?)")
		where_params = append(where_params, equipmentTypeIds)
	}

	fmt.Printf("equipmentTypeIds: %v\n", equipmentTypeIds)

	where_str := ""

	if len(where_arr) > 0 {
		where_str = "WHERE " + strings.Join(where_arr, " AND ")
	}

	var res []*biz.EquipmentTypeGroup

	r.data.GormClient.Raw(`
		SELECT equipment_type_id, count(*) as count
		FROM wms_materials
		`+where_str+`
		GROUP BY equipment_type_id
	`, where_params...).Scan(&res)

	return res, nil
}

// 获取仓库装备统计
func (r *WmsMaterialRepo) GetRepositoryEquipmentStatistics(ctx context.Context,
	repositoryId string,
	repositoryAreaIds []string,
	equipmentTypeIds []string,
	status int,
	equipmentStatus int,
) ([]*biz.RepositoryEquipmentGroup, error) {

	where_arr := make([]string, 0)
	where_params := make([]interface{}, 0)

	where_arr = append(where_arr, "status = ?")
	where_params = append(where_params, status)

	where_arr = append(where_arr, "equipment_status = ?")
	where_params = append(where_params, equipmentStatus)

	if repositoryId != "" {
		where_arr = append(where_arr, "repository_id = ?")
		where_params = append(where_params, repositoryId)
	}

	if len(repositoryAreaIds) > 0 {
		where_arr = append(where_arr, "repository_area_id IN (?)")
		where_params = append(where_params, repositoryAreaIds)
	}

	if len(equipmentTypeIds) > 0 {
		where_arr = append(where_arr, "equipment_type_id IN (?)")
		where_params = append(where_params, equipmentTypeIds)
	}

	where_str := ""

	if len(where_arr) > 0 {
		where_str = "WHERE " + strings.Join(where_arr, " AND ")
	}

	var res []*biz.RepositoryEquipmentGroup

	r.data.GormClient.Raw(`
		SELECT repository_id, repository_area_id, repository_position_id, equipment_id, count(*) as count
		FROM wms_materials
		`+where_str+`
		GROUP BY repository_id, repository_area_id, repository_position_id, equipment_type_id, equipment_id
	`, where_params...).Scan(&res)

	return res, nil
}

// 获取装备列表资产的第一条数据
func (r *WmsMaterialRepo) GetFirstMaterialsByEquipmentIds(ctx context.Context, equipmentIds []string) ([]*v1.WmsMaterial, error) {
	var data []*v1.WmsMaterial

	r.data.GormClient.Raw(`
		SELECT * FROM wms_materials as main
		INNER JOIN (
			SELECT equipment_id, MIN(id) as created_at
			FROM wms_materials
			WHERE equipment_id IN (?)
			GROUP BY equipment_id
		) as material
		ON main.equipment_id = material.equipment_id AND main.id = material.id
	`, &data)

	return data, nil
}

// 获取资产装备分类
func (r *WmsMaterialRepo) GetEquipmentTypeIds(ctx context.Context, repositoryId string, repositoryAreaIds []string, status int, equipmentStatus int) ([]string, error) {
	var res []string

	where_arr := make([]string, 0)
	where_params := make([]interface{}, 0)

	where_arr = append(where_arr, "repository_id = ?")
	where_params = append(where_params, repositoryId)

	where_arr = append(where_arr, "repository_area_id IN (?)")
	where_params = append(where_params, repositoryAreaIds)

	where_arr = append(where_arr, "status = ?")
	where_params = append(where_params, status)

	where_arr = append(where_arr, "equipment_status = ?")
	where_params = append(where_params, equipmentStatus)

	err := r.data.GormClient.Raw(`
	 	SELECT equipment_type_id
		FROM wms_materials
		WHERE `+strings.Join(where_arr, " AND ")+`
		GROUP BY equipment_type_id
	`, where_params...).Scan(&res).Error

	if err != nil {
		return nil, err
	}

	return res, nil
}

func (r *WmsMaterialRepo) GetWmsMaterialByRelationOrderNo(ctx context.Context, relationOrderNo string) ([]*v1.WmsMaterial, error) {
	if relationOrderNo == "" {
		return nil, nil
	}
	data, err := r.getEntClient(ctx).WmsMaterial.Query().
		Where(wmsmaterial.OrderNoEQ(relationOrderNo)).
		All(ctx)

	if err != nil {
		return nil, err
	}

	return r.convertEntListToProto(nil, data, nil)
}

// 统计资产库存按照仓库、装备、详细规格、计量单位分组
func (r *WmsMaterialRepo) GetMaterialStatisticsByRepositoryPositionAndEquipmentAndFeatureAndMeasureUnit(ctx context.Context, repositoryId string, repositoryAreaIds []string, equipmentTypeIds []string) ([]*biz.MaterialStatisticsGroupRepositoryPositionAndEquipmentAndFeatureAndMeasureUnit, error) {
	var res []*biz.MaterialStatisticsGroupRepositoryPositionAndEquipmentAndFeatureAndMeasureUnit

	where_arr := make([]string, 0)
	where_params := make([]interface{}, 0)

	where_arr = append(where_arr, "status = ?")
	where_params = append(where_params, biz.MATERIAL_STATUS_AVAILABLE)

	where_arr = append(where_arr, "equipment_status = ?")
	where_params = append(where_params, biz.MATERIAL_MAINTAIN_STATUS_NORMAL)

	if repositoryId != "" {
		where_arr = append(where_arr, "repository_id = ?")
		where_params = append(where_params, repositoryId)
	}

	if len(repositoryAreaIds) > 0 {
		where_arr = append(where_arr, "repository_area_id IN (?)")
		where_params = append(where_params, repositoryAreaIds)
	}

	if len(equipmentTypeIds) > 0 {
		where_arr = append(where_arr, "equipment_type_id IN (?)")
		where_params = append(where_params, equipmentTypeIds)
	}

	where_str := ""

	if len(where_arr) > 0 {
		where_str = "WHERE " + strings.Join(where_arr, " AND ")
	}

	tx := r.data.GormClient.Raw(`
		SELECT repository_position_id, equipment_id, feature, measure_unit_id, SUM(num) as num
		FROM wms_materials
		`+where_str+`
		GROUP BY repository_position_id, equipment_id, feature, measure_unit_id
	`, where_params...).Scan(&res)

	if tx.Error != nil {
		return nil, tx.Error
	}

	return res, nil
}

// 根据条件获取资产
func (r *WmsMaterialRepo) GetMaterialsByCondition(ctx context.Context, equipmentId string, repositoryId string, feature *structpb.Struct, code string) ([]*v1.WmsMaterial, error) {
	var res []*v1.WmsMaterial

	where_arr := make([]string, 0)
	where_params := make([]interface{}, 0)

	if equipmentId != "" {
		where_arr = append(where_arr, "equipment_id = ?")
		where_params = append(where_params, equipmentId)
	}

	if repositoryId != "" {
		where_arr = append(where_arr, "repository_id = ?")
		where_params = append(where_params, repositoryId)
	}

	if feature != nil {
		for k, v := range feature.AsMap() {
			where_arr = append(where_arr, fmt.Sprintf("feature->>'%s' = ?", k))
			where_params = append(where_params, v)
		}
	}

	if code != "" {
		where_arr = append(where_arr, "code = ?")
		where_params = append(where_params, code)
	}

	where_str := ""

	if len(where_arr) > 0 {
		where_str = "WHERE " + strings.Join(where_arr, " AND ")
	}

	tx := r.data.GormClient.Raw(`
		SELECT * FROM wms_materials
		`+where_str+`
	`, where_params...).Scan(&res)

	if tx.Error != nil {
		return nil, tx.Error
	}

	return res, nil
}
