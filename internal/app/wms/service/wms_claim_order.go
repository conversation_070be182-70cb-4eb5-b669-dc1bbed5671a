package service

import (
	"context"
	"strings"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/app/wms/biz"
	"kratos-mono-demo/internal/pkg/go-utils/trans"

	"google.golang.org/protobuf/types/known/emptypb"
)

type WmsClaimOrderService struct {
	v1.UnimplementedWmsClaimOrderServiceServer

	uc *biz.WmsClaimOrderUsecase
}

func NewWmsClaimOrderService(uc *biz.WmsClaimOrderUsecase) *WmsClaimOrderService {
	return &WmsClaimOrderService{uc: uc}
}

func (s *WmsClaimOrderService) ListWmsClaimOrder(ctx context.Context, req *v1.ListWmsClaimOrderRequest) (*v1.ListWmsClaimOrderResponse, error) {
	return s.uc.ListWmsClaimOrder(ctx, req)
}

func (s *WmsClaimOrderService) GetWmsClaimOrder(ctx context.Context, req *v1.GetWmsClaimOrderRequest) (*v1.WmsClaimOrder, error) {
	return s.uc.GetWmsClaimOrder(ctx, req.GetId())
}

func (s *WmsClaimOrderService) CreateWmsClaimOrder(ctx context.Context, req *v1.CreateWmsClaimOrderRequest) (*v1.WmsClaimOrder, error) {
	return s.uc.CreateWmsClaimOrder(ctx, req)
}

func (s *WmsClaimOrderService) UpdateWmsClaimOrder(ctx context.Context, req *v1.UpdateWmsClaimOrderRequest) (*v1.WmsClaimOrder, error) {
	return s.uc.UpdateWmsClaimOrder(ctx, req)
}

func (s *WmsClaimOrderService) DeleteWmsClaimOrder(ctx context.Context, req *v1.DeleteWmsClaimOrderRequest) (*emptypb.Empty, error) {
	_, err := s.uc.DeleteWmsClaimOrder(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *WmsClaimOrderService) MultiDeleteWmsClaimOrder(ctx context.Context, req *v1.MultiDeleteWmsClaimOrderRequest) (*emptypb.Empty, error) {
	_, err := s.uc.MultiDeleteWmsClaimOrder(ctx, req.GetIds())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// 企业微信审批回调
func (s *WmsClaimOrderService) ReceiveWxworkApprovalEvent(ctx context.Context, spType string, thirdNo string, openSpStatus int32) error {
	id := strings.Replace(thirdNo, spType+"-", "", -1)

	_, err := s.uc.UpdateWmsClaimOrder(ctx, &v1.UpdateWmsClaimOrderRequest{
		Id:     id,
		Status: trans.Int32ToStrPtr(openSpStatus),
	})

	return err
}
