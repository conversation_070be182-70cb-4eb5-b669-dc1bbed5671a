package service

import (
	"context"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/app/wms/biz"

	"google.golang.org/protobuf/types/known/emptypb"
)

type WmsRepairOrderDetailService struct {
	v1.UnimplementedWmsRepairOrderDetailServiceServer

	uc *biz.WmsRepairOrderDetailUsecase
}

func NewWmsRepairOrderDetailService(uc *biz.WmsRepairOrderDetailUsecase) *WmsRepairOrderDetailService {
	return &WmsRepairOrderDetailService{uc: uc}
}

func (s *WmsRepairOrderDetailService) ListWmsRepairOrderDetail(ctx context.Context, req *v1.ListWmsRepairOrderDetailRequest) (*v1.ListWmsRepairOrderDetailResponse, error) {
	return s.uc.ListWmsRepairOrderDetail(ctx, req)
}

func (s *WmsRepairOrderDetailService) GetWmsRepairOrderDetail(ctx context.Context, req *v1.GetWmsRepairOrderDetailRequest) (*v1.WmsRepairOrderDetail, error) {
	return s.uc.GetWmsRepairOrderDetail(ctx, req.GetId())
}

func (s *WmsRepairOrderDetailService) CreateWmsRepairOrderDetail(ctx context.Context, req *v1.CreateWmsRepairOrderDetailRequest) (*v1.WmsRepairOrderDetail, error) {
	return s.uc.CreateWmsRepairOrderDetail(ctx, req)
}

func (s *WmsRepairOrderDetailService) UpdateWmsRepairOrderDetail(ctx context.Context, req *v1.UpdateWmsRepairOrderDetailRequest) (*v1.WmsRepairOrderDetail, error) {
	return s.uc.UpdateWmsRepairOrderDetail(ctx, req)
}

func (s *WmsRepairOrderDetailService) DeleteWmsRepairOrderDetail(ctx context.Context, req *v1.DeleteWmsRepairOrderDetailRequest) (*emptypb.Empty, error) {
	_, err := s.uc.DeleteWmsRepairOrderDetail(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *WmsRepairOrderDetailService) MultiDeleteWmsRepairOrderDetail(ctx context.Context, req *v1.MultiDeleteWmsRepairOrderDetailRequest) (*emptypb.Empty, error) {
	_, err := s.uc.MultiDeleteWmsRepairOrderDetail(ctx, req.GetIds())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}
