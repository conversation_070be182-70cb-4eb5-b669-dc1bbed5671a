package service

import (
	"context"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/app/wms/biz"

	"google.golang.org/protobuf/types/known/emptypb"
)

type WmsClaimOrderDetailService struct {
	v1.UnimplementedWmsClaimOrderDetailServiceServer

	uc *biz.WmsClaimOrderDetailUsecase
}

func NewWmsClaimOrderDetailService(uc *biz.WmsClaimOrderDetailUsecase) *WmsClaimOrderDetailService {
	return &WmsClaimOrderDetailService{uc: uc}
}

func (s *WmsClaimOrderDetailService) ListWmsClaimOrderDetail(ctx context.Context, req *v1.ListWmsClaimOrderDetailRequest) (*v1.ListWmsClaimOrderDetailResponse, error) {
	return s.uc.ListWmsClaimOrderDetail(ctx, req)
}

func (s *WmsClaimOrderDetailService) GetWmsClaimOrderDetail(ctx context.Context, req *v1.GetWmsClaimOrderDetailRequest) (*v1.WmsClaimOrderDetail, error) {
	return s.uc.GetWmsClaimOrderDetail(ctx, req.GetId())
}

func (s *WmsClaimOrderDetailService) CreateWmsClaimOrderDetail(ctx context.Context, req *v1.CreateWmsClaimOrderDetailRequest) (*v1.WmsClaimOrderDetail, error) {
	return s.uc.CreateWmsClaimOrderDetail(ctx, req)
}

func (s *WmsClaimOrderDetailService) UpdateWmsClaimOrderDetail(ctx context.Context, req *v1.UpdateWmsClaimOrderDetailRequest) (*v1.WmsClaimOrderDetail, error) {
	return s.uc.UpdateWmsClaimOrderDetail(ctx, req)
}

func (s *WmsClaimOrderDetailService) DeleteWmsClaimOrderDetail(ctx context.Context, req *v1.DeleteWmsClaimOrderDetailRequest) (*emptypb.Empty, error) {
	_, err := s.uc.DeleteWmsClaimOrderDetail(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *WmsClaimOrderDetailService) MultiDeleteWmsClaimOrderDetail(ctx context.Context, req *v1.MultiDeleteWmsClaimOrderDetailRequest) (*emptypb.Empty, error) {
	_, err := s.uc.MultiDeleteWmsClaimOrderDetail(ctx, req.GetIds())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}
