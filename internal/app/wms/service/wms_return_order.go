package service

import (
	"context"
	"strings"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/app/wms/biz"
	"kratos-mono-demo/internal/pkg/go-utils/trans"

	"google.golang.org/protobuf/types/known/emptypb"
)

type WmsReturnOrderService struct {
	v1.UnimplementedWmsReturnOrderServiceServer

	uc *biz.WmsReturnOrderUsecase
}

func NewWmsReturnOrderService(uc *biz.WmsReturnOrderUsecase) *WmsReturnOrderService {
	return &WmsReturnOrderService{uc: uc}
}

func (s *WmsReturnOrderService) ListWmsReturnOrder(ctx context.Context, req *v1.ListWmsReturnOrderRequest) (*v1.ListWmsReturnOrderResponse, error) {
	return s.uc.ListWmsReturnOrder(ctx, req)
}

func (s *WmsReturnOrderService) GetWmsReturnOrder(ctx context.Context, req *v1.GetWmsReturnOrderRequest) (*v1.WmsReturnOrder, error) {
	return s.uc.GetWmsReturnOrder(ctx, req.GetId())
}

func (s *WmsReturnOrderService) CreateWmsReturnOrder(ctx context.Context, req *v1.CreateWmsReturnOrderRequest) (*v1.WmsReturnOrder, error) {
	return s.uc.CreateWmsReturnOrder(ctx, req)
}

func (s *WmsReturnOrderService) UpdateWmsReturnOrder(ctx context.Context, req *v1.UpdateWmsReturnOrderRequest) (*v1.WmsReturnOrder, error) {
	return s.uc.UpdateWmsReturnOrder(ctx, req)
}

func (s *WmsReturnOrderService) DeleteWmsReturnOrder(ctx context.Context, req *v1.DeleteWmsReturnOrderRequest) (*emptypb.Empty, error) {
	_, err := s.uc.DeleteWmsReturnOrder(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *WmsReturnOrderService) MultiDeleteWmsReturnOrder(ctx context.Context, req *v1.MultiDeleteWmsReturnOrderRequest) (*emptypb.Empty, error) {
	_, err := s.uc.MultiDeleteWmsReturnOrder(ctx, req.GetIds())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// 企业微信审批回调
func (s *WmsReturnOrderService) ReceiveWxworkApprovalEvent(ctx context.Context, spType string, thirdNo string, openSpStatus int32) error {
	id := strings.Replace(thirdNo, spType+"-", "", -1)

	_, err := s.uc.UpdateWmsReturnOrder(ctx, &v1.UpdateWmsReturnOrderRequest{
		Id:     id,
		Status: trans.Int32ToStrPtr(openSpStatus),
	})

	return err
}
