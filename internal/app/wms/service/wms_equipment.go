package service

import (
	"context"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/app/wms/biz"

	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/structpb"
)

type WmsEquipmentService struct {
	v1.UnimplementedWmsEquipmentServiceServer

	uc *biz.WmsEquipmentUsecase
}

func NewWmsEquipmentService(uc *biz.WmsEquipmentUsecase) *WmsEquipmentService {
	return &WmsEquipmentService{uc: uc}
}

func (s *WmsEquipmentService) ListWmsEquipment(ctx context.Context, req *v1.ListWmsEquipmentRequest) (*v1.ListWmsEquipmentResponse, error) {
	return s.uc.ListWmsEquipment(ctx, req)
}

func (s *WmsEquipmentService) GetWmsEquipment(ctx context.Context, req *v1.GetWmsEquipmentRequest) (*v1.WmsEquipment, error) {
	return s.uc.GetWmsEquipment(ctx, req.GetId())
}

func (s *WmsEquipmentService) CreateWmsEquipment(ctx context.Context, req *v1.CreateWmsEquipmentRequest) (*v1.WmsEquipment, error) {
	return s.uc.CreateWmsEquipment(ctx, req)
}

func (s *WmsEquipmentService) UpdateWmsEquipment(ctx context.Context, req *v1.UpdateWmsEquipmentRequest) (*v1.WmsEquipment, error) {
	return s.uc.UpdateWmsEquipment(ctx, req)
}

func (s *WmsEquipmentService) DeleteWmsEquipment(ctx context.Context, req *v1.DeleteWmsEquipmentRequest) (*emptypb.Empty, error) {
	_, err := s.uc.DeleteWmsEquipment(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *WmsEquipmentService) MultiDeleteWmsEquipment(ctx context.Context, req *v1.MultiDeleteWmsEquipmentRequest) (*emptypb.Empty, error) {
	_, err := s.uc.MultiDeleteWmsEquipment(ctx, req.GetIds())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *WmsEquipmentService) GetEquipment(ctx context.Context, req *v1.GetWmsEquipmentRequest) (*v1.GetEquipmentResponse, error) {
	return s.uc.GetEquipment(ctx, req.GetId())
}

func (s *WmsEquipmentService) CreateEquipment(ctx context.Context, req *v1.CreateEquipmentRequest) (*v1.GetEquipmentResponse, error) {
	return s.uc.CreateEquipment(ctx, req)
}

func (s *WmsEquipmentService) UpdateEquipment(ctx context.Context, req *v1.UpdateEquipmentRequest) (*v1.GetEquipmentResponse, error) {
	return s.uc.UpdateEquipment(ctx, req)
}

func (s *WmsEquipmentService) SaveEquipment(ctx context.Context, req *v1.UpdateEquipmentRequest) (*v1.GetEquipmentResponse, error) {
	return s.uc.SaveEquipment(ctx, req)
}

func (s *WmsEquipmentService) ListMyWmsEquipment(ctx context.Context, req *v1.ListWmsEquipmentRequest) (*v1.ListWmsEquipmentResponse, error) {
	return s.uc.ListMyWmsEquipment(ctx, req)
}

// 获取页面代码
func (s *WmsEquipmentService) GetEquipmentPagecode(ctx context.Context, req *v1.GetEquipmentPagecodeRequest) (*structpb.Struct, error) {
	return s.uc.GetEquipmentPagecode(ctx, req)
}

// 获取装备详细规格数据
func (s *WmsEquipmentService) GetEquipmentFeatures(ctx context.Context, req *v1.GetEquipmentFeaturesRequest) (*v1.GetEquipmentFeaturesResponse, error) {
	return s.uc.GetEquipmentFeatures(ctx, req)
}

// 获取装备详细规格数
func (s *WmsEquipmentService) GetEquipmentFeatureCount(ctx context.Context, req *v1.GetEquipmentFeatureCountRequest) (*v1.GetEquipmentFeatureCountResponse, error) {
	return s.uc.GetEquipmentFeatureCount(ctx, req)
}

// 获取装备的库位列表
func (s *WmsEquipmentService) GetEquipmentRepositoryPositionList(ctx context.Context, req *v1.GetEquipmentRepositoryPositionListRequest) (*v1.GetEquipmentRepositoryPositionListResponse, error) {
	return s.uc.GetEquipmentRepositoryPositionList(ctx, req)
}
