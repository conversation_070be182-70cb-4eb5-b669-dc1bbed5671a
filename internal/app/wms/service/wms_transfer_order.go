package service

import (
	"context"
	"strings"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/app/wms/biz"
	"kratos-mono-demo/internal/pkg/go-utils/trans"

	"google.golang.org/protobuf/types/known/emptypb"
)

type WmsTransferOrderService struct {
	v1.UnimplementedWmsTransferOrderServiceServer

	uc *biz.WmsTransferOrderUsecase
}

func NewWmsTransferOrderService(uc *biz.WmsTransferOrderUsecase) *WmsTransferOrderService {
	return &WmsTransferOrderService{uc: uc}
}

func (s *WmsTransferOrderService) ListWmsTransferOrder(ctx context.Context, req *v1.ListWmsTransferOrderRequest) (*v1.ListWmsTransferOrderResponse, error) {
	return s.uc.ListWmsTransferOrder(ctx, req)
}

func (s *WmsTransferOrderService) GetWmsTransferOrder(ctx context.Context, req *v1.GetWmsTransferOrderRequest) (*v1.WmsTransferOrder, error) {
	return s.uc.GetWmsTransferOrder(ctx, req.GetId())
}

func (s *WmsTransferOrderService) CreateWmsTransferOrder(ctx context.Context, req *v1.CreateWmsTransferOrderRequest) (*v1.WmsTransferOrder, error) {
	return s.uc.CreateWmsTransferOrder(ctx, req)
}

func (s *WmsTransferOrderService) UpdateWmsTransferOrder(ctx context.Context, req *v1.UpdateWmsTransferOrderRequest) (*v1.WmsTransferOrder, error) {
	return s.uc.UpdateWmsTransferOrder(ctx, req)
}

func (s *WmsTransferOrderService) DeleteWmsTransferOrder(ctx context.Context, req *v1.DeleteWmsTransferOrderRequest) (*emptypb.Empty, error) {
	_, err := s.uc.DeleteWmsTransferOrder(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *WmsTransferOrderService) MultiDeleteWmsTransferOrder(ctx context.Context, req *v1.MultiDeleteWmsTransferOrderRequest) (*emptypb.Empty, error) {
	_, err := s.uc.MultiDeleteWmsTransferOrder(ctx, req.GetIds())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// 企业微信审批回调
func (s *WmsTransferOrderService) ReceiveWxworkApprovalEvent(ctx context.Context, spType string, thirdNo string, openSpStatus int32) error {
	id := strings.Replace(thirdNo, spType+"-", "", -1)

	_, err := s.uc.UpdateWmsTransferOrder(ctx, &v1.UpdateWmsTransferOrderRequest{
		Id:     id,
		Status: trans.Int32ToStrPtr(openSpStatus),
	})

	return err
}
