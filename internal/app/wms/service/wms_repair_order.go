package service

import (
	"context"
	"strings"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/app/wms/biz"
	"kratos-mono-demo/internal/pkg/go-utils/trans"

	"google.golang.org/protobuf/types/known/emptypb"
)

type WmsRepairOrderService struct {
	v1.UnimplementedWmsRepairOrderServiceServer

	uc *biz.WmsRepairOrderUsecase
}

func NewWmsRepairOrderService(uc *biz.WmsRepairOrderUsecase) *WmsRepairOrderService {
	return &WmsRepairOrderService{uc: uc}
}

func (s *WmsRepairOrderService) ListWmsRepairOrder(ctx context.Context, req *v1.ListWmsRepairOrderRequest) (*v1.ListWmsRepairOrderResponse, error) {
	return s.uc.ListWmsRepairOrder(ctx, req)
}

func (s *WmsRepairOrderService) GetWmsRepairOrder(ctx context.Context, req *v1.GetWmsRepairOrderRequest) (*v1.WmsRepairOrder, error) {
	return s.uc.GetWmsRepairOrder(ctx, req.GetId())
}

func (s *WmsRepairOrderService) CreateWmsRepairOrder(ctx context.Context, req *v1.CreateWmsRepairOrderRequest) (*v1.WmsRepairOrder, error) {
	return s.uc.CreateWmsRepairOrder(ctx, req)
}

func (s *WmsRepairOrderService) UpdateWmsRepairOrder(ctx context.Context, req *v1.UpdateWmsRepairOrderRequest) (*v1.WmsRepairOrder, error) {
	return s.uc.UpdateWmsRepairOrder(ctx, req)
}

func (s *WmsRepairOrderService) DeleteWmsRepairOrder(ctx context.Context, req *v1.DeleteWmsRepairOrderRequest) (*emptypb.Empty, error) {
	_, err := s.uc.DeleteWmsRepairOrder(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *WmsRepairOrderService) MultiDeleteWmsRepairOrder(ctx context.Context, req *v1.MultiDeleteWmsRepairOrderRequest) (*emptypb.Empty, error) {
	_, err := s.uc.MultiDeleteWmsRepairOrder(ctx, req.GetIds())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// 企业微信审批回调
func (s *WmsRepairOrderService) ReceiveWxworkApprovalEvent(ctx context.Context, spType string, thirdNo string, openSpStatus int32) error {
	id := strings.Replace(thirdNo, spType+"-", "", -1)

	_, err := s.uc.UpdateWmsRepairOrder(ctx, &v1.UpdateWmsRepairOrderRequest{
		Id:     id,
		Status: trans.Int32ToStrPtr(openSpStatus),
	})

	return err
}
