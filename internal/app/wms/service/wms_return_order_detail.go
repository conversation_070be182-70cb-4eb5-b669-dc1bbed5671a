package service

import (
	"context"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/app/wms/biz"

	"google.golang.org/protobuf/types/known/emptypb"
)

type WmsReturnOrderDetailService struct {
	v1.UnimplementedWmsReturnOrderDetailServiceServer

	uc *biz.WmsReturnOrderDetailUsecase
}

func NewWmsReturnOrderDetailService(uc *biz.WmsReturnOrderDetailUsecase) *WmsReturnOrderDetailService {
	return &WmsReturnOrderDetailService{uc: uc}
}

func (s *WmsReturnOrderDetailService) ListWmsReturnOrderDetail(ctx context.Context, req *v1.ListWmsReturnOrderDetailRequest) (*v1.ListWmsReturnOrderDetailResponse, error) {
	return s.uc.ListWmsReturnOrderDetail(ctx, req)
}

func (s *WmsReturnOrderDetailService) GetWmsReturnOrderDetail(ctx context.Context, req *v1.GetWmsReturnOrderDetailRequest) (*v1.WmsReturnOrderDetail, error) {
	return s.uc.GetWmsReturnOrderDetail(ctx, req.GetId())
}

func (s *WmsReturnOrderDetailService) CreateWmsReturnOrderDetail(ctx context.Context, req *v1.CreateWmsReturnOrderDetailRequest) (*v1.WmsReturnOrderDetail, error) {
	return s.uc.CreateWmsReturnOrderDetail(ctx, req)
}

func (s *WmsReturnOrderDetailService) UpdateWmsReturnOrderDetail(ctx context.Context, req *v1.UpdateWmsReturnOrderDetailRequest) (*v1.WmsReturnOrderDetail, error) {
	return s.uc.UpdateWmsReturnOrderDetail(ctx, req)
}

func (s *WmsReturnOrderDetailService) DeleteWmsReturnOrderDetail(ctx context.Context, req *v1.DeleteWmsReturnOrderDetailRequest) (*emptypb.Empty, error) {
	_, err := s.uc.DeleteWmsReturnOrderDetail(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *WmsReturnOrderDetailService) MultiDeleteWmsReturnOrderDetail(ctx context.Context, req *v1.MultiDeleteWmsReturnOrderDetailRequest) (*emptypb.Empty, error) {
	_, err := s.uc.MultiDeleteWmsReturnOrderDetail(ctx, req.GetIds())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}
