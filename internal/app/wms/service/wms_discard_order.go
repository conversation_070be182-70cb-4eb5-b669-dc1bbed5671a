package service

import (
	"context"
	"strings"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/app/wms/biz"
	"kratos-mono-demo/internal/pkg/go-utils/trans"

	"google.golang.org/protobuf/types/known/emptypb"
)

type WmsDiscardOrderService struct {
	v1.UnimplementedWmsDiscardOrderServiceServer

	uc *biz.WmsDiscardOrderUsecase
}

func NewWmsDiscardOrderService(uc *biz.WmsDiscardOrderUsecase) *WmsDiscardOrderService {
	return &WmsDiscardOrderService{uc: uc}
}

func (s *WmsDiscardOrderService) ListWmsDiscardOrder(ctx context.Context, req *v1.ListWmsDiscardOrderRequest) (*v1.ListWmsDiscardOrderResponse, error) {
	return s.uc.ListWmsDiscardOrder(ctx, req)
}

func (s *WmsDiscardOrderService) GetWmsDiscardOrder(ctx context.Context, req *v1.GetWmsDiscardOrderRequest) (*v1.WmsDiscardOrder, error) {
	return s.uc.GetWmsDiscardOrder(ctx, req.GetId())
}

func (s *WmsDiscardOrderService) CreateWmsDiscardOrder(ctx context.Context, req *v1.CreateWmsDiscardOrderRequest) (*v1.WmsDiscardOrder, error) {
	return s.uc.CreateWmsDiscardOrder(ctx, req)
}

func (s *WmsDiscardOrderService) UpdateWmsDiscardOrder(ctx context.Context, req *v1.UpdateWmsDiscardOrderRequest) (*v1.WmsDiscardOrder, error) {
	return s.uc.UpdateWmsDiscardOrder(ctx, req)
}

func (s *WmsDiscardOrderService) DeleteWmsDiscardOrder(ctx context.Context, req *v1.DeleteWmsDiscardOrderRequest) (*emptypb.Empty, error) {
	_, err := s.uc.DeleteWmsDiscardOrder(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *WmsDiscardOrderService) MultiDeleteWmsDiscardOrder(ctx context.Context, req *v1.MultiDeleteWmsDiscardOrderRequest) (*emptypb.Empty, error) {
	_, err := s.uc.MultiDeleteWmsDiscardOrder(ctx, req.GetIds())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// 企业微信审批回调
func (s *WmsDiscardOrderService) ReceiveWxworkApprovalEvent(ctx context.Context, spType string, thirdNo string, openSpStatus int32) error {
	id := strings.Replace(thirdNo, spType+"-", "", -1)

	_, err := s.uc.UpdateWmsDiscardOrder(ctx, &v1.UpdateWmsDiscardOrderRequest{
		Id:     id,
		Status: trans.Int32ToStrPtr(openSpStatus),
	})

	return err
}
