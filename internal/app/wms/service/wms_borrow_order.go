package service

import (
	"context"
	"strings"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/app/wms/biz"
	"kratos-mono-demo/internal/pkg/go-utils/trans"

	"google.golang.org/protobuf/types/known/emptypb"
)

type WmsBorrowOrderService struct {
	v1.UnimplementedWmsBorrowOrderServiceServer

	uc *biz.WmsBorrowOrderUsecase
}

func NewWmsBorrowOrderService(uc *biz.WmsBorrowOrderUsecase) *WmsBorrowOrderService {
	return &WmsBorrowOrderService{uc: uc}
}

func (s *WmsBorrowOrderService) ListWmsBorrowOrder(ctx context.Context, req *v1.ListWmsBorrowOrderRequest) (*v1.ListWmsBorrowOrderResponse, error) {
	return s.uc.ListWmsBorrowOrder(ctx, req)
}

func (s *WmsBorrowOrderService) GetWmsBorrowOrder(ctx context.Context, req *v1.GetWmsBorrowOrderRequest) (*v1.WmsBorrowOrder, error) {
	return s.uc.GetWmsBorrowOrder(ctx, req.GetId())
}

func (s *WmsBorrowOrderService) CreateWmsBorrowOrder(ctx context.Context, req *v1.CreateWmsBorrowOrderRequest) (*v1.WmsBorrowOrder, error) {
	return s.uc.CreateWmsBorrowOrder(ctx, req)
}

func (s *WmsBorrowOrderService) UpdateWmsBorrowOrder(ctx context.Context, req *v1.UpdateWmsBorrowOrderRequest) (*v1.WmsBorrowOrder, error) {
	return s.uc.UpdateWmsBorrowOrder(ctx, req)
}

func (s *WmsBorrowOrderService) DeleteWmsBorrowOrder(ctx context.Context, req *v1.DeleteWmsBorrowOrderRequest) (*emptypb.Empty, error) {
	_, err := s.uc.DeleteWmsBorrowOrder(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *WmsBorrowOrderService) MultiDeleteWmsBorrowOrder(ctx context.Context, req *v1.MultiDeleteWmsBorrowOrderRequest) (*emptypb.Empty, error) {
	_, err := s.uc.MultiDeleteWmsBorrowOrder(ctx, req.GetIds())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// 企业微信审批回调
func (s *WmsBorrowOrderService) ReceiveWxworkApprovalEvent(ctx context.Context, spType string, thirdNo string, openSpStatus int32) error {
	id := strings.Replace(thirdNo, spType+"-", "", -1)

	_, err := s.uc.UpdateWmsBorrowOrder(ctx, &v1.UpdateWmsBorrowOrderRequest{
		Id:     id,
		Status: trans.Int32ToStrPtr(openSpStatus),
	})

	return err
}
