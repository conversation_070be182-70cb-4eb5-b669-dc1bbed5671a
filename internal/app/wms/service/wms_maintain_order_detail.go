package service

import (
	"context"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/app/wms/biz"

	"google.golang.org/protobuf/types/known/emptypb"
)

type WmsMaintainOrderDetailService struct {
	v1.UnimplementedWmsMaintainOrderDetailServiceServer

	uc *biz.WmsMaintainOrderDetailUsecase
}

func NewWmsMaintainOrderDetailService(uc *biz.WmsMaintainOrderDetailUsecase) *WmsMaintainOrderDetailService {
	return &WmsMaintainOrderDetailService{uc: uc}
}

func (s *WmsMaintainOrderDetailService) ListWmsMaintainOrderDetail(ctx context.Context, req *v1.ListWmsMaintainOrderDetailRequest) (*v1.ListWmsMaintainOrderDetailResponse, error) {
	return s.uc.ListWmsMaintainOrderDetail(ctx, req)
}

func (s *WmsMaintainOrderDetailService) GetWmsMaintainOrderDetail(ctx context.Context, req *v1.GetWmsMaintainOrderDetailRequest) (*v1.WmsMaintainOrderDetail, error) {
	return s.uc.GetWmsMaintainOrderDetail(ctx, req.GetId())
}

func (s *WmsMaintainOrderDetailService) CreateWmsMaintainOrderDetail(ctx context.Context, req *v1.CreateWmsMaintainOrderDetailRequest) (*v1.WmsMaintainOrderDetail, error) {
	return s.uc.CreateWmsMaintainOrderDetail(ctx, req)
}

func (s *WmsMaintainOrderDetailService) UpdateWmsMaintainOrderDetail(ctx context.Context, req *v1.UpdateWmsMaintainOrderDetailRequest) (*v1.WmsMaintainOrderDetail, error) {
	return s.uc.UpdateWmsMaintainOrderDetail(ctx, req)
}

func (s *WmsMaintainOrderDetailService) DeleteWmsMaintainOrderDetail(ctx context.Context, req *v1.DeleteWmsMaintainOrderDetailRequest) (*emptypb.Empty, error) {
	_, err := s.uc.DeleteWmsMaintainOrderDetail(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *WmsMaintainOrderDetailService) MultiDeleteWmsMaintainOrderDetail(ctx context.Context, req *v1.MultiDeleteWmsMaintainOrderDetailRequest) (*emptypb.Empty, error) {
	_, err := s.uc.MultiDeleteWmsMaintainOrderDetail(ctx, req.GetIds())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}
