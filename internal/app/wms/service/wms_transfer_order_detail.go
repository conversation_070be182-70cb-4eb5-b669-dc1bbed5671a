package service

import (
	"context"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/app/wms/biz"

	"google.golang.org/protobuf/types/known/emptypb"
)

type WmsTransferOrderDetailService struct {
	v1.UnimplementedWmsTransferOrderDetailServiceServer

	uc *biz.WmsTransferOrderDetailUsecase
}

func NewWmsTransferOrderDetailService(uc *biz.WmsTransferOrderDetailUsecase) *WmsTransferOrderDetailService {
	return &WmsTransferOrderDetailService{uc: uc}
}

func (s *WmsTransferOrderDetailService) ListWmsTransferOrderDetail(ctx context.Context, req *v1.ListWmsTransferOrderDetailRequest) (*v1.ListWmsTransferOrderDetailResponse, error) {
	return s.uc.ListWmsTransferOrderDetail(ctx, req)
}

func (s *WmsTransferOrderDetailService) GetWmsTransferOrderDetail(ctx context.Context, req *v1.GetWmsTransferOrderDetailRequest) (*v1.WmsTransferOrderDetail, error) {
	return s.uc.GetWmsTransferOrderDetail(ctx, req.GetId())
}

func (s *WmsTransferOrderDetailService) CreateWmsTransferOrderDetail(ctx context.Context, req *v1.CreateWmsTransferOrderDetailRequest) (*v1.WmsTransferOrderDetail, error) {
	return s.uc.CreateWmsTransferOrderDetail(ctx, req)
}

func (s *WmsTransferOrderDetailService) UpdateWmsTransferOrderDetail(ctx context.Context, req *v1.UpdateWmsTransferOrderDetailRequest) (*v1.WmsTransferOrderDetail, error) {
	return s.uc.UpdateWmsTransferOrderDetail(ctx, req)
}

func (s *WmsTransferOrderDetailService) DeleteWmsTransferOrderDetail(ctx context.Context, req *v1.DeleteWmsTransferOrderDetailRequest) (*emptypb.Empty, error) {
	_, err := s.uc.DeleteWmsTransferOrderDetail(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *WmsTransferOrderDetailService) MultiDeleteWmsTransferOrderDetail(ctx context.Context, req *v1.MultiDeleteWmsTransferOrderDetailRequest) (*emptypb.Empty, error) {
	_, err := s.uc.MultiDeleteWmsTransferOrderDetail(ctx, req.GetIds())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}
