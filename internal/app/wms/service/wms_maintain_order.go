package service

import (
	"context"
	"strings"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/app/wms/biz"
	"kratos-mono-demo/internal/pkg/go-utils/trans"

	"google.golang.org/protobuf/types/known/emptypb"
)

type WmsMaintainOrderService struct {
	v1.UnimplementedWmsMaintainOrderServiceServer

	uc *biz.WmsMaintainOrderUsecase
}

func NewWmsMaintainOrderService(uc *biz.WmsMaintainOrderUsecase) *WmsMaintainOrderService {
	return &WmsMaintainOrderService{uc: uc}
}

func (s *WmsMaintainOrderService) ListWmsMaintainOrder(ctx context.Context, req *v1.ListWmsMaintainOrderRequest) (*v1.ListWmsMaintainOrderResponse, error) {
	return s.uc.ListWmsMaintainOrder(ctx, req)
}

func (s *WmsMaintainOrderService) GetWmsMaintainOrder(ctx context.Context, req *v1.GetWmsMaintainOrderRequest) (*v1.WmsMaintainOrder, error) {
	return s.uc.GetWmsMaintainOrder(ctx, req.GetId())
}

func (s *WmsMaintainOrderService) CreateWmsMaintainOrder(ctx context.Context, req *v1.CreateWmsMaintainOrderRequest) (*v1.WmsMaintainOrder, error) {
	return s.uc.CreateWmsMaintainOrder(ctx, req)
}

func (s *WmsMaintainOrderService) UpdateWmsMaintainOrder(ctx context.Context, req *v1.UpdateWmsMaintainOrderRequest) (*v1.WmsMaintainOrder, error) {
	return s.uc.UpdateWmsMaintainOrder(ctx, req)
}

func (s *WmsMaintainOrderService) DeleteWmsMaintainOrder(ctx context.Context, req *v1.DeleteWmsMaintainOrderRequest) (*emptypb.Empty, error) {
	_, err := s.uc.DeleteWmsMaintainOrder(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *WmsMaintainOrderService) MultiDeleteWmsMaintainOrder(ctx context.Context, req *v1.MultiDeleteWmsMaintainOrderRequest) (*emptypb.Empty, error) {
	_, err := s.uc.MultiDeleteWmsMaintainOrder(ctx, req.GetIds())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// 企业微信审批回调
func (s *WmsMaintainOrderService) ReceiveWxworkApprovalEvent(ctx context.Context, spType string, thirdNo string, openSpStatus int32) error {
	id := strings.Replace(thirdNo, spType+"-", "", -1)

	_, err := s.uc.UpdateWmsMaintainOrder(ctx, &v1.UpdateWmsMaintainOrderRequest{
		Id:     id,
		Status: trans.Int32ToStrPtr(openSpStatus),
	})

	return err
}
