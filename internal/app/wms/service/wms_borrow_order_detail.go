package service

import (
	"context"

	v1 "kratos-mono-demo/gen/api/wms/v1"
	"kratos-mono-demo/internal/app/wms/biz"

	"google.golang.org/protobuf/types/known/emptypb"
)

type WmsBorrowOrderDetailService struct {
	v1.UnimplementedWmsBorrowOrderDetailServiceServer

	uc *biz.WmsBorrowOrderDetailUsecase
}

func NewWmsBorrowOrderDetailService(uc *biz.WmsBorrowOrderDetailUsecase) *WmsBorrowOrderDetailService {
	return &WmsBorrowOrderDetailService{uc: uc}
}

func (s *WmsBorrowOrderDetailService) ListWmsBorrowOrderDetail(ctx context.Context, req *v1.ListWmsBorrowOrderDetailRequest) (*v1.ListWmsBorrowOrderDetailResponse, error) {
	return s.uc.ListWmsBorrowOrderDetail(ctx, req)
}

func (s *WmsBorrowOrderDetailService) GetWmsBorrowOrderDetail(ctx context.Context, req *v1.GetWmsBorrowOrderDetailRequest) (*v1.WmsBorrowOrderDetail, error) {
	return s.uc.GetWmsBorrowOrderDetail(ctx, req.GetId())
}

func (s *WmsBorrowOrderDetailService) CreateWmsBorrowOrderDetail(ctx context.Context, req *v1.CreateWmsBorrowOrderDetailRequest) (*v1.WmsBorrowOrderDetail, error) {
	return s.uc.CreateWmsBorrowOrderDetail(ctx, req)
}

func (s *WmsBorrowOrderDetailService) UpdateWmsBorrowOrderDetail(ctx context.Context, req *v1.UpdateWmsBorrowOrderDetailRequest) (*v1.WmsBorrowOrderDetail, error) {
	return s.uc.UpdateWmsBorrowOrderDetail(ctx, req)
}

func (s *WmsBorrowOrderDetailService) DeleteWmsBorrowOrderDetail(ctx context.Context, req *v1.DeleteWmsBorrowOrderDetailRequest) (*emptypb.Empty, error) {
	_, err := s.uc.DeleteWmsBorrowOrderDetail(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *WmsBorrowOrderDetailService) MultiDeleteWmsBorrowOrderDetail(ctx context.Context, req *v1.MultiDeleteWmsBorrowOrderDetailRequest) (*emptypb.Empty, error) {
	_, err := s.uc.MultiDeleteWmsBorrowOrderDetail(ctx, req.GetIds())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}
