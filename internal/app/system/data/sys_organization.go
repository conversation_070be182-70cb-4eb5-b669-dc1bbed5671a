package data

import (
	"context"
	"kratos-mono-demo/internal/app/system/biz"
	"kratos-mono-demo/internal/data"
	"kratos-mono-demo/internal/data/ent/sysuserorganization"
	"time"

	conf "kratos-mono-demo/gen/config/conf/v1"

	"kratos-mono-demo/internal/data/ent"

	v1 "kratos-mono-demo/gen/api/system/v1"
	"kratos-mono-demo/internal/data/ent/sysorganization"
	"kratos-mono-demo/internal/data/ent/sysuser"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"errors"
	entutils "kratos-mono-demo/internal/pkg/ent-utils"
	"kratos-mono-demo/internal/pkg/go-utils/arrayutil"
	entgo "kratos-mono-demo/internal/pkg/go-utils/entgo/query"
	"kratos-mono-demo/internal/pkg/go-utils/stringutil"
	"kratos-mono-demo/internal/pkg/go-utils/trans"
)

var _ biz.SysOrganizationRepo = (*SysOrganizationRepo)(nil)

type SysOrganizationRepo struct {
	data *data.Data
	log  *log.Helper
}

func NewSysOrganizationRepo(c *conf.Config, logger log.Logger, data *data.Data) biz.SysOrganizationRepo {
	l := log.NewHelper(log.With(logger, "module", "sys_organization/data"))
	return &SysOrganizationRepo{
		data: data,
		log:  l,
	}
}

func (r *SysOrganizationRepo) getEntClient() *ent.Client {
	return r.data.EntClient.Client()
}

func (r *SysOrganizationRepo) convertEntToProto(in *ent.SysOrganization) *v1.SysOrganization {
	if in == nil {
		return nil
	}
	return &v1.SysOrganization{
		Id:            in.ID,
		CreatedAt:     trans.TimeToStrPtr(in.CreatedAt),
		UpdatedAt:     trans.TimeToStrPtr(in.UpdatedAt),
		CreatedBy:     trans.String(in.CreatedBy),
		UpdatedBy:     in.UpdatedBy,
		Name:          trans.String(in.Name),
		TeamId:        trans.String(in.TeamID),
		ApplicationId: trans.String(in.ApplicationID),
		Remark:        trans.String(in.Remark),
		ParentId:      in.ParentID,
		Sort:          trans.Int32(in.Sort),
	}
}
func (r *SysOrganizationRepo) travelChild(node *v1.SysOrganization, nodesMap map[string][]*v1.SysOrganization, level int) ([]*v1.SysOrganization, error) {
	if level > 50 {
		return nil, errors.New("reached maximum recursion depth, possible circular reference")
	}

	children, exist := nodesMap[node.Id]
	if !exist {
		// 没有子节点，直接返回
		return nil, nil
	}

	for _, child := range children {
		traveledChildren, err := r.travelChild(child, nodesMap, level+1)
		if err != nil {
			return nil, err
		}
		child.Children = traveledChildren
	}

	return children, nil
}
func (r *SysOrganizationRepo) getHasChildrenMap(ctx context.Context, results []*ent.SysOrganization) (map[string]bool, error) {
	var ids []string
	for _, m := range results {
		ids = append(ids, m.ID)
	}
	children, err := r.getEntClient().SysOrganization.Query().
		Where(sysorganization.ParentIDIn(ids...)).
		Select(sysorganization.FieldParentID).
		All(ctx)
	if err != nil {
		return nil, err
	}
	hasChildren := make(map[string]bool)
	for _, child := range children {
		hasChildren[*child.ParentID] = true
	}
	return hasChildren, nil
}

func (r *SysOrganizationRepo) Count(ctx context.Context, whereCond []func(s *sql.Selector), onlyRoot bool) (int, error) {
	builder := r.getEntClient().SysOrganization.Query()
	if onlyRoot {
		builder.Where(
			sysorganization.Or(
				sysorganization.ParentIDIsNil(),
				sysorganization.ParentID(""),
			),
		)
	}

	if len(whereCond) != 0 {
		builder.Modify(whereCond...)
	}

	count, err := builder.Count(ctx)
	if err != nil {
		r.log.Errorf("query count failed: %s", err.Error())
	}

	return count, err
}

func (r *SysOrganizationRepo) getUserInfoMap(ctx context.Context, results []*ent.SysOrganization) (map[string]string, error) {
	userIdInfo := map[string]string{}
	for _, m := range results {
		if m.CreatedBy != "" {
			userIdInfo[m.CreatedBy] = ""
		}
		if m.UpdatedBy != nil && *m.UpdatedBy != "" {
			userIdInfo[*m.UpdatedBy] = ""
		}
	}

	// 获取所有用户id
	userIds := make([]string, 0, len(userIdInfo))
	for k := range userIdInfo {
		userIds = append(userIds, k)
	}

	// 获取用户信息
	userInfo, err := r.getEntClient().SysUser.
		Query().
		Where(sysuser.IDIn(userIds...)).
		Select(sysuser.FieldUsername, sysuser.FieldNickname).
		All(ctx)

	if err != nil {
		r.log.Errorf("query user info failed: %s", err.Error())
		return nil, err
	}

	for _, u := range userInfo {
		if u.Nickname != "" {
			userIdInfo[u.ID] = u.Nickname
		} else {
			userIdInfo[u.ID] = u.Username
		}
	}

	return userIdInfo, nil
}

// 创建索引
func (r *SysOrganizationRepo) createIndex(resources []*ent.SysOrganization) map[string]*ent.SysOrganization {
	index := make(map[string]*ent.SysOrganization)
	for _, res := range resources {
		index[res.ID] = res
	}
	return index
}

// 是不是空的父节点
func (r *SysOrganizationRepo) isEmptyParentId(index map[string]*ent.SysOrganization, parentId *string) bool {
	if stringutil.IsEmpty(parentId) {
		return true
	}

	if _, ok := index[*parentId]; !ok {
		return true
	}

	return false
}

func (r *SysOrganizationRepo) convertEntListToProto(
	ctx context.Context,
	noChildren bool,
	userMap map[string]string,
	results []*ent.SysOrganization,
) ([]*v1.SysOrganization, error) {
	items := make([]*v1.SysOrganization, 0, len(results))
	index := r.createIndex(results)
	// 遍历子节点
	if !noChildren {
		root_items := make([]*v1.SysOrganization, 0, len(results))
		nodesMap := make(map[string][]*v1.SysOrganization)
		for _, m := range results {
			// 如果是空的父节点，则跳过
			if r.isEmptyParentId(index, m.ParentID) {
				continue
			}

			item := r.convertEntToProto(m)
			if userMap != nil {
				if stringutil.IsNotEmpty(item.CreatedBy) {
					item.CreatedBy = trans.String(userMap[*item.CreatedBy])
				}
				if stringutil.IsNotEmpty(item.UpdatedBy) {
					item.UpdatedBy = trans.String(userMap[*item.UpdatedBy])
				}
			}

			parentId := trans.StringValue(item.ParentId)
			nodesMap[parentId] = append(nodesMap[parentId], item)
		}

		for _, m := range results {
			if !r.isEmptyParentId(index, m.ParentID) {
				continue
			}

			item := r.convertEntToProto(m)
			if userMap != nil {
				if stringutil.IsNotEmpty(item.CreatedBy) {
					item.CreatedBy = trans.String(userMap[*item.CreatedBy])
				}
				if stringutil.IsNotEmpty(item.UpdatedBy) {
					item.UpdatedBy = trans.String(userMap[*item.UpdatedBy])
				}
			}
			item_children, err := r.travelChild(item, nodesMap, 1)
			if err != nil {
				return nil, err
			}
			item.Children = item_children
			root_items = append(root_items, item)
		}

		return root_items, nil
	}

	hasChildren, err := r.getHasChildrenMap(ctx, results)
	if err != nil {
		return nil, err
	}

	// 不遍历子节点
	for _, m := range results {
		item := r.convertEntToProto(m)
		if userMap != nil {
			if stringutil.IsNotEmpty(item.CreatedBy) {
				item.CreatedBy = trans.String(userMap[*item.CreatedBy])
			}
			if stringutil.IsNotEmpty(item.UpdatedBy) {
				item.UpdatedBy = trans.String(userMap[*item.UpdatedBy])
			}
		}
		item.HasChildren = trans.Bool(hasChildren[item.Id])
		items = append(items, item)
	}
	return items, nil
}

func (r *SysOrganizationRepo) ListSysOrganization(ctx context.Context, req *v1.ListSysOrganizationRequest) (*v1.ListSysOrganizationResponse, error) {
	builder := r.getEntClient().SysOrganization.Query()
	// 如果没有排序，则添加按sort从大到小排序
	if req.GetOrderBy() == "" {
		builder.Order(ent.Desc(sysorganization.FieldSort), ent.Asc(sysorganization.FieldCreatedAt))
	}
	if req.GetOnlyRoot() {
		builder.Where(
			sysorganization.Or(
				sysorganization.ParentIDIsNil(),
				sysorganization.ParentID(""),
			),
		)
	}

	err, whereSelectors, querySelectors := entgo.BuildQuerySelector(
		req.GetQuery(), req.GetOrQuery(),
		req.GetPage(), req.GetPageSize(), req.GetNoPaging(),
		req.GetOrderBy(), sysorganization.FieldCreatedAt,
		req.GetFieldMask().GetPaths(),
	)
	if err != nil {
		r.log.Errorf("build query select faild: %s", err.Error())
		return nil, err
	}

	if querySelectors != nil {
		builder.Modify(querySelectors...)
	}

	results, err := builder.All(ctx)
	if err != nil {
		return nil, err
	}

	// 获取用户信息
	userMap, err := r.getUserInfoMap(ctx, results)
	if err != nil {
		return nil, err
	}

	// 转换成proto
	items, err := r.convertEntListToProto(ctx, trans.BoolValue(req.NoChildren), userMap, results)
	if err != nil {
		return nil, err
	}

	count, err := r.Count(ctx, whereSelectors, req.GetOnlyRoot())
	if err != nil {
		return nil, err
	}

	ret := v1.ListSysOrganizationResponse{
		Total: int32(count),
		Items: items,
	}

	return &ret, err
}

func (r *SysOrganizationRepo) GetSysOrganization(ctx context.Context, id string) (*v1.SysOrganization, error) {
	ret, err := r.getEntClient().SysOrganization.Get(ctx, id)
	if err != nil && !ent.IsNotFound(err) {
		return nil, err
	}

	return r.convertEntToProto(ret), err
}

func (r *SysOrganizationRepo) CreateSysOrganization(ctx context.Context, req *v1.CreateSysOrganizationRequest) (*v1.SysOrganization, error) {
	builder := r.getEntClient().SysOrganization.Create().
		SetName(req.Name).
		SetTeamID(trans.StringValue(req.TeamId)).
		SetApplicationID(trans.StringValue(req.ApplicationId)).
		SetNillableRemark(req.Remark).
		SetNillableParentID(req.ParentId).
		SetSort(req.Sort).
		SetCreatedAt(time.Now())

	entutils.SetCreatedBy(ctx, builder.Mutation())

	ret, err := builder.Save(ctx)
	if err != nil {
		r.log.Errorf("insert one data failed: %s", err.Error())
		return nil, err
	}

	return r.convertEntToProto(ret), err
}

func (r *SysOrganizationRepo) UpdateSysOrganization(ctx context.Context, req *v1.UpdateSysOrganizationRequest) (*v1.SysOrganization, error) {

	builder := r.getEntClient().SysOrganization.UpdateOneID(req.Id).
		SetNillableName(req.Name).
		SetNillableTeamID(req.TeamId).
		SetNillableApplicationID(req.ApplicationId).
		SetNillableRemark(req.Remark).
		SetNillableParentID(req.ParentId).
		SetNillableSort(req.Sort).
		SetUpdatedAt(time.Now())

	entutils.SetUpdatedBy(ctx, builder.Mutation())

	ret, err := builder.Save(ctx)
	if err != nil {
		r.log.Errorf("update one data failed: %s", err.Error())
		return nil, err
	}

	return r.convertEntToProto(ret), err
}
func (r *SysOrganizationRepo) DeleteSysOrganization(ctx context.Context, id string) (bool, error) {
	menus, err := r.getEntClient().SysOrganization.Query().
		Select(sysorganization.FieldID, sysorganization.FieldParentID).
		All(ctx)

	if err != nil {
		return false, err
	}

	ids := []string{id}
	// 递归获取所有子节点
	children_ids, err := r.getChildren(ctx, ids, menus, []string{})

	if err != nil {
		return false, err
	}

	ids = append(ids, children_ids...)

	// 去重
	ids = arrayutil.Distinct(ids)

	_, err = r.getEntClient().SysOrganization.
		Delete().
		Where(sysorganization.IDIn(ids...)).
		Exec(ctx)

	return err != nil, err
}

func (r *SysOrganizationRepo) MultiDeleteSysOrganization(ctx context.Context, ids []string) (bool, error) {
	items, err := r.getEntClient().SysOrganization.Query().
		Select(sysorganization.FieldID, sysorganization.FieldParentID).
		All(ctx)

	if err != nil {
		return false, err
	}

	// 递归获取所有子节点
	children_ids, err := r.getChildren(ctx, ids, items, []string{})

	if err != nil {
		return false, err
	}

	ids = append(ids, children_ids...)

	// 去重
	ids = arrayutil.Distinct(ids)

	_, err = r.getEntClient().SysOrganization.
		Delete().
		Where(sysorganization.IDIn(ids...)).
		Exec(ctx)
	return err != nil, err
}

// 递归获取所有子节点
// ids: 要获取子节点的节点id
// items: 所有节点
// ancestors: 祖先节点, 用于判断是否有环
func (r *SysOrganizationRepo) getChildren(ctx context.Context, ids []string, items []*ent.SysOrganization, ancestors []string) ([]string, error) {
	var children_ids []string
	for _, m := range items {
		if stringutil.IsNotEmpty(m.ParentID) && arrayutil.IsIn(*m.ParentID, ids) {
			children_ids = append(children_ids, m.ID)

			// 如果有祖先节点，则判断是否有环
			if len(ancestors) > 0 {
				// 如果祖先节点中有当前节点，则有环
				if arrayutil.IsIn(m.ID, ancestors) {
					return nil, errors.New("menu has circle")
				}
			}

			ancestors = append(ancestors, m.ID)

			children, err := r.getChildren(ctx, []string{m.ID}, items, ancestors)
			if err != nil {
				return nil, err
			}
			children_ids = append(children_ids, children...)
		}
	}

	// 去重
	children_ids = arrayutil.Distinct(children_ids)

	return children_ids, nil
}

func (r *SysOrganizationRepo) getSysUserInfoMap(ctx context.Context, results []*ent.SysUser) (map[string]string, error) {
	userIdInfo := map[string]string{}
	for _, m := range results {
		if m.CreatedBy != "" {
			userIdInfo[m.CreatedBy] = ""
		}
		if m.UpdatedBy != nil && *m.UpdatedBy != "" {
			userIdInfo[*m.UpdatedBy] = ""
		}
	}

	// 获取所有用户id
	userIds := make([]string, 0, len(userIdInfo))
	for k := range userIdInfo {
		userIds = append(userIds, k)
	}

	// 获取用户信息
	userInfo, err := r.getEntClient().SysUser.
		Query().
		Where(sysuser.IDIn(userIds...)).
		Select(sysuser.FieldUsername, sysuser.FieldNickname).
		All(ctx)

	if err != nil {
		r.log.Errorf("query user info failed: %s", err.Error())
		return nil, err
	}

	for _, u := range userInfo {
		if u.Nickname != "" {
			userIdInfo[u.ID] = u.Nickname
		} else {
			userIdInfo[u.ID] = u.Username
		}
	}

	return userIdInfo, nil
}

func (r *SysOrganizationRepo) convertEntSysUserToProto(in *ent.SysUser) *v1.SysUser {
	if in == nil {
		return nil
	}
	return &v1.SysUser{
		Id:        in.ID,
		CreatedAt: trans.TimeToStrPtr(in.CreatedAt),
		UpdatedAt: trans.TimeToStrPtr(in.UpdatedAt),
		CreatedBy: trans.String(in.CreatedBy),
		UpdatedBy: in.UpdatedBy,
		Username:  trans.String(in.Username),
		Password:  trans.String(in.Password),
	}
}

func (r *SysOrganizationRepo) convertEntSysUserListToProto(
	userMap map[string]string,
	results []*ent.SysUser,
	userOrgMap map[string]string,
) ([]*v1.SysUser, error) {
	items := make([]*v1.SysUser, 0, len(results))
	for _, m := range results {
		item := r.convertEntSysUserToProto(m)
		item.OrgUserId = trans.String(userOrgMap[m.ID])
		if stringutil.IsNotEmpty(item.CreatedBy) {
			item.CreatedBy = trans.String(userMap[*item.CreatedBy])
		}
		if stringutil.IsNotEmpty(item.UpdatedBy) {
			item.UpdatedBy = trans.String(userMap[*item.UpdatedBy])
		}
		items = append(items, item)
	}
	return items, nil
}

func (r *SysOrganizationRepo) ListSysOrganizationUser(ctx context.Context, req *v1.ListSysOrganizationUserRequest) (*v1.ListSysUserResponse, error) {
	userOrgs, err := r.getEntClient().SysUserOrganization.
		Query().
		Where(sysuserorganization.OrganizationID(req.GetId())).
		All(ctx)
	if err != nil {
		return nil, err
	}
	var userIDs []string
	var userOrgMap = make(map[string]string)
	for _, u := range userOrgs {
		userIDs = append(userIDs, u.UserID)
		userOrgMap[u.UserID] = u.ID
	}
	builder := r.getEntClient().SysUser.Query().
		Where(sysuser.IDIn(userIDs...))
	err, whereSelectors, querySelectors := entgo.BuildQuerySelector(
		req.GetQuery(), req.GetOrQuery(),
		req.GetPage(), req.GetPageSize(), req.GetNoPaging(),
		req.GetOrderBy(), sysuser.FieldCreatedAt,
		req.GetFieldMask().GetPaths(),
	)
	if err != nil {
		r.log.Errorf("build query select faild: %s", err.Error())
		return nil, err
	}

	if querySelectors != nil {
		builder.Modify(querySelectors...)
	}

	results, err := builder.All(ctx)
	if err != nil {
		return nil, err
	}

	userMap, err := r.getSysUserInfoMap(ctx, results)
	if err != nil {
		return nil, err
	}
	// 转换成proto
	items, err := r.convertEntSysUserListToProto(userMap, results, userOrgMap)
	if err != nil {
		return nil, err
	}
	countBuilder := r.getEntClient().SysUser.Query().
		Where(sysuser.IDIn(userIDs...))
	if querySelectors != nil {
		countBuilder.Modify(whereSelectors...)
	}
	count, err := countBuilder.Count(ctx)
	if err != nil {
		return nil, err
	}

	ret := v1.ListSysUserResponse{
		Total: int32(count),
		Items: items,
	}
	return &ret, err
}

func (r *SysOrganizationRepo) GetChildrenIds(ctx context.Context, ids []string) ([]string, error) {
	if len(ids) == 0 {
		return nil, nil
	}

	items, err := r.getEntClient().SysOrganization.Query().
		Select(sysorganization.FieldID, sysorganization.FieldParentID).
		All(ctx)

	if err != nil {
		return nil, err
	}

	// 递归获取所有子节点
	children_ids, err := r.getChildren(ctx, ids, items, []string{})

	if err != nil {
		return nil, err
	}

	// 聚合并去重
	children_ids = append(ids, children_ids...)
	children_ids = arrayutil.Distinct(children_ids)

	return children_ids, nil
}

func (r *SysOrganizationRepo) GetParentIds(ctx context.Context, ids []string) ([]string, error) {
	if len(ids) == 0 {
		return nil, nil
	}

	items, err := r.getEntClient().SysOrganization.Query().
		Select(sysorganization.FieldID, sysorganization.FieldParentID).
		All(ctx)

	if err != nil {
		return nil, err
	}

	// 先保存所有父节点关系在map中
	parentsMap := make(map[string]string)
	for _, m := range items {
		if stringutil.IsNotEmpty(m.ParentID) {
			parentsMap[m.ID] = *m.ParentID
		}
	}

	// 循环获取所有的父节点
	var parents []string
	for _, id := range ids {
		loop_count := 0
		for {
			// 如果循环次数超过100次，则跳出
			if loop_count > 100 {
				break
			}

			parent, exist := parentsMap[id]
			if !exist {
				break
			}
			parents = append(parents, parent)
			id = parent
			loop_count++
		}
		parents = append(parents, id)
	}

	// 去重
	parents = arrayutil.Distinct(parents)

	return parents, nil

}

// 获取组织数据根据ids
func (r *SysOrganizationRepo) GetOrganizationsByIds(ctx context.Context, ids []string) ([]*v1.SysOrganization, error) {
	results, err := r.getEntClient().Debug().SysOrganization.Query().
		Where(sysorganization.IDIn(ids...)).
		All(ctx)
	if err != nil {
		return nil, err
	}

	// 获取用户信息
	userMap, err := r.getUserInfoMap(ctx, results)
	if err != nil {
		return nil, err
	}

	// 转换成proto
	items, err := r.convertEntListToProto(ctx, true, userMap, results)
	if err != nil {
		return nil, err
	}

	return items, nil
}
