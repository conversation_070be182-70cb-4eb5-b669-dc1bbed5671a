// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/registry"
	"kratos-mono-demo/gen/config/conf/v1"
	biz5 "kratos-mono-demo/internal/app/approve/biz"
	data7 "kratos-mono-demo/internal/app/approve/data"
	service5 "kratos-mono-demo/internal/app/approve/service"
	"kratos-mono-demo/internal/app/approve/workflows/temporal_client"
	biz2 "kratos-mono-demo/internal/app/auth/biz"
	data4 "kratos-mono-demo/internal/app/auth/data"
	service2 "kratos-mono-demo/internal/app/auth/service"
	service8 "kratos-mono-demo/internal/app/cron/service"
	biz4 "kratos-mono-demo/internal/app/demo/biz"
	data5 "kratos-mono-demo/internal/app/demo/data"
	service4 "kratos-mono-demo/internal/app/demo/service"
	biz7 "kratos-mono-demo/internal/app/message/biz"
	data8 "kratos-mono-demo/internal/app/message/data"
	service7 "kratos-mono-demo/internal/app/message/service"
	biz3 "kratos-mono-demo/internal/app/system/biz"
	data3 "kratos-mono-demo/internal/app/system/data"
	service3 "kratos-mono-demo/internal/app/system/service"
	"kratos-mono-demo/internal/app/user/biz"
	data2 "kratos-mono-demo/internal/app/user/data"
	"kratos-mono-demo/internal/app/user/service"
	biz6 "kratos-mono-demo/internal/app/wms/biz"
	"kratos-mono-demo/internal/app/wms/biz/event_manager"
	data6 "kratos-mono-demo/internal/app/wms/data"
	service6 "kratos-mono-demo/internal/app/wms/service"
	biz8 "kratos-mono-demo/internal/app/workwx/biz"
	data9 "kratos-mono-demo/internal/app/workwx/data"
	service9 "kratos-mono-demo/internal/app/workwx/service"
	"kratos-mono-demo/internal/data"
	"kratos-mono-demo/internal/pkg/bootstrap/gen/bootstrap_conf/v1"
	"kratos-mono-demo/internal/pkg/workwechat"
	"kratos-mono-demo/internal/server"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(logger log.Logger, registrar registry.Registrar, bootstrap *bootstrap_confv1.Bootstrap, config *confv1.Config) (*kratos.App, func(), error) {
	client := data.NewRedisClient(bootstrap)
	entClient := data.NewEntClient(bootstrap, logger)
	db := data.NewGormClient(bootstrap, logger)
	dataData, cleanup := data.NewData(logger, bootstrap, config, client, entClient, db)
	workwxApp := workwechat.NewWorkWechat(config)
	userRepo := data2.NewUserRepo(dataData, logger, workwxApp)
	sysUserRepo := data3.NewSysUserRepo(config, logger, dataData, workwxApp)
	userUsecase := biz.NewUserUsecase(config, userRepo, sysUserRepo, logger)
	userService := service.NewUserService(userUsecase, config)
	authRepo := data4.NewAuthRepo(dataData, config, logger)
	authUsecase := biz2.NewAuthUsecase(authRepo, logger)
	authService := service2.NewAuthService(authUsecase)
	sysApplicationRepo := data3.NewSysApplicationRepo(config, logger, dataData)
	sysResourceRepo := data3.NewSysResourceRepo(config, logger, dataData)
	sysApplicationUsecase := biz3.NewSysApplicationUsecase(sysApplicationRepo, sysResourceRepo, sysUserRepo, logger, config)
	sysApplicationService := service3.NewSysApplicationService(sysApplicationUsecase)
	sysModuleRepo := data3.NewSysModuleRepo(config, logger, dataData)
	sysModuleUsecase := biz3.NewSysModuleUsecase(sysModuleRepo, sysResourceRepo, logger)
	sysModuleService := service3.NewSysModuleService(sysModuleUsecase)
	sysOrganizationRepo := data3.NewSysOrganizationRepo(config, logger, dataData)
	sysOrganizationUsecase := biz3.NewSysOrganizationUsecase(sysOrganizationRepo, logger)
	sysOrganizationService := service3.NewSysOrganizationService(sysOrganizationUsecase)
	sysProjectRepo := data3.NewSysProjectRepo(config, logger, dataData)
	sysProjectUsecase := biz3.NewSysProjectUsecase(sysProjectRepo, logger)
	sysProjectService := service3.NewSysProjectService(sysProjectUsecase)
	sysMenuRepo := data3.NewSysMenuRepo(config, logger, dataData)
	sysRoleRepo := data3.NewSysRoleRepo(config, logger, dataData)
	sysResourceUsecase := biz3.NewSysResourceUsecase(sysResourceRepo, sysApplicationRepo, sysMenuRepo, sysRoleRepo, logger)
	sysResourceService := service3.NewSysResourceService(sysResourceUsecase, authUsecase)
	sysRoleUsecase := biz3.NewSysRoleUsecase(sysRoleRepo, sysMenuRepo, sysResourceRepo, logger)
	sysRoleService := service3.NewSysRoleService(sysRoleUsecase, authUsecase)
	sysTeamRepo := data3.NewSysTeamRepo(config, logger, dataData)
	sysTeamUsecase := biz3.NewSysTeamUsecase(sysTeamRepo, sysApplicationRepo, logger)
	sysTeamService := service3.NewSysTeamService(sysTeamUsecase)
	sysUserLogRepo := data3.NewSysUserLogRepo(config, logger, dataData)
	sysUserUsecase := biz3.NewSysUserUsecase(sysUserRepo, logger, config, sysUserLogRepo)
	sysUserService := service3.NewSysUserService(sysUserUsecase, config)
	sysUserAuthRepo := data3.NewSysUserAuthRepo(config, logger, dataData)
	sysUserAuthUsecase := biz3.NewSysUserAuthUsecase(sysUserAuthRepo, logger)
	sysUserAuthService := service3.NewSysUserAuthService(sysUserAuthUsecase)
	sysUserOrganizationRepo := data3.NewSysUserOrganizationRepo(config, logger, dataData)
	sysUserOrganizationUsecase := biz3.NewSysUserOrganizationUsecase(sysUserOrganizationRepo, logger)
	sysUserOrganizationService := service3.NewSysUserOrganizationService(sysUserOrganizationUsecase)
	sysUserRoleRepo := data3.NewSysUserRoleRepo(config, logger, dataData)
	sysUserRoleUsecase := biz3.NewSysUserRoleUsecase(sysUserRoleRepo, logger)
	sysUserRoleService := service3.NewSysUserRoleService(sysUserRoleUsecase)
	demoBuildingRepo := data5.NewDemoBuildingRepo(config, logger, dataData)
	demoBuildingUsecase := biz4.NewDemoBuildingUsecase(demoBuildingRepo, logger)
	demoBuildingService := service4.NewDemoBuildingService(demoBuildingUsecase)
	sysDictionaryRepo := data3.NewSysDictionaryRepo(config, logger, dataData)
	sysDictionaryUsecase := biz3.NewSysDictionaryUsecase(sysDictionaryRepo, logger)
	sysDictionaryService := service3.NewSysDictionaryService(sysDictionaryUsecase)
	sysDictionaryDetailRepo := data3.NewSysDictionaryDetailRepo(config, logger, dataData)
	sysDictionaryDetailUsecase := biz3.NewSysDictionaryDetailUsecase(sysDictionaryDetailRepo, logger)
	sysDictionaryDetailService := service3.NewSysDictionaryDetailService(sysDictionaryDetailUsecase)
	sysMenuUsecase := biz3.NewSysMenuUsecase(sysMenuRepo, logger)
	sysMenuService := service3.NewSysMenuService(sysMenuUsecase, authUsecase)
	sysLabelRepo := data3.NewSysLabelRepo(config, logger, dataData)
	sysLabelUsecase := biz3.NewSysLabelUsecase(sysLabelRepo, logger)
	sysLabelService := service3.NewSysLabelService(sysLabelUsecase)
	sysProvinceRepo := data3.NewSysProvinceRepo(config, logger, dataData)
	sysProvinceUsecase := biz3.NewSysProvinceUsecase(sysProvinceRepo, logger)
	sysProvinceService := service3.NewSysProvinceService(sysProvinceUsecase)
	sysCityRepo := data3.NewSysCityRepo(config, logger, dataData)
	sysCityUsecase := biz3.NewSysCityUsecase(sysCityRepo, logger)
	sysCityService := service3.NewSysCityService(sysCityUsecase)
	sysAreaRepo := data3.NewSysAreaRepo(config, logger, dataData)
	sysAreaUsecase := biz3.NewSysAreaUsecase(sysAreaRepo, logger)
	sysAreaService := service3.NewSysAreaService(sysAreaUsecase)
	sysStreetRepo := data3.NewSysStreetRepo(config, logger, dataData)
	sysStreetUsecase := biz3.NewSysStreetUsecase(sysStreetRepo, logger)
	sysStreetService := service3.NewSysStreetService(sysStreetUsecase)
	sysUploadRepo := data3.NewSysUploadRepo(config, logger, dataData)
	sysUploadUsecase := biz3.NewSysUploadUsecase(sysUploadRepo, config, logger)
	sysUploadService := service3.NewSysUploadService(sysUploadUsecase)
	sysPageCodeRepo := data3.NewSysPageCodeRepo(config, logger, dataData)
	sysPageCodeUsecase := biz3.NewSysPageCodeUsecase(sysPageCodeRepo, logger)
	sysPageCodeService := service3.NewSysPageCodeService(sysPageCodeUsecase)
	transaction := data.NewTransaction(dataData)
	sysUserLogUsecase := biz3.NewSysUserLogUsecase(sysUserLogRepo, transaction, logger)
	sysUserLogService := service3.NewSysUserLogService(sysUserLogUsecase)
	sysLogRepo := data3.NewSysLogRepo(config, logger, dataData)
	sysLogUsecase := biz3.NewSysLogUsecase(sysLogRepo, transaction, logger)
	sysLogService := service3.NewSysLogService(sysLogUsecase)
	wmsApprovalTaskRepo := data6.NewWmsApprovalTaskRepo(config, logger, dataData)
	wmsOperateLogRepo := data6.NewWmsOperateLogRepo(config, logger, dataData)
	wmsApprovalTaskDetailRepo := data6.NewWmsApprovalTaskDetailRepo(config, logger, dataData)
	temporalClient := temporal_client.NewTemporalClient(bootstrap, dataData, config, logger)
	appApproveRecordRepo := data7.NewAppApproveRecordRepo(config, logger, dataData, temporalClient)
	appApproveRecordUsecase := biz5.NewAppApproveRecordUsecase(appApproveRecordRepo, transaction, logger)
	appApproveRecordService := service5.NewAppApproveRecordService(appApproveRecordUsecase)
	approveRepo := data6.NewApproveRepo(config, logger, dataData, appApproveRecordService)
	wmsDocumentRepo := data6.NewWmsDocumentRepo(config, logger, dataData)
	wmsApprovalTaskRemarkRepo := data6.NewWmsApprovalTaskRemarkRepo(config, logger, dataData)
	wmsMaterialRepo := data6.NewWmsMaterialRepo(config, logger, dataData)
	wmsEquipmentRepo := data6.NewWmsEquipmentRepo(config, logger, dataData)
	eventManager := event_manager.NewEventManager()
	wmsApprovalTaskUsecase := biz6.NewWmsApprovalTaskUsecase(wmsApprovalTaskRepo, wmsOperateLogRepo, wmsApprovalTaskDetailRepo, approveRepo, wmsDocumentRepo, wmsApprovalTaskRemarkRepo, wmsMaterialRepo, transaction, logger, wmsEquipmentRepo, eventManager)
	wmsApprovalTaskService := service6.NewWmsApprovalTaskService(wmsApprovalTaskUsecase)
	wmsApprovalTaskDetailUsecase := biz6.NewWmsApprovalTaskDetailUsecase(wmsApprovalTaskDetailRepo, transaction, logger, wmsEquipmentRepo, wmsMaterialRepo)
	wmsApprovalTaskDetailService := service6.NewWmsApprovalTaskDetailService(wmsApprovalTaskDetailUsecase)
	wmsApprovalTaskRemarkUsecase := biz6.NewWmsApprovalTaskRemarkUsecase(wmsApprovalTaskRemarkRepo, transaction, logger)
	wmsApprovalTaskRemarkService := service6.NewWmsApprovalTaskRemarkService(wmsApprovalTaskRemarkUsecase)
	wmsCompanyRepo := data6.NewWmsCompanyRepo(config, logger, dataData)
	wmsCompanyUsecase := biz6.NewWmsCompanyUsecase(wmsCompanyRepo, transaction, logger)
	wmsCompanyService := service6.NewWmsCompanyService(wmsCompanyUsecase)
	wmsCompanyAddressRepo := data6.NewWmsCompanyAddressRepo(config, logger, dataData)
	wmsCompanyAddressUsecase := biz6.NewWmsCompanyAddressUsecase(wmsCompanyAddressRepo, logger)
	wmsCompanyAddressService := service6.NewWmsCompanyAddressService(wmsCompanyAddressUsecase)
	wmsEquipmentTypeRepo := data6.NewWmsEquipmentTypeRepo(config, logger, dataData)
	wmsEquipmentDetailRepo := data6.NewWmsEquipmentDetailRepo(config, logger, dataData)
	wmsRepositoryRepo := data6.NewWmsRepositoryRepo(config, logger, dataData)
	wmsEquipmentTypePropertyRepo := data6.NewWmsEquipmentTypePropertyRepo(config, logger, dataData)
	wmsMeasureUnitRepo := data6.NewWmsMeasureUnitRepo(config, logger, dataData)
	wmsBorrowOrderRepo := data6.NewWmsBorrowOrderRepo(config, logger, dataData)
	wmsBorrowOrderDetailRepo := data6.NewWmsBorrowOrderDetailRepo(config, logger, dataData)
	wmsClaimOrderRepo := data6.NewWmsClaimOrderRepo(config, logger, dataData)
	wmsClaimOrderDetailRepo := data6.NewWmsClaimOrderDetailRepo(config, logger, dataData)
	wmsTransferOrderRepo := data6.NewWmsTransferOrderRepo(config, logger, dataData)
	wmsTransferOrderDetailRepo := data6.NewWmsTransferOrderDetailRepo(config, logger, dataData)
	wmsRepositoryPositionRepo := data6.NewWmsRepositoryPositionRepo(config, logger, dataData)
	wmsEquipmentUsecase := biz6.NewWmsEquipmentUsecase(wmsEquipmentRepo, wmsEquipmentTypeRepo, wmsEquipmentDetailRepo, sysUserOrganizationRepo, sysOrganizationRepo, transaction, logger, wmsRepositoryRepo, sysPageCodeRepo, wmsEquipmentTypePropertyRepo, wmsMaterialRepo, wmsMeasureUnitRepo, wmsBorrowOrderRepo, wmsBorrowOrderDetailRepo, wmsClaimOrderRepo, wmsClaimOrderDetailRepo, wmsTransferOrderRepo, wmsTransferOrderDetailRepo, wmsRepositoryPositionRepo)
	wmsEquipmentService := service6.NewWmsEquipmentService(wmsEquipmentUsecase)
	wmsEquipmentDetailUsecase := biz6.NewWmsEquipmentDetailUsecase(wmsEquipmentDetailRepo, logger)
	wmsEquipmentDetailService := service6.NewWmsEquipmentDetailService(wmsEquipmentDetailUsecase)
	wmsEquipmentTypeUsecase := biz6.NewWmsEquipmentTypeUsecase(wmsEquipmentTypeRepo, sysPageCodeRepo, logger)
	wmsEquipmentTypeService := service6.NewWmsEquipmentTypeService(wmsEquipmentTypeUsecase)
	wmsEquipmentTypePropertyUsecase := biz6.NewWmsEquipmentTypePropertyUsecase(wmsEquipmentTypePropertyRepo, logger)
	wmsEquipmentTypePropertyService := service6.NewWmsEquipmentTypePropertyService(wmsEquipmentTypePropertyUsecase)
	wmsEquipmentTypePropertyGroupRepo := data6.NewWmsEquipmentTypePropertyGroupRepo(config, logger, dataData)
	wmsEquipmentTypePropertyGroupUsecase := biz6.NewWmsEquipmentTypePropertyGroupUsecase(wmsEquipmentTypePropertyGroupRepo, logger)
	wmsEquipmentTypePropertyGroupService := service6.NewWmsEquipmentTypePropertyGroupService(wmsEquipmentTypePropertyGroupUsecase)
	wmsEquipmentUserGroupRepo := data6.NewWmsEquipmentUserGroupRepo(config, logger, dataData)
	wmsEquipmentUserGroupUsecase := biz6.NewWmsEquipmentUserGroupUsecase(wmsEquipmentUserGroupRepo, logger)
	wmsEquipmentUserGroupService := service6.NewWmsEquipmentUserGroupService(wmsEquipmentUserGroupUsecase)
	wmsLearningPlanRepo := data6.NewWmsLearningPlanRepo(config, logger, dataData)
	wmsLearningPlanRecordRepo := data6.NewWmsLearningPlanRecordRepo(config, logger, dataData)
	wmsLearningCoursewareRecordRepo := data6.NewWmsLearningCoursewareRecordRepo(config, logger, dataData)
	wmsLearningCourseRecordRepo := data6.NewWmsLearningCourseRecordRepo(config, logger, dataData)
	wmsLearningCourseRepo := data6.NewWmsLearningCourseRepo(config, logger, dataData)
	wmsLearningPlanUsecase := biz6.NewWmsLearningPlanUsecase(wmsLearningPlanRepo, sysUserOrganizationRepo, wmsLearningPlanRecordRepo, wmsLearningCoursewareRecordRepo, wmsLearningCourseRecordRepo, wmsLearningCourseRepo, sysUserRepo, transaction, logger, sysOrganizationRepo)
	wmsLearningPlanService := service6.NewWmsLearningPlanService(wmsLearningPlanUsecase)
	wmsLearningCourseUsecase := biz6.NewWmsLearningCourseUsecase(wmsLearningCourseRepo, wmsLearningPlanRepo, sysUserOrganizationRepo, sysUserRepo, logger)
	wmsLearningCourseService := service6.NewWmsLearningCourseService(wmsLearningCourseUsecase)
	wmsLearningCourseLogRepo := data6.NewWmsLearningCourseLogRepo(config, logger, dataData)
	wmsLearningCourseLogUsecase := biz6.NewWmsLearningCourseLogUsecase(wmsLearningCourseLogRepo, transaction, logger)
	wmsLearningCourseLogService := service6.NewWmsLearningCourseLogService(wmsLearningCourseLogUsecase)
	wmsLearningCoursewareRepo := data6.NewWmsLearningCoursewareRepo(config, logger, dataData)
	wmsLearningCoursewareUsecase := biz6.NewWmsLearningCoursewareUsecase(wmsLearningCoursewareRepo, wmsLearningCourseRepo, logger)
	wmsLearningCoursewareService := service6.NewWmsLearningCoursewareService(wmsLearningCoursewareUsecase)
	wmsLearningCourseCoursewareRepo := data6.NewWmsLearningCourseCoursewareRepo(config, logger, dataData)
	wmsLearningCourseCoursewareUsecase := biz6.NewWmsLearningCourseCoursewareUsecase(wmsLearningCourseCoursewareRepo, wmsLearningCourseLogRepo, transaction, logger)
	wmsLearningCourseCoursewareService := service6.NewWmsLearningCourseCoursewareService(wmsLearningCourseCoursewareUsecase)
	wmsRepositoryUsecase := biz6.NewWmsRepositoryUsecase(wmsRepositoryRepo, logger, sysUserOrganizationRepo, sysOrganizationRepo)
	wmsRepositoryService := service6.NewWmsRepositoryService(wmsRepositoryUsecase)
	wmsRepositoryAdminRepo := data6.NewWmsRepositoryAdminRepo(config, logger, dataData)
	wmsRepositoryAdminUsecase := biz6.NewWmsRepositoryAdminUsecase(wmsRepositoryAdminRepo, logger)
	wmsRepositoryAdminService := service6.NewWmsRepositoryAdminService(wmsRepositoryAdminUsecase)
	wmsRepositoryAreaRepo := data6.NewWmsRepositoryAreaRepo(config, logger, dataData)
	wmsRepositoryAreaUsecase := biz6.NewWmsRepositoryAreaUsecase(wmsRepositoryAreaRepo, wmsRepositoryRepo, logger, wmsMaterialRepo)
	wmsRepositoryAreaService := service6.NewWmsRepositoryAreaService(wmsRepositoryAreaUsecase)
	wmsRepositoryPositionUsecase := biz6.NewWmsRepositoryPositionUsecase(wmsRepositoryPositionRepo, wmsRepositoryAreaRepo, logger, wmsMaterialRepo)
	wmsRepositoryPositionService := service6.NewWmsRepositoryPositionService(wmsRepositoryPositionUsecase)
	wmsMeasureUnitUsecase := biz6.NewWmsMeasureUnitUsecase(wmsMeasureUnitRepo, logger)
	wmsMeasureUnitService := service6.NewWmsMeasureUnitService(wmsMeasureUnitUsecase)
	wmsFireStationRepo := data6.NewWmsFireStationRepo(config, logger, dataData)
	wmsFireStationUsecase := biz6.NewWmsFireStationUsecase(wmsFireStationRepo, logger)
	wmsFireStationService := service6.NewWmsFireStationService(wmsFireStationUsecase)
	wmsDiscardMeetingRepo := data6.NewWmsDiscardMeetingRepo(config, logger, dataData)
	wmsDiscardMeetingDetailRepo := data6.NewWmsDiscardMeetingDetailRepo(config, logger, dataData)
	wmsMaterialLogRepo := data6.NewWmsMaterialLogRepo(config, logger, dataData)
	wmsDiscardMeetingUsecase := biz6.NewWmsDiscardMeetingUsecase(wmsDiscardMeetingRepo, transaction, logger, wmsOperateLogRepo, wmsDocumentRepo, wmsMaterialRepo, wmsDiscardMeetingDetailRepo, eventManager, wmsMaterialLogRepo)
	wmsDiscardMeetingService := service6.NewWmsDiscardMeetingService(wmsDiscardMeetingUsecase)
	wmsDiscardMeetingDetailUsecase := biz6.NewWmsDiscardMeetingDetailUsecase(wmsDiscardMeetingDetailRepo, transaction, logger, wmsMaterialRepo, wmsDiscardMeetingRepo, wmsEquipmentRepo)
	wmsDiscardMeetingDetailService := service6.NewWmsDiscardMeetingDetailService(wmsDiscardMeetingDetailUsecase)
	wmsDiscardPlanOrderRepo := data6.NewWmsDiscardPlanOrderRepo(config, logger, dataData)
	wmsDiscardPlanOrderDetailRepo := data6.NewWmsDiscardPlanOrderDetailRepo(config, logger, dataData)
	wmsDiscardPlanOrderUsecase := biz6.NewWmsDiscardPlanOrderUsecase(wmsDiscardPlanOrderRepo, logger, transaction, wmsOperateLogRepo, wmsDocumentRepo, wmsMaterialRepo, wmsDiscardPlanOrderDetailRepo)
	wmsDiscardPlanOrderService := service6.NewWmsDiscardPlanOrderService(wmsDiscardPlanOrderUsecase)
	wmsDiscardPlanOrderDetailUsecase := biz6.NewWmsDiscardPlanOrderDetailUsecase(wmsDiscardPlanOrderDetailRepo, transaction, logger, wmsMaterialRepo, wmsDiscardPlanOrderRepo, wmsRepositoryRepo, wmsRepositoryAreaRepo, wmsRepositoryPositionRepo, wmsEquipmentRepo)
	wmsDiscardPlanOrderDetailService := service6.NewWmsDiscardPlanOrderDetailService(wmsDiscardPlanOrderDetailUsecase)
	wmsDiscardOrderRepo := data6.NewWmsDiscardOrderRepo(config, logger, dataData)
	wmsDiscardOrderDetailRepo := data6.NewWmsDiscardOrderDetailRepo(config, logger, dataData)
	wmsDiscardOrderUsecase := biz6.NewWmsDiscardOrderUsecase(wmsDiscardOrderRepo, transaction, logger, wmsDiscardOrderDetailRepo, wmsDocumentRepo, wmsOperateLogRepo, wmsEquipmentRepo, wmsMaterialRepo)
	wmsDiscardOrderService := service6.NewWmsDiscardOrderService(wmsDiscardOrderUsecase)
	wmsDiscardOrderDetailUsecase := biz6.NewWmsDiscardOrderDetailUsecase(wmsDiscardOrderDetailRepo, transaction, logger)
	wmsDiscardOrderDetailService := service6.NewWmsDiscardOrderDetailService(wmsDiscardOrderDetailUsecase)
	wmsDocumentUsecase := biz6.NewWmsDocumentUsecase(wmsDocumentRepo, logger)
	wmsDocumentService := service6.NewWmsDocumentService(wmsDocumentUsecase)
	wmsEnterRepositoryOrderRepo := data6.NewWmsEnterRepositoryOrderRepo(config, logger, dataData)
	wmsEnterRepositoryOrderDetailRepo := data6.NewWmsEnterRepositoryOrderDetailRepo(config, logger, dataData)
	wmsPurchaseOrderRepo := data6.NewWmsPurchaseOrderRepo(config, logger, dataData)
	wmsPurchaseOrderDetailRepo := data6.NewWmsPurchaseOrderDetailRepo(config, logger, dataData)
	wmsReturnOrderRepo := data6.NewWmsReturnOrderRepo(config, logger, dataData)
	wmsEnterRepositoryOrderUsecase := biz6.NewWmsEnterRepositoryOrderUsecase(eventManager, wmsEnterRepositoryOrderRepo, wmsOperateLogRepo, wmsDocumentRepo, wmsMaterialRepo, wmsEnterRepositoryOrderDetailRepo, wmsPurchaseOrderRepo, wmsPurchaseOrderDetailRepo, wmsEquipmentRepo, logger, transaction, wmsMaterialLogRepo, wmsTransferOrderRepo, wmsReturnOrderRepo)
	wmsEnterRepositoryOrderService := service6.NewWmsEnterRepositoryOrderService(wmsEnterRepositoryOrderUsecase)
	wmsEnterRepositoryOrderDetailUsecase := biz6.NewWmsEnterRepositoryOrderDetailUsecase(wmsEnterRepositoryOrderDetailRepo, wmsMaterialRepo, wmsEnterRepositoryOrderRepo, transaction, logger, wmsEquipmentRepo)
	wmsEnterRepositoryOrderDetailService := service6.NewWmsEnterRepositoryOrderDetailService(wmsEnterRepositoryOrderDetailUsecase)
	wmsPurchaseOrderUsecase := biz6.NewWmsPurchaseOrderUsecase(eventManager, wmsPurchaseOrderRepo, wmsDocumentRepo, wmsOperateLogRepo, wmsEnterRepositoryOrderRepo, wmsEnterRepositoryOrderDetailRepo, transaction, logger)
	wmsPurchaseOrderService := service6.NewWmsPurchaseOrderService(wmsPurchaseOrderUsecase)
	wmsPurchaseOrderDetailUsecase := biz6.NewWmsPurchaseOrderDetailUsecase(wmsPurchaseOrderDetailRepo, wmsPurchaseOrderRepo, transaction, logger, wmsEquipmentRepo, wmsEquipmentDetailRepo, wmsEquipmentTypePropertyRepo)
	wmsPurchaseOrderDetailService := service6.NewWmsPurchaseOrderDetailService(wmsPurchaseOrderDetailUsecase)
	wmsOutRepositoryOrderRepo := data6.NewWmsOutRepositoryOrderRepo(config, logger, dataData)
	wmsOutRepositoryOrderDetailRepo := data6.NewWmsOutRepositoryOrderDetailRepo(config, logger, dataData)
	sysMessageTemplateRepo := data3.NewSysMessageTemplateRepo(config, logger, dataData)
	wmsOutRepositoryOrderUsecase := biz6.NewWmsOutRepositoryOrderUsecase(eventManager, wmsOutRepositoryOrderRepo, transaction, logger, wmsOperateLogRepo, wmsDocumentRepo, wmsMaterialRepo, wmsOutRepositoryOrderDetailRepo, wmsMaterialLogRepo, sysUserAuthRepo, workwxApp, sysMessageTemplateRepo, wmsBorrowOrderRepo, wmsClaimOrderRepo, wmsTransferOrderRepo, wmsEnterRepositoryOrderRepo, wmsEnterRepositoryOrderDetailRepo)
	wmsOutRepositoryOrderService := service6.NewWmsOutRepositoryOrderService(wmsOutRepositoryOrderUsecase)
	wmsOutRepositoryOrderDetailUsecase := biz6.NewWmsOutRepositoryOrderDetailUsecase(wmsOutRepositoryOrderDetailRepo, transaction, logger, wmsOutRepositoryOrderRepo, wmsApprovalTaskRepo, wmsApprovalTaskDetailRepo, wmsMaterialRepo, wmsEquipmentRepo)
	wmsOutRepositoryOrderDetailService := service6.NewWmsOutRepositoryOrderDetailService(wmsOutRepositoryOrderDetailUsecase)
	wmsMaterialUsecase := biz6.NewWmsMaterialUsecase(wmsMaterialRepo, sysUserRepo, transaction, eventManager, logger, wmsEquipmentRepo, sysPageCodeRepo)
	wmsMaterialService := service6.NewWmsMaterialService(wmsMaterialUsecase)
	wmsOperateLogUsecase := biz6.NewWmsOperateLogUsecase(wmsOperateLogRepo, logger)
	wmsOperateLogService := service6.NewWmsOperateLogService(wmsOperateLogUsecase)
	wmsRepairOrderRepo := data6.NewWmsRepairOrderRepo(config, logger, dataData)
	wmsRepairOrderDetailRepo := data6.NewWmsRepairOrderDetailRepo(config, logger, dataData)
	wmsRepairOrderUsecase := biz6.NewWmsRepairOrderUsecase(wmsRepairOrderRepo, transaction, logger, wmsRepairOrderDetailRepo, wmsOperateLogRepo, wmsDocumentRepo, wmsEquipmentRepo, wmsMaterialRepo)
	wmsRepairOrderService := service6.NewWmsRepairOrderService(wmsRepairOrderUsecase)
	wmsRepairOrderDetailUsecase := biz6.NewWmsRepairOrderDetailUsecase(wmsRepairOrderDetailRepo, transaction, logger)
	wmsRepairOrderDetailService := service6.NewWmsRepairOrderDetailService(wmsRepairOrderDetailUsecase)
	wmsMaintainOrderRepo := data6.NewWmsMaintainOrderRepo(config, logger, dataData)
	wmsMaintainOrderDetailRepo := data6.NewWmsMaintainOrderDetailRepo(config, logger, dataData)
	wmsMaintainOrderUsecase := biz6.NewWmsMaintainOrderUsecase(wmsMaintainOrderRepo, transaction, logger, wmsMaintainOrderDetailRepo, wmsDocumentRepo, wmsOperateLogRepo, wmsEquipmentRepo, wmsMaterialRepo)
	wmsMaintainOrderService := service6.NewWmsMaintainOrderService(wmsMaintainOrderUsecase)
	wmsMaintainOrderDetailUsecase := biz6.NewWmsMaintainOrderDetailUsecase(wmsMaintainOrderDetailRepo, transaction, logger)
	wmsMaintainOrderDetailService := service6.NewWmsMaintainOrderDetailService(wmsMaintainOrderDetailUsecase)
	wmsApprovalTaskWorkflowRepo := data6.NewWmsApprovalTaskWorkflowRepo(config, transaction, wmsOperateLogRepo, wmsOutRepositoryOrderDetailRepo, wmsOutRepositoryOrderRepo, logger, dataData)
	wmsApprovalTaskWorkflowUsecase := biz6.NewWmsApprovalTaskWorkflowUsecase(wmsApprovalTaskWorkflowRepo, wmsOperateLogRepo, wmsApprovalTaskRepo, wmsMaterialRepo, wmsApprovalTaskDetailRepo, wmsOutRepositoryOrderRepo, wmsOutRepositoryOrderDetailRepo, wmsEnterRepositoryOrderRepo, wmsEquipmentRepo, wmsMaterialLogRepo, wmsEnterRepositoryOrderUsecase, wmsEnterRepositoryOrderDetailUsecase, eventManager, transaction, logger)
	wmsWorkflowService := service6.NewWmsWorkflowService(wmsApprovalTaskWorkflowUsecase, logger)
	wmsLearningCourseRecordUsecase := biz6.NewWmsLearningCourseRecordUsecase(wmsLearningCourseRecordRepo, transaction, logger)
	wmsLearningCourseRecordService := service6.NewWmsLearningCourseRecordService(wmsLearningCourseRecordUsecase)
	wmsMaintainPlanRepo := data6.NewWmsMaintainPlanRepo(config, logger, dataData)
	wmsMaintainPlanDetailRepo := data6.NewWmsMaintainPlanDetailRepo(config, logger, dataData)
	wmsMaintainPlanUsecase := biz6.NewWmsMaintainPlanUsecase(wmsMaintainPlanRepo, transaction, logger, wmsOperateLogRepo, wmsDocumentRepo, wmsMaterialRepo, wmsMaintainPlanDetailRepo)
	wmsMaintainPlanService := service6.NewWmsMaintainPlanService(wmsMaintainPlanUsecase)
	wmsMaintainPlanDetailUsecase := biz6.NewWmsMaintainPlanDetailUsecase(wmsMaintainPlanDetailRepo, transaction, logger, wmsMaterialRepo, wmsMaintainOrderRepo, wmsMaintainPlanRepo, wmsEquipmentRepo)
	wmsMaintainPlanDetailService := service6.NewWmsMaintainPlanDetailService(wmsMaintainPlanDetailUsecase)
	wmsRfidReaderRecordRepo := data6.NewWmsRfidReaderRecordRepo(config, logger, dataData)
	wmsRfidReaderRepo := data6.NewWmsRfidReaderRepo(config, logger, dataData)
	wmsRfidReaderRecordUsecase := biz6.NewWmsRfidReaderRecordUsecase(eventManager, wmsRfidReaderRecordRepo, wmsRfidReaderRepo, wmsMaterialRepo, transaction, logger)
	wmsRfidReaderRecordService := service6.NewWmsRfidReaderRecordService(wmsRfidReaderRecordUsecase)
	wmsRfidReaderUsecase := biz6.NewWmsRfidReaderUsecase(wmsRfidReaderRepo, transaction, logger, sysUserRepo)
	wmsRfidReaderService := service6.NewWmsRfidReaderService(wmsRfidReaderUsecase)
	wmsRfidRepo := data6.NewWmsRfidRepo(config, logger, dataData)
	wmsRfidUsecase := biz6.NewWmsRfidUsecase(wmsRfidRepo, transaction, logger)
	wmsRfidService := service6.NewWmsRfidService(wmsRfidUsecase)
	wmsAccessDoorLogRepo := data6.NewWmsAccessDoorLogRepo(config, logger, dataData)
	wmsAccessDoorUsecase := biz6.NewWmsAccessDoorUsecase(wmsAccessDoorLogRepo, transaction, logger, config, wmsRfidReaderRepo, wmsRfidReaderRecordRepo, wmsMaterialRepo)
	wmsAccessDoorService := service6.NewWmsAccessDoorService(wmsAccessDoorUsecase)
	wmsAuditPlanRepo := data6.NewWmsAuditPlanRepo(config, logger, dataData)
	wmsAuditPlanDetailRepo := data6.NewWmsAuditPlanDetailRepo(config, logger, dataData)
	wmsAuditPlanUsecase := biz6.NewWmsAuditPlanUsecase(wmsAuditPlanRepo, wmsMaterialRepo, sysUserOrganizationRepo, sysOrganizationRepo, wmsEquipmentTypeRepo, wmsAuditPlanDetailRepo, transaction, logger)
	wmsAuditPlanService := service6.NewWmsAuditPlanService(wmsAuditPlanUsecase)
	wmsAuditPlanDetailUsecase := biz6.NewWmsAuditPlanDetailUsecase(eventManager, wmsAuditPlanDetailRepo, wmsRfidReaderRecordRepo, transaction, logger, wmsEquipmentRepo)
	wmsAuditPlanDetailService := service6.NewWmsAuditPlanDetailService(wmsAuditPlanDetailUsecase)
	sysGameRepo := data3.NewSysGameRepo(config, logger, dataData)
	sysGameUsecase := biz3.NewSysGameUsecase(sysGameRepo, transaction, logger)
	sysGameService := service3.NewSysGameService(sysGameUsecase)
	wmsMaterialLogUsecase := biz6.NewWmsMaterialLogUsecase(wmsMaterialLogRepo, transaction, logger)
	wmsMaterialLogService := service6.NewWmsMaterialLogService(wmsMaterialLogUsecase)
	wmsCarRepo := data6.NewWmsCarRepo(config, logger, dataData)
	wmsGPSRepo := data6.NewWmsGPSRepo(config, logger, dataData)
	wmsGPSRecordRepo := data6.NewWmsGPSRecordRepo(config, logger, dataData)
	wmsCarUsecase := biz6.NewWmsCarUsecase(wmsCarRepo, wmsGPSRepo, wmsGPSRecordRepo, wmsMaterialRepo, transaction, logger, eventManager, wmsEnterRepositoryOrderDetailRepo, wmsEnterRepositoryOrderRepo, wmsEquipmentTypeRepo, wmsPurchaseOrderRepo)
	wmsCarService := service6.NewWmsCarService(wmsCarUsecase)
	wmsGPSUsecase := biz6.NewWmsGPSUsecase(wmsGPSRepo, transaction, logger)
	wmsGPSService := service6.NewWmsGPSService(wmsGPSUsecase)
	wmsGPSRecordUsecase := biz6.NewWmsGPSRecordUsecase(wmsGPSRecordRepo, transaction, logger)
	wmsGPSRecordService := service6.NewWmsGPSRecordService(wmsGPSRecordUsecase)
	wmsApprovalTaskOperationRepo := data6.NewWmsApprovalTaskOperationRepo(config, logger, dataData)
	wmsApprovalTaskOperationUsecase := biz6.NewWmsApprovalTaskOperationUsecase(wmsApprovalTaskOperationRepo, wmsApprovalTaskDetailRepo, transaction, logger, eventManager)
	wmsApprovalTaskOperationService := service6.NewWmsApprovalTaskOperationService(wmsApprovalTaskOperationUsecase)
	wmsBorrowOrderUsecase := biz6.NewWmsBorrowOrderUsecase(wmsBorrowOrderRepo, wmsBorrowOrderDetailRepo, transaction, logger, wmsDocumentRepo, wmsOperateLogRepo, wmsEquipmentRepo, wmsOutRepositoryOrderRepo, wmsOutRepositoryOrderDetailRepo)
	wmsBorrowOrderService := service6.NewWmsBorrowOrderService(wmsBorrowOrderUsecase)
	wmsBorrowOrderDetailUsecase := biz6.NewWmsBorrowOrderDetailUsecase(wmsBorrowOrderDetailRepo, transaction, logger)
	wmsBorrowOrderDetailService := service6.NewWmsBorrowOrderDetailService(wmsBorrowOrderDetailUsecase)
	wmsClaimOrderUsecase := biz6.NewWmsClaimOrderUsecase(wmsClaimOrderRepo, wmsClaimOrderDetailRepo, transaction, logger, wmsDocumentRepo, wmsOperateLogRepo, wmsEquipmentRepo, wmsOutRepositoryOrderRepo, wmsOutRepositoryOrderDetailRepo)
	wmsClaimOrderService := service6.NewWmsClaimOrderService(wmsClaimOrderUsecase)
	wmsClaimOrderDetailUsecase := biz6.NewWmsClaimOrderDetailUsecase(wmsClaimOrderDetailRepo, transaction, logger)
	wmsClaimOrderDetailService := service6.NewWmsClaimOrderDetailService(wmsClaimOrderDetailUsecase)
	wmsReturnOrderDetailRepo := data6.NewWmsReturnOrderDetailRepo(config, logger, dataData)
	wmsReturnOrderUsecase := biz6.NewWmsReturnOrderUsecase(wmsReturnOrderRepo, wmsReturnOrderDetailRepo, transaction, logger, wmsOperateLogRepo, wmsDocumentRepo, wmsEquipmentRepo, wmsEnterRepositoryOrderRepo, wmsEnterRepositoryOrderDetailRepo)
	wmsReturnOrderService := service6.NewWmsReturnOrderService(wmsReturnOrderUsecase)
	wmsReturnOrderDetailUsecase := biz6.NewWmsReturnOrderDetailUsecase(wmsReturnOrderDetailRepo, transaction, logger)
	wmsReturnOrderDetailService := service6.NewWmsReturnOrderDetailService(wmsReturnOrderDetailUsecase)
	wmsTransferOrderUsecase := biz6.NewWmsTransferOrderUsecase(wmsTransferOrderRepo, wmsTransferOrderDetailRepo, wmsOperateLogRepo, wmsDocumentRepo, wmsEquipmentRepo, wmsMaterialRepo, transaction, logger, wmsOutRepositoryOrderRepo, wmsOutRepositoryOrderDetailRepo, wmsEnterRepositoryOrderRepo, wmsEnterRepositoryOrderDetailRepo)
	wmsTransferOrderService := service6.NewWmsTransferOrderService(wmsTransferOrderUsecase)
	wmsTransferOrderDetailUsecase := biz6.NewWmsTransferOrderDetailUsecase(wmsTransferOrderDetailRepo, transaction, logger)
	wmsTransferOrderDetailService := service6.NewWmsTransferOrderDetailService(wmsTransferOrderDetailUsecase)
	appApproveBasicRepo := data7.NewAppApproveBasicRepo(config, logger, dataData)
	appApproveBasicUsecase := biz5.NewAppApproveBasicUsecase(appApproveBasicRepo, logger)
	appApproveBasicService := service5.NewAppApproveBasicService(appApproveBasicUsecase)
	appApproveGroupRepo := data7.NewAppApproveGroupRepo(config, logger, dataData)
	appApproveGroupUsecase := biz5.NewAppApproveGroupUsecase(appApproveGroupRepo, logger)
	appApproveGroupService := service5.NewAppApproveGroupService(appApproveGroupUsecase)
	appApproveWorkflowRepo := data7.NewAppApproveWorkflowRepo(config, logger, dataData)
	appApproveWorkflowUsecase := biz5.NewAppApproveWorkflowUsecase(appApproveWorkflowRepo, logger)
	appApproveWorkflowService := service5.NewAppApproveWorkflowService(appApproveWorkflowUsecase)
	appApproveActivityRepo := data7.NewAppApproveActivityRepo(config, logger, dataData)
	appApproveActivityUsecase := biz5.NewAppApproveActivityUsecase(appApproveActivityRepo, logger)
	appApproveActivityService := service5.NewAppApproveActivityService(appApproveActivityUsecase)
	messageRepo := data8.NewMessageRepo(config, dataData, logger)
	messageUsecase := biz7.NewMessageUsecase(messageRepo, logger)
	messageService := service7.NewMessageService(messageUsecase)
	wmsStatisticsUsecase := biz6.NewWmsStatisticsUsecase(transaction, logger, wmsMaterialRepo, wmsEquipmentTypeRepo, wmsRepositoryRepo, sysOrganizationRepo, wmsEnterRepositoryOrderRepo, wmsOutRepositoryOrderRepo, wmsDiscardMeetingRepo, wmsApprovalTaskRepo, wmsAuditPlanRepo, wmsDocumentRepo, sysUserRepo)
	wmsStatisticsService := service6.NewWmsStatisticsService(wmsStatisticsUsecase, logger)
	wmsDashboardRepositoryScreenUsecase := biz6.NewWmsDashboardRepositoryScreenUsecase(transaction, logger, wmsMaterialRepo, wmsEquipmentTypeRepo, wmsEquipmentRepo, wmsRepositoryRepo, wmsRepositoryAreaRepo, wmsRepositoryPositionRepo, wmsMeasureUnitRepo)
	wmsDashboardRepositoryScreenService := service6.NewWmsDashboardRepositoryScreen(wmsDashboardRepositoryScreenUsecase)
	wmsRepositoryScreenRepo := data6.NewWmsRepositoryScreenRepo(config, logger, dataData)
	wmsRepositoryScreenRepositoryAreaRepo := data6.NewWmsRepositoryScreenRepositoryAreaRepo(config, logger, dataData)
	wmsRepositoryScreenUsecase := biz6.NewWmsRepositoryScreenUsecase(wmsRepositoryScreenRepo, transaction, logger, wmsRepositoryScreenRepositoryAreaRepo, wmsRepositoryRepo, wmsRepositoryAreaRepo)
	wmsRepositoryScreenService := service6.NewWmsRepositoryScreenService(wmsRepositoryScreenUsecase)
	wmsRepositoryScreenRepositoryAreaUsecase := biz6.NewWmsRepositoryScreenRepositoryAreaUsecase(wmsRepositoryScreenRepositoryAreaRepo, transaction, logger)
	wmsRepositoryScreenRepositoryAreaService := service6.NewWmsRepositoryScreenRepositoryAreaService(wmsRepositoryScreenRepositoryAreaUsecase)
	wmsRepairSettlementOrderRepo := data6.NewWmsRepairSettlementOrderRepo(config, logger, dataData)
	wmsRepairSettlementOrderSettlementfeeDetailRepo := data6.NewWmsRepairSettlementOrderSettlementfeeDetailRepo(config, logger, dataData)
	wmsRepairSettlementOrderWorkfeeDetailRepo := data6.NewWmsRepairSettlementOrderWorkfeeDetailRepo(config, logger, dataData)
	wmsRepairSettlementOrderUsecase := biz6.NewWmsRepairSettlementOrderUsecase(wmsRepairSettlementOrderRepo, transaction, logger, wmsRepairSettlementOrderSettlementfeeDetailRepo, wmsRepairSettlementOrderWorkfeeDetailRepo)
	wmsRepairSettlementOrderService := service6.NewWmsRepairSettlementOrderService(wmsRepairSettlementOrderUsecase)
	wmsVehicleRepairOrderRepo := data6.NewWmsVehicleRepairOrderRepo(config, logger, dataData)
	wmsVehicleRepairOrderSettlementfeeDetailRepo := data6.NewWmsVehicleRepairOrderSettlementfeeDetailRepo(config, logger, dataData)
	wmsVehicleRepairOrderWorkfeeDetailRepo := data6.NewWmsVehicleRepairOrderWorkfeeDetailRepo(config, logger, dataData)
	wmsVehicleRepairOrderMaterialfeeDetailRepo := data6.NewWmsVehicleRepairOrderMaterialfeeDetailRepo(config, logger, dataData)
	wmsVehicleRepairOrderUsecase := biz6.NewWmsVehicleRepairOrderUsecase(wmsVehicleRepairOrderRepo, transaction, logger, wmsVehicleRepairOrderSettlementfeeDetailRepo, wmsVehicleRepairOrderWorkfeeDetailRepo, wmsVehicleRepairOrderMaterialfeeDetailRepo)
	wmsVehicleRepairOrderService := service6.NewWmsVehicleRepairOrderService(wmsVehicleRepairOrderUsecase)
	wmsShareDataRepo := data6.NewWmsShareDataRepo(config, logger, dataData)
	wmsShareDataUsecase := biz6.NewWmsShareDataUsecase(config, logger, transaction, wmsMaterialRepo, wmsShareDataRepo, wmsRepositoryRepo)
	cronService := service8.NewCronService(logger, config, wmsShareDataUsecase)
	workwxRepo := data9.NewWorkwxRepo(config, logger, dataData, workwxApp)
	workwxUsecase := biz8.NewWorkwxUsecase(workwxRepo, logger)
	workwxService := service9.NewWorkwxService(workwxUsecase)
	workwxApprovalRepo := data9.NewWorkwxApprovalRepo(config, logger, dataData)
	workwxApprovalUsecase := biz8.NewWorkwxApprovalUsecase(workwxApprovalRepo, logger)
	workwxApprovalService := service9.NewWorkwxApprovalService(workwxApprovalUsecase)
	httpServer := server.NewHTTPServer(bootstrap, logger, config, dataData, userService, authService, sysApplicationService, sysModuleService, sysOrganizationService, sysProjectService, sysResourceService, sysRoleService, sysTeamService, sysUserService, sysUserAuthService, sysUserOrganizationService, sysUserRoleService, demoBuildingService, sysDictionaryService, sysDictionaryDetailService, sysMenuService, sysLabelService, sysProvinceService, sysCityService, sysAreaService, sysStreetService, sysUploadService, sysPageCodeService, sysUserLogService, sysLogService, wmsApprovalTaskService, wmsApprovalTaskDetailService, wmsApprovalTaskRemarkService, wmsCompanyService, wmsCompanyAddressService, wmsEquipmentService, wmsEquipmentDetailService, wmsEquipmentTypeService, wmsEquipmentTypePropertyService, wmsEquipmentTypePropertyGroupService, wmsEquipmentUserGroupService, wmsLearningPlanService, wmsLearningCourseService, wmsLearningCourseLogService, wmsLearningCoursewareService, wmsLearningCourseCoursewareService, wmsRepositoryService, wmsRepositoryAdminService, wmsRepositoryAreaService, wmsRepositoryPositionService, wmsMeasureUnitService, wmsFireStationService, wmsDiscardMeetingService, wmsDiscardMeetingDetailService, wmsDiscardPlanOrderService, wmsDiscardPlanOrderDetailService, wmsDiscardOrderService, wmsDiscardOrderDetailService, wmsDocumentService, wmsEnterRepositoryOrderService, wmsEnterRepositoryOrderDetailService, wmsPurchaseOrderService, wmsPurchaseOrderDetailService, wmsOutRepositoryOrderService, wmsOutRepositoryOrderDetailService, wmsMaterialService, wmsOperateLogService, wmsRepairOrderService, wmsRepairOrderDetailService, wmsMaintainOrderService, wmsMaintainOrderDetailService, wmsWorkflowService, wmsLearningCourseRecordService, wmsMaintainPlanService, wmsMaintainPlanDetailService, wmsRfidReaderRecordService, wmsRfidReaderService, wmsRfidService, wmsAccessDoorService, wmsAuditPlanService, wmsAuditPlanDetailService, sysGameService, wmsMaterialLogService, wmsCarService, wmsGPSService, wmsGPSRecordService, wmsApprovalTaskOperationService, wmsBorrowOrderService, wmsBorrowOrderDetailService, wmsClaimOrderService, wmsClaimOrderDetailService, wmsReturnOrderService, wmsReturnOrderDetailService, wmsTransferOrderService, wmsTransferOrderDetailService, appApproveBasicService, appApproveGroupService, appApproveWorkflowService, appApproveActivityService, appApproveRecordService, messageService, wmsStatisticsService, wmsDashboardRepositoryScreenService, wmsRepositoryScreenService, wmsRepositoryScreenRepositoryAreaService, wmsRepairSettlementOrderService, wmsVehicleRepairOrderService, cronService, workwxService, workwxApprovalService)
	app := newApp(logger, registrar, httpServer)
	return app, func() {
		cleanup()
	}, nil
}
