syntax = "proto3";

package wms.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";
import "wms/v1/wms_claim_order_detail.proto";

service WmsClaimOrderService {
  // 获取领用单列表
  rpc ListWmsClaimOrder(ListWmsClaimOrderRequest) returns (ListWmsClaimOrderResponse) {
    option (google.api.http) = {get: "/wms/v1/wmsClaimOrder"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "领用"
      ]
    };
  }

  // 获取领用单详情
  rpc GetWmsClaimOrder(GetWmsClaimOrderRequest) returns (WmsClaimOrder) {
    option (google.api.http) = {get: "/wms/v1/wmsClaimOrder/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "领用"
      ]
    };
  }

  // 创建领用单
  rpc CreateWmsClaimOrder(CreateWmsClaimOrderRequest) returns (WmsClaimOrder) {
    option (google.api.http) = {
      post: "/wms/v1/wmsClaimOrder"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "领用"
      ]
    };
  }

  // 更新领用单
  rpc UpdateWmsClaimOrder(UpdateWmsClaimOrderRequest) returns (WmsClaimOrder) {
    option (google.api.http) = {
      put: "/wms/v1/wmsClaimOrder/{id}"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "领用"
      ]
    };
  }

  // 删除领用单
  rpc DeleteWmsClaimOrder(DeleteWmsClaimOrderRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/wms/v1/wmsClaimOrder/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "领用"
      ]
    };
  }

  // 批量删除领用单
  rpc MultiDeleteWmsClaimOrder(MultiDeleteWmsClaimOrderRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/wms/v1/wmsClaimOrder/delete"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "领用"
      ]
    };
  }
}

message WmsClaimOrder {
  string id = 1;
  optional string created_at = 2 [
    json_name = "createdAt",
    (gnostic.openapi.v3.property) = {description: "创建时间"}
  ]; // 创建时间
  optional string updated_at = 3 [
    json_name = "updatedAt",
    (gnostic.openapi.v3.property) = {description: "更新时间"}
  ]; // 更新时间
  optional string created_by = 4 [
    json_name = "createdBy",
    (gnostic.openapi.v3.property) = {description: "创建人"}
  ]; // 创建人
  optional string updated_by = 5 [
    json_name = "updatedBy",
    (gnostic.openapi.v3.property) = {description: "更新人"}
  ]; // 更新人
  optional string remark = 6 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string contract_urls = 7 [
    json_name = "contractUrls",
    (gnostic.openapi.v3.property) = {description: "合同附件"}
  ]; // 合同附件
  optional string invoice_urls = 8 [
    json_name = "invoiceUrls",
    (gnostic.openapi.v3.property) = {description: "发票附件"}
  ]; // 发票附件
  optional string audit_urls = 9 [
    json_name = "auditUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string other_urls = 10 [
    json_name = "otherUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string order_no = 11 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "领用单号"}
  ]; // 领用单号
  optional string repository_id = 12 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional string claim_time = 13 [
    json_name = "claimTime",
    (gnostic.openapi.v3.property) = {description: "领用时间"}
  ]; // 领用时间
  optional string claim_user_id = 14 [
    json_name = "claimUserId",
    (gnostic.openapi.v3.property) = {description: "领用人"}
  ]; // 领用人
  optional string status = 15 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "领用状态"}
  ]; // 领用状态
  optional uint32 equipment_num = 16 [
    json_name = "equipmentNum",
    (gnostic.openapi.v3.property) = {description: "设备数量"}
  ]; // 设备数量
  optional string reason = 17 [
    json_name = "reason",
    (gnostic.openapi.v3.property) = {description: "事由"}
  ]; // 事由
  
  optional string create_by_organization_name = 18 [
    json_name = "createByOrganizationName",
    (gnostic.openapi.v3.property) = {description: "创建人部门"}
  ]; // 创建人部门

  // 明细列表
  repeated WmsClaimOrderDetail details = 19 [
    json_name = "details",
    (gnostic.openapi.v3.property) = {description: "领用单明细列表"}
  ]; // 领用单明细列表
}

message ListWmsClaimOrderResponse {
  repeated WmsClaimOrder items = 1;
  int32 total = 2;
}

message GetWmsClaimOrderRequest {
  string id = 1;
}

message CreateWmsClaimOrderRequest {
  string remark = 1 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string other_urls = 2 [
    json_name = "otherUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string claim_time = 3 [
    json_name = "claimTime",
    (gnostic.openapi.v3.property) = {description: "领用时间"}
  ]; // 领用时间
  optional uint32 equipment_num = 4 [
    json_name = "equipmentNum",
    (gnostic.openapi.v3.property) = {description: "设备数量"}
  ]; // 设备数量
  optional string contract_urls = 5 [
    json_name = "contractUrls",
    (gnostic.openapi.v3.property) = {description: "合同附件"}
  ]; // 合同附件
  optional string invoice_urls = 6 [
    json_name = "invoiceUrls",
    (gnostic.openapi.v3.property) = {description: "发票附件"}
  ]; // 发票附件
  optional string repository_id = 7 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional string order_no = 8 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "领用单号"}
  ]; // 领用单号
  optional string claim_user_id = 9 [
    json_name = "claimUserId",
    (gnostic.openapi.v3.property) = {description: "领用人"}
  ]; // 领用人
  optional string status = 10 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "领用状态"}
  ]; // 领用状态
  optional string audit_urls = 11 [
    json_name = "auditUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string reason = 12 [
    json_name = "reason",
    (gnostic.openapi.v3.property) = {description: "事由"}
  ]; // 事由

  // 明细列表
  repeated CreateWmsClaimOrderDetailRequest details = 13 [
    json_name = "details",
    (gnostic.openapi.v3.property) = {description: "领用单明细列表"}
  ]; // 领用单明细列表
}

message UpdateWmsClaimOrderRequest {
  string id = 1;
  optional string remark = 2 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string contract_urls = 3 [
    json_name = "contractUrls",
    (gnostic.openapi.v3.property) = {description: "合同附件"}
  ]; // 合同附件
  optional string invoice_urls = 4 [
    json_name = "invoiceUrls",
    (gnostic.openapi.v3.property) = {description: "发票附件"}
  ]; // 发票附件
  optional string audit_urls = 5 [
    json_name = "auditUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string other_urls = 6 [
    json_name = "otherUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string order_no = 7 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "领用单号"}
  ]; // 领用单号
  optional string repository_id = 8 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional string claim_time = 9 [
    json_name = "claimTime",
    (gnostic.openapi.v3.property) = {description: "领用时间"}
  ]; // 领用时间
  optional string claim_user_id = 10 [
    json_name = "claimUserId",
    (gnostic.openapi.v3.property) = {description: "领用人"}
  ]; // 领用人
  optional string status = 11 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "领用状态"}
  ]; // 领用状态
  optional uint32 equipment_num = 12 [
    json_name = "equipmentNum",
    (gnostic.openapi.v3.property) = {description: "设备数量"}
  ]; // 设备数量
  optional string reason = 13 [
    json_name = "reason",
    (gnostic.openapi.v3.property) = {description: "事由"}
  ]; // 事由

  // 明细列表
  repeated CreateWmsClaimOrderDetailRequest details = 14 [
    json_name = "details",
    (gnostic.openapi.v3.property) = {description: "领用单明细列表"}
  ]; // 领用单明细列表
}

message DeleteWmsClaimOrderRequest {
  string id = 1;
}

message MultiDeleteWmsClaimOrderRequest {
  repeated string ids = 1;
}

message ListWmsClaimOrderRequest {
  // 当前页码
  optional int32 page = 1 [
    json_name = "page",
    (gnostic.openapi.v3.property) = {
      description: "当前页码",
      default: {number: 1}
    }
  ];

  // 每页的行数
  optional int32 page_size = 2 [
    json_name = "pageSize",
    (gnostic.openapi.v3.property) = {
      description: "每一页的行数",
      default: {number: 10}
    }
  ];

  // 与过滤参数
  optional string query = 3 [
    json_name = "query",
    (gnostic.openapi.v3.property) = {
      description: "与过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 或过滤参数
  optional string or_query = 4 [
    json_name = "or",
    (gnostic.openapi.v3.property) = {
      description: "或过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 排序条件
  string order_by = 5 [
    json_name = "orderBy",
    (gnostic.openapi.v3.property) = {
      description: "排序条件，字段名前加'-'为降序，否则为升序。"
      example: {yaml: "{\"val1\", \"-val2\"}"}
    }
  ];

  // 是否不分页
  optional bool no_paging = 6 [
    json_name = "nopaging",
    (gnostic.openapi.v3.property) = {description: "是否不分页"}
  ];

  // 字段掩码
  google.protobuf.FieldMask field_mask = 7 [
    json_name = "fieldMask",
    (gnostic.openapi.v3.property) = {
      description: "字段掩码，如果为空则选中所有字段。",
      example: {yaml: "id,realName,userName"}
    }
  ];
}
