syntax = "proto3";

package wms.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";

service WmsEnterRepositoryOrderDetailService {
  // 获取入库单明细列表
  rpc ListWmsEnterRepositoryOrderDetail(ListWmsEnterRepositoryOrderDetailRequest) returns (ListWmsEnterRepositoryOrderDetailResponse) {
    option (google.api.http) = {get: "/wms/v1/wmsEnterRepositoryOrderDetail"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "入库"
      ]
    };
  }

  // 获取入库单明细详情
  rpc GetWmsEnterRepositoryOrderDetail(GetWmsEnterRepositoryOrderDetailRequest) returns (WmsEnterRepositoryOrderDetail) {
    option (google.api.http) = {get: "/wms/v1/wmsEnterRepositoryOrderDetail/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "入库"
      ]
    };
  }

  // 创建入库单明细
  rpc CreateWmsEnterRepositoryOrderDetail(CreateWmsEnterRepositoryOrderDetailRequest) returns (WmsEnterRepositoryOrderDetail) {
    option (google.api.http) = {
      post: "/wms/v1/wmsEnterRepositoryOrderDetail"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "入库"
      ]
    };
  }

  // 更新入库单明细
  rpc UpdateWmsEnterRepositoryOrderDetail(UpdateWmsEnterRepositoryOrderDetailRequest) returns (WmsEnterRepositoryOrderDetail) {
    option (google.api.http) = {
      put: "/wms/v1/wmsEnterRepositoryOrderDetail/{id}"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "入库"
      ]
    };
  }

  // 删除入库单明细
  rpc DeleteWmsEnterRepositoryOrderDetail(DeleteWmsEnterRepositoryOrderDetailRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/wms/v1/wmsEnterRepositoryOrderDetail/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "入库"
      ]
    };
  }

  // 批量删除入库单明细
  rpc MultiDeleteWmsEnterRepositoryOrderDetail(MultiDeleteWmsEnterRepositoryOrderDetailRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/wms/v1/wmsEnterRepositoryOrderDetail/delete"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "入库"
      ]
    };
  }
}

message WmsEnterRepositoryOrderDetail {
  string id = 1;
  optional string created_at = 2 [
    json_name = "createdAt",
    (gnostic.openapi.v3.property) = {description: "创建时间"}
  ]; // 创建时间
  optional string updated_at = 3 [
    json_name = "updatedAt",
    (gnostic.openapi.v3.property) = {description: "更新时间"}
  ]; // 更新时间
  optional string created_by = 4 [
    json_name = "createdBy",
    (gnostic.openapi.v3.property) = {description: "创建人"}
  ]; // 创建人
  optional string updated_by = 5 [
    json_name = "updatedBy",
    (gnostic.openapi.v3.property) = {description: "更新人"}
  ]; // 更新人
  optional string remark = 6 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string enter_repository_order_id = 7 [
    json_name = "enterRepositoryOrderId",
    (gnostic.openapi.v3.property) = {description: "入库单"}
  ]; // 入库单
  optional string equipment_type_id = 8 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "设备类型"}
  ]; // 设备类型
  optional string equipment_id = 9 [
    json_name = "equipmentId",
    (gnostic.openapi.v3.property) = {description: "设备"}
  ]; // 设备
  optional string name = 10 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "设备名称"}
  ]; // 设备名称
  optional string code = 11 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "编码:RFID编码或二维码"}
  ]; // 编码:RFID编码或二维码
  optional string code_type = 12 [
    json_name = "codeType",
    (gnostic.openapi.v3.property) = {description: "编码类型"}
  ]; // 编码类型
  optional string model_no = 13 [
    json_name = "modelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string measure_unit_id = 14 [
    json_name = "measureUnitId",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位
  optional string provider_id = 15 [
    json_name = "providerId",
    (gnostic.openapi.v3.property) = {description: "供应商"}
  ]; // 供应商
  optional uint32 num = 16 [
    json_name = "num",
    (gnostic.openapi.v3.property) = {description: "数量"}
  ]; // 数量
  optional double price = 17 [
    json_name = "price",
    (gnostic.openapi.v3.property) = {description: "单价"}
  ]; // 单价
  optional string repository_area_id = 18 [
    json_name = "repositoryAreaId",
    (gnostic.openapi.v3.property) = {description: "入库库区"}
  ]; // 入库库区
  optional string repository_position_id = 19 [
    json_name = "repositoryPositionId",
    (gnostic.openapi.v3.property) = {description: "入库仓位"}
  ]; // 入库仓位
  optional string from_repository_id = 20 [
    json_name = "fromRepositoryId",
    (gnostic.openapi.v3.property) = {description: "入库前仓库"}
  ]; // 入库前仓库
  optional string to_repository_id = 21 [
    json_name = "toRepositoryId",
    (gnostic.openapi.v3.property) = {description: "入库后仓库"}
  ]; // 入库后仓库
  optional string order_no = 22 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "入库单号"}
  ]; // 入库单号
  optional string expire_time = 23 [
    json_name = "expireTime",
    (gnostic.openapi.v3.property) = {description: "过期日期"}
  ]; // 过期日期
  optional google.protobuf.Struct feature = 24 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格
  optional string feature_str = 25 [
    json_name = "featureStr",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格 用于显示

  optional string equipment_type_name = 100 [
    json_name = "equipmentTypeName",
    (gnostic.openapi.v3.property) = {description: "设备类型"}
  ]; // 设备类型  
  optional string measure_unit_name = 101 [
    json_name = "measureUnitName",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位

  optional string material_id = 102 [
    json_name = "materialId",
    (gnostic.openapi.v3.property) = {description: "资产id"}
  ]; // 资产id
}

message ListWmsEnterRepositoryOrderDetailResponse {
  repeated WmsEnterRepositoryOrderDetail items = 1;
  int32 total = 2;
}

message GetWmsEnterRepositoryOrderDetailRequest {
  string id = 1;
}

message CreateWmsEnterRepositoryOrderDetailRequest {
  string remark = 1 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string code_type = 2 [
    json_name = "codeType",
    (gnostic.openapi.v3.property) = {description: "编码类型"}
  ]; // 编码类型
  optional string enter_repository_order_id = 3 [
    json_name = "enterRepositoryOrderId",
    (gnostic.openapi.v3.property) = {description: "入库单"}
  ]; // 入库单
  optional string to_repository_id = 4 [
    json_name = "toRepositoryId",
    (gnostic.openapi.v3.property) = {description: "入库后仓库"}
  ]; // 入库后仓库
  optional string equipment_type_id = 5 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "设备类型"}
  ]; // 设备类型
  optional string code = 6 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "编码:RFID编码或二维码"}
  ]; // 编码:RFID编码或二维码
  optional string repository_position_id = 7 [
    json_name = "repositoryPositionId",
    (gnostic.openapi.v3.property) = {description: "入库仓位"}
  ]; // 入库仓位
  optional string measure_unit_id = 8 [
    json_name = "measureUnitId",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位
  optional uint32 num = 9 [
    json_name = "num",
    (gnostic.openapi.v3.property) = {description: "数量"}
  ]; // 数量
  optional string expire_time = 10 [
    json_name = "expireTime",
    (gnostic.openapi.v3.property) = {description: "过期日期"}
  ]; // 过期日期
  optional string repository_area_id = 11 [
    json_name = "repositoryAreaId",
    (gnostic.openapi.v3.property) = {description: "入库库区"}
  ]; // 入库库区
  optional string name = 12 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "设备名称"}
  ]; // 设备名称
  optional string from_repository_id = 13 [
    json_name = "fromRepositoryId",
    (gnostic.openapi.v3.property) = {description: "入库前仓库"}
  ]; // 入库前仓库
  optional double price = 14 [
    json_name = "price",
    (gnostic.openapi.v3.property) = {description: "单价"}
  ]; // 单价
  optional string model_no = 15 [
    json_name = "modelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string order_no = 16 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "入库单号"}
  ]; // 入库单号
  optional string equipment_id = 17 [
    json_name = "equipmentId",
    (gnostic.openapi.v3.property) = {description: "设备"}
  ]; // 设备
  optional string provider_id = 18 [
    json_name = "providerId",
    (gnostic.openapi.v3.property) = {description: "供应商"}
  ]; // 供应商
  optional google.protobuf.Struct feature = 19 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格
  optional string material_id = 20 [
    json_name = "materialId",
    (gnostic.openapi.v3.property) = {description: "资产id"}
  ]; // 资产id
}

message UpdateWmsEnterRepositoryOrderDetailRequest {
  string id = 1;
  optional string remark = 2 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string enter_repository_order_id = 3 [
    json_name = "enterRepositoryOrderId",
    (gnostic.openapi.v3.property) = {description: "入库单"}
  ]; // 入库单
  optional string equipment_type_id = 4 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "设备类型"}
  ]; // 设备类型
  optional string equipment_id = 5 [
    json_name = "equipmentId",
    (gnostic.openapi.v3.property) = {description: "设备"}
  ]; // 设备
  optional string name = 6 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "设备名称"}
  ]; // 设备名称
  optional string code = 7 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "编码:RFID编码或二维码"}
  ]; // 编码:RFID编码或二维码
  optional string code_type = 8 [
    json_name = "codeType",
    (gnostic.openapi.v3.property) = {description: "编码类型"}
  ]; // 编码类型
  optional string model_no = 9 [
    json_name = "modelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string measure_unit_id = 10 [
    json_name = "measureUnitId",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位
  optional string provider_id = 11 [
    json_name = "providerId",
    (gnostic.openapi.v3.property) = {description: "供应商"}
  ]; // 供应商
  optional uint32 num = 12 [
    json_name = "num",
    (gnostic.openapi.v3.property) = {description: "数量"}
  ]; // 数量
  optional double price = 13 [
    json_name = "price",
    (gnostic.openapi.v3.property) = {description: "单价"}
  ]; // 单价
  optional string repository_area_id = 14 [
    json_name = "repositoryAreaId",
    (gnostic.openapi.v3.property) = {description: "入库库区"}
  ]; // 入库库区
  optional string repository_position_id = 15 [
    json_name = "repositoryPositionId",
    (gnostic.openapi.v3.property) = {description: "入库仓位"}
  ]; // 入库仓位
  optional string from_repository_id = 16 [
    json_name = "fromRepositoryId",
    (gnostic.openapi.v3.property) = {description: "入库前仓库"}
  ]; // 入库前仓库
  optional string to_repository_id = 17 [
    json_name = "toRepositoryId",
    (gnostic.openapi.v3.property) = {description: "入库后仓库"}
  ]; // 入库后仓库
  optional string order_no = 18 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "入库单号"}
  ]; // 入库单号
  optional string expire_time = 19 [
    json_name = "expireTime",
    (gnostic.openapi.v3.property) = {description: "过期日期"}
  ]; // 过期日期
  optional google.protobuf.Struct feature = 20 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格
  optional string material_id = 21 [
    json_name = "materialId",
    (gnostic.openapi.v3.property) = {description: "资产id"}
  ]; // 资产id
}

message DeleteWmsEnterRepositoryOrderDetailRequest {
  string id = 1;
}

message MultiDeleteWmsEnterRepositoryOrderDetailRequest {
  repeated string ids = 1;
}

message ListWmsEnterRepositoryOrderDetailRequest {
  // 当前页码
  optional int32 page = 1 [
    json_name = "page",
    (gnostic.openapi.v3.property) = {
      description: "当前页码",
      default: {number: 1}
    }
  ];

  // 每页的行数
  optional int32 page_size = 2 [
    json_name = "pageSize",
    (gnostic.openapi.v3.property) = {
      description: "每一页的行数",
      default: {number: 10}
    }
  ];

  // 与过滤参数
  optional string query = 3 [
    json_name = "query",
    (gnostic.openapi.v3.property) = {
      description: "与过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 或过滤参数
  optional string or_query = 4 [
    json_name = "or",
    (gnostic.openapi.v3.property) = {
      description: "或过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 排序条件
  string order_by = 5 [
    json_name = "orderBy",
    (gnostic.openapi.v3.property) = {
      description: "排序条件，字段名前加'-'为降序，否则为升序。"
      example: {yaml: "{\"val1\", \"-val2\"}"}
    }
  ];

  // 是否不分页
  optional bool no_paging = 6 [
    json_name = "nopaging",
    (gnostic.openapi.v3.property) = {description: "是否不分页"}
  ];

  // 字段掩码
  google.protobuf.FieldMask field_mask = 7 [
    json_name = "fieldMask",
    (gnostic.openapi.v3.property) = {
      description: "字段掩码，如果为空则选中所有字段。",
      example: {yaml: "id,realName,userName"}
    }
  ];
}
