syntax = "proto3";

package wms.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";

service WmsOutRepositoryOrderService {
  // 获取出库单列表
  rpc ListWmsOutRepositoryOrder(ListWmsOutRepositoryOrderRequest) returns (ListWmsOutRepositoryOrderResponse) {
    option (google.api.http) = {get: "/wms/v1/wmsOutRepositoryOrder"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "出库"
      ]
    };
  }

  // 获取出库单详情
  rpc GetWmsOutRepositoryOrder(GetWmsOutRepositoryOrderRequest) returns (WmsOutRepositoryOrder) {
    option (google.api.http) = {get: "/wms/v1/wmsOutRepositoryOrder/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "出库"
      ]
    };
  }

  // 创建出库单
  rpc CreateWmsOutRepositoryOrder(CreateWmsOutRepositoryOrderRequest) returns (WmsOutRepositoryOrder) {
    option (google.api.http) = {
      post: "/wms/v1/wmsOutRepositoryOrder"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "出库"
      ]
    };
  }

  // 更新出库单
  rpc UpdateWmsOutRepositoryOrder(UpdateWmsOutRepositoryOrderRequest) returns (WmsOutRepositoryOrder) {
    option (google.api.http) = {
      put: "/wms/v1/wmsOutRepositoryOrder/{id}"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "出库"
      ]
    };
  }

  // 删除出库单
  rpc DeleteWmsOutRepositoryOrder(DeleteWmsOutRepositoryOrderRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/wms/v1/wmsOutRepositoryOrder/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "出库"
      ]
    };
  }

  // 批量删除出库单
  rpc MultiDeleteWmsOutRepositoryOrder(MultiDeleteWmsOutRepositoryOrderRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/wms/v1/wmsOutRepositoryOrder/delete"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "出库"
      ]
    };
  }

  // 确认取货
  rpc ConfirmReceiveWmsOutRepositoryOrder(ConfirmReceiveWmsOutRepositoryOrderRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/wms/v1/wmsOutRepositoryOrder/{id}/confirmReceive"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "出库"
      ]
    };
  }

  // 自动更新出库单明细编码(扫码枪自动识别)
  rpc AutoUpdateWmsOutRepositoryOrderDetail(AutoUpdateWmsOutRepositoryOrderDetailRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/wms/v1/wmsOutRepositoryOrder/{id}/autoUpdateDetail"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "出库"
      ]
    };
  }
}

message WmsOutRepositoryOrder {
  string id = 1;
  optional string created_at = 2 [
    json_name = "createdAt",
    (gnostic.openapi.v3.property) = {description: "创建时间"}
  ]; // 创建时间
  optional string updated_at = 3 [
    json_name = "updatedAt",
    (gnostic.openapi.v3.property) = {description: "更新时间"}
  ]; // 更新时间
  optional string created_by = 4 [
    json_name = "createdBy",
    (gnostic.openapi.v3.property) = {description: "创建人"}
  ]; // 创建人
  optional string updated_by = 5 [
    json_name = "updatedBy",
    (gnostic.openapi.v3.property) = {description: "更新人"}
  ]; // 更新人
  optional string remark = 6 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string order_no = 7 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "出库单号"}
  ]; // 出库单号
  optional string type = 8 [
    json_name = "type",
    (gnostic.openapi.v3.property) = {description: "出库类型"}
  ]; // 出库类型
  optional string repository_id = 9 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "出库仓库"}
  ]; // 出库仓库
  optional string out_time = 10 [
    json_name = "outTime",
    (gnostic.openapi.v3.property) = {description: "出库时间"}
  ]; // 出库时间
  optional string relation_order_no = 11 [
    json_name = "relationOrderNo",
    (gnostic.openapi.v3.property) = {description: "关联采购单号"}
  ]; // 关联采购单号
  optional string status = 12 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "出库状态"}
  ]; // 出库状态
  optional uint32 equipment_num = 13 [
    json_name = "equipmentNum",
    (gnostic.openapi.v3.property) = {description: "设备数量"}
  ]; // 设备数量

  optional string contract_urls = 14 [
    json_name = "contractUrls",
    (gnostic.openapi.v3.property) = {description: "合同附件"}
  ]; // 合同附件
  optional string invoice_urls = 15 [
    json_name = "invoiceUrls",
    (gnostic.openapi.v3.property) = {description: "发票附件"}
  ]; // 发票附件
  optional string audit_urls = 16 [
    json_name = "auditUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string other_urls = 17 [
    json_name = "otherUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string receiver = 18 [
    json_name = "receiver",
    (gnostic.openapi.v3.property) = {description: "领用人"}
  ]; // 领用人
  optional bool is_confirm_receive = 19 [
    json_name = "isConfirmReceive",
    (gnostic.openapi.v3.property) = {description: "是否确认取货"}
  ]; // 是否确认取货
  optional string sign_image = 20 [
    json_name = "signImage",
    (gnostic.openapi.v3.property) = {description: "签名的图片地址"}
  ]; // 签名的图片地址
  optional string repository_name = 100 [
    json_name = "repositoryName",
    (gnostic.openapi.v3.property) = {description: "出库仓库"}
  ]; // 出库仓库  
}

message ListWmsOutRepositoryOrderResponse {
  repeated WmsOutRepositoryOrder items = 1;
  int32 total = 2;
}

message GetWmsOutRepositoryOrderRequest {
  string id = 1;
}

message CreateWmsOutRepositoryOrderRequest {
  string remark = 1 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string order_no = 2 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "出库单号"}
  ]; // 出库单号
  optional string type = 3 [
    json_name = "type",
    (gnostic.openapi.v3.property) = {description: "出库类型"}
  ]; // 出库类型
  optional string repository_id = 4 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "出库仓库"}
  ]; // 出库仓库
  optional string out_time = 5 [
    json_name = "outTime",
    (gnostic.openapi.v3.property) = {description: "出库时间"}
  ]; // 出库时间
  optional string relation_order_no = 6 [
    json_name = "relationOrderNo",
    (gnostic.openapi.v3.property) = {description: "关联采购单号"}
  ]; // 关联采购单号
  optional string status = 7 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "出库状态"}
  ]; // 出库状态
  optional uint32 equipment_num = 8 [
    json_name = "equipmentNum",
    (gnostic.openapi.v3.property) = {description: "设备数量"}
  ]; // 设备数量

  optional string contract_urls = 9 [
    json_name = "contractUrls",
    (gnostic.openapi.v3.property) = {description: "合同附件"}
  ]; // 合同附件
  optional string invoice_urls = 10 [
    json_name = "invoiceUrls",
    (gnostic.openapi.v3.property) = {description: "发票附件"}
  ]; // 发票附件
  optional string audit_urls = 11 [
    json_name = "auditUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string other_urls = 12 [
    json_name = "otherUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string receiver = 13 [
    json_name = "receiver",
    (gnostic.openapi.v3.property) = {description: "领用人"}
  ]; // 领用人
    optional bool is_confirm_receive = 14 [
    json_name = "isConfirmReceive",
    (gnostic.openapi.v3.property) = {description: "是否确认取货"}
  ]; // 是否确认取货
  optional string sign_image = 15 [
    json_name = "signImage",
    (gnostic.openapi.v3.property) = {description: "签名的图片地址"}
  ]; // 签名的图片地址
}

message UpdateWmsOutRepositoryOrderRequest {
  string id = 1;
  optional string remark = 2 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string order_no = 3 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "出库单号"}
  ]; // 出库单号
  optional string type = 4 [
    json_name = "type",
    (gnostic.openapi.v3.property) = {description: "出库类型"}
  ]; // 出库类型
  optional string repository_id = 5 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "出库仓库"}
  ]; // 出库仓库
  optional string out_time = 6 [
    json_name = "outTime",
    (gnostic.openapi.v3.property) = {description: "出库时间"}
  ]; // 出库时间
  optional string relation_order_no = 7 [
    json_name = "relationOrderNo",
    (gnostic.openapi.v3.property) = {description: "关联采购单号"}
  ]; // 关联采购单号
  optional string status = 8 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "出库状态"}
  ]; // 出库状态
  optional uint32 equipment_num = 9 [
    json_name = "equipmentNum",
    (gnostic.openapi.v3.property) = {description: "设备数量"}
  ]; // 设备数量

  optional string contract_urls = 10 [
    json_name = "contractUrls",
    (gnostic.openapi.v3.property) = {description: "合同附件"}
  ]; // 合同附件
  optional string invoice_urls = 11 [
    json_name = "invoiceUrls",
    (gnostic.openapi.v3.property) = {description: "发票附件"}
  ]; // 发票附件
  optional string audit_urls = 12 [
    json_name = "auditUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string other_urls = 13 [
    json_name = "otherUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string receiver = 14 [
    json_name = "receiver",
    (gnostic.openapi.v3.property) = {description: "领用人"}
  ]; // 领用人
  optional bool is_confirm_receive = 15 [
    json_name = "isConfirmReceive",
    (gnostic.openapi.v3.property) = {description: "是否确认取货"}
  ]; // 是否确认取货
  optional string sign_image = 16 [
    json_name = "signImage",
    (gnostic.openapi.v3.property) = {description: "签名的图片地址"}
  ]; // 签名的图片地址
}

message DeleteWmsOutRepositoryOrderRequest {
  string id = 1;
}

message MultiDeleteWmsOutRepositoryOrderRequest {
  repeated string ids = 1;
}

message ListWmsOutRepositoryOrderRequest {
  // 当前页码
  optional int32 page = 1 [
    json_name = "page",
    (gnostic.openapi.v3.property) = {
      description: "当前页码",
      default: {number: 1}
    }
  ];

  // 每页的行数
  optional int32 page_size = 2 [
    json_name = "pageSize",
    (gnostic.openapi.v3.property) = {
      description: "每一页的行数",
      default: {number: 10}
    }
  ];

  // 与过滤参数
  optional string query = 3 [
    json_name = "query",
    (gnostic.openapi.v3.property) = {
      description: "与过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 或过滤参数
  optional string or_query = 4 [
    json_name = "or",
    (gnostic.openapi.v3.property) = {
      description: "或过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 排序条件
  string order_by = 5 [
    json_name = "orderBy",
    (gnostic.openapi.v3.property) = {
      description: "排序条件，字段名前加'-'为降序，否则为升序。"
      example: {yaml: "{\"val1\", \"-val2\"}"}
    }
  ];

  // 是否不分页
  optional bool no_paging = 6 [
    json_name = "nopaging",
    (gnostic.openapi.v3.property) = {description: "是否不分页"}
  ];

  // 字段掩码
  google.protobuf.FieldMask field_mask = 7 [
    json_name = "fieldMask",
    (gnostic.openapi.v3.property) = {
      description: "字段掩码，如果为空则选中所有字段。",
      example: {yaml: "id,realName,userName"}
    }
  ];
}

message ConfirmReceiveWmsOutRepositoryOrderRequest {
  string id = 1;

  string sign_image = 2 [
    json_name = "signImage",
    (gnostic.openapi.v3.property) = {description: "签名的图片"}
  ]; // 签名的图片
}
