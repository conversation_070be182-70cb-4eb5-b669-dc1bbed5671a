syntax = "proto3";

package wms.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";
import "wms/v1/wms_equipment_detail.proto";
import "wms/v1/wms_equipment_type.proto";

service WmsEquipmentService {
  // 获取设备列表
  rpc ListWmsEquipment(ListWmsEquipmentRequest) returns (ListWmsEquipmentResponse) {
    option (google.api.http) = {get: "/wms/v1/wmsEquipment"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "装备管理"
      ]
    };
  }

  // 获取设备详情
  rpc GetWmsEquipment(GetWmsEquipmentRequest) returns (WmsEquipment) {
    option (google.api.http) = {get: "/wms/v1/wmsEquipment/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "装备管理"
      ]
    };
  }

  // 创建设备
  rpc CreateWmsEquipment(CreateWmsEquipmentRequest) returns (WmsEquipment) {
    option (google.api.http) = {
      post: "/wms/v1/wmsEquipment"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "装备管理"
      ]
    };
  }

  // 更新设备
  rpc UpdateWmsEquipment(UpdateWmsEquipmentRequest) returns (WmsEquipment) {
    option (google.api.http) = {
      put: "/wms/v1/wmsEquipment/{id}"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "装备管理"
      ]
    };
  }

  // 删除设备
  rpc DeleteWmsEquipment(DeleteWmsEquipmentRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/wms/v1/wmsEquipment/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "装备管理"
      ]
    };
  }

  // 批量删除设备
  rpc MultiDeleteWmsEquipment(MultiDeleteWmsEquipmentRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/wms/v1/wmsEquipment/delete"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "装备管理"
      ]
    };
  }

  // 获取设备详情(自定义)
  rpc GetEquipment(GetWmsEquipmentRequest) returns (GetEquipmentResponse) {
    option (google.api.http) = {get: "/wms/v1/equipment/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "装备管理"
      ]
    };
  }

  // 创建设备(自定义)
  rpc CreateEquipment(CreateEquipmentRequest) returns (GetEquipmentResponse) {
    option (google.api.http) = {
      post: "/wms/v1/equipment"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "装备管理"
      ]
    };
  }

  // 更新设备(自定义)
  rpc UpdateEquipment(UpdateEquipmentRequest) returns (GetEquipmentResponse) {
    option (google.api.http) = {
      put: "/wms/v1/equipment/{id}"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "装备管理"
      ]
    };
  }

  // 保存设备(自定义)
  rpc SaveEquipment(UpdateEquipmentRequest) returns (GetEquipmentResponse) {
    option (google.api.http) = {
      post: "/wms/v1/equipment/save"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "装备管理"
      ]
    };
  }
  
  // 获取设备列表, 带库存数量
  rpc ListMyWmsEquipment(ListWmsEquipmentRequest) returns (ListWmsEquipmentResponse) {
    option (google.api.http) = {get: "/wms/v1/myWmsEquipment"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "装备管理"
      ]
    };
  }

  // 获取页面代码
  rpc GetEquipmentPagecode(GetEquipmentPagecodeRequest) returns (google.protobuf.Struct) {
    option (google.api.http) = {get: "/wms/v1/wmsEquipment/pageCode/get"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "装备管理"
      ]
    };
  }

  // 获取装备详细规格数据
  rpc GetEquipmentFeatures(GetEquipmentFeaturesRequest) returns (GetEquipmentFeaturesResponse) {
    option (google.api.http) = {get: "/wms/v1/wmsEquipmentFeatures"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "装备管理"
      ]
    };
  }

  // 获取装备详细规格数
  rpc GetEquipmentFeatureCount(GetEquipmentFeatureCountRequest) returns (GetEquipmentFeatureCountResponse) {
    option (google.api.http) = {get: "/wms/v1/wmsEquipmentFeature/count"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "装备管理"
      ]
    };
  }

  // 获取装备的库位列表
  rpc GetEquipmentRepositoryPositionList(GetEquipmentRepositoryPositionListRequest) returns (GetEquipmentRepositoryPositionListResponse) {
    option (google.api.http) = {get: "/wms/v1/wmsEquipment/repositoryPosition/list"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "装备管理"
      ]
    };
  }
}

message WmsEquipment {
  string id = 1;
  optional string created_at = 2 [
    json_name = "createdAt",
    (gnostic.openapi.v3.property) = {description: "创建时间"}
  ]; // 创建时间
  optional string updated_at = 3 [
    json_name = "updatedAt",
    (gnostic.openapi.v3.property) = {description: "更新时间"}
  ]; // 更新时间
  optional string created_by = 4 [
    json_name = "createdBy",
    (gnostic.openapi.v3.property) = {description: "创建人"}
  ]; // 创建人
  optional string updated_by = 5 [
    json_name = "updatedBy",
    (gnostic.openapi.v3.property) = {description: "更新人"}
  ]; // 更新人
  optional int32 status = 6 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "状态：0 禁用、 1启用"}
  ]; // 状态：0 禁用、 1启用
  optional string auth_type = 7 [
    json_name = "authType",
    (gnostic.openapi.v3.property) = {description: "授权类型"}
  ]; // 授权类型
  optional string auth_user_ids = 8 [
    json_name = "authUserIds",
    (gnostic.openapi.v3.property) = {description: "授权人员组ID"}
  ]; // 授权人员组ID
  optional string auth_organization_ids = 9 [
    json_name = "authOrganizationIds",
    (gnostic.openapi.v3.property) = {description: "授权组织架构ID"}
  ]; // 授权组织架构ID
  optional string type = 10 [
    json_name = "type",
    (gnostic.openapi.v3.property) = {description: "设备类型"}
  ]; // 设备类型
  string equipment_type_id = 11 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "设备分类"}
  ]; // 设备分类
  string name = 12 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "名称"}
  ]; // 名称
  string model_no = 13 [
    json_name = "modelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string weight = 14 [
    json_name = "weight",
    (gnostic.openapi.v3.property) = {description: "重量"}
  ]; // 重量
  string provider_name = 15 [
    json_name = "providerName",
    (gnostic.openapi.v3.property) = {description: "生产厂家名称"}
  ]; // 生产厂家名称
  string provider_country = 16 [
    json_name = "providerCountry",
    (gnostic.openapi.v3.property) = {description: "生产厂家国别"}
  ]; // 生产厂家国别
  string images = 17 [
    json_name = "images",
    (gnostic.openapi.v3.property) = {description: "图片"}
  ]; // 图片
  string remark = 18 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "使用方法"}
  ]; // 使用方法
  string code = 19 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "设备编码"}
  ]; // 设备编码

  optional WmsEquipmentType equipment_type = 20 [
    json_name = "equipmentType",
    (gnostic.openapi.v3.property) = {description: "设备分类"}
  ]; // 设备分类

  repeated WmsEquipmentDetail details = 21 [
    json_name = "details",
    (gnostic.openapi.v3.property) = {description: "设备明细"}
  ]; // 设备明细

  string provider_area = 22 [
    json_name = "providerArea",
    (gnostic.openapi.v3.property) = {description: "生产区县"}
  ];

  string provider_city = 23 [
    json_name = "providerCity",
    (gnostic.openapi.v3.property) = {description: "生产城市"}
  ]; 

  string seller_name = 24 [
    json_name = "sellerName",
    (gnostic.openapi.v3.property) = {description: "销售企业"}
  ];         
  optional string readme = 25 [
    json_name = "readme",
    (gnostic.openapi.v3.property) = {description: "使用说明"}
  ]; // 使用说明  
  optional int32 inventory_count = 26 [
    json_name = "inventoryCount",
    (gnostic.openapi.v3.property) = {description: "库存数量"}
  ]; // 库存数量  
  optional bool is_no_code = 27 [
    json_name = "isNoCode",
    (gnostic.openapi.v3.property) = {description: "是否有编码"}
  ]; // 是否有编码
  optional string repository_id = 28 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库ID"}
  ]; // 仓库ID
  optional string repository_name = 29 [
    json_name = "repositoryName",
    (gnostic.openapi.v3.property) = {description: "仓库名称"}
  ]; // 仓库名称

  bool is_one_material_one_code = 30 [
    json_name = "isOneMaterialOneCode",
    (gnostic.openapi.v3.property) = {description: "是否一物一码"}
  ]; // 是否一物一码

  int32 discard_method = 31 [
    json_name = "discardMethod",
    (gnostic.openapi.v3.property) = {description: "报废方式"}
  ]; // 报废方式 1 审核报废、 2 上会报废、 3 出库即报废
}

message ListWmsEquipmentResponse {
  repeated WmsEquipment items = 1;
  int32 total = 2;
}

message GetWmsEquipmentRequest {
  string id = 1;
}

message CreateWmsEquipmentRequest {
  int32 status = 1 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "状态：0 禁用、 1启用"}
  ]; // 状态：0 禁用、 1启用
  optional string auth_type = 2 [
    json_name = "authType",
    (gnostic.openapi.v3.property) = {description: "授权类型"}
  ]; // 授权类型
  optional string auth_user_ids = 3 [
    json_name = "authUserIds",
    (gnostic.openapi.v3.property) = {description: "授权人员组ID"}
  ]; // 授权人员组ID
  optional string auth_organization_ids = 4 [
    json_name = "authOrganizationIds",
    (gnostic.openapi.v3.property) = {description: "授权组织架构ID"}
  ]; // 授权组织架构ID
  optional string type = 5 [
    json_name = "type",
    (gnostic.openapi.v3.property) = {description: "设备类型"}
  ]; // 设备类型
  string equipment_type_id = 6 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "设备分类"}
  ]; // 设备分类
  string name = 7 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "名称"}
  ]; // 名称
  string model_no = 8 [
    json_name = "modelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string weight = 9 [
    json_name = "weight",
    (gnostic.openapi.v3.property) = {description: "重量"}
  ]; // 重量
  string provider_name = 10 [
    json_name = "providerName",
    (gnostic.openapi.v3.property) = {description: "生产厂家名称"}
  ]; // 生产厂家名称
  string provider_country = 11 [
    json_name = "providerCountry",
    (gnostic.openapi.v3.property) = {description: "生产厂家国别"}
  ]; // 生产厂家国别
  string images = 12 [
    json_name = "images",
    (gnostic.openapi.v3.property) = {description: "图片"}
  ]; // 图片
  string remark = 13 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "使用方法"}
  ]; // 使用方法
  string code = 14 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "设备编码"}
  ]; // 设备编码
  

  repeated CreateWmsEquipmentDetailRequest details = 15 [
    json_name = "details",
    (gnostic.openapi.v3.property) = {description: "设备明细"}
  ]; // 设备明细
  
  string provider_area = 17 [
    json_name = "providerArea",
    (gnostic.openapi.v3.property) = {description: "生产区县"}
  ];

  string provider_city = 18 [
    json_name = "providerCity",
    (gnostic.openapi.v3.property) = {description: "生产城市"}
  ]; 

  string seller_name = 19 [
    json_name = "sellerName",
    (gnostic.openapi.v3.property) = {description: "销售企业"}
  ];         
  optional string readme = 20 [
    json_name = "readme",
    (gnostic.openapi.v3.property) = {description: "使用说明"}
  ]; // 使用说明    
  optional bool is_no_code = 27 [
    json_name = "isNoCode",
    (gnostic.openapi.v3.property) = {description: "是否有编码"}
  ]; // 是否有编码

  bool is_one_material_one_code = 28 [
    json_name = "isOneMaterialOneCode",
    (gnostic.openapi.v3.property) = {description: "是否一物一码"}
  ]; // 是否一物一码

  int32 discard_method = 29 [
    json_name = "discardMethod",
    (gnostic.openapi.v3.property) = {description: "报废方式"}
  ]; // 报废方式 1 审核报废、 2 上会报废、 3 出库即报废
}

message UpdateWmsEquipmentRequest {
  string id = 1;
  optional int32 status = 2 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "状态：0 禁用、 1启用"}
  ]; // 状态：0 禁用、 1启用
  optional string auth_type = 3 [
    json_name = "authType",
    (gnostic.openapi.v3.property) = {description: "授权类型"}
  ]; // 授权类型
  optional string auth_user_ids = 4 [
    json_name = "authUserIds",
    (gnostic.openapi.v3.property) = {description: "授权人员组ID"}
  ]; // 授权人员组ID
  optional string auth_organization_ids = 5 [
    json_name = "authOrganizationIds",
    (gnostic.openapi.v3.property) = {description: "授权组织架构ID"}
  ]; // 授权组织架构ID
  optional string type = 6 [
    json_name = "type",
    (gnostic.openapi.v3.property) = {description: "设备类型"}
  ]; // 设备类型
  string equipment_type_id = 7 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "设备分类"}
  ]; // 设备分类
  string name = 8 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "名称"}
  ]; // 名称
  string model_no = 9 [
    json_name = "modelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string weight = 10 [
    json_name = "weight",
    (gnostic.openapi.v3.property) = {description: "重量"}
  ]; // 重量
  string provider_name = 11 [
    json_name = "providerName",
    (gnostic.openapi.v3.property) = {description: "生产厂家名称"}
  ]; // 生产厂家名称
  string provider_country = 12 [
    json_name = "providerCountry",
    (gnostic.openapi.v3.property) = {description: "生产厂家国别"}
  ]; // 生产厂家国别
  string images = 13 [
    json_name = "images",
    (gnostic.openapi.v3.property) = {description: "图片"}
  ]; // 图片
  string remark = 14 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "使用方法"}
  ]; // 使用方法
  string code = 15 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "设备编码"}
  ]; // 设备编码

  repeated CreateWmsEquipmentDetailRequest details = 16 [
    json_name = "details",
    (gnostic.openapi.v3.property) = {description: "设备明细"}
  ]; // 设备明细

  string provider_area = 17 [
    json_name = "providerArea",
    (gnostic.openapi.v3.property) = {description: "生产区县"}
  ];

  string provider_city = 18 [
    json_name = "providerCity",
    (gnostic.openapi.v3.property) = {description: "生产城市"}
  ]; 

  string seller_name = 19 [
    json_name = "sellerName",
    (gnostic.openapi.v3.property) = {description: "销售企业"}
  ];         
  optional string readme = 20 [
    json_name = "readme",
    (gnostic.openapi.v3.property) = {description: "使用说明"}
  ]; // 使用说明    
  optional bool is_no_code = 27 [
    json_name = "isNoCode",
    (gnostic.openapi.v3.property) = {description: "是否有编码"}
  ]; // 是否有编码

  bool is_one_material_one_code = 28 [
    json_name = "isOneMaterialOneCode",
    (gnostic.openapi.v3.property) = {description: "是否一物一码"}
  ]; // 是否一物一码

  int32 discard_method = 29 [
    json_name = "discardMethod",
    (gnostic.openapi.v3.property) = {description: "报废方式"}
  ]; // 报废方式 1 审核报废、 2 上会报废、 3 出库即报废
}

message DeleteWmsEquipmentRequest {
  string id = 1;
}

message MultiDeleteWmsEquipmentRequest {
  repeated string ids = 1;
}

message ListWmsEquipmentRequest {
  // 当前页码
  optional int32 page = 1 [
    json_name = "page",
    (gnostic.openapi.v3.property) = {
      description: "当前页码",
      default: {number: 1}
    }
  ];

  // 每页的行数
  optional int32 page_size = 2 [
    json_name = "pageSize",
    (gnostic.openapi.v3.property) = {
      description: "每一页的行数",
      default: {number: 10}
    }
  ];

  // 与过滤参数
  optional string query = 3 [
    json_name = "query",
    (gnostic.openapi.v3.property) = {
      description: "与过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 或过滤参数
  optional string or_query = 4 [
    json_name = "or",
    (gnostic.openapi.v3.property) = {
      description: "或过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 排序条件
  string order_by = 5 [
    json_name = "orderBy",
    (gnostic.openapi.v3.property) = {
      description: "排序条件，字段名前加'-'为降序，否则为升序。"
      example: {yaml: "{\"val1\", \"-val2\"}"}
    }
  ];

  // 是否不分页
  optional bool no_paging = 6 [
    json_name = "nopaging",
    (gnostic.openapi.v3.property) = {description: "是否不分页"}
  ];

  // 字段掩码
  google.protobuf.FieldMask field_mask = 7 [
    json_name = "fieldMask",
    (gnostic.openapi.v3.property) = {
      description: "字段掩码，如果为空则选中所有字段。",
      example: {yaml: "id,realName,userName"}
    }
  ];
}

message GetEquipmentResponse {
  string id = 1;
  optional string created_at = 2 [
    json_name = "createdAt",
    (gnostic.openapi.v3.property) = {description: "创建时间"}
  ]; // 创建时间
  optional string updated_at = 3 [
    json_name = "updatedAt",
    (gnostic.openapi.v3.property) = {description: "更新时间"}
  ]; // 更新时间
  optional string created_by = 4 [
    json_name = "createdBy",
    (gnostic.openapi.v3.property) = {description: "创建人"}
  ]; // 创建人
  optional string updated_by = 5 [
    json_name = "updatedBy",
    (gnostic.openapi.v3.property) = {description: "更新人"}
  ]; // 更新人
  optional int32 status = 6 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "状态：0 禁用、 1启用"}
  ]; // 状态：0 禁用、 1启用
  optional string auth_type = 7 [
    json_name = "authType",
    (gnostic.openapi.v3.property) = {description: "授权类型"}
  ]; // 授权类型
  optional string auth_user_ids = 8 [
    json_name = "authUserIds",
    (gnostic.openapi.v3.property) = {description: "授权人员组ID"}
  ]; // 授权人员组ID
  optional string auth_organization_ids = 9 [
    json_name = "authOrganizationIds",
    (gnostic.openapi.v3.property) = {description: "授权组织架构ID"}
  ]; // 授权组织架构ID
  optional string type = 10 [
    json_name = "type",
    (gnostic.openapi.v3.property) = {description: "设备类型"}
  ]; // 设备类型
  string equipment_type_id = 11 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "设备分类"}
  ]; // 设备分类
  string name = 12 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "名称"}
  ]; // 名称
  string model_no = 13 [
    json_name = "modelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string weight = 14 [
    json_name = "weight",
    (gnostic.openapi.v3.property) = {description: "重量"}
  ]; // 重量
  string provider_name = 15 [
    json_name = "providerName",
    (gnostic.openapi.v3.property) = {description: "生产厂家名称"}
  ]; // 生产厂家名称
  string provider_country = 16 [
    json_name = "providerCountry",
    (gnostic.openapi.v3.property) = {description: "生产厂家国别"}
  ]; // 生产厂家国别
  string images = 17 [
    json_name = "images",
    (gnostic.openapi.v3.property) = {description: "图片"}
  ]; // 图片
  string remark = 18 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "使用方法"}
  ]; // 使用方法
  string code = 19 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "设备编码"}
  ]; // 设备编码

  google.protobuf.Struct detail = 20 [
    json_name = "detail",
    (gnostic.openapi.v3.property) = {description: "设备详情"}
  ]; // 设备详情


  string provider_area = 21 [
    json_name = "providerArea",
    (gnostic.openapi.v3.property) = {description: "生产区县"}
  ];

  string provider_city = 22 [
    json_name = "providerCity",
    (gnostic.openapi.v3.property) = {description: "生产城市"}
  ];   

  string seller_name = 23 [
    json_name = "sellerName",
    (gnostic.openapi.v3.property) = {description: "销售企业"}
  ];     
 
  optional string readme = 24 [
    json_name = "readme",
    (gnostic.openapi.v3.property) = {description: "使用说明"}
  ]; // 使用说明    

  optional bool is_no_code = 27 [
    json_name = "isNoCode",
    (gnostic.openapi.v3.property) = {description: "是否有编码"}
  ]; // 是否有编码    

  bool is_one_material_one_code = 28 [
    json_name = "isOneMaterialOneCode",
    (gnostic.openapi.v3.property) = {description: "是否一物一码"}
  ]; // 是否一物一码

  int32 discard_method = 29 [
    json_name = "discardMethod",
    (gnostic.openapi.v3.property) = {description: "报废方式"}
  ]; // 报废方式 1 审核报废、 2 上会报废、 3 出库即报废
}

message CreateEquipmentRequest {
  optional int32 status = 1 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "状态：0 禁用、 1启用"}
  ]; // 状态：0 禁用、 1启用
  optional string auth_type = 2 [
    json_name = "authType",
    (gnostic.openapi.v3.property) = {description: "授权类型"}
  ]; // 授权类型
  optional string auth_user_ids = 3 [
    json_name = "authUserIds",
    (gnostic.openapi.v3.property) = {description: "授权人员组ID"}
  ]; // 授权人员组ID
  optional string auth_organization_ids = 4 [
    json_name = "authOrganizationIds",
    (gnostic.openapi.v3.property) = {description: "授权组织架构ID"}
  ]; // 授权组织架构ID
  optional string type = 5 [
    json_name = "type",
    (gnostic.openapi.v3.property) = {description: "设备类型"}
  ]; // 设备类型
  string equipment_type_id = 6 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "设备分类"}
  ]; // 设备分类
  string name = 7 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "名称"}
  ]; // 名称
  string model_no = 8 [
    json_name = "modelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string weight = 9 [
    json_name = "weight",
    (gnostic.openapi.v3.property) = {description: "重量"}
  ]; // 重量
  string provider_name = 10 [
    json_name = "providerName",
    (gnostic.openapi.v3.property) = {description: "生产厂家名称"}
  ]; // 生产厂家名称
  string provider_country = 11 [
    json_name = "providerCountry",
    (gnostic.openapi.v3.property) = {description: "生产厂家国别"}
  ]; // 生产厂家国别
  string images = 12 [
    json_name = "images",
    (gnostic.openapi.v3.property) = {description: "图片"}
  ]; // 图片
  string remark = 13 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "使用方法"}
  ]; // 使用方法
  string code = 14 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "设备编码"}
  ]; // 设备编码

   optional google.protobuf.Struct detail = 15 [
    json_name = "detail",
    (gnostic.openapi.v3.property) = {description: "设备详情"}
  ]; // 设备详情


  string provider_area = 17 [
    json_name = "providerArea",
    (gnostic.openapi.v3.property) = {description: "生产区县"}
  ];

  string provider_city = 18 [
    json_name = "providerCity",
    (gnostic.openapi.v3.property) = {description: "生产城市"}
  ];   

  string seller_name = 19 [
    json_name = "sellerName",
    (gnostic.openapi.v3.property) = {description: "销售企业"}
  ];       

  optional string readme = 20 [
    json_name = "readme",
    (gnostic.openapi.v3.property) = {description: "使用说明"}
  ]; // 使用说明    

  optional bool is_no_code = 27 [
    json_name = "isNoCode",
    (gnostic.openapi.v3.property) = {description: "是否有编码"}
  ]; // 是否有编码   

  bool is_one_material_one_code = 28 [
    json_name = "isOneMaterialOneCode",
    (gnostic.openapi.v3.property) = {description: "是否一物一码"}
  ]; // 是否一物一码

  int32 discard_method = 29 [
    json_name = "discardMethod",
    (gnostic.openapi.v3.property) = {description: "报废方式"}
  ]; // 报废方式 1 审核报废、 2 上会报废、 3 出库即报废 
}

message UpdateEquipmentRequest {
  string id = 1;
  optional int32 status = 2 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "状态：0 禁用、 1启用"}
  ]; // 状态：0 禁用、 1启用
  optional string auth_type = 3 [
    json_name = "authType",
    (gnostic.openapi.v3.property) = {description: "授权类型"}
  ]; // 授权类型
  optional string auth_user_ids = 4 [
    json_name = "authUserIds",
    (gnostic.openapi.v3.property) = {description: "授权人员组ID"}
  ]; // 授权人员组ID
  optional string auth_organization_ids = 5 [
    json_name = "authOrganizationIds",
    (gnostic.openapi.v3.property) = {description: "授权组织架构ID"}
  ]; // 授权组织架构ID
  optional string type = 6 [
    json_name = "type",
    (gnostic.openapi.v3.property) = {description: "设备类型"}
  ]; // 设备类型
  string equipment_type_id = 7 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "设备分类"}
  ]; // 设备分类
  string name = 8 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "名称"}
  ]; // 名称
  string model_no = 9 [
    json_name = "modelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string weight = 10 [
    json_name = "weight",
    (gnostic.openapi.v3.property) = {description: "重量"}
  ]; // 重量
  string provider_name = 11 [
    json_name = "providerName",
    (gnostic.openapi.v3.property) = {description: "生产厂家名称"}
  ]; // 生产厂家名称
  string provider_country = 12 [
    json_name = "providerCountry",
    (gnostic.openapi.v3.property) = {description: "生产厂家国别"}
  ]; // 生产厂家国别
  string images = 13 [
    json_name = "images",
    (gnostic.openapi.v3.property) = {description: "图片"}
  ]; // 图片
  string remark = 14 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "使用方法"}
  ]; // 使用方法
  string code = 15 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "设备编码"}
  ]; // 设备编码

  optional google.protobuf.Struct detail = 16 [
    json_name = "detail",
    (gnostic.openapi.v3.property) = {description: "设备详情"}
  ]; // 设备详情

  string provider_area = 17 [
    json_name = "providerArea",
    (gnostic.openapi.v3.property) = {description: "生产区县"}
  ];

  string provider_city = 18 [
    json_name = "providerCity",
    (gnostic.openapi.v3.property) = {description: "生产城市"}
  ]; 

  string seller_name = 19 [
    json_name = "sellerName",
    (gnostic.openapi.v3.property) = {description: "销售企业"}
  ];       

  optional string readme = 20 [
    json_name = "readme",
    (gnostic.openapi.v3.property) = {description: "使用说明"}
  ]; // 使用说明    

  optional bool is_no_code = 27 [
    json_name = "isNoCode",
    (gnostic.openapi.v3.property) = {description: "是否有编码"}
  ]; // 是否有编码   

  bool is_one_material_one_code = 28 [
    json_name = "isOneMaterialOneCode",
    (gnostic.openapi.v3.property) = {description: "是否一物一码"}
  ]; // 是否一物一码

  int32 discard_method = 29 [
    json_name = "discardMethod",
    (gnostic.openapi.v3.property) = {description: "报废方式"}
  ]; // 报废方式 1 审核报废、 2 上会报废、 3 出库即报废 
}

message GetEquipmentPagecodeRequest {
  string name = 1 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "页面名称"}
  ]; // 页面名称

  string id = 2 [
    json_name = "id",
    (gnostic.openapi.v3.property) = {description: "页面ID"}
  ]; // 页面ID
}

message GetEquipmentFeaturesRequest {
  string id = 1 [
    json_name = "id",
    (gnostic.openapi.v3.property) = {description: "设备ID"}
  ]; // 设备ID
  optional string repository_id = 3 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库ID"}
  ]; // 仓库ID
  optional google.protobuf.Struct feature = 4 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格
}

message GetEquipmentFeaturesResponse {
  repeated EquipmentFeature items = 1 [
    json_name = "items",
    (gnostic.openapi.v3.property) = {description: "设备特性"}
  ]; // 设备特性
}

message EquipmentFeature {
  string id = 1 [
    json_name = "id",
    (gnostic.openapi.v3.property) = {description: "属性ID"}
  ]; // 属性ID
  string name = 2 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "属性名称"}
  ]; // 属性名称
  string label = 3 [
    json_name = "label",
    (gnostic.openapi.v3.property) = {description: "值显示名称"}
  ]; // 值显示名称
  string value = 4 [
    json_name = "value",
    (gnostic.openapi.v3.property) = {description: "值"}
  ]; // 值
  optional int32 inventory_count = 5 [
    json_name = "inventoryCount",
    (gnostic.openapi.v3.property) = {description: "库存数量"}
  ]; // 库存数量
  optional int32 sort = 6 [
    json_name = "sort",
    (gnostic.openapi.v3.property) = {description: "排序"}
  ]; // 排序
}

message GetEquipmentFeatureCountRequest {
   string id = 1 [
    json_name = "id",
    (gnostic.openapi.v3.property) = {description: "设备ID"}
  ]; // 设备ID
  optional string repository_id = 3 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库ID"}
  ]; // 仓库ID
  optional google.protobuf.Struct feature = 4 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格
}

message GetEquipmentFeatureCountResponse {
  int32 count = 1 [
    json_name = "count",
    (gnostic.openapi.v3.property) = {description: "特性数量"}
  ]; // 特性数量
}

message GetEquipmentRepositoryPositionListRequest {
  string equipment_id = 1 [
    json_name = "equipmentId",
    (gnostic.openapi.v3.property) = {description: "装备ID"}
  ]; // 装备ID

  string repository_id = 2 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库ID"}
  ]; // 仓库ID

  optional google.protobuf.Struct feature = 3 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格 

  optional string code = 4 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "装备编码"}
  ]; // 装备编码
}

message GetEquipmentRepositoryPositionListResponse {
  repeated EquipmentRepositoryPosition items = 1 [
    json_name = "items",
    (gnostic.openapi.v3.property) = {description: "装备库位列表"}
  ]; // 装备库位列表
}

message EquipmentRepositoryPosition {
  string id = 1 [
    json_name = "id",
    (gnostic.openapi.v3.property) = {description: "装备库位ID"}
  ]; // 装备库位ID

  string name = 2 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "装备库位名称"}
  ]; // 装备库位名称
}



