syntax = "proto3";

package wms.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";

service WmsAuditPlanDetailService {
  // 获取装备盘点计划详情列表
  rpc ListWmsAuditPlanDetail(ListWmsAuditPlanDetailRequest) returns (ListWmsAuditPlanDetailResponse) {
    option (google.api.http) = {get: "/wms/v1/wmsAuditPlanDetail"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "盘点计划详情"
      ]
    };
  }

  // 获取装备盘点计划详情详情
  rpc GetWmsAuditPlanDetail(GetWmsAuditPlanDetailRequest) returns (WmsAuditPlanDetail) {
    option (google.api.http) = {get: "/wms/v1/wmsAuditPlanDetail/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "盘点计划详情"
      ]
    };
  }

  // 创建装备盘点计划详情
  rpc CreateWmsAuditPlanDetail(CreateWmsAuditPlanDetailRequest) returns (WmsAuditPlanDetail) {
    option (google.api.http) = {
      post: "/wms/v1/wmsAuditPlanDetail"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "盘点计划详情"
      ]
    };
  }

  // 更新装备盘点计划详情
  rpc UpdateWmsAuditPlanDetail(UpdateWmsAuditPlanDetailRequest) returns (WmsAuditPlanDetail) {
    option (google.api.http) = {
      put: "/wms/v1/wmsAuditPlanDetail/{id}"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "盘点计划详情"
      ]
    };
  }

  // 删除装备盘点计划详情
  rpc DeleteWmsAuditPlanDetail(DeleteWmsAuditPlanDetailRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/wms/v1/wmsAuditPlanDetail/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "盘点计划详情"
      ]
    };
  }

  // 批量删除装备盘点计划详情
  rpc MultiDeleteWmsAuditPlanDetail(MultiDeleteWmsAuditPlanDetailRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/wms/v1/wmsAuditPlanDetail/delete"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "盘点计划详情"
      ]
    };
  }


  // 批量更新装备盘点计划详情
  rpc MultiUpdateWmsAuditPlanDetail(MultiUpdateWmsAuditPlanDetailRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/wms/v1/wmsAuditPlanDetail/update"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "盘点计划详情"
      ]
    };
  }  

  // 获取我的装备盘点计划详情列表
  rpc ListMyWmsAuditPlanDetail(ListMyWmsAuditPlanDetailRequest) returns (ListWmsAuditPlanDetailResponse) {
    option (google.api.http) = {get: "/wms/v1/myWmsAuditPlanDetail"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "装备",
        "盘点计划详情"
      ]
    };
  }  

  // 提交我得装备盘点计划详情
  rpc PostMyWmsAuditPlanDetailState(PostMyWmsAuditPlanDetailRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/wms/v1/myWmsAuditPlanDetail"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库",
        "盘点计划详情"
      ]
    };
  }  
}

message WmsAuditPlanDetail {
  string id = 1;
  optional string created_at = 2 [
    json_name = "createdAt",
    (gnostic.openapi.v3.property) = {description: "创建时间"}
  ]; // 创建时间
  optional string updated_at = 3 [
    json_name = "updatedAt",
    (gnostic.openapi.v3.property) = {description: "更新时间"}
  ]; // 更新时间
  optional string created_by = 4 [
    json_name = "createdBy",
    (gnostic.openapi.v3.property) = {description: "创建人"}
  ]; // 创建人
  optional string updated_by = 5 [
    json_name = "updatedBy",
    (gnostic.openapi.v3.property) = {description: "更新人"}
  ]; // 更新人
  optional string remark = 6 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional int32 status = 7 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "状态：0 未找到, 1 找到, -1 异常"}
  ]; // 状态：0 禁用、 1启用
  optional string audit_plan_id = 8 [
    json_name = "auditPlanId",
    (gnostic.openapi.v3.property) = {description: "盘点计划ID"}
  ]; // 盘点计划ID
  optional string material_id = 9 [
    json_name = "materialId",
    (gnostic.openapi.v3.property) = {description: "资产ID"}
  ]; // 资产ID
  optional string scope = 10 [
    json_name = "scope",
    (gnostic.openapi.v3.property) = {description: "盘点范围"}
  ]; // 盘点范围
  optional string state = 11 [
    json_name = "state",
    (gnostic.openapi.v3.property) = {description: "使用状态"}
  ]; // 使用状态; 枚举类型: InUse,InStock
  optional bool is_audit = 12 [
    json_name = "isAudit",
    (gnostic.openapi.v3.property) = {description: "是否已盘点"}
  ]; // 是否已盘点
  optional bool is_reader = 13 [
    json_name = "isReader",
    (gnostic.openapi.v3.property) = {description: "是否来自RFID读取"}
  ]; // 是否来自RFID读取
  optional string source = 14 [
    json_name = "source",
    (gnostic.openapi.v3.property) = {description: "数据来源"}
  ]; // 数据来源
  optional string fire_station_id = 15 [
    json_name = "fireStationId",
    (gnostic.openapi.v3.property) = {description: "消防站"}
  ]; // 消防站
  optional string material_type = 16 [
    json_name = "materialType",
    (gnostic.openapi.v3.property) = {description: "装备类型"}
  ]; // 装备类型
  optional string equipment_type_id = 17 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "装备分类"}
  ]; // 装备分类
  optional string equipment_id = 18 [
    json_name = "equipmentId",
    (gnostic.openapi.v3.property) = {description: "装备"}
  ]; // 装备
  optional string code = 19 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "编码"}
  ]; // 编码
  optional string code_type = 20 [
    json_name = "codeType",
    (gnostic.openapi.v3.property) = {description: "编码类型"}
  ]; // 编码类型
  optional string name = 21 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "装备名称"}
  ]; // 装备名称
  optional string model_no = 22 [
    json_name = "modelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string provider_id = 23 [
    json_name = "providerId",
    (gnostic.openapi.v3.property) = {description: "供应商"}
  ]; // 供应商
  optional uint32 num = 24 [
    json_name = "num",
    (gnostic.openapi.v3.property) = {description: "数量"}
  ]; // 数量
  optional double price = 25 [
    json_name = "price",
    (gnostic.openapi.v3.property) = {description: "单价"}
  ]; // 单价
  optional string repository_id = 26 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional string repository_area_id = 27 [
    json_name = "repositoryAreaId",
    (gnostic.openapi.v3.property) = {description: "库区"}
  ]; // 库区
  optional string repository_position_id = 28 [
    json_name = "repositoryPositionId",
    (gnostic.openapi.v3.property) = {description: "库位"}
  ]; // 库位
  optional string measure_unit_id = 29 [
    json_name = "measureUnitId",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位
  optional string expire_time = 30 [
    json_name = "expireTime",
    (gnostic.openapi.v3.property) = {description: "过期日期"}
  ]; // 过期日期
  optional int32 equipment_status = 31 [
    json_name = "equipmentStatus",
    (gnostic.openapi.v3.property) = {description: "装备状态"}
  ]; // 装备状态
  optional string owner_id = 32 [
    json_name = "ownerId",
    (gnostic.openapi.v3.property) = {description: "持有人"}
  ]; // 持有人

  optional string equipment_type_name= 50 [
    json_name = "equipmentTypeName",
    (gnostic.openapi.v3.property) = {description: "装备分类名称"}
  ]; // 装备分类  
  optional string repository_name = 51 [
    json_name = "repositoryName",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional string repository_area_name = 52 [
    json_name = "repositoryAreaName",
    (gnostic.openapi.v3.property) = {description: "库区"}
  ]; // 库区
  optional string repository_position_name = 53 [
    json_name = "repositoryPositionName",
    (gnostic.openapi.v3.property) = {description: "库位"}
  ]; // 库位  
  optional string measure_unit_name = 54 [
    json_name = "measureUnitName",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位      
  optional string owner_name = 55 [
    json_name = "ownerName",
    (gnostic.openapi.v3.property) = {description: "持有人"}
  ]; // 持有人  
  optional string provider_name = 100 [
    json_name = "providerName",
    (gnostic.openapi.v3.property) = {description: "供应商"}
  ]; // 供应商    

  optional google.protobuf.Struct feature = 101 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格

  optional string feature_str = 102 [
    json_name = "featureStr",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格
}

message ListWmsAuditPlanDetailResponse {
  repeated WmsAuditPlanDetail items = 1;
  int32 total = 2;
}

message GetWmsAuditPlanDetailRequest {
  string id = 1;
}

message CreateWmsAuditPlanDetailRequest {
  string remark = 1 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  int32 status = 2 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "状态：0 禁用、 1启用"}
  ]; // 状态：0 禁用、 1启用
  optional string material_type = 3 [
    json_name = "materialType",
    (gnostic.openapi.v3.property) = {description: "装备类型"}
  ]; // 装备类型
  optional string audit_plan_id = 4 [
    json_name = "auditPlanId",
    (gnostic.openapi.v3.property) = {description: "盘点计划ID"}
  ]; // 盘点计划ID
  optional string expire_time = 5 [
    json_name = "expireTime",
    (gnostic.openapi.v3.property) = {description: "过期日期"}
  ]; // 过期日期
  optional string equipment_type_id = 6 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "装备分类"}
  ]; // 装备分类
  optional string material_id = 7 [
    json_name = "materialId",
    (gnostic.openapi.v3.property) = {description: "资产ID"}
  ]; // 资产ID
  optional bool is_audit = 8 [
    json_name = "isAudit",
    (gnostic.openapi.v3.property) = {description: "是否已盘点"}
  ]; // 是否已盘点
  optional string repository_position_id = 9 [
    json_name = "repositoryPositionId",
    (gnostic.openapi.v3.property) = {description: "库位"}
  ]; // 库位
  optional string name = 10 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "装备名称"}
  ]; // 装备名称
  optional string repository_id = 11 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional string code_type = 12 [
    json_name = "codeType",
    (gnostic.openapi.v3.property) = {description: "编码类型"}
  ]; // 编码类型
  optional string code = 13 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "编码"}
  ]; // 编码
  optional string source = 14 [
    json_name = "source",
    (gnostic.openapi.v3.property) = {description: "数据来源"}
  ]; // 数据来源
  optional string repository_area_id = 15 [
    json_name = "repositoryAreaId",
    (gnostic.openapi.v3.property) = {description: "库区"}
  ]; // 库区
  optional string model_no = 16 [
    json_name = "modelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional double price = 17 [
    json_name = "price",
    (gnostic.openapi.v3.property) = {description: "单价"}
  ]; // 单价
  optional string owner_id = 18 [
    json_name = "ownerId",
    (gnostic.openapi.v3.property) = {description: "持有人"}
  ]; // 持有人
  optional string fire_station_id = 19 [
    json_name = "fireStationId",
    (gnostic.openapi.v3.property) = {description: "消防站"}
  ]; // 消防站
  optional bool is_reader = 20 [
    json_name = "isReader",
    (gnostic.openapi.v3.property) = {description: "是否来自RFID读取"}
  ]; // 是否来自RFID读取
  optional string measure_unit_id = 21 [
    json_name = "measureUnitId",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位
  optional string provider_id = 22 [
    json_name = "providerId",
    (gnostic.openapi.v3.property) = {description: "供应商"}
  ]; // 供应商
  optional int32 equipment_status = 23 [
    json_name = "equipmentStatus",
    (gnostic.openapi.v3.property) = {description: "装备状态"}
  ]; // 装备状态
  optional string state = 24 [
    json_name = "state",
    (gnostic.openapi.v3.property) = {description: "使用状态"}
  ]; // 使用状态; 枚举类型: InUse,InStock
  optional string equipment_id = 25 [
    json_name = "equipmentId",
    (gnostic.openapi.v3.property) = {description: "装备"}
  ]; // 装备
  optional string scope = 26 [
    json_name = "scope",
    (gnostic.openapi.v3.property) = {description: "盘点范围"}
  ]; // 盘点范围
  optional uint32 num = 27 [
    json_name = "num",
    (gnostic.openapi.v3.property) = {description: "数量"}
  ]; // 数量
  optional google.protobuf.Struct feature = 28 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格
}

message UpdateWmsAuditPlanDetailRequest {
  string id = 1;
  optional string remark = 2 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional int32 status = 3 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "状态：0 禁用、 1启用"}
  ]; // 状态：0 禁用、 1启用
  optional string audit_plan_id = 4 [
    json_name = "auditPlanId",
    (gnostic.openapi.v3.property) = {description: "盘点计划ID"}
  ]; // 盘点计划ID
  optional string material_id = 5 [
    json_name = "materialId",
    (gnostic.openapi.v3.property) = {description: "资产ID"}
  ]; // 资产ID
  optional string scope = 6 [
    json_name = "scope",
    (gnostic.openapi.v3.property) = {description: "盘点范围"}
  ]; // 盘点范围
  optional string state = 7 [
    json_name = "state",
    (gnostic.openapi.v3.property) = {description: "使用状态"}
  ]; // 使用状态; 枚举类型: InUse,InStock
  optional bool is_audit = 8 [
    json_name = "isAudit",
    (gnostic.openapi.v3.property) = {description: "是否已盘点"}
  ]; // 是否已盘点
  optional bool is_reader = 9 [
    json_name = "isReader",
    (gnostic.openapi.v3.property) = {description: "是否来自RFID读取"}
  ]; // 是否来自RFID读取
  optional string source = 10 [
    json_name = "source",
    (gnostic.openapi.v3.property) = {description: "数据来源"}
  ]; // 数据来源
  optional string fire_station_id = 11 [
    json_name = "fireStationId",
    (gnostic.openapi.v3.property) = {description: "消防站"}
  ]; // 消防站
  optional string material_type = 12 [
    json_name = "materialType",
    (gnostic.openapi.v3.property) = {description: "装备类型"}
  ]; // 装备类型
  optional string equipment_type_id = 13 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "装备分类"}
  ]; // 装备分类
  optional string equipment_id = 14 [
    json_name = "equipmentId",
    (gnostic.openapi.v3.property) = {description: "装备"}
  ]; // 装备
  optional string code = 15 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "编码"}
  ]; // 编码
  optional string code_type = 16 [
    json_name = "codeType",
    (gnostic.openapi.v3.property) = {description: "编码类型"}
  ]; // 编码类型
  optional string name = 17 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "装备名称"}
  ]; // 装备名称
  optional string model_no = 18 [
    json_name = "modelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string provider_id = 19 [
    json_name = "providerId",
    (gnostic.openapi.v3.property) = {description: "供应商"}
  ]; // 供应商
  optional uint32 num = 20 [
    json_name = "num",
    (gnostic.openapi.v3.property) = {description: "数量"}
  ]; // 数量
  optional double price = 21 [
    json_name = "price",
    (gnostic.openapi.v3.property) = {description: "单价"}
  ]; // 单价
  optional string repository_id = 22 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional string repository_area_id = 23 [
    json_name = "repositoryAreaId",
    (gnostic.openapi.v3.property) = {description: "库区"}
  ]; // 库区
  optional string repository_position_id = 24 [
    json_name = "repositoryPositionId",
    (gnostic.openapi.v3.property) = {description: "库位"}
  ]; // 库位
  optional string measure_unit_id = 25 [
    json_name = "measureUnitId",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位
  optional string expire_time = 26 [
    json_name = "expireTime",
    (gnostic.openapi.v3.property) = {description: "过期日期"}
  ]; // 过期日期
  optional int32 equipment_status = 27 [
    json_name = "equipmentStatus",
    (gnostic.openapi.v3.property) = {description: "装备状态"}
  ]; // 装备状态
  optional string owner_id = 28 [
    json_name = "ownerId",
    (gnostic.openapi.v3.property) = {description: "持有人"}
  ]; // 持有人
  optional google.protobuf.Struct feature = 29 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格
}

message DeleteWmsAuditPlanDetailRequest {
  string id = 1;
}

message MultiDeleteWmsAuditPlanDetailRequest {
  repeated string ids = 1;
}

message ListWmsAuditPlanDetailRequest {
  // 当前页码
  optional int32 page = 1 [
    json_name = "page",
    (gnostic.openapi.v3.property) = {
      description: "当前页码",
      default: {number: 1}
    }
  ];

  // 每页的行数
  optional int32 page_size = 2 [
    json_name = "pageSize",
    (gnostic.openapi.v3.property) = {
      description: "每一页的行数",
      default: {number: 10}
    }
  ];

  // 与过滤参数
  optional string query = 3 [
    json_name = "query",
    (gnostic.openapi.v3.property) = {
      description: "与过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 或过滤参数
  optional string or_query = 4 [
    json_name = "or",
    (gnostic.openapi.v3.property) = {
      description: "或过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 排序条件
  string order_by = 5 [
    json_name = "orderBy",
    (gnostic.openapi.v3.property) = {
      description: "排序条件，字段名前加'-'为降序，否则为升序。"
      example: {yaml: "{\"val1\", \"-val2\"}"}
    }
  ];

  // 是否不分页
  optional bool no_paging = 6 [
    json_name = "nopaging",
    (gnostic.openapi.v3.property) = {description: "是否不分页"}
  ];

  // 字段掩码
  google.protobuf.FieldMask field_mask = 7 [
    json_name = "fieldMask",
    (gnostic.openapi.v3.property) = {
      description: "字段掩码，如果为空则选中所有字段。",
      example: {yaml: "id,realName,userName"}
    }
  ];
}

message MultiUpdateWmsAuditPlanDetailRequest {
  repeated string ids = 100;
  string remark = 1 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  int32 status = 2 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "状态：0 未找到, 1 找到, -1 异常"}
  ]; // 状态：0 未找到, 1 找到, -1 异常
}

message ListMyWmsAuditPlanDetailRequest {
  string audit_plan_id = 1 [
    json_name = "auditPlanId",
    (gnostic.openapi.v3.property) = {description: "盘点计划ID"}
  ]; // 盘点计划ID
  optional bool is_audit = 2 [
    json_name = "isAudit",
    (gnostic.openapi.v3.property) = {description: "是否已盘点"}
  ]; // 是否已盘点
}

message PostMyWmsAuditPlanDetailRequest {
  string id = 1;
  string remark = 2 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  int32 status = 3 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "状态：0 未找到, 1 找到, -1 异常"}
  ]; // 状态：0 未找到, 1 找到, -1 异常
}