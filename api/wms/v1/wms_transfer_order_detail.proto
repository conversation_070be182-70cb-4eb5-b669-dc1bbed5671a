syntax = "proto3";

package wms.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";

service WmsTransferOrderDetailService {
  // 获取调拨单明细列表
  rpc ListWmsTransferOrderDetail(ListWmsTransferOrderDetailRequest) returns (ListWmsTransferOrderDetailResponse) {
    option (google.api.http) = {get: "/wms/v1/wmsTransferOrderDetail"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["调拨管理"]
    };
  }

  // 获取调拨单明细详情
  rpc GetWmsTransferOrderDetail(GetWmsTransferOrderDetailRequest) returns (WmsTransferOrderDetail) {
    option (google.api.http) = {get: "/wms/v1/wmsTransferOrderDetail/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["调拨管理"]
    };
  }

  // 创建调拨单明细
  rpc CreateWmsTransferOrderDetail(CreateWmsTransferOrderDetailRequest) returns (WmsTransferOrderDetail) {
    option (google.api.http) = {
      post: "/wms/v1/wmsTransferOrderDetail"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["调拨管理"]
    };
  }

  // 更新调拨单明细
  rpc UpdateWmsTransferOrderDetail(UpdateWmsTransferOrderDetailRequest) returns (WmsTransferOrderDetail) {
    option (google.api.http) = {
      put: "/wms/v1/wmsTransferOrderDetail/{id}"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["调拨管理"]
    };
  }

  // 删除调拨单明细
  rpc DeleteWmsTransferOrderDetail(DeleteWmsTransferOrderDetailRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/wms/v1/wmsTransferOrderDetail/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["调拨管理"]
    };
  }

  // 批量删除调拨单明细
  rpc MultiDeleteWmsTransferOrderDetail(MultiDeleteWmsTransferOrderDetailRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/wms/v1/wmsTransferOrderDetail/delete"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["调拨管理"]
    };
  }
}

message WmsTransferOrderDetail {
  string id = 1;
  optional string created_at = 2 [
    json_name = "createdAt",
    (gnostic.openapi.v3.property) = {description: "创建时间"}
  ]; // 创建时间
  optional string updated_at = 3 [
    json_name = "updatedAt",
    (gnostic.openapi.v3.property) = {description: "更新时间"}
  ]; // 更新时间
  optional string created_by = 4 [
    json_name = "createdBy",
    (gnostic.openapi.v3.property) = {description: "创建人"}
  ]; // 创建人
  optional string updated_by = 5 [
    json_name = "updatedBy",
    (gnostic.openapi.v3.property) = {description: "更新人"}
  ]; // 更新人
  optional string remark = 6 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string order_id = 7 [
    json_name = "orderId",
    (gnostic.openapi.v3.property) = {description: "调拨单"}
  ]; // 调拨单
  optional string material_id = 8 [
    json_name = "materialId",
    (gnostic.openapi.v3.property) = {description: "物料"}
  ]; // 物料
  optional string material_name = 9 [
    json_name = "materialName",
    (gnostic.openapi.v3.property) = {description: "物料名称"}
  ]; // 物料名称
  optional string code = 10 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "编码"}
  ]; // 编码
  optional string equipment_id = 11 [
    json_name = "equipmentId",
    (gnostic.openapi.v3.property) = {description: "装备"}
  ]; // 装备
  optional string equipment_type_id = 12 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "装备类型"}
  ]; // 装备类型
  optional google.protobuf.Struct feature = 13 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格
  optional string repository_id = 14 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional string repository_area_id = 15 [
    json_name = "repositoryAreaId",
    (gnostic.openapi.v3.property) = {description: "库区"}
  ]; // 库区
  optional string repository_position_id = 16 [
    json_name = "repositoryPositionId",
    (gnostic.openapi.v3.property) = {description: "库位"}
  ]; // 库位
  optional string owner_id = 17 [
    json_name = "ownerId",
    (gnostic.openapi.v3.property) = {description: "归属人"}
  ]; // 归属人
  optional string model_no = 18 [
    json_name = "ModelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string measure_unit_id = 19 [
    json_name = "measureUnitId",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位
  optional uint32 num = 20 [
    json_name = "num",
    (gnostic.openapi.v3.property) = {description: "调拨数量"}
  ]; // 调拨数量
  optional string transfer_reason = 21 [
    json_name = "transferReason",
    (gnostic.openapi.v3.property) = {description: "调拨原因"}
  ]; // 调拨原因
  optional string transfer_time = 22 [
    json_name = "transferTime",
    (gnostic.openapi.v3.property) = {description: "调拨时间"}
  ]; // 调拨时间

  optional string feature_str = 23 [
    json_name = "featureStr",
    (gnostic.openapi.v3.property) = {description: "详细规格字符串"}
  ]; // 详细规格字符串
}

message ListWmsTransferOrderDetailResponse {
  repeated WmsTransferOrderDetail items = 1;
  int32 total = 2;
}

message GetWmsTransferOrderDetailRequest {
  string id = 1;
}

message CreateWmsTransferOrderDetailRequest {
  string remark = 1 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string equipment_type_id = 2 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "装备类型"}
  ]; // 装备类型
  optional string repository_id = 3 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional google.protobuf.Struct feature = 4 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格
  optional string order_id = 5 [
    json_name = "orderId",
    (gnostic.openapi.v3.property) = {description: "调拨单"}
  ]; // 调拨单
  optional string equipment_id = 6 [
    json_name = "equipmentId",
    (gnostic.openapi.v3.property) = {description: "装备"}
  ]; // 装备
  optional string owner_id = 7 [
    json_name = "ownerId",
    (gnostic.openapi.v3.property) = {description: "归属人"}
  ]; // 归属人
  optional string measure_unit_id = 8 [
    json_name = "measureUnitId",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位
  optional string material_id = 9 [
    json_name = "materialId",
    (gnostic.openapi.v3.property) = {description: "物料"}
  ]; // 物料
  optional string repository_area_id = 10 [
    json_name = "repositoryAreaId",
    (gnostic.openapi.v3.property) = {description: "库区"}
  ]; // 库区
  optional string transfer_reason = 11 [
    json_name = "transferReason",
    (gnostic.openapi.v3.property) = {description: "调拨原因"}
  ]; // 调拨原因
  optional string model_no = 12 [
    json_name = "ModelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string code = 13 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "编码"}
  ]; // 编码
  optional uint32 num = 14 [
    json_name = "num",
    (gnostic.openapi.v3.property) = {description: "调拨数量"}
  ]; // 调拨数量
  optional string material_name = 15 [
    json_name = "materialName",
    (gnostic.openapi.v3.property) = {description: "物料名称"}
  ]; // 物料名称
  optional string repository_position_id = 16 [
    json_name = "repositoryPositionId",
    (gnostic.openapi.v3.property) = {description: "库位"}
  ]; // 库位
  optional string transfer_time = 17 [
    json_name = "transferTime",
    (gnostic.openapi.v3.property) = {description: "调拨时间"}
  ]; // 调拨时间
}

message UpdateWmsTransferOrderDetailRequest {
  string id = 1;
  optional string remark = 2 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string order_id = 3 [
    json_name = "orderId",
    (gnostic.openapi.v3.property) = {description: "调拨单"}
  ]; // 调拨单
  optional string material_id = 4 [
    json_name = "materialId",
    (gnostic.openapi.v3.property) = {description: "物料"}
  ]; // 物料
  optional string material_name = 5 [
    json_name = "materialName",
    (gnostic.openapi.v3.property) = {description: "物料名称"}
  ]; // 物料名称
  optional string code = 6 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "编码"}
  ]; // 编码
  optional string equipment_id = 7 [
    json_name = "equipmentId",
    (gnostic.openapi.v3.property) = {description: "装备"}
  ]; // 装备
  optional string equipment_type_id = 8 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "装备类型"}
  ]; // 装备类型
  optional google.protobuf.Struct feature = 9 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格
  optional string repository_id = 10 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional string repository_area_id = 11 [
    json_name = "repositoryAreaId",
    (gnostic.openapi.v3.property) = {description: "库区"}
  ]; // 库区
  optional string repository_position_id = 12 [
    json_name = "repositoryPositionId",
    (gnostic.openapi.v3.property) = {description: "库位"}
  ]; // 库位
  optional string owner_id = 13 [
    json_name = "ownerId",
    (gnostic.openapi.v3.property) = {description: "归属人"}
  ]; // 归属人
  optional string model_no = 14 [
    json_name = "ModelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string measure_unit_id = 15 [
    json_name = "measureUnitId",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位
  optional uint32 num = 16 [
    json_name = "num",
    (gnostic.openapi.v3.property) = {description: "调拨数量"}
  ]; // 调拨数量
  optional string transfer_reason = 17 [
    json_name = "transferReason",
    (gnostic.openapi.v3.property) = {description: "调拨原因"}
  ]; // 调拨原因
  optional string transfer_time = 18 [
    json_name = "transferTime",
    (gnostic.openapi.v3.property) = {description: "调拨时间"}
  ]; // 调拨时间
}

message DeleteWmsTransferOrderDetailRequest {
  string id = 1;
}

message MultiDeleteWmsTransferOrderDetailRequest {
  repeated string ids = 1;
}

message ListWmsTransferOrderDetailRequest {
  // 当前页码
  optional int32 page = 1 [
    json_name = "page",
    (gnostic.openapi.v3.property) = {
      description: "当前页码",
      default: {number: 1}
    }
  ];

  // 每页的行数
  optional int32 page_size = 2 [
    json_name = "pageSize",
    (gnostic.openapi.v3.property) = {
      description: "每一页的行数",
      default: {number: 10}
    }
  ];

  // 与过滤参数
  optional string query = 3 [
    json_name = "query",
    (gnostic.openapi.v3.property) = {
      description: "与过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 或过滤参数
  optional string or_query = 4 [
    json_name = "or",
    (gnostic.openapi.v3.property) = {
      description: "或过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 排序条件
  string order_by = 5 [
    json_name = "orderBy",
    (gnostic.openapi.v3.property) = {
      description: "排序条件，字段名前加'-'为降序，否则为升序。"
      example: {yaml: "{\"val1\", \"-val2\"}"}
    }
  ];

  // 是否不分页
  optional bool no_paging = 6 [
    json_name = "nopaging",
    (gnostic.openapi.v3.property) = {description: "是否不分页"}
  ];

  // 字段掩码
  google.protobuf.FieldMask field_mask = 7 [
    json_name = "fieldMask",
    (gnostic.openapi.v3.property) = {
      description: "字段掩码，如果为空则选中所有字段。",
      example: {yaml: "id,realName,userName"}
    }
  ];
}
