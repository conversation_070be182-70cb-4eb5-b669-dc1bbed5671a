syntax = "proto3";

package wms.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";
import "wms/v1/wms_discard_order_detail.proto";

service WmsDiscardOrderService {
  // 获取报废单列表
  rpc ListWmsDiscardOrder(ListWmsDiscardOrderRequest) returns (ListWmsDiscardOrderResponse) {
    option (google.api.http) = {get: "/wms/v1/wmsDiscardOrder"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["报废管理"]
    };
  }

  // 获取报废单详情
  rpc GetWmsDiscardOrder(GetWmsDiscardOrderRequest) returns (WmsDiscardOrder) {
    option (google.api.http) = {get: "/wms/v1/wmsDiscardOrder/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["报废管理"]
    };
  }

  // 创建报废单
  rpc CreateWmsDiscardOrder(CreateWmsDiscardOrderRequest) returns (WmsDiscardOrder) {
    option (google.api.http) = {
      post: "/wms/v1/wmsDiscardOrder"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["报废管理"]
    };
  }

  // 更新报废单
  rpc UpdateWmsDiscardOrder(UpdateWmsDiscardOrderRequest) returns (WmsDiscardOrder) {
    option (google.api.http) = {
      put: "/wms/v1/wmsDiscardOrder/{id}"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["报废管理"]
    };
  }

  // 删除报废单
  rpc DeleteWmsDiscardOrder(DeleteWmsDiscardOrderRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/wms/v1/wmsDiscardOrder/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["报废管理"]
    };
  }

  // 批量删除报废单
  rpc MultiDeleteWmsDiscardOrder(MultiDeleteWmsDiscardOrderRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/wms/v1/wmsDiscardOrder/delete"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["报废管理"]
    };
  }
}

message WmsDiscardOrder {
  string id = 1;
  optional string created_at = 2 [
    json_name = "createdAt",
    (gnostic.openapi.v3.property) = {description: "创建时间"}
  ]; // 创建时间
  optional string updated_at = 3 [
    json_name = "updatedAt",
    (gnostic.openapi.v3.property) = {description: "更新时间"}
  ]; // 更新时间
  optional string created_by = 4 [
    json_name = "createdBy",
    (gnostic.openapi.v3.property) = {description: "创建人"}
  ]; // 创建人
  optional string updated_by = 5 [
    json_name = "updatedBy",
    (gnostic.openapi.v3.property) = {description: "更新人"}
  ]; // 更新人
  optional string remark = 6 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string contract_urls = 7 [
    json_name = "contractUrls",
    (gnostic.openapi.v3.property) = {description: "合同附件"}
  ]; // 合同附件
  optional string invoice_urls = 8 [
    json_name = "invoiceUrls",
    (gnostic.openapi.v3.property) = {description: "发票附件"}
  ]; // 发票附件
  optional string audit_urls = 9 [
    json_name = "auditUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string other_urls = 10 [
    json_name = "otherUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string order_no = 11 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "报废单号"}
  ]; // 报废单号
  optional string status = 12 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "报废状态"}
  ]; // 报废状态
  optional uint32 equipment_num = 13 [
    json_name = "equipmentNum",
    (gnostic.openapi.v3.property) = {description: "设备数量"}
  ]; // 设备数量

  repeated WmsDiscardOrderDetail details = 14 [
    json_name = "details",
    (gnostic.openapi.v3.property) = {description: "报废单明细"}
  ]; // 报废单明细

  optional string create_by_organization_name = 15 [
    json_name = "createByOrganizationName",
    (gnostic.openapi.v3.property) = {description: "创建人部门"}
  ]; // 创建人部门
}

message ListWmsDiscardOrderResponse {
  repeated WmsDiscardOrder items = 1;
  int32 total = 2;
}

message GetWmsDiscardOrderRequest {
  string id = 1;
}

message CreateWmsDiscardOrderRequest {
  string remark = 1 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string contract_urls = 2 [
    json_name = "contractUrls",
    (gnostic.openapi.v3.property) = {description: "合同附件"}
  ]; // 合同附件
  optional string invoice_urls = 3 [
    json_name = "invoiceUrls",
    (gnostic.openapi.v3.property) = {description: "发票附件"}
  ]; // 发票附件
  optional string audit_urls = 4 [
    json_name = "auditUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string other_urls = 5 [
    json_name = "otherUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string order_no = 6 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "报废单号"}
  ]; // 报废单号
  optional string status = 7 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "报废状态"}
  ]; // 报废状态
  optional uint32 equipment_num = 8 [
    json_name = "equipmentNum",
    (gnostic.openapi.v3.property) = {description: "设备数量"}
  ]; // 设备数量

  repeated CreateWmsDiscardOrderDetailRequest details = 9 [
    json_name = "details",
    (gnostic.openapi.v3.property) = {description: "报废单明细"}
  ]; // 报废单明细
}

message UpdateWmsDiscardOrderRequest {
  string id = 1;
  optional string remark = 2 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string contract_urls = 3 [
    json_name = "contractUrls",
    (gnostic.openapi.v3.property) = {description: "合同附件"}
  ]; // 合同附件
  optional string invoice_urls = 4 [
    json_name = "invoiceUrls",
    (gnostic.openapi.v3.property) = {description: "发票附件"}
  ]; // 发票附件
  optional string audit_urls = 5 [
    json_name = "auditUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string other_urls = 6 [
    json_name = "otherUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string order_no = 7 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "报废单号"}
  ]; // 报废单号
  optional string status = 8 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "报废状态"}
  ]; // 报废状态
  optional uint32 equipment_num = 9 [
    json_name = "equipmentNum",
    (gnostic.openapi.v3.property) = {description: "设备数量"}
  ]; // 设备数量

  repeated CreateWmsDiscardOrderDetailRequest details = 10 [
    json_name = "details",
    (gnostic.openapi.v3.property) = {description: "报废单明细"}
  ]; // 报废单明细
}

message DeleteWmsDiscardOrderRequest {
  string id = 1;
}

message MultiDeleteWmsDiscardOrderRequest {
  repeated string ids = 1;
}

message ListWmsDiscardOrderRequest {
  // 当前页码
  optional int32 page = 1 [
    json_name = "page",
    (gnostic.openapi.v3.property) = {
      description: "当前页码",
      default: {number: 1}
    }
  ];

  // 每页的行数
  optional int32 page_size = 2 [
    json_name = "pageSize",
    (gnostic.openapi.v3.property) = {
      description: "每一页的行数",
      default: {number: 10}
    }
  ];

  // 与过滤参数
  optional string query = 3 [
    json_name = "query",
    (gnostic.openapi.v3.property) = {
      description: "与过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 或过滤参数
  optional string or_query = 4 [
    json_name = "or",
    (gnostic.openapi.v3.property) = {
      description: "或过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 排序条件
  string order_by = 5 [
    json_name = "orderBy",
    (gnostic.openapi.v3.property) = {
      description: "排序条件，字段名前加'-'为降序，否则为升序。"
      example: {yaml: "{\"val1\", \"-val2\"}"}
    }
  ];

  // 是否不分页
  optional bool no_paging = 6 [
    json_name = "nopaging",
    (gnostic.openapi.v3.property) = {description: "是否不分页"}
  ];

  // 字段掩码
  google.protobuf.FieldMask field_mask = 7 [
    json_name = "fieldMask",
    (gnostic.openapi.v3.property) = {
      description: "字段掩码，如果为空则选中所有字段。",
      example: {yaml: "id,realName,userName"}
    }
  ];
}
