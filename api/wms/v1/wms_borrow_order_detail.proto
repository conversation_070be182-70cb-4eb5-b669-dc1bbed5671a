syntax = "proto3";

package wms.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";

service WmsBorrowOrderDetailService {
  // 获取借用单明细列表
  rpc ListWmsBorrowOrderDetail(ListWmsBorrowOrderDetailRequest) returns (ListWmsBorrowOrderDetailResponse) {
    option (google.api.http) = {get: "/wms/v1/wmsBorrowOrderDetail"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "借用"
      ]
    };
  }

  // 获取借用单明细详情
  rpc GetWmsBorrowOrderDetail(GetWmsBorrowOrderDetailRequest) returns (WmsBorrowOrderDetail) {
    option (google.api.http) = {get: "/wms/v1/wmsBorrowOrderDetail/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "借用"
      ]
    };
  }

  // 创建借用单明细
  rpc CreateWmsBorrowOrderDetail(CreateWmsBorrowOrderDetailRequest) returns (WmsBorrowOrderDetail) {
    option (google.api.http) = {
      post: "/wms/v1/wmsBorrowOrderDetail"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "借用"
      ]
    };
  }

  // 更新借用单明细
  rpc UpdateWmsBorrowOrderDetail(UpdateWmsBorrowOrderDetailRequest) returns (WmsBorrowOrderDetail) {
    option (google.api.http) = {
      put: "/wms/v1/wmsBorrowOrderDetail/{id}"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "借用"
      ]
    };
  }

  // 删除借用单明细
  rpc DeleteWmsBorrowOrderDetail(DeleteWmsBorrowOrderDetailRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/wms/v1/wmsBorrowOrderDetail/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "借用"
      ]
    };
  }

  // 批量删除借用单明细
  rpc MultiDeleteWmsBorrowOrderDetail(MultiDeleteWmsBorrowOrderDetailRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/wms/v1/wmsBorrowOrderDetail/delete"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "借用"
      ]
    };
  }
}

message WmsBorrowOrderDetail {
  string id = 1;
  optional string created_at = 2 [
    json_name = "createdAt",
    (gnostic.openapi.v3.property) = {description: "创建时间"}
  ]; // 创建时间
  optional string updated_at = 3 [
    json_name = "updatedAt",
    (gnostic.openapi.v3.property) = {description: "更新时间"}
  ]; // 更新时间
  optional string created_by = 4 [
    json_name = "createdBy",
    (gnostic.openapi.v3.property) = {description: "创建人"}
  ]; // 创建人
  optional string updated_by = 5 [
    json_name = "updatedBy",
    (gnostic.openapi.v3.property) = {description: "更新人"}
  ]; // 更新人
  optional string remark = 6 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string order_id = 7 [
    json_name = "orderId",
    (gnostic.openapi.v3.property) = {description: "借用单"}
  ]; // 借用单
  optional string equipment_type_id = 8 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "设备类型"}
  ]; // 设备类型
  optional string equipment_id = 9 [
    json_name = "equipmentId",
    (gnostic.openapi.v3.property) = {description: "设备"}
  ]; // 设备
  optional string name = 10 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "设备名称"}
  ]; // 设备名称
  optional string model_no = 11 [
    json_name = "modelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string measure_unit_id = 12 [
    json_name = "measureUnitId",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位
  optional uint32 num = 13 [
    json_name = "num",
    (gnostic.openapi.v3.property) = {description: "数量"}
  ]; // 数量
  optional string repository_id = 14 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional string return_time = 15 [
    json_name = "returnTime",
    (gnostic.openapi.v3.property) = {description: "归还时间"}
  ]; // 归还时间
  optional bool is_return = 16 [
    json_name = "isReturn",
    (gnostic.openapi.v3.property) = {description: "是否归还"}
  ]; // 是否归还
  optional google.protobuf.Struct feature = 17 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格
  optional string feature_str = 18 [
    json_name = "featureStr",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格
}

message ListWmsBorrowOrderDetailResponse {
  repeated WmsBorrowOrderDetail items = 1;
  int32 total = 2;
}

message GetWmsBorrowOrderDetailRequest {
  string id = 1;
}

message CreateWmsBorrowOrderDetailRequest {
  string remark = 1 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string name = 2 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "设备名称"}
  ]; // 设备名称
  optional uint32 num = 3 [
    json_name = "num",
    (gnostic.openapi.v3.property) = {description: "数量"}
  ]; // 数量
  optional bool is_return = 4 [
    json_name = "isReturn",
    (gnostic.openapi.v3.property) = {description: "是否归还"}
  ]; // 是否归还
  optional string order_id = 5 [
    json_name = "orderId",
    (gnostic.openapi.v3.property) = {description: "借用单"}
  ]; // 借用单
  optional string equipment_type_id = 6 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "设备类型"}
  ]; // 设备类型
  optional string measure_unit_id = 7 [
    json_name = "measureUnitId",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位
  optional string model_no = 8 [
    json_name = "modelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string repository_id = 9 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional string return_time = 10 [
    json_name = "returnTime",
    (gnostic.openapi.v3.property) = {description: "归还时间"}
  ]; // 归还时间
  optional string equipment_id = 11 [
    json_name = "equipmentId",
    (gnostic.openapi.v3.property) = {description: "设备"}
  ]; // 设备
  optional google.protobuf.Struct feature = 12 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格
}

message UpdateWmsBorrowOrderDetailRequest {
  string id = 1;
  optional string remark = 2 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string order_id = 3 [
    json_name = "orderId",
    (gnostic.openapi.v3.property) = {description: "借用单"}
  ]; // 借用单
  optional string equipment_type_id = 4 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "设备类型"}
  ]; // 设备类型
  optional string equipment_id = 5 [
    json_name = "equipmentId",
    (gnostic.openapi.v3.property) = {description: "设备"}
  ]; // 设备
  optional string name = 6 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "设备名称"}
  ]; // 设备名称
  optional string model_no = 7 [
    json_name = "modelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string measure_unit_id = 8 [
    json_name = "measureUnitId",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位
  optional uint32 num = 9 [
    json_name = "num",
    (gnostic.openapi.v3.property) = {description: "数量"}
  ]; // 数量
  optional string repository_id = 10 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional string return_time = 11 [
    json_name = "returnTime",
    (gnostic.openapi.v3.property) = {description: "归还时间"}
  ]; // 归还时间
  optional bool is_return = 12 [
    json_name = "isReturn",
    (gnostic.openapi.v3.property) = {description: "是否归还"}
  ]; // 是否归还
  optional google.protobuf.Struct feature = 13 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格
}

message DeleteWmsBorrowOrderDetailRequest {
  string id = 1;
}

message MultiDeleteWmsBorrowOrderDetailRequest {
  repeated string ids = 1;
}

message ListWmsBorrowOrderDetailRequest {
  // 当前页码
  optional int32 page = 1 [
    json_name = "page",
    (gnostic.openapi.v3.property) = {
      description: "当前页码",
      default: {number: 1}
    }
  ];

  // 每页的行数
  optional int32 page_size = 2 [
    json_name = "pageSize",
    (gnostic.openapi.v3.property) = {
      description: "每一页的行数",
      default: {number: 10}
    }
  ];

  // 与过滤参数
  optional string query = 3 [
    json_name = "query",
    (gnostic.openapi.v3.property) = {
      description: "与过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 或过滤参数
  optional string or_query = 4 [
    json_name = "or",
    (gnostic.openapi.v3.property) = {
      description: "或过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 排序条件
  string order_by = 5 [
    json_name = "orderBy",
    (gnostic.openapi.v3.property) = {
      description: "排序条件，字段名前加'-'为降序，否则为升序。"
      example: {yaml: "{\"val1\", \"-val2\"}"}
    }
  ];

  // 是否不分页
  optional bool no_paging = 6 [
    json_name = "nopaging",
    (gnostic.openapi.v3.property) = {description: "是否不分页"}
  ];

  // 字段掩码
  google.protobuf.FieldMask field_mask = 7 [
    json_name = "fieldMask",
    (gnostic.openapi.v3.property) = {
      description: "字段掩码，如果为空则选中所有字段。",
      example: {yaml: "id,realName,userName"}
    }
  ];
}
