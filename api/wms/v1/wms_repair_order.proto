syntax = "proto3";

package wms.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";
import "wms/v1/wms_repair_order_detail.proto";

service WmsRepairOrderService {
  // 获取维修单列表
  rpc ListWmsRepairOrder(ListWmsRepairOrderRequest) returns (ListWmsRepairOrderResponse) {
    option (google.api.http) = {get: "/wms/v1/wmsRepairOrder"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["维修管理"]
    };
  }

  // 获取维修单详情
  rpc GetWmsRepairOrder(GetWmsRepairOrderRequest) returns (WmsRepairOrder) {
    option (google.api.http) = {get: "/wms/v1/wmsRepairOrder/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["维修管理"]
    };
  }

  // 创建维修单
  rpc CreateWmsRepairOrder(CreateWmsRepairOrderRequest) returns (WmsRepairOrder) {
    option (google.api.http) = {
      post: "/wms/v1/wmsRepairOrder"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["维修管理"]
    };
  }

  // 更新维修单
  rpc UpdateWmsRepairOrder(UpdateWmsRepairOrderRequest) returns (WmsRepairOrder) {
    option (google.api.http) = {
      put: "/wms/v1/wmsRepairOrder/{id}"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["维修管理"]
    };
  }

  // 删除维修单
  rpc DeleteWmsRepairOrder(DeleteWmsRepairOrderRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/wms/v1/wmsRepairOrder/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["维修管理"]
    };
  }

  // 批量删除维修单
  rpc MultiDeleteWmsRepairOrder(MultiDeleteWmsRepairOrderRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/wms/v1/wmsRepairOrder/delete"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["维修管理"]
    };
  }
}

message WmsRepairOrder {
  string id = 1;
  optional string created_at = 2 [
    json_name = "createdAt",
    (gnostic.openapi.v3.property) = {description: "创建时间"}
  ]; // 创建时间
  optional string updated_at = 3 [
    json_name = "updatedAt",
    (gnostic.openapi.v3.property) = {description: "更新时间"}
  ]; // 更新时间
  optional string created_by = 4 [
    json_name = "createdBy",
    (gnostic.openapi.v3.property) = {description: "创建人"}
  ]; // 创建人
  optional string updated_by = 5 [
    json_name = "updatedBy",
    (gnostic.openapi.v3.property) = {description: "更新人"}
  ]; // 更新人
  optional string remark = 6 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string order_no = 7 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "维修单号"}
  ]; // 维修单号
  optional uint32 equipment_num = 8 [
    json_name = "equipmentNum",
    (gnostic.openapi.v3.property) = {description: "装备数量"}
  ]; // 装备数量
  optional string status = 9 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "维修状态"}
  ]; // 维修状态
  optional string repair_time = 10 [
    json_name = "repairTime",
    (gnostic.openapi.v3.property) = {description: "维修时间"}
  ]; // 维修时间

  repeated WmsRepairOrderDetail details = 11 [
    json_name = "details",
    (gnostic.openapi.v3.property) = {description: "维修单详情"}
  ]; // 维修单详情

  optional string create_by_organization_name = 12 [
    json_name = "createByOrganizationName",
    (gnostic.openapi.v3.property) = {description: "创建人组织名称"}
  ]; // 创建人组织名称
}

message ListWmsRepairOrderResponse {
  repeated WmsRepairOrder items = 1;
  int32 total = 2;
}

message GetWmsRepairOrderRequest {
  string id = 1;
}

message CreateWmsRepairOrderRequest {
  string remark = 1 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string order_no = 2 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "维修单号"}
  ]; // 维修单号
  optional uint32 equipment_num = 3 [
    json_name = "equipmentNum",
    (gnostic.openapi.v3.property) = {description: "装备数量"}
  ]; // 装备数量
  optional string status = 4 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "维修状态"}
  ]; // 维修状态
  optional string repair_time = 5 [
    json_name = "repairTime",
    (gnostic.openapi.v3.property) = {description: "维修时间"}
  ]; // 维修时间

  repeated CreateWmsRepairOrderDetailRequest details = 11 [
    json_name = "details",
    (gnostic.openapi.v3.property) = {description: "维修单详情"}
  ]; // 维修单详情
}

message UpdateWmsRepairOrderRequest {
  string id = 1;
  optional string remark = 2 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string order_no = 3 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "维修单号"}
  ]; // 维修单号
  optional uint32 equipment_num = 4 [
    json_name = "equipmentNum",
    (gnostic.openapi.v3.property) = {description: "装备数量"}
  ]; // 装备数量
  optional string status = 5 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "维修状态"}
  ]; // 维修状态
  optional string repair_time = 6 [
    json_name = "repairTime",
    (gnostic.openapi.v3.property) = {description: "维修时间"}
  ]; // 维修时间

  repeated CreateWmsRepairOrderDetailRequest details = 11 [
    json_name = "details",
    (gnostic.openapi.v3.property) = {description: "维修单详情"}
  ]; // 维修单详情
}

message DeleteWmsRepairOrderRequest {
  string id = 1;
}

message MultiDeleteWmsRepairOrderRequest {
  repeated string ids = 1;
}

message ListWmsRepairOrderRequest {
  // 当前页码
  optional int32 page = 1 [
    json_name = "page",
    (gnostic.openapi.v3.property) = {
      description: "当前页码",
      default: {number: 1}
    }
  ];

  // 每页的行数
  optional int32 page_size = 2 [
    json_name = "pageSize",
    (gnostic.openapi.v3.property) = {
      description: "每一页的行数",
      default: {number: 10}
    }
  ];

  // 与过滤参数
  optional string query = 3 [
    json_name = "query",
    (gnostic.openapi.v3.property) = {
      description: "与过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 或过滤参数
  optional string or_query = 4 [
    json_name = "or",
    (gnostic.openapi.v3.property) = {
      description: "或过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 排序条件
  string order_by = 5 [
    json_name = "orderBy",
    (gnostic.openapi.v3.property) = {
      description: "排序条件，字段名前加'-'为降序，否则为升序。"
      example: {yaml: "{\"val1\", \"-val2\"}"}
    }
  ];

  // 是否不分页
  optional bool no_paging = 6 [
    json_name = "nopaging",
    (gnostic.openapi.v3.property) = {description: "是否不分页"}
  ];

  // 字段掩码
  google.protobuf.FieldMask field_mask = 7 [
    json_name = "fieldMask",
    (gnostic.openapi.v3.property) = {
      description: "字段掩码，如果为空则选中所有字段。",
      example: {yaml: "id,realName,userName"}
    }
  ];
}
