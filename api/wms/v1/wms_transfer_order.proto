syntax = "proto3";

package wms.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";
import "wms/v1/wms_transfer_order_detail.proto";

service WmsTransferOrderService {
  // 获取调拨单列表
  rpc ListWmsTransferOrder(ListWmsTransferOrderRequest) returns (ListWmsTransferOrderResponse) {
    option (google.api.http) = {get: "/wms/v1/wmsTransferOrder"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["调拨管理"]
    };
  }

  // 获取调拨单详情
  rpc GetWmsTransferOrder(GetWmsTransferOrderRequest) returns (WmsTransferOrder) {
    option (google.api.http) = {get: "/wms/v1/wmsTransferOrder/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["调拨管理"]
    };
  }

  // 创建调拨单
  rpc CreateWmsTransferOrder(CreateWmsTransferOrderRequest) returns (WmsTransferOrder) {
    option (google.api.http) = {
      post: "/wms/v1/wmsTransferOrder"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["调拨管理"]
    };
  }

  // 更新调拨单
  rpc UpdateWmsTransferOrder(UpdateWmsTransferOrderRequest) returns (WmsTransferOrder) {
    option (google.api.http) = {
      put: "/wms/v1/wmsTransferOrder/{id}"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["调拨管理"]
    };
  }

  // 删除调拨单
  rpc DeleteWmsTransferOrder(DeleteWmsTransferOrderRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/wms/v1/wmsTransferOrder/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["调拨管理"]
    };
  }

  // 批量删除调拨单
  rpc MultiDeleteWmsTransferOrder(MultiDeleteWmsTransferOrderRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/wms/v1/wmsTransferOrder/delete"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["调拨管理"]
    };
  }
}

message WmsTransferOrder {
  string id = 1;
  optional string created_at = 2 [
    json_name = "createdAt",
    (gnostic.openapi.v3.property) = {description: "创建时间"}
  ]; // 创建时间
  optional string updated_at = 3 [
    json_name = "updatedAt",
    (gnostic.openapi.v3.property) = {description: "更新时间"}
  ]; // 更新时间
  optional string created_by = 4 [
    json_name = "createdBy",
    (gnostic.openapi.v3.property) = {description: "创建人"}
  ]; // 创建人
  optional string updated_by = 5 [
    json_name = "updatedBy",
    (gnostic.openapi.v3.property) = {description: "更新人"}
  ]; // 更新人
  optional string remark = 6 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string contract_urls = 7 [
    json_name = "contractUrls",
    (gnostic.openapi.v3.property) = {description: "合同附件"}
  ]; // 合同附件
  optional string invoice_urls = 8 [
    json_name = "invoiceUrls",
    (gnostic.openapi.v3.property) = {description: "发票附件"}
  ]; // 发票附件
  optional string audit_urls = 9 [
    json_name = "auditUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string other_urls = 10 [
    json_name = "otherUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string order_no = 11 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "调拨单号"}
  ]; // 调拨单号
  optional uint32 equipment_num = 12 [
    json_name = "equipmentNum",
    (gnostic.openapi.v3.property) = {description: "装备数量"}
  ]; // 装备数量
  optional string from_repository_id = 13 [
    json_name = "fromRepositoryId",
    (gnostic.openapi.v3.property) = {description: "调出仓库"}
  ]; // 调出仓库
  optional string to_repository_id = 14 [
    json_name = "toRepositoryId",
    (gnostic.openapi.v3.property) = {description: "调入仓库"}
  ]; // 调入仓库
  optional string status = 15 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "调拨状态"}
  ]; // 调拨状态
  optional string transfer_time = 16 [
    json_name = "transferTime",
    (gnostic.openapi.v3.property) = {description: "调拨时间"}
  ]; // 调拨时间

  repeated WmsTransferOrderDetail details = 17 [
    json_name = "details",
    (gnostic.openapi.v3.property) = {description: "调拨单明细"}
  ]; // 调拨单明细

  optional string create_by_organization_name = 18 [
    json_name = "createByOrganizationName",
    (gnostic.openapi.v3.property) = {description: "创建人部门"}
  ]; // 创建人部门
}

message ListWmsTransferOrderResponse {
  repeated WmsTransferOrder items = 1;
  int32 total = 2;
}

message GetWmsTransferOrderRequest {
  string id = 1;
}

message CreateWmsTransferOrderRequest {
  string remark = 1 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string invoice_urls = 2 [
    json_name = "invoiceUrls",
    (gnostic.openapi.v3.property) = {description: "发票附件"}
  ]; // 发票附件
  optional string other_urls = 3 [
    json_name = "otherUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string contract_urls = 4 [
    json_name = "contractUrls",
    (gnostic.openapi.v3.property) = {description: "合同附件"}
  ]; // 合同附件
  optional string audit_urls = 5 [
    json_name = "auditUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string order_no = 6 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "调拨单号"}
  ]; // 调拨单号
  optional uint32 equipment_num = 7 [
    json_name = "equipmentNum",
    (gnostic.openapi.v3.property) = {description: "装备数量"}
  ]; // 装备数量
  optional string from_repository_id = 8 [
    json_name = "fromRepositoryId",
    (gnostic.openapi.v3.property) = {description: "调出仓库"}
  ]; // 调出仓库
  optional string to_repository_id = 9 [
    json_name = "toRepositoryId",
    (gnostic.openapi.v3.property) = {description: "调入仓库"}
  ]; // 调入仓库
  optional string status = 10 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "调拨状态"}
  ]; // 调拨状态
  optional string transfer_time = 11 [
    json_name = "transferTime",
    (gnostic.openapi.v3.property) = {description: "调拨时间"}
  ]; // 调拨时间

  repeated CreateWmsTransferOrderDetailRequest details = 13 [
    json_name = "details",
    (gnostic.openapi.v3.property) = {description: "调拨单明细"}
  ]; // 调拨单明细
}

message UpdateWmsTransferOrderRequest {
  string id = 1;
  optional string remark = 2 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string contract_urls = 3 [
    json_name = "contractUrls",
    (gnostic.openapi.v3.property) = {description: "合同附件"}
  ]; // 合同附件
  optional string invoice_urls = 4 [
    json_name = "invoiceUrls",
    (gnostic.openapi.v3.property) = {description: "发票附件"}
  ]; // 发票附件
  optional string audit_urls = 5 [
    json_name = "auditUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string other_urls = 6 [
    json_name = "otherUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string order_no = 7 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "调拨单号"}
  ]; // 调拨单号
  optional uint32 equipment_num = 8 [
    json_name = "equipmentNum",
    (gnostic.openapi.v3.property) = {description: "装备数量"}
  ]; // 装备数量
  optional string from_repository_id = 9 [
    json_name = "fromRepositoryId",
    (gnostic.openapi.v3.property) = {description: "调出仓库"}
  ]; // 调出仓库
  optional string to_repository_id = 10 [
    json_name = "toRepositoryId",
    (gnostic.openapi.v3.property) = {description: "调入仓库"}
  ]; // 调入仓库
  optional string status = 11 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "调拨状态"}
  ]; // 调拨状态
  optional string transfer_time = 12 [
    json_name = "transferTime",
    (gnostic.openapi.v3.property) = {description: "调拨时间"}
  ]; // 调拨时间

  repeated CreateWmsTransferOrderDetailRequest details = 13 [
    json_name = "details",
    (gnostic.openapi.v3.property) = {description: "调拨单明细"}
  ]; // 调拨单明细
}

message DeleteWmsTransferOrderRequest {
  string id = 1;
}

message MultiDeleteWmsTransferOrderRequest {
  repeated string ids = 1;
}

message ListWmsTransferOrderRequest {
  // 当前页码
  optional int32 page = 1 [
    json_name = "page",
    (gnostic.openapi.v3.property) = {
      description: "当前页码",
      default: {number: 1}
    }
  ];

  // 每页的行数
  optional int32 page_size = 2 [
    json_name = "pageSize",
    (gnostic.openapi.v3.property) = {
      description: "每一页的行数",
      default: {number: 10}
    }
  ];

  // 与过滤参数
  optional string query = 3 [
    json_name = "query",
    (gnostic.openapi.v3.property) = {
      description: "与过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 或过滤参数
  optional string or_query = 4 [
    json_name = "or",
    (gnostic.openapi.v3.property) = {
      description: "或过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 排序条件
  string order_by = 5 [
    json_name = "orderBy",
    (gnostic.openapi.v3.property) = {
      description: "排序条件，字段名前加'-'为降序，否则为升序。"
      example: {yaml: "{\"val1\", \"-val2\"}"}
    }
  ];

  // 是否不分页
  optional bool no_paging = 6 [
    json_name = "nopaging",
    (gnostic.openapi.v3.property) = {description: "是否不分页"}
  ];

  // 字段掩码
  google.protobuf.FieldMask field_mask = 7 [
    json_name = "fieldMask",
    (gnostic.openapi.v3.property) = {
      description: "字段掩码，如果为空则选中所有字段。",
      example: {yaml: "id,realName,userName"}
    }
  ];
}
