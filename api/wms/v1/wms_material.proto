syntax = "proto3";

package wms.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";
import "wms/v1/wms_equipment.proto";

service WmsMaterialService {
  // 获取物料列表
  rpc ListWmsMaterial(ListWmsMaterialRequest) returns (ListWmsMaterialResponse) {
    option (google.api.http) = {get: "/wms/v1/wmsMaterial"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "资产管理"
      ]
    };
  }

  // 获取物料详情
  rpc GetWmsMaterial(GetWmsMaterialRequest) returns (WmsMaterial) {
    option (google.api.http) = {get: "/wms/v1/wmsMaterial/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "资产管理"
      ]
    };
  }

  // 创建物料
  rpc CreateWmsMaterial(CreateWmsMaterialRequest) returns (WmsMaterial) {
    option (google.api.http) = {
      post: "/wms/v1/wmsMaterial"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "资产管理"
      ]
    };
  }

  // 更新物料
  rpc UpdateWmsMaterial(UpdateWmsMaterialRequest) returns (WmsMaterial) {
    option (google.api.http) = {
      put: "/wms/v1/wmsMaterial/{id}"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "资产管理"
      ]
    };
  }

  // 删除物料
  rpc DeleteWmsMaterial(DeleteWmsMaterialRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/wms/v1/wmsMaterial/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "资产管理"
      ]
    };
  }

  // 批量删除物料
  rpc MultiDeleteWmsMaterial(MultiDeleteWmsMaterialRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/wms/v1/wmsMaterial/delete"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "资产管理"
      ]
    };
  }

  // 获取物料详情
  rpc GetWmsMaterialByCode(GetWmsMaterialByCodeReqest) returns (WmsMaterial) {
    option (google.api.http) = {get: "/wms/v1/wmsMaterialByCode"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "资产管理"
      ]
    };
  }  

  // 获取物料详情，通过code、feature、repository_id、repository_area_id、repository_position_id
  rpc GetWmsMaterialByCodeFeatureRepository(GetWmsMaterialByCodeFeatureRepositoryRequest) returns (WmsMaterial) {
    option (google.api.http) = {get: "/wms/v1/wmsMaterialByCodeFeatureRepository"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "资产管理"
      ]
    };
  }

  // 获取物料列表
  rpc ListMyWmsMaterial(ListWmsMaterialRequest) returns (ListWmsMaterialResponse) {
    option (google.api.http) = {get: "/wms/v1/myWmsMaterial"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "资产管理"
      ]
    };
  }

  // 获取页面代码
  rpc GetWmsMaterialPageCode(GetWmsMaterialPageCodeRequest) returns (google.protobuf.Struct) {
    option (google.api.http) = {get: "/wms/v1/wmsMaterial/pageCode/get"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "仓库管理",
        "资产管理"
      ]
    };
  }
}

message WmsMaterial {
  string id = 1;
  optional string created_at = 2 [
    json_name = "createdAt",
    (gnostic.openapi.v3.property) = {description: "创建时间"}
  ]; // 创建时间
  optional string updated_at = 3 [
    json_name = "updatedAt",
    (gnostic.openapi.v3.property) = {description: "更新时间"}
  ]; // 更新时间
  optional string created_by = 4 [
    json_name = "createdBy",
    (gnostic.openapi.v3.property) = {description: "创建人"}
  ]; // 创建人
  optional string updated_by = 5 [
    json_name = "updatedBy",
    (gnostic.openapi.v3.property) = {description: "更新人"}
  ]; // 更新人
  optional int32 status = 6 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "状态：0 禁用、 1启用"}
  ]; // 状态：0 禁用、 1启用
  optional string remark = 7 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string fire_station_id = 8 [
    json_name = "fireStationId",
    (gnostic.openapi.v3.property) = {description: "消防站"}
  ]; // 消防站
  optional string material_type = 9 [
    json_name = "materialType",
    (gnostic.openapi.v3.property) = {description: "装备类型"}
  ]; // 装备类型
  optional string equipment_type_id = 10 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "装备分类"}
  ]; // 装备分类
  optional string equipment_id = 11 [
    json_name = "equipmentId",
    (gnostic.openapi.v3.property) = {description: "装备"}
  ]; // 装备
  optional string code = 12 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "编码"}
  ]; // 编码
  optional string code_type = 13 [
    json_name = "codeType",
    (gnostic.openapi.v3.property) = {description: "编码类型"}
  ]; // 编码类型
  optional string name = 14 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "装备名称"}
  ]; // 装备名称
  optional string model_no = 15 [
    json_name = "modelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string provider_id = 16 [
    json_name = "providerId",
    (gnostic.openapi.v3.property) = {description: "供应商"}
  ]; // 供应商
  optional uint32 num = 17 [
    json_name = "num",
    (gnostic.openapi.v3.property) = {description: "数量"}
  ]; // 数量
  optional double price = 18 [
    json_name = "price",
    (gnostic.openapi.v3.property) = {description: "单价"}
  ]; // 单价
  optional string repository_id = 19 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional string repository_area_id = 20 [
    json_name = "repositoryAreaId",
    (gnostic.openapi.v3.property) = {description: "库区"}
  ]; // 库区
  optional string repository_position_id = 21 [
    json_name = "repositoryPositionId",
    (gnostic.openapi.v3.property) = {description: "库位"}
  ]; // 库位
  optional string measure_unit_id = 22 [
    json_name = "measureUnitId",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位
  optional string expire_time = 23 [
    json_name = "expireTime",
    (gnostic.openapi.v3.property) = {description: "过期日期"}
  ]; // 过期日期
  optional int32 equipment_status = 24 [
    json_name = "equipmentStatus",
    (gnostic.openapi.v3.property) = {description: "装备状态"}
  ]; // 装备状态
  optional string owner_id = 25 [
    json_name = "ownerId",
    (gnostic.openapi.v3.property) = {description: "持有人"}
  ]; // 持有人  
  optional string equipment_type_name= 30 [
    json_name = "equipmentTypeName",
    (gnostic.openapi.v3.property) = {description: "装备分类名称"}
  ]; // 装备分类  
  optional string repository_name = 31 [
    json_name = "repositoryName",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional string repository_area_name = 32 [
    json_name = "repositoryAreaName",
    (gnostic.openapi.v3.property) = {description: "库区"}
  ]; // 库区
  optional string repository_position_name = 33 [
    json_name = "repositoryPositionName",
    (gnostic.openapi.v3.property) = {description: "库位"}
  ]; // 库位  
  optional string measure_unit_name = 34 [
    json_name = "measureUnitName",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位    

  optional string fire_station_name = 100 [
    json_name = "fireStationName",
    (gnostic.openapi.v3.property) = {description: "消防站名称"}
  ]; // 消防站名称      
  optional WmsEquipment equipment = 101 [
    json_name = "equipment",
    (gnostic.openapi.v3.property) = {description: "装备"}
  ]; // 装备
  optional bool is_one_material_one_code = 102 [
    json_name = "isOneMaterialOneCode",
    (gnostic.openapi.v3.property) = {description: "是否一物一码"}
  ]; // 是否一物一码
  optional int32 discard_method = 103 [
    json_name = "discardMethod",
    (gnostic.openapi.v3.property) = {description: "报废方式"}
  ]; // 报废方式
  optional google.protobuf.Struct feature = 104 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格
  optional string original_id = 105 [
    json_name = "originalId",
    (gnostic.openapi.v3.property) = {description: "原始ID"}
  ]; // 原始ID
  optional string key = 106 [
    json_name = "key",
    (gnostic.openapi.v3.property) = {description: "key"}
  ]; // key
  optional string feature_str = 107 [
    json_name = "featureStr",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格
  optional string finance_system_no = 108 [
    json_name = "financeSystemNo",
    (gnostic.openapi.v3.property) = {description: "财务系统编号"}
  ]; // 财务系统编号
  optional string use_start_time = 109 [
    json_name = "useStartTime",
    (gnostic.openapi.v3.property) = {description: "使用开始时间"}
  ]; // 使用开始时间
}

message ListWmsMaterialResponse {
  repeated WmsMaterial items = 1;
  int32 total = 2;
}

message GetWmsMaterialRequest {
  string id = 1;
}

message CreateWmsMaterialRequest {
  int32 status = 1 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "状态：0 禁用、 1启用"}
  ]; // 状态：0 禁用、 1启用
  string remark = 2 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string name = 3 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "装备名称"}
  ]; // 装备名称
  optional string material_type = 4 [
    json_name = "materialType",
    (gnostic.openapi.v3.property) = {description: "装备类型"}
  ]; // 装备类型
  optional string expire_time = 5 [
    json_name = "expireTime",
    (gnostic.openapi.v3.property) = {description: "过期日期"}
  ]; // 过期日期
  optional string owner_id = 6 [
    json_name = "ownerId",
    (gnostic.openapi.v3.property) = {description: "持有人"}
  ]; // 持有人
  optional string equipment_type_id = 7 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "装备分类"}
  ]; // 装备分类
  optional string equipment_id = 8 [
    json_name = "equipmentId",
    (gnostic.openapi.v3.property) = {description: "装备"}
  ]; // 装备
  optional string repository_area_id = 9 [
    json_name = "repositoryAreaId",
    (gnostic.openapi.v3.property) = {description: "库区"}
  ]; // 库区
  optional uint32 num = 10 [
    json_name = "num",
    (gnostic.openapi.v3.property) = {description: "数量"}
  ]; // 数量
  optional string measure_unit_id = 11 [
    json_name = "measureUnitId",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位
  optional string model_no = 12 [
    json_name = "modelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string repository_id = 13 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional string fire_station_id = 14 [
    json_name = "fireStationId",
    (gnostic.openapi.v3.property) = {description: "消防站"}
  ]; // 消防站
  optional string repository_position_id = 15 [
    json_name = "repositoryPositionId",
    (gnostic.openapi.v3.property) = {description: "库位"}
  ]; // 库位
  optional string code = 16 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "编码"}
  ]; // 编码
  optional double price = 17 [
    json_name = "price",
    (gnostic.openapi.v3.property) = {description: "单价"}
  ]; // 单价
  optional string provider_id = 18 [
    json_name = "providerId",
    (gnostic.openapi.v3.property) = {description: "供应商"}
  ]; // 供应商
  optional string code_type = 19 [
    json_name = "codeType",
    (gnostic.openapi.v3.property) = {description: "编码类型"}
  ]; // 编码类型
  optional int32 equipment_status = 20 [
    json_name = "equipmentStatus",
    (gnostic.openapi.v3.property) = {description: "装备状态"}
  ]; // 装备状态
  optional bool is_one_material_one_code = 21 [
    json_name = "isOneMaterialOneCode",
    (gnostic.openapi.v3.property) = {description: "是否一物一码"}
  ]; // 是否一物一码
  optional int32 discard_method = 22 [
    json_name = "discardMethod",
    (gnostic.openapi.v3.property) = {description: "报废方式"}
  ]; // 报废方式
  optional google.protobuf.Struct feature = 23 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格
  optional string original_id = 24 [
    json_name = "originalId",
    (gnostic.openapi.v3.property) = {description: "原始ID"}
  ]; // 原始ID
  string key = 25 [
    json_name = "key",
    (gnostic.openapi.v3.property) = {description: "key"}
  ]; // key
  optional string finance_system_no = 26 [
    json_name = "financeSystemNo",
    (gnostic.openapi.v3.property) = {description: "财务系统编号"}
  ]; // 财务系统编号
  optional string use_start_time = 27 [
    json_name = "useStartTime",
    (gnostic.openapi.v3.property) = {description: "使用开始时间"}
  ]; // 使用开始时间
}

message UpdateWmsMaterialRequest {
  string id = 1;
  optional int32 status = 2 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "状态：0 禁用、 1启用"}
  ]; // 状态：0 禁用、 1启用
  optional string remark = 3 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string fire_station_id = 4 [
    json_name = "fireStationId",
    (gnostic.openapi.v3.property) = {description: "消防站"}
  ]; // 消防站
  optional string material_type = 5 [
    json_name = "materialType",
    (gnostic.openapi.v3.property) = {description: "装备类型"}
  ]; // 装备类型
  optional string equipment_type_id = 6 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "装备分类"}
  ]; // 装备分类
  optional string equipment_id = 7 [
    json_name = "equipmentId",
    (gnostic.openapi.v3.property) = {description: "装备"}
  ]; // 装备
  optional string code = 8 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "编码"}
  ]; // 编码
  optional string code_type = 9 [
    json_name = "codeType",
    (gnostic.openapi.v3.property) = {description: "编码类型"}
  ]; // 编码类型
  optional string name = 10 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "装备名称"}
  ]; // 装备名称
  optional string model_no = 11 [
    json_name = "modelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string provider_id = 12 [
    json_name = "providerId",
    (gnostic.openapi.v3.property) = {description: "供应商"}
  ]; // 供应商
  optional uint32 num = 13 [
    json_name = "num",
    (gnostic.openapi.v3.property) = {description: "数量"}
  ]; // 数量
  optional double price = 14 [
    json_name = "price",
    (gnostic.openapi.v3.property) = {description: "单价"}
  ]; // 单价
  optional string repository_id = 15 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional string repository_area_id = 16 [
    json_name = "repositoryAreaId",
    (gnostic.openapi.v3.property) = {description: "库区"}
  ]; // 库区
  optional string repository_position_id = 17 [
    json_name = "repositoryPositionId",
    (gnostic.openapi.v3.property) = {description: "库位"}
  ]; // 库位
  optional string measure_unit_id = 18 [
    json_name = "measureUnitId",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位
  optional string expire_time = 19 [
    json_name = "expireTime",
    (gnostic.openapi.v3.property) = {description: "过期日期"}
  ]; // 过期日期
  optional int32 equipment_status = 20 [
    json_name = "equipmentStatus",
    (gnostic.openapi.v3.property) = {description: "装备状态"}
  ]; // 装备状态
  optional string owner_id = 21 [
    json_name = "ownerId",
    (gnostic.openapi.v3.property) = {description: "持有人"}
  ]; // 持有人
  optional bool is_one_material_one_code = 22 [
    json_name = "isOneMaterialOneCode",
    (gnostic.openapi.v3.property) = {description: "是否一物一码"}
  ]; // 是否一物一码
  optional int32 discard_method = 23 [
    json_name = "discardMethod",
    (gnostic.openapi.v3.property) = {description: "报废方式"}
  ]; // 报废方式
  optional google.protobuf.Struct feature = 24 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "详细规格"}
  ]; // 详细规格
  optional string original_id = 25 [
    json_name = "originalId",
    (gnostic.openapi.v3.property) = {description: "原始ID"}
  ]; // 原始ID
  string key = 26 [
    json_name = "key",
    (gnostic.openapi.v3.property) = {description: "key"}
  ]; // key
  optional string finance_system_no = 27 [
    json_name = "financeSystemNo",
    (gnostic.openapi.v3.property) = {description: "财务系统编号"}
  ]; // 财务系统编号

  optional string use_start_time = 28 [
    json_name = "useStartTime",
    (gnostic.openapi.v3.property) = {description: "使用开始时间"}
  ]; // 使用开始时间
}

message DeleteWmsMaterialRequest {
  string id = 1;
}

message MultiDeleteWmsMaterialRequest {
  repeated string ids = 1;
}

message ListWmsMaterialRequest {
  // 当前页码
  optional int32 page = 1 [
    json_name = "page",
    (gnostic.openapi.v3.property) = {
      description: "当前页码",
      default: {number: 1}
    }
  ];

  // 每页的行数
  optional int32 page_size = 2 [
    json_name = "pageSize",
    (gnostic.openapi.v3.property) = {
      description: "每一页的行数",
      default: {number: 10}
    }
  ];

  // 与过滤参数
  optional string query = 3 [
    json_name = "query",
    (gnostic.openapi.v3.property) = {
      description: "与过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 或过滤参数
  optional string or_query = 4 [
    json_name = "or",
    (gnostic.openapi.v3.property) = {
      description: "或过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 排序条件
  string order_by = 5 [
    json_name = "orderBy",
    (gnostic.openapi.v3.property) = {
      description: "排序条件，字段名前加'-'为降序，否则为升序。"
      example: {yaml: "{\"val1\", \"-val2\"}"}
    }
  ];

  // 是否不分页
  optional bool no_paging = 6 [
    json_name = "nopaging",
    (gnostic.openapi.v3.property) = {description: "是否不分页"}
  ];

  // 字段掩码
  google.protobuf.FieldMask field_mask = 7 [
    json_name = "fieldMask",
    (gnostic.openapi.v3.property) = {
      description: "字段掩码，如果为空则选中所有字段。",
      example: {yaml: "id,realName,userName"}
    }
  ];
}

message GetWmsMaterialByCodeReqest {
  string code = 1;
}

message GetWmsMaterialByCodeFeatureRepositoryRequest {
  string code = 1;
  optional google.protobuf.Struct feature = 2;
  optional string repository_id = 3 [
    json_name = "repositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional string repository_area_id = 4 [
    json_name = "repositoryAreaId",
    (gnostic.openapi.v3.property) = {description: "库区"}
  ]; // 库区
  optional string repository_position_id = 5 [
    json_name = "repositoryPositionId",
    (gnostic.openapi.v3.property) = {description: "库位"}
  ]; // 库位
}

message GetWmsMaterialPageCodeRequest {
  string name = 1;
  string code = 2;
}