syntax = "proto3";

package wms.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";

service WmsReturnOrderDetailService {
  // 获取退还单明细列表
  rpc ListWmsReturnOrderDetail(ListWmsReturnOrderDetailRequest) returns (ListWmsReturnOrderDetailResponse) {
    option (google.api.http) = {get: "/wms/v1/wmsReturnOrderDetail"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["退还管理"]
    };
  }

  // 获取退还单明细详情
  rpc GetWmsReturnOrderDetail(GetWmsReturnOrderDetailRequest) returns (WmsReturnOrderDetail) {
    option (google.api.http) = {get: "/wms/v1/wmsReturnOrderDetail/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["退还管理"]
    };
  }

  // 创建退还单明细
  rpc CreateWmsReturnOrderDetail(CreateWmsReturnOrderDetailRequest) returns (WmsReturnOrderDetail) {
    option (google.api.http) = {
      post: "/wms/v1/wmsReturnOrderDetail"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["退还管理"]
    };
  }

  // 更新退还单明细
  rpc UpdateWmsReturnOrderDetail(UpdateWmsReturnOrderDetailRequest) returns (WmsReturnOrderDetail) {
    option (google.api.http) = {
      put: "/wms/v1/wmsReturnOrderDetail/{id}"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["退还管理"]
    };
  }

  // 删除退还单明细
  rpc DeleteWmsReturnOrderDetail(DeleteWmsReturnOrderDetailRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/wms/v1/wmsReturnOrderDetail/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["退还管理"]
    };
  }

  // 批量删除退还单明细
  rpc MultiDeleteWmsReturnOrderDetail(MultiDeleteWmsReturnOrderDetailRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/wms/v1/wmsReturnOrderDetail/delete"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["退还管理"]
    };
  }
}

message WmsReturnOrderDetail {
  string id = 1;
  optional string created_at = 2 [
    json_name = "createdAt",
    (gnostic.openapi.v3.property) = {description: "创建时间"}
  ]; // 创建时间
  optional string updated_at = 3 [
    json_name = "updatedAt",
    (gnostic.openapi.v3.property) = {description: "更新时间"}
  ]; // 更新时间
  optional string created_by = 4 [
    json_name = "createdBy",
    (gnostic.openapi.v3.property) = {description: "创建人"}
  ]; // 创建人
  optional string updated_by = 5 [
    json_name = "updatedBy",
    (gnostic.openapi.v3.property) = {description: "更新人"}
  ]; // 更新人
  optional string remark = 6 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string order_id = 7 [
    json_name = "orderId",
    (gnostic.openapi.v3.property) = {description: "退还单"}
  ]; // 退还单
  optional string material_id = 8 [
    json_name = "materialId",
    (gnostic.openapi.v3.property) = {description: "物料"}
  ]; // 物料
  optional string material_name = 9 [
    json_name = "materialName",
    (gnostic.openapi.v3.property) = {description: "物料名称"}
  ]; // 物料名称
  optional string code = 10 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "编码"}
  ]; // 编码
  optional string equipment_id = 11 [
    json_name = "equipmentId",
    (gnostic.openapi.v3.property) = {description: "装备"}
  ]; // 装备
  optional string equipment_type_id = 12 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "装备类型"}
  ]; // 装备类型
  optional google.protobuf.Struct feature = 13 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "特征"}
  ]; // 特征
  optional string owner_id = 14 [
    json_name = "ownerId",
    (gnostic.openapi.v3.property) = {description: "归属人"}
  ]; // 归属人
  optional string model_no = 15 [
    json_name = "ModelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string measure_unit_id = 16 [
    json_name = "measureUnitId",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位
  optional uint32 num = 17 [
    json_name = "num",
    (gnostic.openapi.v3.property) = {description: "退还数量"}
  ]; // 退还数量
  optional string to_repository_id = 18 [
    json_name = "toRepositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional string to_repository_area_id = 19 [
    json_name = "toRepositoryAreaId",
    (gnostic.openapi.v3.property) = {description: "库区"}
  ]; // 库区
  optional string to_repository_position_id = 20 [
    json_name = "toRepositoryPositionId",
    (gnostic.openapi.v3.property) = {description: "库位"}
  ]; // 库位
  optional string reason = 21 [
    json_name = "reason",
    (gnostic.openapi.v3.property) = {description: "退还原因"}
  ]; // 退还原因
  optional string return_time = 22 [
    json_name = "returnTime",
    (gnostic.openapi.v3.property) = {description: "退还时间"}
  ]; // 退还时间

  optional string feature_str = 23 [
    json_name = "featureStr",
    (gnostic.openapi.v3.property) = {description: "特征字符串"}
  ]; // 特征字符串
}

message ListWmsReturnOrderDetailResponse {
  repeated WmsReturnOrderDetail items = 1;
  int32 total = 2;
}

message GetWmsReturnOrderDetailRequest {
  string id = 1;
}

message CreateWmsReturnOrderDetailRequest {
  string remark = 1 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string equipment_type_id = 2 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "装备类型"}
  ]; // 装备类型
  optional string owner_id = 3 [
    json_name = "ownerId",
    (gnostic.openapi.v3.property) = {description: "归属人"}
  ]; // 归属人
  optional google.protobuf.Struct feature = 4 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "特征"}
  ]; // 特征
  optional string order_id = 5 [
    json_name = "orderId",
    (gnostic.openapi.v3.property) = {description: "退还单"}
  ]; // 退还单
  optional string equipment_id = 6 [
    json_name = "equipmentId",
    (gnostic.openapi.v3.property) = {description: "装备"}
  ]; // 装备
  optional uint32 num = 7 [
    json_name = "num",
    (gnostic.openapi.v3.property) = {description: "退还数量"}
  ]; // 退还数量
  optional string to_repository_area_id = 8 [
    json_name = "toRepositoryAreaId",
    (gnostic.openapi.v3.property) = {description: "库区"}
  ]; // 库区
  optional string material_id = 9 [
    json_name = "materialId",
    (gnostic.openapi.v3.property) = {description: "物料"}
  ]; // 物料
  optional string model_no = 10 [
    json_name = "ModelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string reason = 11 [
    json_name = "reason",
    (gnostic.openapi.v3.property) = {description: "退还原因"}
  ]; // 退还原因
  optional string to_repository_id = 12 [
    json_name = "toRepositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional string code = 13 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "编码"}
  ]; // 编码
  optional string to_repository_position_id = 14 [
    json_name = "toRepositoryPositionId",
    (gnostic.openapi.v3.property) = {description: "库位"}
  ]; // 库位
  optional string material_name = 15 [
    json_name = "materialName",
    (gnostic.openapi.v3.property) = {description: "物料名称"}
  ]; // 物料名称
  optional string measure_unit_id = 16 [
    json_name = "measureUnitId",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位
  optional string return_time = 17 [
    json_name = "returnTime",
    (gnostic.openapi.v3.property) = {description: "退还时间"}
  ]; // 退还时间
}

message UpdateWmsReturnOrderDetailRequest {
  string id = 1;
  optional string remark = 2 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string order_id = 3 [
    json_name = "orderId",
    (gnostic.openapi.v3.property) = {description: "退还单"}
  ]; // 退还单
  optional string material_id = 4 [
    json_name = "materialId",
    (gnostic.openapi.v3.property) = {description: "物料"}
  ]; // 物料
  optional string material_name = 5 [
    json_name = "materialName",
    (gnostic.openapi.v3.property) = {description: "物料名称"}
  ]; // 物料名称
  optional string code = 6 [
    json_name = "code",
    (gnostic.openapi.v3.property) = {description: "编码"}
  ]; // 编码
  optional string equipment_id = 7 [
    json_name = "equipmentId",
    (gnostic.openapi.v3.property) = {description: "装备"}
  ]; // 装备
  optional string equipment_type_id = 8 [
    json_name = "equipmentTypeId",
    (gnostic.openapi.v3.property) = {description: "装备类型"}
  ]; // 装备类型
  optional google.protobuf.Struct feature = 9 [
    json_name = "feature",
    (gnostic.openapi.v3.property) = {description: "特征"}
  ]; // 特征
  optional string owner_id = 10 [
    json_name = "ownerId",
    (gnostic.openapi.v3.property) = {description: "归属人"}
  ]; // 归属人
  optional string model_no = 11 [
    json_name = "ModelNo",
    (gnostic.openapi.v3.property) = {description: "规格型号"}
  ]; // 规格型号
  optional string measure_unit_id = 12 [
    json_name = "measureUnitId",
    (gnostic.openapi.v3.property) = {description: "计量单位"}
  ]; // 计量单位
  optional uint32 num = 13 [
    json_name = "num",
    (gnostic.openapi.v3.property) = {description: "退还数量"}
  ]; // 退还数量
  optional string to_repository_id = 14 [
    json_name = "toRepositoryId",
    (gnostic.openapi.v3.property) = {description: "仓库"}
  ]; // 仓库
  optional string to_repository_area_id = 15 [
    json_name = "toRepositoryAreaId",
    (gnostic.openapi.v3.property) = {description: "库区"}
  ]; // 库区
  optional string to_repository_position_id = 16 [
    json_name = "toRepositoryPositionId",
    (gnostic.openapi.v3.property) = {description: "库位"}
  ]; // 库位
  optional string reason = 17 [
    json_name = "reason",
    (gnostic.openapi.v3.property) = {description: "退还原因"}
  ]; // 退还原因
  optional string return_time = 18 [
    json_name = "returnTime",
    (gnostic.openapi.v3.property) = {description: "退还时间"}
  ]; // 退还时间
}

message DeleteWmsReturnOrderDetailRequest {
  string id = 1;
}

message MultiDeleteWmsReturnOrderDetailRequest {
  repeated string ids = 1;
}

message ListWmsReturnOrderDetailRequest {
  // 当前页码
  optional int32 page = 1 [
    json_name = "page",
    (gnostic.openapi.v3.property) = {
      description: "当前页码",
      default: {number: 1}
    }
  ];

  // 每页的行数
  optional int32 page_size = 2 [
    json_name = "pageSize",
    (gnostic.openapi.v3.property) = {
      description: "每一页的行数",
      default: {number: 10}
    }
  ];

  // 与过滤参数
  optional string query = 3 [
    json_name = "query",
    (gnostic.openapi.v3.property) = {
      description: "与过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 或过滤参数
  optional string or_query = 4 [
    json_name = "or",
    (gnostic.openapi.v3.property) = {
      description: "或过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 排序条件
  string order_by = 5 [
    json_name = "orderBy",
    (gnostic.openapi.v3.property) = {
      description: "排序条件，字段名前加'-'为降序，否则为升序。"
      example: {yaml: "{\"val1\", \"-val2\"}"}
    }
  ];

  // 是否不分页
  optional bool no_paging = 6 [
    json_name = "nopaging",
    (gnostic.openapi.v3.property) = {description: "是否不分页"}
  ];

  // 字段掩码
  google.protobuf.FieldMask field_mask = 7 [
    json_name = "fieldMask",
    (gnostic.openapi.v3.property) = {
      description: "字段掩码，如果为空则选中所有字段。",
      example: {yaml: "id,realName,userName"}
    }
  ];
}
