syntax = "proto3";

package wms.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";
import "wms/v1/wms_maintain_order_detail.proto";

service WmsMaintainOrderService {
  // 获取保养单列表
  rpc ListWmsMaintainOrder(ListWmsMaintainOrderRequest) returns (ListWmsMaintainOrderResponse) {
    option (google.api.http) = {get: "/wms/v1/wmsMaintainOrder"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["保养管理"]
    };
  }

  // 获取保养单详情
  rpc GetWmsMaintainOrder(GetWmsMaintainOrderRequest) returns (WmsMaintainOrder) {
    option (google.api.http) = {get: "/wms/v1/wmsMaintainOrder/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["保养管理"]
    };
  }

  // 创建保养单
  rpc CreateWmsMaintainOrder(CreateWmsMaintainOrderRequest) returns (WmsMaintainOrder) {
    option (google.api.http) = {
      post: "/wms/v1/wmsMaintainOrder"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["保养管理"]
    };
  }

  // 更新保养单
  rpc UpdateWmsMaintainOrder(UpdateWmsMaintainOrderRequest) returns (WmsMaintainOrder) {
    option (google.api.http) = {
      put: "/wms/v1/wmsMaintainOrder/{id}"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["保养管理"]
    };
  }

  // 删除保养单
  rpc DeleteWmsMaintainOrder(DeleteWmsMaintainOrderRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/wms/v1/wmsMaintainOrder/{id}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["保养管理"]
    };
  }

  // 批量删除保养单
  rpc MultiDeleteWmsMaintainOrder(MultiDeleteWmsMaintainOrderRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/wms/v1/wmsMaintainOrder/delete"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: ["保养管理"]
    };
  }
}

message WmsMaintainOrder {
  string id = 1;
  optional string created_at = 2 [
    json_name = "createdAt",
    (gnostic.openapi.v3.property) = {description: "创建时间"}
  ]; // 创建时间
  optional string updated_at = 3 [
    json_name = "updatedAt",
    (gnostic.openapi.v3.property) = {description: "更新时间"}
  ]; // 更新时间
  optional string created_by = 4 [
    json_name = "createdBy",
    (gnostic.openapi.v3.property) = {description: "创建人"}
  ]; // 创建人
  optional string updated_by = 5 [
    json_name = "updatedBy",
    (gnostic.openapi.v3.property) = {description: "更新人"}
  ]; // 更新人
  optional string remark = 6 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string contract_urls = 7 [
    json_name = "contractUrls",
    (gnostic.openapi.v3.property) = {description: "合同附件"}
  ]; // 合同附件
  optional string invoice_urls = 8 [
    json_name = "invoiceUrls",
    (gnostic.openapi.v3.property) = {description: "发票附件"}
  ]; // 发票附件
  optional string audit_urls = 9 [
    json_name = "auditUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string other_urls = 10 [
    json_name = "otherUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string order_no = 11 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "保养单号"}
  ]; // 保养单号
  optional uint32 equipment_num = 12 [
    json_name = "equipmentNum",
    (gnostic.openapi.v3.property) = {description: "装备数量"}
  ]; // 装备数量
  optional string status = 13 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "保养状态"}
  ]; // 保养状态
  optional string maintain_time = 14 [
    json_name = "maintainTime",
    (gnostic.openapi.v3.property) = {description: "保养时间"}
  ]; // 保养时间

  repeated WmsMaintainOrderDetail details = 15 [
    json_name = "details",
    (gnostic.openapi.v3.property) = {description: "保养单明细"}
  ]; // 保养单明细

  optional string create_by_organization_name = 16 [
    json_name = "createByOrganizationName",
    (gnostic.openapi.v3.property) = {description: "创建人部门"}
  ]; // 创建人部门
}

message ListWmsMaintainOrderResponse {
  repeated WmsMaintainOrder items = 1;
  int32 total = 2;
}

message GetWmsMaintainOrderRequest {
  string id = 1;
}

message CreateWmsMaintainOrderRequest {
  string remark = 1 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string invoice_urls = 2 [
    json_name = "invoiceUrls",
    (gnostic.openapi.v3.property) = {description: "发票附件"}
  ]; // 发票附件
  optional string contract_urls = 3 [
    json_name = "contractUrls",
    (gnostic.openapi.v3.property) = {description: "合同附件"}
  ]; // 合同附件
  optional string audit_urls = 4 [
    json_name = "auditUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string other_urls = 5 [
    json_name = "otherUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string order_no = 6 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "保养单号"}
  ]; // 保养单号
  optional uint32 equipment_num = 7 [
    json_name = "equipmentNum",
    (gnostic.openapi.v3.property) = {description: "装备数量"}
  ]; // 装备数量
  optional string status = 8 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "保养状态"}
  ]; // 保养状态
  optional string maintain_time = 9 [
    json_name = "maintainTime",
    (gnostic.openapi.v3.property) = {description: "保养时间"}
  ]; // 保养时间

  repeated CreateWmsMaintainOrderDetailRequest details = 10 [
    json_name = "details",
    (gnostic.openapi.v3.property) = {description: "保养单明细"}
  ]; // 保养单明细
}

message UpdateWmsMaintainOrderRequest {
  string id = 1;
  optional string remark = 2 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {description: "备注"}
  ]; // 备注
  optional string contract_urls = 3 [
    json_name = "contractUrls",
    (gnostic.openapi.v3.property) = {description: "合同附件"}
  ]; // 合同附件
  optional string invoice_urls = 4 [
    json_name = "invoiceUrls",
    (gnostic.openapi.v3.property) = {description: "发票附件"}
  ]; // 发票附件
  optional string audit_urls = 5 [
    json_name = "auditUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string other_urls = 6 [
    json_name = "otherUrls",
    (gnostic.openapi.v3.property) = {description: "会审凭证附件"}
  ]; // 会审凭证附件
  optional string order_no = 7 [
    json_name = "orderNo",
    (gnostic.openapi.v3.property) = {description: "保养单号"}
  ]; // 保养单号
  optional uint32 equipment_num = 8 [
    json_name = "equipmentNum",
    (gnostic.openapi.v3.property) = {description: "装备数量"}
  ]; // 装备数量
  optional string status = 9 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {description: "保养状态"}
  ]; // 保养状态
  optional string maintain_time = 10 [
    json_name = "maintainTime",
    (gnostic.openapi.v3.property) = {description: "保养时间"}
  ]; // 保养时间

  repeated CreateWmsMaintainOrderDetailRequest details = 11 [
    json_name = "details",
    (gnostic.openapi.v3.property) = {description: "保养单明细"}
  ]; // 保养单明细
}

message DeleteWmsMaintainOrderRequest {
  string id = 1;
}

message MultiDeleteWmsMaintainOrderRequest {
  repeated string ids = 1;
}

message ListWmsMaintainOrderRequest {
  // 当前页码
  optional int32 page = 1 [
    json_name = "page",
    (gnostic.openapi.v3.property) = {
      description: "当前页码",
      default: {number: 1}
    }
  ];

  // 每页的行数
  optional int32 page_size = 2 [
    json_name = "pageSize",
    (gnostic.openapi.v3.property) = {
      description: "每一页的行数",
      default: {number: 10}
    }
  ];

  // 与过滤参数
  optional string query = 3 [
    json_name = "query",
    (gnostic.openapi.v3.property) = {
      description: "与过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 或过滤参数
  optional string or_query = 4 [
    json_name = "or",
    (gnostic.openapi.v3.property) = {
      description: "或过滤参数",
      example: {yaml: "{\"key1\":\"val1\",\"key2\":\"val2\"}"}
    }
  ];

  // 排序条件
  string order_by = 5 [
    json_name = "orderBy",
    (gnostic.openapi.v3.property) = {
      description: "排序条件，字段名前加'-'为降序，否则为升序。"
      example: {yaml: "{\"val1\", \"-val2\"}"}
    }
  ];

  // 是否不分页
  optional bool no_paging = 6 [
    json_name = "nopaging",
    (gnostic.openapi.v3.property) = {description: "是否不分页"}
  ];

  // 字段掩码
  google.protobuf.FieldMask field_mask = 7 [
    json_name = "fieldMask",
    (gnostic.openapi.v3.property) = {
      description: "字段掩码，如果为空则选中所有字段。",
      example: {yaml: "id,realName,userName"}
    }
  ];
}
