test:
  name: demo
cache:
  auth:
    # 用户角色缓存时间
    user_role_bindings_expire: 1s
user:
  encode_key: c67f89017a384969
  password_salt_legnth: 6
  strong_password:
    enabled: true
    rules:
      must_contain_number: true
      must_contain_uppercase: true
      must_contain_lowercase: true
      must_contain_special: true
      min_length: 8
      max_length: 20
  security:
    enabled: true
    rules:
      # 登录失败次数
      login_fail_count: 5
      # 登录失败次数检查时间段 N秒内登录失败次数超过N次则锁定账号
      login_fail_count_check_duration: 600s
      # 登录失败锁定时间
      login_fail_lock_duration: 86400s
      # 登录后N秒内无操作则自动退出
      login_no_operation_duration: 432000s
      # 密码更换时间间隔
      password_change_duration: 15552000s
      # 脱敏数据
      desensitization:
        enabled: true
        fields: 
          - phone
          - nickname
jwt:
  secret: 9F11B4E503C7F2B577E5F9366BDDAB64
  expire: 604800s
upload:
  local:
    path: ./statics/files
    url: /uploads/statics/files
    host: http://file.119.net:15001
message:
  app:
    service:
      - aliyun_service
    aliyun:
      - name: aliyun_service
        app_key: 28275857
        ios_app_key: 28371281
        access_key_id: LTAI4FsciaygtvCKvXWyXHTB
        access_key_secret: ******************************
        endpoint: https://cloudpush.aliyuncs.com
  sms:
    service:
      - tencent_service
    tencent:
      - name: tencent_service
        secret_id: AKIDtY41h8jM3W9nTi7S3NQUHnANRceUsjws
        secret_key: pOhkWIp7Y1LWdidKnFUaVTGtLgc3JNZb
        app_id: 1400193769
  wechat:
    service:
      # - tengyu_service
    access_token_api: https://api.weixin.qq.com/cgi-bin/token
    send_message_api: https://api.weixin.qq.com/cgi-bin/message/template/send
    check:
      interval: 3000s
    services:
      - name: tengyu_service
        app_id: wx3c768bb259ac26f4
        secret: 7488957fb1476257cf445ff752db0f2d
  wxwork:
    service:
      # - tengyu_service
    access_token_api: https://qyapi.weixin.qq.com/cgi-bin/gettoken
    send_message_api: https://qyapi.weixin.qq.com/cgi-bin/message/send
    wecom_update_template_card_api: https://qyapi.weixin.qq.com/cgi-bin/message/update_template_card
    services:
      - name: tengyu_service
        corp_id: wwff763906648e2c1a
        corp_secret: bzI-rWB0r8vnKFvtg0LhHQqgJm1GHx7sJdNAC_IbgZE
        agent_id: 1000018

oauth:
  work_wechat:
    corp_id: wwff763906648e2c1a
    agent_id: 1000018
    corp_secret: bzI-rWB0r8vnKFvtg0LhHQqgJm1GHx7sJdNAC_IbgZE
    msg_callback_token: XEQuj8aTpelRaWh7DX2G
    msg_callback_aes_key: 51v8XhEn7ibUnhj4McBjAGmDLNUXrdPd5jp2fhg35Ii
    check_file_url: /WW_verify_GbYqkY5jeuQbh11O.txt
    check_file_content: GbYqkY5jeuQbh11O

workflow:
  url: http://127.0.0.1:8000
  # url: http://file.119.net:8080

# 通道门
access_door:
  # 中间件服务器地址
  middleware_server: http://192.168.20.115:10090
  # 心跳超时时间
  heartbeat_timeout: 12s
  # 读取标签超时时间
  tag_read_timeout: 5s
  # 遮挡读取超时时间
  gpi_read_timeout: 5s
  # 读取标签成功最小值
  tag_read_rc_min_count: 1

# GPS
gps:
  # 端口
  port: 13000
  # 保持连接时间
  keepalive: 60s

uni_login:
  url: http://173.82.77.3/api/ext/hp/userCenter/getUser
  app_key: 7ac17a9443333dsafd323232323f73
  app_secret: 94135837fd5safddasfdsf3abe8981e

# 图像识别
ocr:
  port: 13003
  service: tencent_service
  tencent:
      - name: tencent_service
        secret_id: AKIDd4IJ6g292gMtELEuMaaR4UgdSDkxvfBT
        secret_key: 94kCQVP5i8KI2pWzFG3e2Zks02ugMBzk
        endpoint: ocr.ap-shanghai.tencentcloudapi.com
captcha:
    keyLong: 4 // 验证码长度
    imgWidth: 240 // 验证码宽度
    imgHeight: 80 // 验证码高度
    openCaptcha: 3 // 防爆破验证码开启此数，0代表每次登录都需要验证码，其他数字代表错误密码此数，如3代表错误三次后出现验证码
    openCaptchaTimeout: 3600 // 防爆破验证码超时时间，单位：s(秒) 
 
share_data:
  net119_mysql_host: '***************'
  net119_mysql_port: '3306'
  net119_mysql_db: hp_net119
  net119_mysql_user: root
  net119_mysql_pass: '123456'

# 定时任务
crontab:
  enabled: false
  jobs:
    # 同步仓库统计 每日一点执行一次
    - name: sync_repository_statistics
      spec: "0 0 1 * * ?"