data:
  database:
    driver: "postgres"
    source: "host=postgres port=5432 user=postgres password=123456 dbname=demo sslmode=disable TimeZone=Asia/Shanghai"
    migrate: false
    debug: true
    max_idle_connections: 25
    max_open_connections: 25
    connection_max_lifetime: 300s

  redis:
    addr: "redis:6379"
    password: "123456"
    dial_timeout: 10s
    read_timeout: 0.4s
    write_timeout: 0.6s
  temporal:
    host_port: "temporal:7233"
    namespace: "default"