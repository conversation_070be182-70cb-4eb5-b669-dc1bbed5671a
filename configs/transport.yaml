server:
  rest:
    addr: "0.0.0.0:8000"
    timeout: 1000s
    enable_swagger: true
    enable_pprof: false
    cors:
      headers:
        - "X-Requested-With"
        - "Content-Type"
        - "Authorization"
      methods:
        - "GET"
        - "POST"
        - "PUT"
        - "DELETE"
        - "HEAD"
        - "OPTIONS"
      origins:
        - "*"
    middleware:
      enable_logging: true
      enable_recovery: true
      enable_tracing: true
      enable_validate: true
      enable_metadata: true
      auth:
        method: "HS256"
        key: "some_api_key"
      limiter:
        name: "bbr"
    white_list:
      - path: "/system/v1/sysUser/login"
        method: "POST"
      - path: "/system/v1/sysUser/uniLogin"
        method: "POST"        
      - path: "/system/v1/sysApplication/system/sysMenu"
        method: "GET"
      - path: "/WW_verify_GbYqkY5jeuQbh11O.txt"
        method: "GET"
      - path: "/system/v1/sysUser/loginWithWorkWechat"
        method: "POST"
      - path: "/wms/v1/workflow/event/receive"
        method: "POST"
      - path: "/system/v1/workWechatLoginUrl"
        method: "GET"
      - path: "/wms/v1/wmsRfidReaderRecord/post"
        method: "POST"
      - path: "/system/v1/sysUser/bindWxwork"
        method: "POST"
      - path: "/wms/v1/dashboard/repositoryScreen/repositoryMaterials"
        method: "GET"
      - path: "/wms/v1/dashboard/repositoryScreen/equipmentTypeStatistics"
        method: "GET"
      - path: "/wms/v1/dashboard/repositoryScreen/equipmentTypeSubLevel1Statistics"
        method: "GET"
      - path: "/wms/v1/getWmsRepositoryScreen"
        method: "GET"
      - path: "/wms/v1/dashboard/repositoryScreen/statistics"
        method: "GET"
      - path: '/system/v1/sysUser/captcha'
        method: 'GET'    
      - path: "/wms/v1/wmsAccessDoor/ping"
        method: "POST"
      - path: "/workwx/v1/jsSDKSignature"
        method: "GET"
      - path: "/workwx/v1/jsSDKConfigSignature"
        method: "GET"
  grpc:
    addr: "0.0.0.0:9000"
    timeout: 10s
    middleware:
      enable_logging: true
      enable_recovery: true
      enable_tracing: true
      enable_validate: true
  asynq:
    endpoint: "localhost:6379"
    password: "123456"
    db: 1

client:
  grpc:
    timeout: 10s
    middleware:
      enable_logging: true
      enable_recovery: true
      enable_tracing: true
      enable_validate: true
      enable_circuit_breaker: true
      auth:
        method: ""
        key: "some_api_key"
